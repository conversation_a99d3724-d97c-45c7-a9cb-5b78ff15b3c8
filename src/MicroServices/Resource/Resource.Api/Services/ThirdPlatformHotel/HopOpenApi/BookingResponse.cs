using Newtonsoft.Json;

namespace Resource.Api.Services.ThirdPlatformHotel.HopOpenApi;

public class BookingResponse : BaseResponse
{
    /// <summary>
    /// 分销商唯一订单号，分销商唯一订单号
    /// </summary>
    [JsonProperty("agentOrderId")]
    public string AgentOrderId { get; set; }

    /// <summary>
    /// 汇智订单号，汇智订单号，预订失败时可能会空
    /// </summary>
    [JsonProperty("orderId")]
    public string? OrderId { get; set; }

    /// <summary>
    /// 订单状态，详见订单状态
    /// orderStatus	说明 1-已确认 2-处理中 3-待处理 4-待付款 6-订单关闭 10-已退款 11-退款中 13-已拒单
    /// </summary>
    [JsonProperty("orderStatus")]
    public int? OrderStatus { get; set; }
}