using Newtonsoft.Json;

namespace Resource.Api.Services.ThirdPlatformHotel.HopOpenApi;

public class SellHotelRequest
{
    /// <summary>
    /// 当前页
    /// </summary>
    [JsonProperty("pageIndex")]
    public long PageIndex { get; set; }

    /// <summary>
    /// 每页条数
    /// </summary>
    [JsonProperty("pageSize")]
    public long PageSize { get; set; }

    /// <summary>
    /// 售卖标识，0 在售酒店，1 全量酒店
    /// </summary>
    [JsonProperty("saleFlag")]
    public long SaleFlag { get; set; }
}
