namespace Resource.Api.Services.ThirdPlatformHotel.HuiZhiHotel;

public class QueryRateplanPriceResult
{
    public RateplanPrice[] Prices { get; set; } = Array.Empty<RateplanPrice>();
}

public class RateplanPrice
{
    /// <summary>
    /// 酒店Id
    /// </summary>
    public string hid { get; set; }

    /// <summary>
    /// 房型Id
    /// </summary>
    public string rid { get; set; }

    /// <summary>
    /// 价格计划id
    /// </summary>
    public string rpid { get; set; }
    /// <summary>
    /// 价格计划名称
    /// </summary>
    public string name { get; set; }
    /// <summary>
    /// 价格计划英文名称
    /// </summary>
    public string en_name { get; set; }

    /// <summary>
    /// 最小提前预订小时数
    /// </summary>
    public int min_adv_hours { get; set; }
    /// <summary>
    /// 最小入住天数（1-365）
    /// </summary>
    public int min_days { get; set; }
    /// <summary>
    /// 最大入住天数（1-365）
    /// </summary>
    public int max_days { get; set; }
    /// <summary>
    /// 入住日期
    /// </summary>
    public DateTime checkin { get; set; }
    /// <summary>
    /// 离店日期
    /// </summary>
    public DateTime checkout { get; set; }
    /// <summary>
    /// 最大入住人数
    /// </summary>
    public int max_occupancy { get; set; }
    /// <summary>
    /// 早餐数
    /// </summary>
    public int breakfast_count { get; set; }
    /// <summary>
    /// 回收时间
    /// </summary>
    public int cutoff_hour { get; set; }

    /// <summary>
    /// 取消政策(最晚免费取消时间)
    /// 晚免费取消时间，当前时间小于最晚免费取消时间则可免费取消订单；当前时间大于最晚免费取消时间或最晚免费取消时间为空则订单不可免费取消；在入住日取消 订单，以实际取消结果为准，避免客人已入住情况
    /// </summary>
    public string new_cancel_policy { get; set; }
    /// <summary>
    /// 国籍限制
    /// 宾客国籍限制，值为城市数据接口返回的 countrycode；为空则不限制国籍，不为空则表示允许售卖国籍
    /// </summary>
    public string national_codes { get; set; }
    /// <summary>
    /// 国籍限制
    /// 宾客国籍限制，值为城市数据接口返回的 countryname；为空则不限制国籍，不为空则表示允许售卖国籍
    /// </summary>
    public string national_names { get; set; }

    public Nightlyrate[] nightlyrate { get; set; }

    /// <summary>
    /// 是否团房 1-是 0-否
    /// </summary>
    public int is_reunion_room { get; set; }

    /// <summary>
    /// 团房起订间数
    /// </summary>
    public int? reunion_room_min_count { get; set; }

    /// <summary>
    /// 是否直采
    /// </summary>
    public int isdirect { get; set; }

    public int? tag { get; set; }

    /// <summary>
    /// 尊享套餐描述信息
    /// </summary>
    public string? premium  { get; set; }
}

public class Nightlyrate
{
    /// <summary>
    /// 报价日期
    /// </summary>
    public DateTime date { get; set; }
    /// <summary>
    /// 每天底价
    /// </summary>
    public decimal cost { get; set; }
    /// <summary>
    /// 房态，true-开房，false-关房
    /// </summary>
    public bool status { get; set; }
    /// <summary>
    /// 可售房量，>0且status = true表示保留房（即时确认房），=0且status = true表示非保留房（待查房需人工确认）
    /// </summary>
    public int roomnum { get; set; }
    /// <summary>
    /// 是否可超售，1-可超售，代表当前房量不受限制，0-不可超售，代表预定的房量必须<=当前房量
    /// </summary>
    public int is_oversell { get; set; }

    /// <summary>
    /// 价格属性，0-日历房，1-商旅
    /// </summary>
    public int sale_channel { get; set; }
}
