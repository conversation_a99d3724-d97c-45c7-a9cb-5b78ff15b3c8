using Contracts.Common.Resource.Enums;

namespace Resource.Api.Services.ThirdPlatformHotel.HuiZhiHotel;

public class CheckAvailabilityResult
{
    /// <summary>
    /// -1(异常)，1(有房)，2(RP无效)，3(超过最大入住人数),4(满房),5(不可超售),
    /// 15(超过最大连住数量),16(不满足最小连住日期),17(不足预订范围),18(不在预订范围)
    /// </summary>
    public int check_code { get; set; }
    /// <summary>
    /// 酒店Id（汇智国际旅游）
    /// </summary>
    public int hid { get; set; }
    /// <summary>
    /// 房型Id
    /// </summary>
    public string rid { get; set; }
    /// <summary>
    /// 报价计划code（汇智国际旅游）
    /// </summary>
    public string rpid { get; set; }
    /// <summary>
    /// 入住日期yyyy-mm-dd
    /// </summary>
    public string checkin { get; set; }
    /// <summary>
    /// 离店日期yyyy-mm-dd
    /// </summary>
    public string checkout { get; set; }
    /// <summary>
    /// 最晚取消日期，当前时间小于最晚取消日期则可免费取消订单；当前时间大于最晚取消日期或最晚取消日期为空则订单不可免费取消
    /// </summary>
    public string cancel_policy { get; set; }
    /// <summary>
    /// 最大入住人数
    /// </summary>
    public int max_occupancy { get; set; }
    /// <summary>
    /// 早餐数
    /// </summary>
    public int breakfast_count { get; set; }
    /// <summary>
    /// 价格属性，0-日历房，1-商旅
    /// </summary>
    public int sale_channel { get; set; }
    /// <summary>
    /// 
    /// </summary>
    public CheckNightlyrate[] nightlyrate { get; set; }

    /// <summary>
    /// 税费提醒
    /// </summary>
    public string? tax_description { get; set; }

    /// <summary>
    /// 税费说明 当接口有返回arrival_tax_fees节点（酒店接口给的），就展示该节点的税费说明
    /// </summary>
    public HotelFee[]? arrival_tax_fees { get; set; }
}

public class CheckNightlyrate
{
    /// <summary>
    /// 报价日期
    /// </summary>
    public DateTime date { get; set; }
    /// <summary>
    /// 每天底价
    /// </summary>
    public decimal cost { get; set; }
    /// <summary>
    /// 房态，true-开房，false-关房
    /// </summary>
    public bool status { get; set; }
    /// <summary>
    /// 可售房量，>0且status = true表示保留房（即时确认房），=0且status = true表示非保留房（待查房需人工确认
    /// </summary>
    public int roomnum { get; set; }
    /// <summary>
    /// 是否可超售，1-可超售，代表当前房量不受限制，0-不可超售，代表预定的房量必须<=当前房量
    /// </summary>
    public int is_oversell { get; set; }
}

public class HotelFee
{
    /// <summary>
    /// 费用类型
    /// </summary>
    public HotelFeeType Type { get; set; }

    /// <summary>
    /// 是否强制，某些酒店可能允许客人选择是否支付度假村费
    /// </summary>
    public bool IsMandatory { get; set; }

    /// <summary>
    /// 费用的具体金额
    /// </summary>
    public decimal Amount { get; set; }

    /// <summary>
    /// 币种
    /// </summary>
    public string Currency { get; set; }
}
