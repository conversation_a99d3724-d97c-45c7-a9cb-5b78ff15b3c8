using Contracts.Common.Resource.DTOs.Airport;
using Contracts.Common.Resource.DTOs.TradingArea;
using FluentValidation;

namespace Resource.Api.Services.Validator.Airport;

public class AddAirportInputValidator : AbstractValidator<AddAirportInput>
{
    public AddAirportInputValidator()
    {
        RuleFor(x => x.AirportZHName).NotEmpty();
        RuleFor(x => x.IATA).NotEmpty().MaximumLength(3);
        RuleFor(x => x.CityCode).NotNull().GreaterThan(0);
        RuleFor(x => x.Longitude).Must(m => Math.Abs(m) <= 180).WithMessage("经度范围错误");
        RuleFor(x => x.Latitude).Must(m => Math.Abs(m) <= 90).WithMessage("纬度范围错误");
    }
}
