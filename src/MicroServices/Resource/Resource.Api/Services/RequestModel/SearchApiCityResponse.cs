using Newtonsoft.Json;

namespace Resource.Api.Services.RequestModel;

public class SearchApiCityResponse : List<SearchApiCityInfo>
{
}

public class SearchApiCityInfo
{
    [JsonProperty("country_code")]
    public int CountryCode { get; set; }

    [JsonProperty("country_name")]
    public string CountryName { get; set; }

    [JsonProperty("country_enname")]
    public string CountryenName { get; set; }

    [JsonProperty("province_code")]
    public int ProvinceCode { get; set; }

    [JsonProperty("province_name")]
    public string ProvinceName { get; set; }

    [JsonProperty("city_code")]
    public int CityCode { get; set; }

    [JsonProperty("city_name")]
    public string CityName { get; set; }

    [JsonProperty("city_enname")]
    public string CityenName { get; set; }
}
