using AutoMapper;
using Contracts.Common.Resource.DTOs.HotelMeeting;
using EfCoreExtensions.Abstract;

namespace Resource.Api.Services.MappingProfiles;

public class HotelMeetingProfiles : Profile
{
    public HotelMeetingProfiles()
    {
        CreateMap<AddHotelMeetingInput, HotelMeeting>();

        CreateMap<UpdateHotelMeetingInput, HotelMeeting>();

        CreateMap<HotelMeeting, HotelMeetingDetailOutput>();

        CreateMap<HotelMeeting, SearchHotelMeetingOuput>();

        CreateMap<PagingModel<HotelMeeting>, PagingModel<SearchHotelMeetingOuput>>();
    }

}
