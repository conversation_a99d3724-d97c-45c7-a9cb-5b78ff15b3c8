using AutoMapper;
using Contracts.Common.Resource.DTOs.City;
using Contracts.Common.Resource.DTOs.EsPopularCity;
using Contracts.Common.Resource.EsDocuments;

namespace Resource.Api.Services.MappingProfiles;

public class EsPopularCityProfiles : Profile
{
    public EsPopularCityProfiles()
    {
        CreateMap<PopularCityDocument, SearchEsPopularCityOutput>()
            .ForMember(x => x.EnCityName, o => o.MapFrom(m => m.CityEnName));
        CreateMap<City, CanalMonitorResourceCityDto>()
             .ForMember(x => x.Longitude, f => f.MapFrom(x => x.Location.X))
             .ForMember(x => x.Latitude, f => f.MapFrom(x => x.Location.Y)); ;
    }
}