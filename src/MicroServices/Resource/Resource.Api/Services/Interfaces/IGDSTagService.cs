using Contracts.Common.Resource.DTOs.GDSTag;

namespace Resource.Api.Services.Interfaces;

public interface IGDSTagService
{
    /// <summary>
    /// 查询分页数据
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<List<SearchGDSTagOutput>> Search(SearchGDSTagInput input);

    /// <summary>
    /// 新增标签
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task Add(AddGDSTagInput input);

    /// <summary>
    /// 编辑标签
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task Edit(EditGDSTagInput input);

    /// <summary>
    /// 删除标签
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    Task Delete(long id);

    /// <summary>
    /// 获取租户下所有标签
    /// </summary>
    /// <returns></returns>
    Task<IEnumerable<GetGDSTagsOutput>> GetAll();

    /// <summary>
    /// 更新排序
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task UpdateSort(UpdateSortInput input);
}
