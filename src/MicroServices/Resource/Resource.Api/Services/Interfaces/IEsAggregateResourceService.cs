using Contracts.Common.Resource.DTOs.AggregateResource;

namespace Resource.Api.Services.Interfaces;

public interface IEsAggregateResourceService
{
    /// <summary>
    /// 目的地筛选器
    /// <list type="bullet">
    /// <item>排序规则：国家 > 城市 > 酒店 > 机场 > 景点 > 城市，相同类型，多条记录按照搜索关键词召回得分进行排序</item>
    /// </list>
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<DestinationSearchOutput> SearchDestinations(DestinationSearchInput input);
}