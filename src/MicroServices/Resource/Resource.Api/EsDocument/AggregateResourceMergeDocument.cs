using Contracts.Common.Resource.Enums;
using Nest;

namespace Resource.Api.EsDocument;

public class AggregateResourceMergeDocument
{
    /// <summary>
    /// es文档id
    /// </summary>
    public string EsKeyId { get; set; }

    /// <summary>
    /// 资源父类型
    /// </summary>
    public EsAggregateResourceParentType ResourceParentType { get; set; }

    /// <summary>
    /// 资源子类型
    /// </summary>
    public EsAggregateResourceSubType ResourceSubType { get; set; }

    /// <summary>
    /// 国家编码
    /// <list type="bullet">
    /// <item>线路产品:目的地国家</item>
    /// <item>其他:所属国家</item> 
    /// </list>
    /// </summary>
    public int CountryCode { get; set; }

    /// <summary>
    /// 省份编码
    /// <list type="bullet">
    /// <item>线路产品:目的地省份</item>
    /// <item>其他:所属省份</item> 
    /// </list>
    /// </summary>
    public int ProvinceCode { get; set; }

    /// <summary>
    /// 城市编码
    /// <list type="bullet">
    /// <item>线路产品:目的地城市</item>
    /// <item>其他:所属城市</item> 
    /// </list>
    /// </summary>
    public int CityCode { get; set; }

    /// <summary>
    /// 资源名称 - 中文
    /// </summary>
    public string ResourceZhName { get; set; }

    /// <summary>
    /// 资源名称 - 英文
    /// </summary>
    public string ResourceEnName { get; set; }
    
    /// <summary>
    /// 资源 - 商圈/酒店地理坐标点
    /// </summary>
    public GeoLocation Location { get; set; }

    #region Data Supplement

    /// <summary>
    /// 相关的id
    /// </summary>
    public long RelatedId { get; set; }

    /// <summary>
    /// 国家中文名
    /// </summary>
    public string CountryZhName { get; set; }

    /// <summary>
    /// 国家英文名
    /// </summary>
    public string CountryEnName { get; set; }

    /// <summary>
    /// 省份中文名
    /// </summary>
    public string ProvinceZhName { get; set; }

    /// <summary>
    /// 省份英文名
    /// </summary>
    public string ProvinceEnName { get; set; }

    /// <summary>
    /// 城市中文名
    /// </summary>
    public string CityZhName { get; set; }

    /// <summary>
    /// 城市英文名
    /// </summary>
    public string CityEnName { get; set; }

    /// <summary>
    /// 三字码
    /// </summary>
    public string IATA { get; set; }

    /// <summary>
    /// 四字码
    /// </summary>
    public string ICAO { get; set; }
    
    /// <summary>
    /// 资源地址 - 中文名
    /// </summary>
    public string AddressZhName { get; set; }

    /// <summary>
    /// 资源地址 - 英文名
    /// </summary>
    public string AddressEnName { get; set; }
    
    /// <summary>
    /// Google位置Id
    /// </summary>
    public string GooglePlaceId { get; set; } 

    #endregion
}