using Contracts.Common.Resource.Enums;
using EfCoreExtensions.EntityBase;

namespace Resource.Api.Model;

public class HotelPhotoExtend : KeyBase
{
    /// <summary>
    /// Id
    /// </summary>
    public long ProductId { get; set; }

    /// <summary>
    /// 类型
    /// </summary>
    public HotelMediaTypeOfPhotos MediaType { get; set; }

    /// <summary>
    /// 地址
    /// </summary>
    public string? Path { get; set; }

    /// <summary>
    /// 排序
    /// </summary>
    public int Sort { get; set; }

    /// <summary>
    /// 是否可用，删除照片时设置为false
    /// </summary>
    public bool Enabled { get; set; }

    public DateTime CreateTime { get; set; } = DateTime.Now;

    public DateTime? UpdateTime { get; set; } = DateTime.Now;

    /// <summary>
    /// 文件名
    /// </summary>
    public string? Name { get; set; }
}
