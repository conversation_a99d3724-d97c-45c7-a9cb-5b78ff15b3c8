using Contracts.Common.Resource.DTOs.GDSHotel;
using EfCoreExtensions.EntityConfigurationsBase;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.ChangeTracking;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Newtonsoft.Json;

namespace Resource.Api.Infrastructure.EntityConfigurations;

public class GDSHotelExtendEntityTypeConfiguration : KeyBaseConfiguration<Model.GDSHotelExtend>, IEntityTypeConfiguration<Model.GDSHotelExtend>
{
    public void Configure(EntityTypeBuilder<Model.GDSHotelExtend> builder)
    {
        base.ConfigureBase(builder);

        builder.Property(s => s.HotelId)
            .HasColumnType("bigint");

        builder.Property(s => s.CheckInFrom)
            .HasColumnType("time");

        builder.Property(s => s.CheckInTo)
            .HasColumnType("time");

        builder.Property(s => s.CheckOutFrom)
            .HasColumnType("time");

        builder.Property(s => s.CheckOutTo)
            .HasColumnType("time");

        builder.Property(s => s.DescribeInfo)
            .HasColumnType("text")
            .HasConversion(
                mod => JsonConvert.SerializeObject(mod),
                str => JsonConvert.DeserializeObject<List<GDSHotelDescribeInfo>>(str))
            .Metadata
            .SetValueComparer(new ValueComparer<List<GDSHotelDescribeInfo>>(
                (c1, c2) => c1.SequenceEqual(c2),
                c => c.Aggregate(0, (a, v) => HashCode.Combine(a, v.GetHashCode())),
                c => c.ToList()));

        builder.Property(s => s.CreateTime)
            .HasColumnType("datetime");

        builder.Property(s => s.UpdateTime)
            .HasColumnType("datetime");

        //索引
        builder.HasIndex(s => s.HotelId);
    }
}
