using Contracts.Common.Resource.Enums;
using EfCoreExtensions.EntityConfigurationsBase;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Resource.Api.Infrastructure.EntityConfigurations
{
    public class HotelMealChildPolicyEntityTypeConfiguration : KeyBaseConfiguration<Model.HotelMealChildPolicy>, IEntityTypeConfiguration<Model.HotelMealChildPolicy>
    {
        public void Configure(EntityTypeBuilder<Model.HotelMealChildPolicy> builder)
        {
            base.ConfigureBase(builder);

            builder.Property(s => s.HotelId)
                .HasColumnType("bigint");

            builder.Property(s => s.Min)
                .HasColumnType("int");

            builder.Property(s => s.Max)
                .HasColumnType("int");

            builder.Property(s => s.LimitType)
                .HasColumnType("tinyint");

            builder.Property(s => s.Amount)
                .HasColumnType("decimal(18,2)");

            builder.Property(s => s.Currency)
                .HasColumnType("varchar(20)");

            builder.Property(s => s.CreateTime)
                .HasColumnType("datetime");

            builder.Property(s => s.UpdateTime)
                .HasColumnType("datetime");


            //索引
            builder.HasIndex(s => s.HotelId);
        }
    }
}
