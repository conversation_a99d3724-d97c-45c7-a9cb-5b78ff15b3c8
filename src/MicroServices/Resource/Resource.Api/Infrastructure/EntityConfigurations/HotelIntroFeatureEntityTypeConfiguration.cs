using EfCoreExtensions.EntityConfigurationsBase;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Resource.Api.Infrastructure.EntityConfigurations
{
    public class HotelIntroFeatureEntityTypeConfiguration : KeyBaseConfiguration<Model.HotelIntroFeature>, IEntityTypeConfiguration<Model.HotelIntroFeature>
    {
        public void Configure(EntityTypeBuilder<Model.HotelIntroFeature> builder)
        {
            base.ConfigureBase(builder);

            builder.Property(s => s.HotelId)
                .HasColumnType("bigint")
                .IsRequired();

            builder.Property(s => s.Name)
                .HasColumnType("varchar(255)");

            builder.Property(s => s.EnName)
                .HasColumnType("varchar(255)");

            builder.Property(s => s.Description)
                .HasColumnType("varchar(500)");

            builder.Property(s => s.EnDescription)
                .HasColumnType("varchar(500)");

            //索引
            builder.HasIndex(s => s.HotelId);
        }
    }
}
