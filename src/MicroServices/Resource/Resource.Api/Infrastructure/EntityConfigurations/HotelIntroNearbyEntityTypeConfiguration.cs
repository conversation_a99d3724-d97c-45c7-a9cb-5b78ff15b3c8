using EfCoreExtensions.EntityConfigurationsBase;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Resource.Api.Infrastructure.EntityConfigurations
{
    public class HotelIntroNearbyEntityTypeConfiguration : KeyBaseConfiguration<Model.HotelIntroNearby>, IEntityTypeConfiguration<Model.HotelIntroNearby>
    {
        public void Configure(EntityTypeBuilder<Model.HotelIntroNearby> builder)
        {
            base.ConfigureBase(builder);

            builder.Property(s => s.HotelId)
                .HasColumnType("bigint")
                .IsRequired();

            builder.Property(s => s.Name)
                .HasColumnType("varchar(255)");

            builder.Property(s => s.EnName)
                .HasColumnType("varchar(255)");

            builder.Property(s => s.Distance)
                .HasColumnType("int");

            builder.Property(s => s.DistanceUnit)
                .HasColumnType("tinyint");

            builder.Property(s => s.UseTime)
                .HasColumnType("int");

            builder.Property(s => s.UseTimeUnit)
               .HasColumnType("tinyint");

            //索引
            builder.HasIndex(s => s.HotelId);
        }
    }
}
