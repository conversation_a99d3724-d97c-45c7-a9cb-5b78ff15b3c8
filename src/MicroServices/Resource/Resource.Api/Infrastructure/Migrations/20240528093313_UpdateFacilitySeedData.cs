using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Resource.Api.Infrastructure.Migrations
{
    public partial class UpdateFacilitySeedData : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DeleteData(
                table: "Facility",
                keyColumn: "Id",
                keyValue: 900668034491519028L);

            migrationBuilder.UpdateData(
                table: "Facility",
                keyColumn: "Id",
                keyValue: 900668034491518976L,
                column: "ENName",
                value: "Free parking");

            migrationBuilder.UpdateData(
                table: "Facility",
                keyColumn: "Id",
                keyValue: 900668034491518977L,
                column: "ENName",
                value: "Meeting Room");

            migrationBuilder.UpdateData(
                table: "Facility",
                keyColumn: "Id",
                keyValue: 900668034491518978L,
                column: "ENName",
                value: "Garden");

            migrationBuilder.UpdateData(
                table: "Facility",
                keyColumn: "Id",
                keyValue: 900668034491518979L,
                column: "ENName",
                value: "Laundry room");

            migrationBuilder.UpdateData(
                table: "Facility",
                keyColumn: "Id",
                keyValue: 900668034491518980L,
                column: "ENName",
                value: "ATM");

            migrationBuilder.UpdateData(
                table: "Facility",
                keyColumn: "Id",
                keyValue: 900668034491518981L,
                column: "ENName",
                value: "Accessibility facilities");

            migrationBuilder.UpdateData(
                table: "Facility",
                keyColumn: "Id",
                keyValue: 900668034491518982L,
                column: "ENName",
                value: "Smoking area");

            migrationBuilder.UpdateData(
                table: "Facility",
                keyColumn: "Id",
                keyValue: 900668034491518983L,
                column: "ENName",
                value: "SPA");

            migrationBuilder.UpdateData(
                table: "Facility",
                keyColumn: "Id",
                keyValue: 900668034491518984L,
                column: "ENName",
                value: "Massage");

            migrationBuilder.UpdateData(
                table: "Facility",
                keyColumn: "Id",
                keyValue: 900668034491518985L,
                column: "ENName",
                value: "The library");

            migrationBuilder.UpdateData(
                table: "Facility",
                keyColumn: "Id",
                keyValue: 900668034491518986L,
                column: "ENName",
                value: "Casino");

            migrationBuilder.UpdateData(
                table: "Facility",
                keyColumn: "Id",
                keyValue: 900668034491518987L,
                column: "ENName",
                value: "Free WIFI");

            migrationBuilder.UpdateData(
                table: "Facility",
                keyColumn: "Id",
                keyValue: 900668034491518988L,
                column: "ENName",
                value: "Air conditioner");

            migrationBuilder.UpdateData(
                table: "Facility",
                keyColumn: "Id",
                keyValue: 900668034491518989L,
                column: "ENName",
                value: "Television");

            migrationBuilder.UpdateData(
                table: "Facility",
                keyColumn: "Id",
                keyValue: 900668034491518990L,
                column: "ENName",
                value: "Bathtub");

            migrationBuilder.UpdateData(
                table: "Facility",
                keyColumn: "Id",
                keyValue: 900668034491518991L,
                column: "ENName",
                value: "Jacuzzi");

            migrationBuilder.UpdateData(
                table: "Facility",
                keyColumn: "Id",
                keyValue: 900668034491518992L,
                column: "ENName",
                value: "Non-smoking rooms");

            migrationBuilder.UpdateData(
                table: "Facility",
                keyColumn: "Id",
                keyValue: 900668034491518993L,
                column: "ENName",
                value: "Heating");

            migrationBuilder.UpdateData(
                table: "Facility",
                keyColumn: "Id",
                keyValue: 900668034491518994L,
                column: "ENName",
                value: "Fitness center");

            migrationBuilder.UpdateData(
                table: "Facility",
                keyColumn: "Id",
                keyValue: 900668034491518995L,
                column: "ENName",
                value: "Indoor swimming pool");

            migrationBuilder.UpdateData(
                table: "Facility",
                keyColumn: "Id",
                keyValue: 900668034491518996L,
                column: "ENName",
                value: "Outdoor swimming pool");

            migrationBuilder.UpdateData(
                table: "Facility",
                keyColumn: "Id",
                keyValue: 900668034491518997L,
                column: "ENName",
                value: "Golf");

            migrationBuilder.UpdateData(
                table: "Facility",
                keyColumn: "Id",
                keyValue: 900668034491518998L,
                column: "ENName",
                value: "Child care");

            migrationBuilder.UpdateData(
                table: "Facility",
                keyColumn: "Id",
                keyValue: 900668034491518999L,
                column: "ENName",
                value: "Kids club");

            migrationBuilder.UpdateData(
                table: "Facility",
                keyColumn: "Id",
                keyValue: 900668034491519000L,
                column: "ENName",
                value: "Water Park");

            migrationBuilder.UpdateData(
                table: "Facility",
                keyColumn: "Id",
                keyValue: 900668034491519001L,
                column: "ENName",
                value: "Amusement Park");

            migrationBuilder.UpdateData(
                table: "Facility",
                keyColumn: "Id",
                keyValue: 900668034491519002L,
                column: "ENName",
                value: "Customer breakfast");

            migrationBuilder.UpdateData(
                table: "Facility",
                keyColumn: "Id",
                keyValue: 900668034491519003L,
                column: "ENName",
                value: "Snack bar");

            migrationBuilder.UpdateData(
                table: "Facility",
                keyColumn: "Id",
                keyValue: 900668034491519004L,
                column: "ENName",
                value: "Cafeteria");

            migrationBuilder.UpdateData(
                table: "Facility",
                keyColumn: "Id",
                keyValue: 900668034491519005L,
                column: "ENName",
                value: "Bar");

            migrationBuilder.UpdateData(
                table: "Facility",
                keyColumn: "Id",
                keyValue: 900668034491519006L,
                column: "ENName",
                value: "Restaurant");

            migrationBuilder.UpdateData(
                table: "Facility",
                keyColumn: "Id",
                keyValue: 900668034491519007L,
                column: "ENName",
                value: "24 hour reception");

            migrationBuilder.UpdateData(
                table: "Facility",
                keyColumn: "Id",
                keyValue: 900668034491519008L,
                column: "ENName",
                value: "Express check in and out");

            migrationBuilder.UpdateData(
                table: "Facility",
                keyColumn: "Id",
                keyValue: 900668034491519009L,
                column: "ENName",
                value: "Wake-up service");

            migrationBuilder.UpdateData(
                table: "Facility",
                keyColumn: "Id",
                keyValue: 900668034491519010L,
                column: "ENName",
                value: "Pick up service");

            migrationBuilder.UpdateData(
                table: "Facility",
                keyColumn: "Id",
                keyValue: 900668034491519011L,
                column: "ENName",
                value: "Rent a car");

            migrationBuilder.UpdateData(
                table: "Facility",
                keyColumn: "Id",
                keyValue: 900668034491519012L,
                column: "ENName",
                value: "Shuttle Bus");

            migrationBuilder.UpdateData(
                table: "Facility",
                keyColumn: "Id",
                keyValue: 900668034491519013L,
                column: "ENName",
                value: "Chinese service");

            migrationBuilder.UpdateData(
                table: "Facility",
                keyColumn: "Id",
                keyValue: 900668034491519014L,
                column: "ENName",
                value: "Travel advice");

            migrationBuilder.UpdateData(
                table: "Facility",
                keyColumn: "Id",
                keyValue: 900668034491519015L,
                column: "ENName",
                value: "Foreign currency exchange");

            migrationBuilder.UpdateData(
                table: "Facility",
                keyColumn: "Id",
                keyValue: 900668034491519016L,
                column: "ENName",
                value: "Exclusive beach");

            migrationBuilder.UpdateData(
                table: "Facility",
                keyColumn: "Id",
                keyValue: 900668034491519017L,
                column: "ENName",
                value: "Hot spring");

            migrationBuilder.UpdateData(
                table: "Facility",
                keyColumn: "Id",
                keyValue: 900668034491519018L,
                column: "ENName",
                value: "Parking lot");

            migrationBuilder.UpdateData(
                table: "Facility",
                keyColumn: "Id",
                keyValue: 900668034491519019L,
                column: "ENName",
                value: "Shop");

            migrationBuilder.UpdateData(
                table: "Facility",
                keyColumn: "Id",
                keyValue: 900668034491519020L,
                column: "ENName",
                value: "Sauna");

            migrationBuilder.UpdateData(
                table: "Facility",
                keyColumn: "Id",
                keyValue: 900668034491519021L,
                column: "ENName",
                value: "Charging station");

            migrationBuilder.UpdateData(
                table: "Facility",
                keyColumn: "Id",
                keyValue: 900668034491519022L,
                column: "ENName",
                value: "Hairdressers");

            migrationBuilder.UpdateData(
                table: "Facility",
                keyColumn: "Id",
                keyValue: 900668034491519023L,
                column: "ENName",
                value: "Church");

            migrationBuilder.UpdateData(
                table: "Facility",
                keyColumn: "Id",
                keyValue: 900668034491519024L,
                column: "ENName",
                value: "Game consoles");

            migrationBuilder.UpdateData(
                table: "Facility",
                keyColumn: "Id",
                keyValue: 900668034491519025L,
                column: "ENName",
                value: "Washing machine");

            migrationBuilder.UpdateData(
                table: "Facility",
                keyColumn: "Id",
                keyValue: 900668034491519026L,
                column: "ENName",
                value: "Toiletries");

            migrationBuilder.UpdateData(
                table: "Facility",
                keyColumn: "Id",
                keyValue: 900668034491519027L,
                column: "ENName",
                value: "Network services");

            migrationBuilder.UpdateData(
                table: "Facility",
                keyColumn: "Id",
                keyValue: 900668034491519029L,
                column: "ENName",
                value: "Free Internet");

            migrationBuilder.UpdateData(
                table: "Facility",
                keyColumn: "Id",
                keyValue: 900668034491519030L,
                column: "ENName",
                value: "Oven/microwave");

            migrationBuilder.UpdateData(
                table: "Facility",
                keyColumn: "Id",
                keyValue: 900668034491519031L,
                column: "ENName",
                value: "Soundproof room");

            migrationBuilder.UpdateData(
                table: "Facility",
                keyColumn: "Id",
                keyValue: 900668034491519032L,
                column: "ENName",
                value: "Clothes dryer");

            migrationBuilder.UpdateData(
                table: "Facility",
                keyColumn: "Id",
                keyValue: 900668034491519033L,
                column: "ENName",
                value: "Allergy-free room");

            migrationBuilder.UpdateData(
                table: "Facility",
                keyColumn: "Id",
                keyValue: 900668034491519034L,
                column: "ENName",
                value: "Computer");

            migrationBuilder.UpdateData(
                table: "Facility",
                keyColumn: "Id",
                keyValue: 900668034491519035L,
                column: "ENName",
                value: "Hair Dryer");

            migrationBuilder.UpdateData(
                table: "Facility",
                keyColumn: "Id",
                keyValue: 900668034491519036L,
                column: "ENName",
                value: "Refrigerator");

            migrationBuilder.UpdateData(
                table: "Facility",
                keyColumn: "Id",
                keyValue: 900668034491519037L,
                column: "ENName",
                value: "Safe deposit box");

            migrationBuilder.UpdateData(
                table: "Facility",
                keyColumn: "Id",
                keyValue: 900668034491519038L,
                column: "ENName",
                value: "Office area");

            migrationBuilder.UpdateData(
                table: "Facility",
                keyColumn: "Id",
                keyValue: 900668034491519039L,
                column: "ENName",
                value: "WIFI network");

            migrationBuilder.UpdateData(
                table: "Facility",
                keyColumn: "Id",
                keyValue: 900668034491519040L,
                column: "ENName",
                value: "Bicycle");

            migrationBuilder.UpdateData(
                table: "Facility",
                keyColumn: "Id",
                keyValue: 900668034491519041L,
                column: "ENName",
                value: "Badminton");

            migrationBuilder.UpdateData(
                table: "Facility",
                keyColumn: "Id",
                keyValue: 900668034491519042L,
                column: "ENName",
                value: "Yoga room");

            migrationBuilder.UpdateData(
                table: "Facility",
                keyColumn: "Id",
                keyValue: 900668034491519043L,
                column: "ENName",
                value: "Game Room");

            migrationBuilder.UpdateData(
                table: "Facility",
                keyColumn: "Id",
                keyValue: 900668034491519044L,
                column: "ENName",
                value: "Nightclub");

            migrationBuilder.UpdateData(
                table: "Facility",
                keyColumn: "Id",
                keyValue: 900668034491519045L,
                column: "ENName",
                value: "Tennis");

            migrationBuilder.UpdateData(
                table: "Facility",
                keyColumn: "Id",
                keyValue: 900668034491519046L,
                column: "ENName",
                value: "Billiards");

            migrationBuilder.UpdateData(
                table: "Facility",
                keyColumn: "Id",
                keyValue: 900668034491519047L,
                column: "ENName",
                value: "Water sports");

            migrationBuilder.UpdateData(
                table: "Facility",
                keyColumn: "Id",
                keyValue: 900668034491519048L,
                column: "ENName",
                value: "Diving");

            migrationBuilder.UpdateData(
                table: "Facility",
                keyColumn: "Id",
                keyValue: 900668034491519049L,
                column: "ENName",
                value: "Ride a horse");

            migrationBuilder.UpdateData(
                table: "Facility",
                keyColumn: "Id",
                keyValue: 900668034491519050L,
                column: "ENName",
                value: "Table tennis");

            migrationBuilder.UpdateData(
                table: "Facility",
                keyColumn: "Id",
                keyValue: 900668034491519051L,
                column: "ENName",
                value: "Skiing");

            migrationBuilder.UpdateData(
                table: "Facility",
                keyColumn: "Id",
                keyValue: 900668034491519052L,
                column: "ENName",
                value: "Sail");

            migrationBuilder.UpdateData(
                table: "Facility",
                keyColumn: "Id",
                keyValue: 900668034491519053L,
                column: "ENName",
                value: "A dartboard");

            migrationBuilder.UpdateData(
                table: "Facility",
                keyColumn: "Id",
                keyValue: 900668034491519054L,
                column: "ENName",
                value: "Children''s pool");

            migrationBuilder.UpdateData(
                table: "Facility",
                keyColumn: "Id",
                keyValue: 900668034491519055L,
                column: "ENName",
                value: "Go fishing");

            migrationBuilder.UpdateData(
                table: "Facility",
                keyColumn: "Id",
                keyValue: 900668034491519056L,
                column: "ENName",
                value: "Bowling");

            migrationBuilder.UpdateData(
                table: "Facility",
                keyColumn: "Id",
                keyValue: 900668034491519057L,
                column: "ENName",
                value: "KTV");

            migrationBuilder.UpdateData(
                table: "Facility",
                keyColumn: "Id",
                keyValue: 900668034491519058L,
                column: "ENName",
                value: "Fruit/snacks");

            migrationBuilder.UpdateData(
                table: "Facility",
                keyColumn: "Id",
                keyValue: 900668034491519059L,
                column: "ENName",
                value: "Barbecue facilities");

            migrationBuilder.UpdateData(
                table: "Facility",
                keyColumn: "Id",
                keyValue: 900668034491519060L,
                column: "ENName",
                value: "Coffee shop");

            migrationBuilder.UpdateData(
                table: "Facility",
                keyColumn: "Id",
                keyValue: 900668034491519061L,
                column: "ENName",
                value: "Kitchen");

            migrationBuilder.UpdateData(
                table: "Facility",
                keyColumn: "Id",
                keyValue: 900668034491519062L,
                column: "ENName",
                value: "Withdrawal service");

            migrationBuilder.UpdateData(
                table: "Facility",
                keyColumn: "Id",
                keyValue: 900668034491519063L,
                column: "ENName",
                value: "Ticket service");

            migrationBuilder.UpdateData(
                table: "Facility",
                keyColumn: "Id",
                keyValue: 900668034491519064L,
                column: "ENName",
                value: "Concierge service");

            migrationBuilder.UpdateData(
                table: "Facility",
                keyColumn: "Id",
                keyValue: 900668034491519065L,
                column: "ENName",
                value: "Pets allowed");

            migrationBuilder.UpdateData(
                table: "Facility",
                keyColumn: "Id",
                keyValue: 900668034491519066L,
                column: "ENName",
                value: "Taxi service");

            migrationBuilder.UpdateData(
                table: "Facility",
                keyColumn: "Id",
                keyValue: 900668034491519067L,
                column: "ENName",
                value: "Valet parking");

            migrationBuilder.UpdateData(
                table: "Facility",
                keyColumn: "Id",
                keyValue: 900668034491519068L,
                column: "ENName",
                value: "Laundry service");

            migrationBuilder.UpdateData(
                table: "Facility",
                keyColumn: "Id",
                keyValue: 900668034491519069L,
                column: "ENName",
                value: "Room service");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.UpdateData(
                table: "Facility",
                keyColumn: "Id",
                keyValue: 900668034491518976L,
                column: "ENName",
                value: null);

            migrationBuilder.UpdateData(
                table: "Facility",
                keyColumn: "Id",
                keyValue: 900668034491518977L,
                column: "ENName",
                value: null);

            migrationBuilder.UpdateData(
                table: "Facility",
                keyColumn: "Id",
                keyValue: 900668034491518978L,
                column: "ENName",
                value: null);

            migrationBuilder.UpdateData(
                table: "Facility",
                keyColumn: "Id",
                keyValue: 900668034491518979L,
                column: "ENName",
                value: null);

            migrationBuilder.UpdateData(
                table: "Facility",
                keyColumn: "Id",
                keyValue: 900668034491518980L,
                column: "ENName",
                value: null);

            migrationBuilder.UpdateData(
                table: "Facility",
                keyColumn: "Id",
                keyValue: 900668034491518981L,
                column: "ENName",
                value: null);

            migrationBuilder.UpdateData(
                table: "Facility",
                keyColumn: "Id",
                keyValue: 900668034491518982L,
                column: "ENName",
                value: null);

            migrationBuilder.UpdateData(
                table: "Facility",
                keyColumn: "Id",
                keyValue: 900668034491518983L,
                column: "ENName",
                value: null);

            migrationBuilder.UpdateData(
                table: "Facility",
                keyColumn: "Id",
                keyValue: 900668034491518984L,
                column: "ENName",
                value: null);

            migrationBuilder.UpdateData(
                table: "Facility",
                keyColumn: "Id",
                keyValue: 900668034491518985L,
                column: "ENName",
                value: null);

            migrationBuilder.UpdateData(
                table: "Facility",
                keyColumn: "Id",
                keyValue: 900668034491518986L,
                column: "ENName",
                value: null);

            migrationBuilder.UpdateData(
                table: "Facility",
                keyColumn: "Id",
                keyValue: 900668034491518987L,
                column: "ENName",
                value: null);

            migrationBuilder.UpdateData(
                table: "Facility",
                keyColumn: "Id",
                keyValue: 900668034491518988L,
                column: "ENName",
                value: null);

            migrationBuilder.UpdateData(
                table: "Facility",
                keyColumn: "Id",
                keyValue: 900668034491518989L,
                column: "ENName",
                value: null);

            migrationBuilder.UpdateData(
                table: "Facility",
                keyColumn: "Id",
                keyValue: 900668034491518990L,
                column: "ENName",
                value: null);

            migrationBuilder.UpdateData(
                table: "Facility",
                keyColumn: "Id",
                keyValue: 900668034491518991L,
                column: "ENName",
                value: null);

            migrationBuilder.UpdateData(
                table: "Facility",
                keyColumn: "Id",
                keyValue: 900668034491518992L,
                column: "ENName",
                value: null);

            migrationBuilder.UpdateData(
                table: "Facility",
                keyColumn: "Id",
                keyValue: 900668034491518993L,
                column: "ENName",
                value: null);

            migrationBuilder.UpdateData(
                table: "Facility",
                keyColumn: "Id",
                keyValue: 900668034491518994L,
                column: "ENName",
                value: null);

            migrationBuilder.UpdateData(
                table: "Facility",
                keyColumn: "Id",
                keyValue: 900668034491518995L,
                column: "ENName",
                value: null);

            migrationBuilder.UpdateData(
                table: "Facility",
                keyColumn: "Id",
                keyValue: 900668034491518996L,
                column: "ENName",
                value: null);

            migrationBuilder.UpdateData(
                table: "Facility",
                keyColumn: "Id",
                keyValue: 900668034491518997L,
                column: "ENName",
                value: null);

            migrationBuilder.UpdateData(
                table: "Facility",
                keyColumn: "Id",
                keyValue: 900668034491518998L,
                column: "ENName",
                value: null);

            migrationBuilder.UpdateData(
                table: "Facility",
                keyColumn: "Id",
                keyValue: 900668034491518999L,
                column: "ENName",
                value: null);

            migrationBuilder.UpdateData(
                table: "Facility",
                keyColumn: "Id",
                keyValue: 900668034491519000L,
                column: "ENName",
                value: null);

            migrationBuilder.UpdateData(
                table: "Facility",
                keyColumn: "Id",
                keyValue: 900668034491519001L,
                column: "ENName",
                value: null);

            migrationBuilder.UpdateData(
                table: "Facility",
                keyColumn: "Id",
                keyValue: 900668034491519002L,
                column: "ENName",
                value: null);

            migrationBuilder.UpdateData(
                table: "Facility",
                keyColumn: "Id",
                keyValue: 900668034491519003L,
                column: "ENName",
                value: null);

            migrationBuilder.UpdateData(
                table: "Facility",
                keyColumn: "Id",
                keyValue: 900668034491519004L,
                column: "ENName",
                value: null);

            migrationBuilder.UpdateData(
                table: "Facility",
                keyColumn: "Id",
                keyValue: 900668034491519005L,
                column: "ENName",
                value: null);

            migrationBuilder.UpdateData(
                table: "Facility",
                keyColumn: "Id",
                keyValue: 900668034491519006L,
                column: "ENName",
                value: null);

            migrationBuilder.UpdateData(
                table: "Facility",
                keyColumn: "Id",
                keyValue: 900668034491519007L,
                column: "ENName",
                value: null);

            migrationBuilder.UpdateData(
                table: "Facility",
                keyColumn: "Id",
                keyValue: 900668034491519008L,
                column: "ENName",
                value: null);

            migrationBuilder.UpdateData(
                table: "Facility",
                keyColumn: "Id",
                keyValue: 900668034491519009L,
                column: "ENName",
                value: null);

            migrationBuilder.UpdateData(
                table: "Facility",
                keyColumn: "Id",
                keyValue: 900668034491519010L,
                column: "ENName",
                value: null);

            migrationBuilder.UpdateData(
                table: "Facility",
                keyColumn: "Id",
                keyValue: 900668034491519011L,
                column: "ENName",
                value: null);

            migrationBuilder.UpdateData(
                table: "Facility",
                keyColumn: "Id",
                keyValue: 900668034491519012L,
                column: "ENName",
                value: null);

            migrationBuilder.UpdateData(
                table: "Facility",
                keyColumn: "Id",
                keyValue: 900668034491519013L,
                column: "ENName",
                value: null);

            migrationBuilder.UpdateData(
                table: "Facility",
                keyColumn: "Id",
                keyValue: 900668034491519014L,
                column: "ENName",
                value: null);

            migrationBuilder.UpdateData(
                table: "Facility",
                keyColumn: "Id",
                keyValue: 900668034491519015L,
                column: "ENName",
                value: null);

            migrationBuilder.UpdateData(
                table: "Facility",
                keyColumn: "Id",
                keyValue: 900668034491519016L,
                column: "ENName",
                value: null);

            migrationBuilder.UpdateData(
                table: "Facility",
                keyColumn: "Id",
                keyValue: 900668034491519017L,
                column: "ENName",
                value: null);

            migrationBuilder.UpdateData(
                table: "Facility",
                keyColumn: "Id",
                keyValue: 900668034491519018L,
                column: "ENName",
                value: null);

            migrationBuilder.UpdateData(
                table: "Facility",
                keyColumn: "Id",
                keyValue: 900668034491519019L,
                column: "ENName",
                value: null);

            migrationBuilder.UpdateData(
                table: "Facility",
                keyColumn: "Id",
                keyValue: 900668034491519020L,
                column: "ENName",
                value: null);

            migrationBuilder.UpdateData(
                table: "Facility",
                keyColumn: "Id",
                keyValue: 900668034491519021L,
                column: "ENName",
                value: null);

            migrationBuilder.UpdateData(
                table: "Facility",
                keyColumn: "Id",
                keyValue: 900668034491519022L,
                column: "ENName",
                value: null);

            migrationBuilder.UpdateData(
                table: "Facility",
                keyColumn: "Id",
                keyValue: 900668034491519023L,
                column: "ENName",
                value: null);

            migrationBuilder.UpdateData(
                table: "Facility",
                keyColumn: "Id",
                keyValue: 900668034491519024L,
                column: "ENName",
                value: null);

            migrationBuilder.UpdateData(
                table: "Facility",
                keyColumn: "Id",
                keyValue: 900668034491519025L,
                column: "ENName",
                value: null);

            migrationBuilder.UpdateData(
                table: "Facility",
                keyColumn: "Id",
                keyValue: 900668034491519026L,
                column: "ENName",
                value: null);

            migrationBuilder.UpdateData(
                table: "Facility",
                keyColumn: "Id",
                keyValue: 900668034491519027L,
                column: "ENName",
                value: null);

            migrationBuilder.UpdateData(
                table: "Facility",
                keyColumn: "Id",
                keyValue: 900668034491519029L,
                column: "ENName",
                value: null);

            migrationBuilder.UpdateData(
                table: "Facility",
                keyColumn: "Id",
                keyValue: 900668034491519030L,
                column: "ENName",
                value: null);

            migrationBuilder.UpdateData(
                table: "Facility",
                keyColumn: "Id",
                keyValue: 900668034491519031L,
                column: "ENName",
                value: null);

            migrationBuilder.UpdateData(
                table: "Facility",
                keyColumn: "Id",
                keyValue: 900668034491519032L,
                column: "ENName",
                value: null);

            migrationBuilder.UpdateData(
                table: "Facility",
                keyColumn: "Id",
                keyValue: 900668034491519033L,
                column: "ENName",
                value: null);

            migrationBuilder.UpdateData(
                table: "Facility",
                keyColumn: "Id",
                keyValue: 900668034491519034L,
                column: "ENName",
                value: null);

            migrationBuilder.UpdateData(
                table: "Facility",
                keyColumn: "Id",
                keyValue: 900668034491519035L,
                column: "ENName",
                value: null);

            migrationBuilder.UpdateData(
                table: "Facility",
                keyColumn: "Id",
                keyValue: 900668034491519036L,
                column: "ENName",
                value: null);

            migrationBuilder.UpdateData(
                table: "Facility",
                keyColumn: "Id",
                keyValue: 900668034491519037L,
                column: "ENName",
                value: null);

            migrationBuilder.UpdateData(
                table: "Facility",
                keyColumn: "Id",
                keyValue: 900668034491519038L,
                column: "ENName",
                value: null);

            migrationBuilder.UpdateData(
                table: "Facility",
                keyColumn: "Id",
                keyValue: 900668034491519039L,
                column: "ENName",
                value: null);

            migrationBuilder.UpdateData(
                table: "Facility",
                keyColumn: "Id",
                keyValue: 900668034491519040L,
                column: "ENName",
                value: null);

            migrationBuilder.UpdateData(
                table: "Facility",
                keyColumn: "Id",
                keyValue: 900668034491519041L,
                column: "ENName",
                value: null);

            migrationBuilder.UpdateData(
                table: "Facility",
                keyColumn: "Id",
                keyValue: 900668034491519042L,
                column: "ENName",
                value: null);

            migrationBuilder.UpdateData(
                table: "Facility",
                keyColumn: "Id",
                keyValue: 900668034491519043L,
                column: "ENName",
                value: null);

            migrationBuilder.UpdateData(
                table: "Facility",
                keyColumn: "Id",
                keyValue: 900668034491519044L,
                column: "ENName",
                value: null);

            migrationBuilder.UpdateData(
                table: "Facility",
                keyColumn: "Id",
                keyValue: 900668034491519045L,
                column: "ENName",
                value: null);

            migrationBuilder.UpdateData(
                table: "Facility",
                keyColumn: "Id",
                keyValue: 900668034491519046L,
                column: "ENName",
                value: null);

            migrationBuilder.UpdateData(
                table: "Facility",
                keyColumn: "Id",
                keyValue: 900668034491519047L,
                column: "ENName",
                value: null);

            migrationBuilder.UpdateData(
                table: "Facility",
                keyColumn: "Id",
                keyValue: 900668034491519048L,
                column: "ENName",
                value: null);

            migrationBuilder.UpdateData(
                table: "Facility",
                keyColumn: "Id",
                keyValue: 900668034491519049L,
                column: "ENName",
                value: null);

            migrationBuilder.UpdateData(
                table: "Facility",
                keyColumn: "Id",
                keyValue: 900668034491519050L,
                column: "ENName",
                value: null);

            migrationBuilder.UpdateData(
                table: "Facility",
                keyColumn: "Id",
                keyValue: 900668034491519051L,
                column: "ENName",
                value: null);

            migrationBuilder.UpdateData(
                table: "Facility",
                keyColumn: "Id",
                keyValue: 900668034491519052L,
                column: "ENName",
                value: null);

            migrationBuilder.UpdateData(
                table: "Facility",
                keyColumn: "Id",
                keyValue: 900668034491519053L,
                column: "ENName",
                value: null);

            migrationBuilder.UpdateData(
                table: "Facility",
                keyColumn: "Id",
                keyValue: 900668034491519054L,
                column: "ENName",
                value: null);

            migrationBuilder.UpdateData(
                table: "Facility",
                keyColumn: "Id",
                keyValue: 900668034491519055L,
                column: "ENName",
                value: null);

            migrationBuilder.UpdateData(
                table: "Facility",
                keyColumn: "Id",
                keyValue: 900668034491519056L,
                column: "ENName",
                value: null);

            migrationBuilder.UpdateData(
                table: "Facility",
                keyColumn: "Id",
                keyValue: 900668034491519057L,
                column: "ENName",
                value: null);

            migrationBuilder.UpdateData(
                table: "Facility",
                keyColumn: "Id",
                keyValue: 900668034491519058L,
                column: "ENName",
                value: null);

            migrationBuilder.UpdateData(
                table: "Facility",
                keyColumn: "Id",
                keyValue: 900668034491519059L,
                column: "ENName",
                value: null);

            migrationBuilder.UpdateData(
                table: "Facility",
                keyColumn: "Id",
                keyValue: 900668034491519060L,
                column: "ENName",
                value: null);

            migrationBuilder.UpdateData(
                table: "Facility",
                keyColumn: "Id",
                keyValue: 900668034491519061L,
                column: "ENName",
                value: null);

            migrationBuilder.UpdateData(
                table: "Facility",
                keyColumn: "Id",
                keyValue: 900668034491519062L,
                column: "ENName",
                value: null);

            migrationBuilder.UpdateData(
                table: "Facility",
                keyColumn: "Id",
                keyValue: 900668034491519063L,
                column: "ENName",
                value: null);

            migrationBuilder.UpdateData(
                table: "Facility",
                keyColumn: "Id",
                keyValue: 900668034491519064L,
                column: "ENName",
                value: null);

            migrationBuilder.UpdateData(
                table: "Facility",
                keyColumn: "Id",
                keyValue: 900668034491519065L,
                column: "ENName",
                value: null);

            migrationBuilder.UpdateData(
                table: "Facility",
                keyColumn: "Id",
                keyValue: 900668034491519066L,
                column: "ENName",
                value: null);

            migrationBuilder.UpdateData(
                table: "Facility",
                keyColumn: "Id",
                keyValue: 900668034491519067L,
                column: "ENName",
                value: null);

            migrationBuilder.UpdateData(
                table: "Facility",
                keyColumn: "Id",
                keyValue: 900668034491519068L,
                column: "ENName",
                value: null);

            migrationBuilder.UpdateData(
                table: "Facility",
                keyColumn: "Id",
                keyValue: 900668034491519069L,
                column: "ENName",
                value: null);

            migrationBuilder.InsertData(
                table: "Facility",
                columns: new[] { "Id", "CreateTime", "FacilityType", "Name" },
                values: new object[] { 900668034491519028L, new DateTime(2023, 11, 8, 0, 0, 0, 0, DateTimeKind.Unspecified), 2, "暖气" });
        }
    }
}
