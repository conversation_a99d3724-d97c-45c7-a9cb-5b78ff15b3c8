using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Resource.Api.Infrastructure.Migrations
{
    public partial class AddHotelMeeting : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<string>(
                name: "Brand",
                table: "Hotel",
                type: "longtext",
                nullable: true)
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.AddColumn<string>(
                name: "ENMeetingIntro",
                table: "Hotel",
                type: "longtext",
                nullable: true)
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.AddColumn<int>(
                name: "MeetingCount",
                table: "Hotel",
                type: "int",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "MeetingIntro",
                table: "Hotel",
                type: "longtext",
                nullable: true)
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.AddColumn<string>(
                name: "OfficialWebsite",
                table: "Hotel",
                type: "longtext",
                nullable: true)
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.CreateTable(
                name: "HotelIntroFeature",
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false),
                    HotelId = table.Column<long>(type: "bigint", nullable: false),
                    Name = table.Column<string>(type: "varchar(128)", nullable: true)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    EnName = table.Column<string>(type: "varchar(128)", nullable: true)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    Description = table.Column<string>(type: "varchar(500)", nullable: true)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    EnDescription = table.Column<string>(type: "varchar(500)", nullable: true)
                        .Annotation("MySql:CharSet", "utf8mb4")
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_HotelIntroFeature", x => x.Id);
                })
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.CreateTable(
                name: "HotelIntroNearby",
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false),
                    HotelId = table.Column<long>(type: "bigint", nullable: false),
                    Name = table.Column<string>(type: "varchar(128)", nullable: true)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    EnName = table.Column<string>(type: "varchar(128)", nullable: true)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    Distance = table.Column<int>(type: "int", nullable: true),
                    DistanceUnit = table.Column<string>(type: "varchar(20)", nullable: true)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    DistanceUnitName = table.Column<string>(type: "varchar(20)", nullable: true)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    UseTime = table.Column<int>(type: "int", nullable: true),
                    UseTimeUnit = table.Column<string>(type: "varchar(20)", nullable: true)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    UseTimeUnitName = table.Column<string>(type: "varchar(20)", nullable: true)
                        .Annotation("MySql:CharSet", "utf8mb4")
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_HotelIntroNearby", x => x.Id);
                })
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.CreateTable(
                name: "HotelIntroRoom",
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false),
                    HotelId = table.Column<long>(type: "bigint", nullable: false),
                    HotelRoomType = table.Column<sbyte>(type: "tinyint", nullable: false),
                    Name = table.Column<string>(type: "varchar(128)", nullable: true)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    EnName = table.Column<string>(type: "varchar(128)", nullable: true)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    Quantity = table.Column<int>(type: "int", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_HotelIntroRoom", x => x.Id);
                })
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.CreateTable(
                name: "HotelMeeting",
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false),
                    HotelId = table.Column<long>(type: "bigint", nullable: false),
                    ZHName = table.Column<string>(type: "varchar(128)", nullable: false)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    ENName = table.Column<string>(type: "varchar(128)", nullable: false)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    Floor = table.Column<string>(type: "varchar(100)", nullable: false)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    Area = table.Column<decimal>(type: "decimal(18,2)", nullable: false),
                    Height = table.Column<decimal>(type: "decimal(18,2)", nullable: false),
                    Theatre = table.Column<int>(type: "int", nullable: true),
                    Classroom = table.Column<int>(type: "int", nullable: true),
                    UShape = table.Column<int>(type: "int", nullable: true),
                    Banquet = table.Column<int>(type: "int", nullable: true),
                    Cocktail = table.Column<int>(type: "int", nullable: true),
                    Boardroom = table.Column<int>(type: "int", nullable: true),
                    CreateTime = table.Column<DateTime>(type: "datetime", nullable: false),
                    UpdateTime = table.Column<DateTime>(type: "datetime", nullable: true),
                    Enabled = table.Column<bool>(type: "tinyint(1)", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_HotelMeeting", x => x.Id);
                })
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.CreateTable(
                name: "HotelPhotoExtend",
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false),
                    ProductId = table.Column<long>(type: "bigint", nullable: false),
                    MediaType = table.Column<sbyte>(type: "tinyint", nullable: false),
                    Path = table.Column<string>(type: "varchar(255)", nullable: true)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    Sort = table.Column<int>(type: "int", nullable: false),
                    Enabled = table.Column<bool>(type: "tinyint(1)", nullable: false),
                    CreateTime = table.Column<DateTime>(type: "datetime", nullable: false),
                    UpdateTime = table.Column<DateTime>(type: "datetime", nullable: true),
                    Name = table.Column<string>(type: "varchar(100)", nullable: true)
                        .Annotation("MySql:CharSet", "utf8mb4")
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_HotelPhotoExtend", x => x.Id);
                })
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.CreateIndex(
                name: "IX_HotelIntroFeature_HotelId",
                table: "HotelIntroFeature",
                column: "HotelId");

            migrationBuilder.CreateIndex(
                name: "IX_HotelIntroNearby_HotelId",
                table: "HotelIntroNearby",
                column: "HotelId");

            migrationBuilder.CreateIndex(
                name: "IX_HotelIntroRoom_HotelId",
                table: "HotelIntroRoom",
                column: "HotelId");

            migrationBuilder.CreateIndex(
                name: "IX_HotelMeeting_HotelId",
                table: "HotelMeeting",
                column: "HotelId");

            migrationBuilder.CreateIndex(
                name: "IX_HotelPhotoExtend_ProductId",
                table: "HotelPhotoExtend",
                column: "ProductId");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "HotelIntroFeature");

            migrationBuilder.DropTable(
                name: "HotelIntroNearby");

            migrationBuilder.DropTable(
                name: "HotelIntroRoom");

            migrationBuilder.DropTable(
                name: "HotelMeeting");

            migrationBuilder.DropTable(
                name: "HotelPhotoExtend");

            migrationBuilder.DropColumn(
                name: "Brand",
                table: "Hotel");

            migrationBuilder.DropColumn(
                name: "ENMeetingIntro",
                table: "Hotel");

            migrationBuilder.DropColumn(
                name: "MeetingCount",
                table: "Hotel");

            migrationBuilder.DropColumn(
                name: "MeetingIntro",
                table: "Hotel");

            migrationBuilder.DropColumn(
                name: "OfficialWebsite",
                table: "Hotel");
        }
    }
}
