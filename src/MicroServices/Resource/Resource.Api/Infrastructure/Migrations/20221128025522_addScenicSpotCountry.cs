using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Resource.Api.Infrastructure.Migrations
{
    public partial class addScenicSpotCountry : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<int>(
                name: "CountryCode",
                table: "ScenicSpot",
                type: "int",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<string>(
                name: "CountryName",
                table: "ScenicSpot",
                type: "varchar(50)",
                nullable: true)
                .Annotation("MySql:CharSet", "utf8mb4");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "CountryCode",
                table: "ScenicSpot");

            migrationBuilder.DropColumn(
                name: "CountryName",
                table: "ScenicSpot");
        }
    }
}
