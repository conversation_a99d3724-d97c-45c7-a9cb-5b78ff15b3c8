using AutoMapper;
using Contracts.Common.Resource.Messages;
using Newtonsoft.Json;
using Resource.Api.Infrastructure;
using Resource.Api.Model;
using Resource.Api.Services;
using Resource.Api.Services.Interfaces;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using UnitTest.Base;
using Xunit;

namespace Resource.UnitTest
{
    public class HotelServiceTests : TestBase<CustomDbContext>
    {
        public static IHotelService GetService(IMapper mapper ,CustomDbContext dbContext, IEsHotelService esHotelService)
        {
            return new HotelService(mapper, dbContext, esHotelService);
        }

        [Fact(DisplayName = "更新酒店资源图片路径_成功")]
        public async Task UpdateHotelPhotos_Success()
        {
            string _aesKey = "HopOpenApiHotels";
            //string DES_KEY = "HUITRAVEL.TENANT@2024-SECRECY-32";
            string DES_IV = "huitravel.tenant";
            var data = JsonConvert.SerializeObject(new { test = 1 });
            var code = Common.Utils.SecurityUtil.AESEncrypt(data, _aesKey, DES_IV);
            var decode = Common.Utils.SecurityUtil.AESDecrypt(code, _aesKey, DES_IV);
            //arrange
            var hotelId = 1;
            List<HotelPhotos> hotelPhotos = new() {
                new HotelPhotos{ HotelId=hotelId, Path= "http://1.jpeg" , Enabled=true },
                new HotelPhotos{ HotelId=hotelId, Path= "http://2.jpeg" , Enabled=true },
                new HotelPhotos{ HotelId=hotelId, Path= "http://3.jpeg" , Enabled=true },
                new HotelPhotos{ HotelId=hotelId, Path= "http://4.jpeg" , Enabled=true },
            };
            var dbContext = GetNewDbContext();
            await dbContext.AddRangeAsync(hotelPhotos);
            await dbContext.SaveChangesAsync();
            //act
            var service = GetService(null, dbContext,null);
            ResourceHotelPhotoUpdateMessage receive = new()
            {
                ResourceHotelId = hotelId,
                Photos = new List<ResourceHotelPhoto>() {
                   new ResourceHotelPhoto{ OrgPath="http://1.jpeg", Path="development/tenant/hotel/1.jpeg" },
                   new ResourceHotelPhoto{ OrgPath="http://2.jpeg", Path=""},
                }
            };
            await service.UpdateHotelPhotos(receive);
            //assert
            Assert.True(dbContext.HotelPhotos.Any(x => x.Path == "http://1.jpeg") is false
                && dbContext.HotelPhotos.Single(x => x.Path == "development/tenant/hotel/1.jpeg") != null);
            Assert.True(dbContext.HotelPhotos.Any(x => x.Path == "http://2.jpeg") is false);
        }
    }
}
