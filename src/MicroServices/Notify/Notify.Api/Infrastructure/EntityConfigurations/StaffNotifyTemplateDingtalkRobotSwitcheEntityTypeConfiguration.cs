using EfCoreExtensions.EntityConfigurationsBase;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Notify.Api.Infrastructure.EntityConfigurations;

public class StaffNotifyTemplateDingtalkRobotSwitcheEntityTypeConfiguration
     : TenantBaseConfiguration<StaffNotifyTemplateDingtalkRobotSwitche>,
       IEntityTypeConfiguration<StaffNotifyTemplateDingtalkRobotSwitche>
{
    public void Configure(EntityTypeBuilder<StaffNotifyTemplateDingtalkRobotSwitche> builder)
    {
        base.ConfigureBase(builder);

        builder.Property(s => s.TemplateDingtalkId)
               .HasColumnType("bigint");

        builder.Property(s => s.NotifyEventType)
               .HasColumnType("tinyint");

        builder.Property(s => s.NotifyEventSubType)
               .HasColumnType("int");

        builder.Property(s => s.CreateTime)
               .HasColumnType("datetime");

    }
}
