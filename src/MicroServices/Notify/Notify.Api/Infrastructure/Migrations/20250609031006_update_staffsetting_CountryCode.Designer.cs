// <auto-generated />
using System;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using Notify.Api.Infrastructure;

#nullable disable

namespace Notify.Api.Infrastructure.Migrations
{
    [DbContext(typeof(CustomDbContext))]
    [Migration("20250609031006_update_staffsetting_CountryCode")]
    partial class update_staffsetting_CountryCode
    {
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "6.0.5")
                .HasAnnotation("Relational:MaxIdentifierLength", 64);

            modelBuilder.Entity("Notify.Api.Model.AgencyNotifyTemplateLibrary", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<int>("NotifyEventSubType")
                        .HasColumnType("int");

                    b.Property<sbyte>("NotifyEventType")
                        .HasColumnType("tinyint");

                    b.Property<string>("SmsContentExample")
                        .IsRequired()
                        .HasColumnType("varchar(200)");

                    b.Property<string>("SmsTemplateNo")
                        .IsRequired()
                        .HasColumnType("varchar(64)");

                    b.HasKey("Id");

                    b.ToTable("AgencyNotifyTemplateLibrary", (string)null);
                });

            modelBuilder.Entity("Notify.Api.Model.AgencyNotifyTemplateSwitch", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<bool>("IsOpen")
                        .HasColumnType("tinyint(1)");

                    b.Property<int>("NotifyEventSubType")
                        .HasColumnType("int");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.HasIndex("NotifyEventSubType", "TenantId")
                        .IsUnique();

                    b.ToTable("AgencyNotifyTemplateSwitch", (string)null);
                });

            modelBuilder.Entity("Notify.Api.Model.CustomerNotifyTemplateChannelPullInfo", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<int>("NotifyChannel")
                        .HasColumnType("int");

                    b.Property<int>("NotifyEventSubType")
                        .HasColumnType("int");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<string>("WechatTemplateCode")
                        .IsRequired()
                        .HasColumnType("varchar(64)");

                    b.Property<string>("WechatTemplateNo")
                        .IsRequired()
                        .HasColumnType("varchar(64)");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.HasIndex("WechatTemplateNo", "TenantId", "NotifyEventSubType", "NotifyChannel")
                        .IsUnique();

                    b.ToTable("CustomerNotifyTemplateChannelPullInfo", (string)null);
                });

            modelBuilder.Entity("Notify.Api.Model.CustomerNotifyTemplateLibrary", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<string>("EmailTemplateContent")
                        .HasColumnType("mediumtext");

                    b.Property<string>("EmailTemplateTitle")
                        .HasColumnType("text");

                    b.Property<string>("Keywords")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<sbyte>("NotifyChannel")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("tinyint")
                        .HasDefaultValue((sbyte)1);

                    b.Property<int>("NotifyEventSubType")
                        .HasColumnType("int");

                    b.Property<sbyte>("NotifyEventType")
                        .HasColumnType("tinyint");

                    b.Property<string>("SmsContentExample")
                        .HasColumnType("varchar(200)");

                    b.Property<string>("SmsTemplateNo")
                        .HasColumnType("varchar(64)");

                    b.Property<string>("WechatContentExample")
                        .HasColumnType("varchar(500)");

                    b.Property<string>("WechatTemplateNo")
                        .IsRequired()
                        .HasColumnType("varchar(64)");

                    b.HasKey("Id");

                    b.ToTable("CustomerNotifyTemplateLibrary", (string)null);
                });

            modelBuilder.Entity("Notify.Api.Model.CustomerNotifyTemplatePullInfo", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<int>("NotifyEventSubType")
                        .HasColumnType("int");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<string>("WechatTemplateCode")
                        .IsRequired()
                        .HasColumnType("varchar(64)");

                    b.Property<string>("WechatTemplateNo")
                        .IsRequired()
                        .HasColumnType("varchar(64)");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.HasIndex("WechatTemplateNo", "TenantId", "NotifyEventSubType")
                        .IsUnique();

                    b.ToTable("CustomerNotifyTemplatePullInfo", (string)null);
                });

            modelBuilder.Entity("Notify.Api.Model.CustomerNotifyTemplateSwitch", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<bool>("EmailNotifyIsOpen")
                        .HasColumnType("tinyint(1)");

                    b.Property<sbyte>("NotifyChannel")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("tinyint")
                        .HasDefaultValue((sbyte)1);

                    b.Property<int>("NotifyEventSubType")
                        .HasColumnType("int");

                    b.Property<bool>("SmsNotifyIsOpen")
                        .HasColumnType("tinyint(1)");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime");

                    b.Property<bool>("WechatNotifyIsOpen")
                        .HasColumnType("tinyint(1)");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.HasIndex("NotifyEventSubType", "NotifyChannel", "TenantId")
                        .IsUnique();

                    b.ToTable("CustomerNotifyTemplateSwitch", (string)null);
                });

            modelBuilder.Entity("Notify.Api.Model.ManagerNotifyTemplateDingtalkRobot", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<string>("CountryCode")
                        .HasColumnType("varchar(500)");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<string>("GroupName")
                        .HasColumnType("varchar(500)");

                    b.Property<int>("NotifyEventSubType")
                        .HasColumnType("int");

                    b.Property<sbyte>("NotifyEventType")
                        .HasColumnType("tinyint");

                    b.Property<string>("Salt")
                        .HasColumnType("varchar(500)");

                    b.Property<string>("WebHook")
                        .HasColumnType("varchar(500)");

                    b.HasKey("Id");

                    b.ToTable("ManagerNotifyTemplateDingtalkRobot", (string)null);
                });

            modelBuilder.Entity("Notify.Api.Model.ManagerNotifyTemplateLibrary", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<string>("Keywords")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<int>("NotifyEventSubType")
                        .HasColumnType("int");

                    b.Property<sbyte>("NotifyEventType")
                        .HasColumnType("tinyint");

                    b.Property<string>("WechatCategoryTemplateNo")
                        .IsRequired()
                        .HasColumnType("varchar(64)");

                    b.Property<string>("WechatContentExample")
                        .HasColumnType("varchar(500)");

                    b.Property<string>("WechatTemplateCode")
                        .HasColumnType("varchar(64)");

                    b.Property<string>("WechatTemplateNo")
                        .IsRequired()
                        .HasColumnType("varchar(64)");

                    b.HasKey("Id");

                    b.ToTable("ManagerNotifyTemplateLibrary", (string)null);
                });

            modelBuilder.Entity("Notify.Api.Model.ManagerNotifyTemplateSetting", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<int>("CountryCode")
                        .HasColumnType("int");

                    b.Property<int?>("NotifyEventSubType")
                        .HasColumnType("int");

                    b.Property<sbyte?>("NotifyEventType")
                        .HasColumnType("tinyint");

                    b.Property<long>("StaffId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.ToTable("ManagerNotifyTemplateSetting", (string)null);
                });

            modelBuilder.Entity("Notify.Api.Model.ManagerNotifyTemplateSwitch", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<bool>("IsOpen")
                        .HasColumnType("tinyint(1)");

                    b.Property<int>("NotifyEventSubType")
                        .HasColumnType("int");

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime");

                    b.HasKey("Id");

                    b.HasIndex("NotifyEventSubType")
                        .IsUnique();

                    b.ToTable("ManagerNotifyTemplateSwitch", (string)null);
                });

            modelBuilder.Entity("Notify.Api.Model.SiteMessage", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<long>("AgencyId")
                        .HasColumnType("bigint");

                    b.Property<string>("Content")
                        .IsRequired()
                        .HasColumnType("varchar(512)");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<string>("EnContent")
                        .IsRequired()
                        .HasColumnType("varchar(512)");

                    b.Property<string>("EnTitle")
                        .IsRequired()
                        .HasColumnType("varchar(256)");

                    b.Property<long?>("LinkId")
                        .HasColumnType("bigint");

                    b.Property<sbyte>("LinkType")
                        .HasColumnType("tinyint");

                    b.Property<int>("NotifyEventSubType")
                        .HasColumnType("int");

                    b.Property<sbyte>("NotifyEventType")
                        .HasColumnType("tinyint");

                    b.Property<sbyte>("ObjectType")
                        .HasColumnType("tinyint");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<string>("Title")
                        .IsRequired()
                        .HasColumnType("varchar(256)");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("SiteMessage", (string)null);
                });

            modelBuilder.Entity("Notify.Api.Model.SiteMessageReceiver", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<bool>("IsRead")
                        .HasColumnType("tinyint(1)");

                    b.Property<long>("SiteMessageId")
                        .HasColumnType("bigint");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime");

                    b.Property<long>("UserId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("SiteMessageId");

                    b.HasIndex("TenantId");

                    b.HasIndex("UserId", "SiteMessageId", "IsRead");

                    b.ToTable("SiteMessageReceiver", (string)null);
                });

            modelBuilder.Entity("Notify.Api.Model.SiteMessageTemplateLibrary", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<string>("Content")
                        .IsRequired()
                        .HasColumnType("varchar(512)");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<string>("EnContent")
                        .IsRequired()
                        .HasColumnType("varchar(512)");

                    b.Property<string>("EnTitile")
                        .IsRequired()
                        .HasColumnType("varchar(256)");

                    b.Property<sbyte>("LinkType")
                        .HasColumnType("tinyint");

                    b.Property<int>("NotifyEventSubType")
                        .HasColumnType("int");

                    b.Property<sbyte>("NotifyEventType")
                        .HasColumnType("tinyint");

                    b.Property<sbyte>("ObjectType")
                        .HasColumnType("tinyint");

                    b.Property<string>("Title")
                        .IsRequired()
                        .HasColumnType("varchar(256)");

                    b.HasKey("Id");

                    b.ToTable("SiteMessageTemplateLibrary", (string)null);
                });

            modelBuilder.Entity("Notify.Api.Model.StaffNotifyTemplateDingtalkRobot", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<string>("CountryCode")
                        .HasColumnType("varchar(500)");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<string>("GroupName")
                        .HasColumnType("varchar(500)");

                    b.Property<int>("NotifyEventSubType")
                        .HasColumnType("int");

                    b.Property<sbyte>("NotifyEventType")
                        .HasColumnType("tinyint");

                    b.Property<string>("Salt")
                        .HasColumnType("varchar(500)");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<string>("WebHook")
                        .HasColumnType("varchar(500)");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("StaffNotifyTemplateDingtalkRobot", (string)null);
                });

            modelBuilder.Entity("Notify.Api.Model.StaffNotifyTemplateLibrary", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<string>("Keywords")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<int>("NotifyEventSubType")
                        .HasColumnType("int");

                    b.Property<sbyte>("NotifyEventType")
                        .HasColumnType("tinyint");

                    b.Property<string>("WechatCategoryTemplateNo")
                        .IsRequired()
                        .HasColumnType("varchar(64)");

                    b.Property<string>("WechatContentExample")
                        .HasColumnType("varchar(500)");

                    b.Property<string>("WechatTemplateCode")
                        .HasColumnType("varchar(64)");

                    b.Property<string>("WechatTemplateNo")
                        .IsRequired()
                        .HasColumnType("varchar(64)");

                    b.HasKey("Id");

                    b.ToTable("StaffNotifyTemplateLibrary", (string)null);
                });

            modelBuilder.Entity("Notify.Api.Model.StaffNotifyTemplateSetting", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<long?>("AgencyId")
                        .HasColumnType("bigint");

                    b.Property<string>("CountryCode")
                        .HasColumnType("varchar(1000)");

                    b.Property<int?>("NotifyEventSubType")
                        .HasColumnType("int");

                    b.Property<sbyte?>("NotifyEventType")
                        .HasColumnType("tinyint");

                    b.Property<long>("StaffId")
                        .HasColumnType("bigint");

                    b.Property<sbyte>("StaffRole")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("tinyint")
                        .HasDefaultValue((sbyte)1);

                    b.Property<long?>("SupplierId")
                        .HasColumnType("bigint");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("StaffNotifyTemplateSetting", (string)null);
                });

            modelBuilder.Entity("Notify.Api.Model.StaffNotifyTemplateSwitch", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<bool>("IsOpen")
                        .HasColumnType("tinyint(1)");

                    b.Property<int>("NotifyEventSubType")
                        .HasColumnType("int");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.HasIndex("NotifyEventSubType", "TenantId")
                        .IsUnique();

                    b.ToTable("StaffNotifyTemplateSwitch", (string)null);
                });

            modelBuilder.Entity("Notify.Api.Model.SupplierNotifyTemplateLibrary", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<string>("Keywords")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<int>("NotifyEventSubType")
                        .HasColumnType("int");

                    b.Property<sbyte>("NotifyEventType")
                        .HasColumnType("tinyint");

                    b.Property<string>("WechatCategoryTemplateNo")
                        .IsRequired()
                        .HasColumnType("varchar(64)");

                    b.Property<string>("WechatContentExample")
                        .HasColumnType("varchar(500)");

                    b.Property<string>("WechatTemplateCode")
                        .IsRequired()
                        .HasColumnType("varchar(64)");

                    b.Property<string>("WechatTemplateNo")
                        .IsRequired()
                        .HasColumnType("varchar(64)");

                    b.HasKey("Id");

                    b.ToTable("SupplierNotifyTemplateLibrary", (string)null);
                });

            modelBuilder.Entity("Notify.Api.Model.SupplierNotifyTemplateSwitch", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<bool>("IsOpen")
                        .HasColumnType("tinyint(1)");

                    b.Property<int>("NotifyEventSubType")
                        .HasColumnType("int");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.HasIndex("NotifyEventSubType", "TenantId")
                        .IsUnique();

                    b.ToTable("SupplierNotifyTemplateSwitch", (string)null);
                });

            modelBuilder.Entity("Notify.Api.Model.TenantEmailBox", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<string>("Address")
                        .IsRequired()
                        .HasColumnType("varchar(200)");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<string>("Password")
                        .IsRequired()
                        .HasColumnType("varchar(50)");

                    b.Property<string>("ServerHost")
                        .IsRequired()
                        .HasColumnType("varchar(50)");

                    b.Property<int>("ServerPort")
                        .HasColumnType("int");

                    b.Property<int>("TenantEmailBoxType")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasDefaultValue(1);

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("UpdateTime")
                        .HasColumnType("datetime");

                    b.Property<bool>("UseSsl")
                        .HasColumnType("tinyint(1)");

                    b.Property<string>("UserName")
                        .IsRequired()
                        .HasColumnType("varchar(20)");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.HasIndex("TenantId", "TenantEmailBoxType")
                        .IsUnique();

                    b.ToTable("TenantEmailBox", (string)null);
                });

            modelBuilder.Entity("Notify.Api.Model.TenantSmsSign", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<sbyte>("ApplyStatus")
                        .HasColumnType("tinyint");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<string>("Description")
                        .HasColumnType("varchar(64)");

                    b.Property<string>("Sign")
                        .IsRequired()
                        .HasColumnType("varchar(16)");

                    b.Property<string>("SignId")
                        .IsRequired()
                        .HasColumnType("varchar(32)");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime");

                    b.HasKey("Id");

                    b.HasIndex("Sign")
                        .IsUnique();

                    b.HasIndex("TenantId")
                        .IsUnique();

                    b.ToTable("TenantSmsSign", (string)null);
                });
#pragma warning restore 612, 618
        }
    }
}
