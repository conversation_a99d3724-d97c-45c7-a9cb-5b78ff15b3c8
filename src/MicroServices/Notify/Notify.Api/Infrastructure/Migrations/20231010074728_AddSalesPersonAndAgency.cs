using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Notify.Api.Infrastructure.Migrations
{
    public partial class AddSalesPersonAndAgency : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AlterColumn<sbyte>(
                name: "NotifyEventType",
                table: "StaffNotifyTemplateSetting",
                type: "tinyint",
                nullable: true,
                oldClrType: typeof(sbyte),
                oldType: "tinyint");

            migrationBuilder.AddColumn<int>(
                name: "NotifyEventSubType",
                table: "StaffNotifyTemplateSetting",
                type: "int",
                nullable: true);

            migrationBuilder.AddColumn<sbyte>(
                name: "StaffRole",
                table: "StaffNotifyTemplateSetting",
                type: "tinyint",
                nullable: false,
                defaultValue: (sbyte)1);

            migrationBuilder.CreateTable(
                name: "AgencyNotifyTemplateLibrary",
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false),
                    NotifyEventType = table.Column<sbyte>(type: "tinyint", nullable: false),
                    NotifyEventSubType = table.Column<int>(type: "int", nullable: false),
                    SmsTemplateNo = table.Column<string>(type: "varchar(64)", nullable: false)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    SmsContentExample = table.Column<string>(type: "varchar(200)", nullable: false)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    CreateTime = table.Column<DateTime>(type: "datetime", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_AgencyNotifyTemplateLibrary", x => x.Id);
                })
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.CreateTable(
                name: "AgencyNotifyTemplateSwitch",
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false),
                    NotifyEventSubType = table.Column<int>(type: "int", nullable: false),
                    IsOpen = table.Column<bool>(type: "tinyint(1)", nullable: false),
                    CreateTime = table.Column<DateTime>(type: "datetime", nullable: false),
                    UpdateTime = table.Column<DateTime>(type: "datetime", nullable: true),
                    TenantId = table.Column<long>(type: "bigint", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_AgencyNotifyTemplateSwitch", x => x.Id);
                })
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.CreateIndex(
                name: "IX_AgencyNotifyTemplateSwitch_NotifyEventSubType_TenantId",
                table: "AgencyNotifyTemplateSwitch",
                columns: new[] { "NotifyEventSubType", "TenantId" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_AgencyNotifyTemplateSwitch_TenantId",
                table: "AgencyNotifyTemplateSwitch",
                column: "TenantId");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "AgencyNotifyTemplateLibrary");

            migrationBuilder.DropTable(
                name: "AgencyNotifyTemplateSwitch");

            migrationBuilder.DropColumn(
                name: "NotifyEventSubType",
                table: "StaffNotifyTemplateSetting");

            migrationBuilder.DropColumn(
                name: "StaffRole",
                table: "StaffNotifyTemplateSetting");

            migrationBuilder.AlterColumn<sbyte>(
                name: "NotifyEventType",
                table: "StaffNotifyTemplateSetting",
                type: "tinyint",
                nullable: false,
                defaultValue: (sbyte)0,
                oldClrType: typeof(sbyte),
                oldType: "tinyint",
                oldNullable: true);
        }
    }
}
