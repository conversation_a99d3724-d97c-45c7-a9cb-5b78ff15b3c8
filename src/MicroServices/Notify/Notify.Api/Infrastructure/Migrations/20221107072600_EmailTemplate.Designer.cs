// <auto-generated />
using System;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using Notify.Api.Infrastructure;

#nullable disable

namespace Notify.Api.Infrastructure.Migrations
{
    [DbContext(typeof(CustomDbContext))]
    [Migration("20221107072600_EmailTemplate")]
    partial class EmailTemplate
    {
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "6.0.5")
                .HasAnnotation("Relational:MaxIdentifierLength", 64);

            modelBuilder.Entity("Notify.Api.Model.CustomerNotifyTemplateLibrary", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<string>("EmailTemplateContent")
                        .HasColumnType("text");

                    b.Property<string>("EmailTemplateTitle")
                        .HasColumnType("varchar(200)");

                    b.Property<sbyte>("NotifyChannel")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("tinyint")
                        .HasDefaultValue((sbyte)1);

                    b.Property<int>("NotifyEventSubType")
                        .HasColumnType("int");

                    b.Property<sbyte>("NotifyEventType")
                        .HasColumnType("tinyint");

                    b.Property<string>("SmsContentExample")
                        .HasColumnType("varchar(200)");

                    b.Property<string>("SmsTemplateNo")
                        .HasColumnType("varchar(64)");

                    b.Property<string>("WechatContentExample")
                        .HasColumnType("varchar(500)");

                    b.Property<string>("WechatTemplateNo")
                        .IsRequired()
                        .HasColumnType("varchar(64)");

                    b.HasKey("Id");

                    b.ToTable("CustomerNotifyTemplateLibrary", (string)null);
                });

            modelBuilder.Entity("Notify.Api.Model.CustomerNotifyTemplatePullInfo", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<string>("WechatTemplateCode")
                        .IsRequired()
                        .HasColumnType("varchar(64)");

                    b.Property<string>("WechatTemplateNo")
                        .IsRequired()
                        .HasColumnType("varchar(64)");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.HasIndex("WechatTemplateNo", "TenantId")
                        .IsUnique();

                    b.ToTable("CustomerNotifyTemplatePullInfo", (string)null);
                });

            modelBuilder.Entity("Notify.Api.Model.CustomerNotifyTemplateSwitch", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<bool>("EmailNotifyIsOpen")
                        .HasColumnType("tinyint(1)");

                    b.Property<sbyte>("NotifyChannel")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("tinyint")
                        .HasDefaultValue((sbyte)1);

                    b.Property<int>("NotifyEventSubType")
                        .HasColumnType("int");

                    b.Property<bool>("SmsNotifyIsOpen")
                        .HasColumnType("tinyint(1)");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime");

                    b.Property<bool>("WechatNotifyIsOpen")
                        .HasColumnType("tinyint(1)");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.HasIndex("NotifyEventSubType", "NotifyChannel", "TenantId")
                        .IsUnique();

                    b.ToTable("CustomerNotifyTemplateSwitch", (string)null);
                });

            modelBuilder.Entity("Notify.Api.Model.StaffNotifyTemplateLibrary", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<int>("NotifyEventSubType")
                        .HasColumnType("int");

                    b.Property<sbyte>("NotifyEventType")
                        .HasColumnType("tinyint");

                    b.Property<string>("WechatContentExample")
                        .HasColumnType("varchar(500)");

                    b.Property<string>("WechatTemplateCode")
                        .HasColumnType("varchar(64)");

                    b.Property<string>("WechatTemplateNo")
                        .IsRequired()
                        .HasColumnType("varchar(64)");

                    b.HasKey("Id");

                    b.ToTable("StaffNotifyTemplateLibrary", (string)null);
                });

            modelBuilder.Entity("Notify.Api.Model.StaffNotifyTemplateSetting", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<sbyte>("NotifyEventType")
                        .HasColumnType("tinyint");

                    b.Property<long>("StaffId")
                        .HasColumnType("bigint");

                    b.Property<long>("SupplierId")
                        .HasColumnType("bigint");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("StaffNotifyTemplateSetting", (string)null);
                });

            modelBuilder.Entity("Notify.Api.Model.StaffNotifyTemplateSwitch", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<bool>("IsOpen")
                        .HasColumnType("tinyint(1)");

                    b.Property<int>("NotifyEventSubType")
                        .HasColumnType("int");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.HasIndex("NotifyEventSubType", "TenantId")
                        .IsUnique();

                    b.ToTable("StaffNotifyTemplateSwitch", (string)null);
                });

            modelBuilder.Entity("Notify.Api.Model.SupplierNotifyTemplateLibrary", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<int>("NotifyEventSubType")
                        .HasColumnType("int");

                    b.Property<sbyte>("NotifyEventType")
                        .HasColumnType("tinyint");

                    b.Property<string>("WechatContentExample")
                        .HasColumnType("varchar(500)");

                    b.Property<string>("WechatTemplateCode")
                        .IsRequired()
                        .HasColumnType("varchar(64)");

                    b.Property<string>("WechatTemplateNo")
                        .IsRequired()
                        .HasColumnType("varchar(64)");

                    b.HasKey("Id");

                    b.ToTable("SupplierNotifyTemplateLibrary", (string)null);
                });

            modelBuilder.Entity("Notify.Api.Model.SupplierNotifyTemplateSwitch", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<bool>("IsOpen")
                        .HasColumnType("tinyint(1)");

                    b.Property<int>("NotifyEventSubType")
                        .HasColumnType("int");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.HasIndex("NotifyEventSubType", "TenantId")
                        .IsUnique();

                    b.ToTable("SupplierNotifyTemplateSwitch", (string)null);
                });

            modelBuilder.Entity("Notify.Api.Model.TenantEmailBox", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<string>("Address")
                        .IsRequired()
                        .HasColumnType("varchar(200)");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<string>("Password")
                        .IsRequired()
                        .HasColumnType("varchar(50)");

                    b.Property<string>("ServerHost")
                        .IsRequired()
                        .HasColumnType("varchar(50)");

                    b.Property<int>("ServerPort")
                        .HasColumnType("int");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("UpdateTime")
                        .HasColumnType("datetime");

                    b.Property<bool>("UseSsl")
                        .HasColumnType("tinyint(1)");

                    b.Property<string>("UserName")
                        .IsRequired()
                        .HasColumnType("varchar(20)");

                    b.HasKey("Id");

                    b.HasIndex("TenantId")
                        .IsUnique();

                    b.ToTable("TenantEmailBox", (string)null);
                });

            modelBuilder.Entity("Notify.Api.Model.TenantSmsSign", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<sbyte>("ApplyStatus")
                        .HasColumnType("tinyint");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<string>("Description")
                        .HasColumnType("varchar(64)");

                    b.Property<string>("Sign")
                        .IsRequired()
                        .HasColumnType("varchar(16)");

                    b.Property<string>("SignId")
                        .IsRequired()
                        .HasColumnType("varchar(32)");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime");

                    b.HasKey("Id");

                    b.HasIndex("Sign")
                        .IsUnique();

                    b.HasIndex("TenantId")
                        .IsUnique();

                    b.ToTable("TenantSmsSign", (string)null);
                });
#pragma warning restore 612, 618
        }
    }
}
