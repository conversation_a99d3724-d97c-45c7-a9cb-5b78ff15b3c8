using Contracts.Common.Notify.Enums;
using EfCoreExtensions.EntityBase;

namespace Notify.Api.Model
{
    public class AgencyNotifyTemplateLibrary : KeyBase
    {
        /// <summary>
        /// 通知事件类型
        /// </summary>
        public NotifyEventType NotifyEventType { get; set; }

        /// <summary>
        /// 通知事件子类型
        /// </summary>
        public NotifyEventSubType NotifyEventSubType { get; set; }

        /// <summary>
        /// 短信模板编号
        /// </summary>
        public string SmsTemplateNo { get; set; }

        /// <summary>
        /// 短信模板内容示例
        /// <para> 526852 (手机动态验证码，10分钟内有效，请完成验证)。</para>
        /// </summary>
        public string SmsContentExample { get; set; }


        public DateTime CreateTime { get; set; } = DateTime.Now;
    }
}
