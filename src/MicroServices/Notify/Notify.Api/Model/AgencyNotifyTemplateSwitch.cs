using Contracts.Common.Notify.Enums;
using EfCoreExtensions.EntityBase;

namespace Notify.Api.Model;

/// <summary>
/// 分销商通知开关
/// </summary>
public class AgencyNotifyTemplateSwitch : TenantBase
{
    /// <summary>
    /// 通知事件子类型
    /// </summary>
    public NotifyEventSubType NotifyEventSubType { get; set; }

    public bool IsOpen { get; set; }

    public DateTime CreateTime { get; set; } = DateTime.Now;

    public DateTime? UpdateTime { get; set; } = DateTime.Now;
}
