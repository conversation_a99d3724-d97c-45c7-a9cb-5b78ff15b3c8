using Contracts.Common.Notify.DTOs.SiteMessage;
using EfCoreExtensions.Abstract;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Notify.Api.Services.Interfaces;

namespace Notify.Api.Controllers;
[Route("[controller]/[action]")]
[ApiController]
public class SiteMessageController : ControllerBase
{
    private readonly ISiteMessageService _siteMessageService;

    public SiteMessageController(ISiteMessageService siteMessageService)
    {
        _siteMessageService = siteMessageService;
    }

    /// <summary>
    /// 搜索
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    public async Task<PagingModel<SearchOutput>> Search(SearchInput input)
    {
        return await _siteMessageService.Search(input);
    }

    /// <summary>
    /// 详情
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    public async Task<DetailOutput> Detail(DetailInput input)
    {
        return await _siteMessageService.Detail(input);
    }

    /// <summary>
    /// 读取 已读
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    public async Task<DetailOutput> Read(ReadInput input)
    {
        var result = await _siteMessageService.Read(input);
        return result;
    }

    /// <summary>
    /// 批量已读
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    public async Task<IActionResult> BatchRead(BatchReadInput input)
    {
        await _siteMessageService.BatchRead(input);
        return Ok();
    }

}
