using Contracts.Common.Notify.Enums;
using Contracts.Common.Order.Enums;
using Notify.Api.Services.MessageCenter.MessageModelBase;
using System.Text.Json;

namespace Notify.Api.Services.MessageCenter.TenantStaff.DingTalk;

//团房 - 团房申请未指派
[MessageModelMatching(SendToTheRole.TenantStaff, NotifyEventSubType.Hotel_GroupRoom_Application_NotAssign, NotifyMode.DingTalkRobot)]
public class HotelGroupRoomApplication : DingtalkGroupMessageModelBase
{
    #region 必传参数列表
    public long OrderId { get; set; }
    public GroupBookingApplicationFormStatus Status { get; set; }

    public string AgencyName { get; set; }
    public long TenantId { get; set; }
    public string HotelName { get; set; }

    /// <summary>
    /// 入住日期
    /// </summary>
    public DateTime? CheckInDate { get; set; }

    /// <summary>
    /// 离店日期
    /// </summary>
    public DateTime? CheckOutDate { get; set; }
    #endregion

    public HotelGroupRoomApplication() { }
    public HotelGroupRoomApplication(JsonElement variables, IParamsSupportService paramsSupportService, long tenantId)
    {
        var item = JsonSerializer.Deserialize<HotelGroupRoomApplication>(variables, JsonSerializerHelper.serializerOptions);

        RegionCodes = item.RegionCodes;
        StaffUserIds = item.StaffUserIds;

        var tenantConfig = paramsSupportService.GetTenantSysConfig(tenantId).GetAwaiter().GetResult();
        var sysSetting = paramsSupportService.GetSysSetting();
        string vebkPath = $"http://{tenantConfig.Subdomain}{sysSetting.Vebk}";

        string hotelName = string.IsNullOrEmpty(item.HotelName) ? "酒店待SAAS确认" : item.HotelName;

        var statusName = string.Empty;
        switch (item.Status)
        {
            case GroupBookingApplicationFormStatus.NewApplication:
                statusName = "【待确认】团房新询单";
                break;
            case GroupBookingApplicationFormStatus.Inquiried:
                statusName = "【待审核】团房报价";
                break;
            case GroupBookingApplicationFormStatus.WaitForAuditPreOrder:
                statusName = "【待审核】团房支付订单";
                break;
        }

        Content += $"## {statusName}  \n";
        Content += $"团房单号：{item.OrderId}  \n";
        Content += $"分销商名称：{item.AgencyName}  \n";
        Content += $"酒店名称：{hotelName}  \n";
        Content += $"入离日期：{item.CheckInDate:yyyy-MM-dd}~{item.CheckOutDate:yyyy-MM-dd}  ";

        Btns = new List<Cit.Message.DingTalk.Robot.Message.BtnsItem>
        {
            new()
            {
                title = "点击跳转查看详情",
                actionURL = Cit.Message.DingTalk.Robot.Utils.LinkUtil
                    .PcSlide($"{vebkPath}/#/groupRoomOrder/groupRoomApplyList?id={item.OrderId}")
            }
        };
    }

}
