using Contracts.Common.Notify.Enums;
using Contracts.Common.Order.Enums;
using Extensions;
using Notify.Api.Services.MessageCenter.MessageModelBase;
using System.Text.Json;

namespace Notify.Api.Services.MessageCenter.ManagerStaff.DingTalk;

[MessageModelMatching(SendToTheRole.ManagerStaff, NotifyEventSubType.Hotel_GroupRoom_Paid_Reminder, NotifyMode.DingTalkRobot)]
public class GroupRoomPaidReminder : DingtalkGroupMessageModelBase
{
    #region 必传参数列表

    public long GroupBookingApplicationFormId { get; set; }

    public string AgencyName { get; set; }

    /// <summary>
    /// 支付比例类型 1-首款 2-尾款
    /// </summary>
    public PaymentRatioType PaymentRatioType { get; set; }

    /// <summary>
    /// 首款金额
    /// </summary>
    public decimal InitialPaymentAmount { get; set; }

    /// <summary>
    /// 尾款金额
    /// </summary>
    public decimal FinalPaymentAmount { get; set; }

    public override long[] StaffUserIds { get; set; }

    #endregion

    public GroupRoomPaidReminder()
    {
    }

    public GroupRoomPaidReminder(JsonElement variables, IParamsSupportService paramsSupportService, long tenantId)
    {
        var item = JsonSerializer.Deserialize<GroupRoomPaidReminder>(variables, JsonSerializerHelper.serializerOptions);

        RegionCodes = item.RegionCodes;
        StaffUserIds = item.StaffUserIds;

        string manageWebHost = paramsSupportService.GetManageWebHost();
        //Content = $"""
        //    ## 客户支付团房订单{item.PaymentRatioType.GetDescription()}  
        //    团房单号：{item.GroupBookingOrderId}  
        //    分销商名称：{item.AgencyName}  
        //    金额：首付{item.InitialPaymentAmount:N}，尾款{item.FinalPaymentAmount:N}  
        //    """;
        Content += $"## 客户支付团房订单{item.PaymentRatioType.GetDescription()}  \n";
        Content += $"团房单号：{item.GroupBookingApplicationFormId}  \n";
        Content += $"分销商名称：{item.AgencyName}  \n";
        Content += $"金额：首付{item.InitialPaymentAmount:N}，尾款{item.FinalPaymentAmount:N}  ";

        Btns = new List<Cit.Message.DingTalk.Robot.Message.BtnsItem>
        {
            new() 
            {
                title = "点击跳转查看详情",
                actionURL = Cit.Message.DingTalk.Robot.Utils.LinkUtil
                    .PcSlide($"{manageWebHost}/#/gruopRoomBusiness/groopRoomApply?id={item.GroupBookingApplicationFormId}")
            }
        };
    }
}