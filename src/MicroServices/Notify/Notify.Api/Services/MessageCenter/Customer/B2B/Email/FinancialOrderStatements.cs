using Contracts.Common.Notify.Enums;
using Contracts.Common.Notify.Messages;
using System.Text.Json;

namespace Notify.Api.Services.MessageCenter.Customer.B2B.Email;

//财务通知 - 对账单提醒
[MessageModelMatching(SendToTheRole.Customer, NotifyEventSubType.Financial_OrderStatements, NotifyMode.Email, NotifyChannel.B2b)]
public class FinancialOrderStatements : EmailMessageModelBase
{
    public FinancialOrderStatements(
        JsonElement variables,
        IParamsSupportService paramsSupportService,
        long tenantId) : base(tenantId)
    {
        var item = JsonSerializer.Deserialize<FinancialOrderStatementsValues>(variables, JsonSerializerHelper.serializerOptions);
        Addressee = item.Addressee;
        CcAddressee = item.CcAddressee;
        AgencyName = item.AgencyName;
        BillingCycleBegin = item.BillingCycleBegin;
        BillingCycleEnd = item.BillingCycleEnd;
        AttachmentFilePaths = item.AttachmentFilePaths;
        TotalAmount = item.TotalAmount.ToString("0.00");
        TotalAmountCurrencyCode = item.TotalAmountCurrencyCode;
        PaymentDate = item.PaymentDate;

        // ￥ 500 CNY
        TotalAmount = $"{GetCurrencySymbol(item.TotalAmountCurrencyCode)} {TotalAmount}";
    }

    /// <summary>
    /// 收件人
    /// </summary>
    public override string Addressee { get; protected set; }
    public string AgencyName { get; private set; }
    public string BillingCycleBegin { get; private set; }
    public string BillingCycleEnd { get; private set; }
    /// <summary>
    /// 附件
    /// </summary>
    public List<AttachmentFilePath>? AttachmentFilePaths { get; set; }
    /// <summary>
    /// 合计金额
    /// </summary>
    public string TotalAmount { get; set; }

    /// <summary>
    /// 应收总额币种
    /// </summary>
    public string TotalAmountCurrencyCode { get; set; }

    /// <summary>
    /// 付款日期
    /// </summary>
    public string PaymentDate { get; set; }

    public struct FinancialOrderStatementsValues
    {
        public string Addressee { get; set; }
        public IEnumerable<string> CcAddressee { get; set; }
        public string AgencyName { get; set; }
        public string BillingCycleBegin { get; set; }
        public string BillingCycleEnd { get; set; }
        /// <summary>
        /// 附件
        /// </summary>
        public List<AttachmentFilePath>? AttachmentFilePaths { get; set; }

        /// <summary>
        /// 合计金额
        /// </summary>
        public decimal TotalAmount { get; set; }

        /// <summary>
        /// 应收总额币种
        /// </summary>
        public string TotalAmountCurrencyCode { get; set; }

        /// <summary>
        /// 付款日期
        /// </summary>
        public string PaymentDate { get; set; }
    }
}
