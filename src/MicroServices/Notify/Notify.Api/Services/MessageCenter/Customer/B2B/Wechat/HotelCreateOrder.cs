using Contracts.Common.Notify.Enums;
using Contracts.Common.WeChat.DTOs;
using System.Text.Json;

namespace Notify.Api.Services.MessageCenter.Customer.B2B.Wechat;

//日历酒店 - 酒店下单成功
[MessageModelMatching(SendToTheRole.Customer, NotifyEventSubType.Hotel_CreateOrder, NotifyMode.Wechat, NotifyChannel.B2b)]
public class HotelCreateOrder : CustomerWechatMessageModelBase
{
    #region 必传参数列表

    public override long UserId { get; set; }
    public long BaseOrderId { get; set; }
    public string HotelName { get; set; }
    public string RoomName { get; set; }
    public DateTime CheckInDate { get; set; }
    public DateTime CheckOutDate { get; set; }
    public int Quantity { get; set; }
    public decimal PaymentAmount { get; set; }
    public string PaymentCurrencyCode { get; set; }

    #endregion

    //for deserialize
    public HotelCreateOrder()
    {
    }

    public HotelCreateOrder(JsonElement variables)
    {
        var item = JsonSerializer.Deserialize<HotelCreateOrder>(variables, JsonSerializerHelper.serializerOptions);
        UserId = item.UserId;
        Url = $"pages/order/detail/hotel/index?orderId={item.BaseOrderId}";
        Data = new List<WechatMessageTempalteSendData>
        {
             new WechatMessageTempalteSendData
            {
                Key = "character_string9",
                Content = new WechatMessageTempalteSendDataContent
                {
                    Value = $"{item.BaseOrderId}"
                }
            },
            new WechatMessageTempalteSendData
            {
                Key = "thing1",
                Content = new WechatMessageTempalteSendDataContent
                {
                    Value = item.HotelName
                }
            },
            new WechatMessageTempalteSendData
            {
                Key = "thing6",
                Content = new WechatMessageTempalteSendDataContent
                {
                    Value = item.RoomName + $"({item.Quantity})"
                }
            },
            new WechatMessageTempalteSendData
            {
                Key = "time3",
                Content = new WechatMessageTempalteSendDataContent
                {
                    Value = $"{item.CheckInDate:yyyy-MM-dd} ~ {item.CheckOutDate:yyyy-MM-dd}"
                }
            },
             new WechatMessageTempalteSendData
            {
                Key = "amount2",
                Content = new WechatMessageTempalteSendDataContent
                {
                    Value = $"{item.PaymentAmount:0.0}"
                }
            }

        };
    }
}