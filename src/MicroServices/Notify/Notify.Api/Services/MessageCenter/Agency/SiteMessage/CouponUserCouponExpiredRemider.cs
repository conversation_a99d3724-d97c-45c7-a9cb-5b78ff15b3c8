using Contracts.Common.Notify.Enums;
using Newtonsoft.Json.Linq;
using System.Text.Json;

namespace Notify.Api.Services.MessageCenter.Agency.SiteMessage;

[MessageModelMatching(SendToTheRole.AgencyStaff, NotifyEventSubType.Coupon_UserCouponExpiredRemider, NotifyMode.SiteMessage)]
public class CouponUserCouponExpiredRemider : SiteMessageModelBase
{
    public CouponUserCouponExpiredRemider()
    {
    }

    public CouponUserCouponExpiredRemider(JsonElement variables, NotificationTemplate notificationTemplate) : base(variables, notificationTemplate)
    {
        var item = JsonSerializer.Deserialize<CouponUserCouponExpiredRemider>(variables, JsonSerializerHelper.serializerOptions);
        AgencyId = item.AgencyId;
        Count = item.Count;
        CouponDesc = item.CouponDesc;
        ValidDays= item.ValidDays;

        base.RenderMessage(item, notificationTemplate);
    }

    public int Count { get; set; }

    public string CouponDesc { get; set; }
    public int ValidDays { get; set; }

    public override long AgencyId { get; set; }
    public override long? UserId { get; set; }
}
