using Contracts.Common.Notify.Enums;
using Newtonsoft.Json.Linq;
using System.Text.Json;

namespace Notify.Api.Services.MessageCenter.Agency.SiteMessage;

[MessageModelMatching(SendToTheRole.AgencyStaff, NotifyEventSubType.Coupon_UserCouponReceived, NotifyMode.SiteMessage)]
public class CouponUserCouponReceived : SiteMessageModelBase
{
    public CouponUserCouponReceived()
    {
    }

    public CouponUserCouponReceived(JsonElement variables, NotificationTemplate notificationTemplate) : base(variables, notificationTemplate)
    {
        var item = JsonSerializer.Deserialize<CouponUserCouponReceived>(variables, JsonSerializerHelper.serializerOptions);
        AgencyId = item.AgencyId;
        ReceivedTime = item.ReceivedTime;
        Count = item.Count;

        base.RenderMessage(item, notificationTemplate);
    }

    public string ReceivedTime { get; set; }
    public int Count { get; set; }
    public override long AgencyId { get; set ; }
    public override long? UserId { get ; set; }
}
