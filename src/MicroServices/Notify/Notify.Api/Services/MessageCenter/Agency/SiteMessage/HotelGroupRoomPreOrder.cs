using Contracts.Common.Notify.Enums;
using Newtonsoft.Json.Linq;
using System.Text.Json;

namespace Notify.Api.Services.MessageCenter.Agency.SiteMessage;

[MessageModelMatching(SendToTheRole.AgencyStaff, NotifyEventSubType.Hotel_GroupRoom_PreOrder, NotifyMode.SiteMessage)]
public class HotelGroupRoomPreOrder : SiteMessageModelBase
{
    public HotelGroupRoomPreOrder()
    {
    }

    public HotelGroupRoomPreOrder(JsonElement variables, NotificationTemplate notificationTemplate) : base(variables, notificationTemplate)
    {
        var item = JsonSerializer.Deserialize<HotelGroupRoomPreOrder>(variables, JsonSerializerHelper.serializerOptions);
        AgencyId = item.AgencyId;
        UserId = item.UserId;

        notificationTemplate.SetLinkId(item.ApplicationFormId);
        base.RenderMessage(item, notificationTemplate);
    }

    public override long AgencyId { get; set; }
    public override long? UserId { get; set; }

    public long ApplicationFormId { get; set; }
}
