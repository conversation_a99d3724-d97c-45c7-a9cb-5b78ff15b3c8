using AutoMapper;
using Contracts.Common.Notify.DTOs.ManagerNotify.Dingtalks;
using Contracts.Common.Notify.DTOs.StaffNotify.Dingtalks;

namespace Notify.Api.Services.MappingProfiles;

public class NotifyTemplateDingtalkProfiles : Profile
{
    public NotifyTemplateDingtalkProfiles()
    {
        CreateMap<ManagerNotifyTemplateDingtalk, ManagerNotifyTemplateDingtalkDto>().ReverseMap();

        CreateMap<ManagerNotifyTemplateDingtalk, ManagerNotifyTemplateDingtalkDetailDto>().ReverseMap();

        CreateMap<StaffNotifyTemplateDingtalk, StaffNotifyTemplateDingtalkDto>().ReverseMap();

        CreateMap<StaffNotifyTemplateDingtalk, StaffNotifyTemplateDingtalkDetailDto>().ReverseMap();


    }
}
