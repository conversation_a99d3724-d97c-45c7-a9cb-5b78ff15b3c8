using Contracts.Common.Notify.DTOs.StaffNotify;
using Contracts.Common.Notify.Enums;
using FluentValidation;

namespace Notify.Api.Services.Validator;

public class AddStaffNotifySettingInputValidator : AbstractValidator<AddStaffNotifySettingInput>
{
    public AddStaffNotifySettingInputValidator()
    {
        RuleFor(x => x.NotifyEventType)
            .NotEmpty()
            .IsInEnum()
            .When(x => x.StaffRole is StaffRole.TenantStaff);

        RuleFor(x => x.NotifyEventType)
            .Empty()
            .When(x => x.StaffRole is not StaffRole.TenantStaff);

        RuleFor(x => x.NotifyEventSubType)
            .NotEmpty()
            .IsInEnum()
            .When(x => x.StaffRole is StaffRole.SalesPerson);

        RuleFor(x => x.NotifyEventSubType)
           .Empty()
           .When(x => x.StaffRole is not StaffRole.SalesPerson);

        RuleFor(x => x.SupplierId)
            .NotEmpty()
            .When(x => x.StaffRole is StaffRole.TenantStaff);

        RuleFor(x => x.StaffIds)
            .NotEmpty();

        RuleFor(x => x.StaffRole)
            .NotEmpty()
            .IsInEnum();
    }
}
