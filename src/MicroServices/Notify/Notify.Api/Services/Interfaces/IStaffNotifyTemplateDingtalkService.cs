using Contracts.Common.Notify.DTOs.StaffNotify.Dingtalks;
using Contracts.Common.Notify.DTOs.StaffNotify.DingtalkSwitche;
using Contracts.Common.Notify.Enums;
using EfCoreExtensions.Abstract;

namespace Notify.Api.Services.Interfaces;

public interface IStaffNotifyTemplateDingtalkService
{
    /// <summary>
    /// 查询钉钉服务模版
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<PagingModel<StaffNotifyTemplateDingtalkListDto>> SearchAsync(GetStaffNotifyTemplateDingtalkInput input);

    /// <summary>
    /// 获取钉钉模版下拉选择
    /// </summary>
    /// <returns></returns>
    Task<List<StaffNotifyTemplateDingtalkDto>> ListAsync(NotifyEventType? notifyEventType);

    /// <summary>
    /// 删除钉钉服务模版
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task DeleteAsync(StaffNotifyTemplateDingtalkInput input);

    /// <summary>
    /// 获取模版信息与机器人列表
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<StaffNotifyTemplateDingtalkDetailDto> DetailAsync(long templateDingtalkId);

    /// <summary>
    /// 创建钉钉模版
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task AddAsync(StaffNotifyTemplateDingtalkDetailDto input);

    /// <summary>
    /// 修改钉钉模版
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task UpdateAsync(StaffNotifyTemplateDingtalkDetailDto input);

    /// <summary>
    /// 获取该业务类型的钉钉模版配置
    /// </summary>
    /// <param name="notifyEventSubType"></param>
    /// <returns></returns>
    Task<StaffNotifyTemplateDingtalkRobotSwitcheDto?> GetSwitche(NotifyEventSubType notifyEventSubType);
}
