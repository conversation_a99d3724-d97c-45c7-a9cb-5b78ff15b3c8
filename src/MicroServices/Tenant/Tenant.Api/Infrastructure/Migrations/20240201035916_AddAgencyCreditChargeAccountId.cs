using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Tenant.Api.Infrastructure.Migrations
{
    public partial class AddAgencyCreditChargeAccountId : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<long>(
                name: "AccountInfo_TenantBankAccountId",
                table: "AgencyCreditCharge",
                type: "bigint",
                nullable: true);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "AccountInfo_TenantBankAccountId",
                table: "AgencyCreditCharge");
        }
    }
}
