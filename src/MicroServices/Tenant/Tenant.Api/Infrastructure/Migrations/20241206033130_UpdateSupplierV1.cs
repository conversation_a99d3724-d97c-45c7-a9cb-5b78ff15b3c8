using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Tenant.Api.Infrastructure.Migrations
{
    public partial class UpdateSupplierV1 : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<sbyte>(
                name: "PriceAdjustmentType",
                table: "Supplier",
                type: "tinyint",
                nullable: true);

            migrationBuilder.AddColumn<decimal>(
                name: "PriceAdjustmentValue",
                table: "Supplier",
                type: "decimal(18,2)",
                nullable: true);

            migrationBuilder.AddColumn<sbyte>(
                name: "PriceBasisType",
                table: "Supplier",
                type: "tinyint",
                nullable: true);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "PriceAdjustmentType",
                table: "Supplier");

            migrationBuilder.DropColumn(
                name: "PriceAdjustmentValue",
                table: "Supplier");

            migrationBuilder.DropColumn(
                name: "PriceBasisType",
                table: "Supplier");
        }
    }
}
