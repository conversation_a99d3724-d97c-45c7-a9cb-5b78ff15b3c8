using EfCoreExtensions.EntityConfigurationsBase;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Tenant.Api.Infrastructure.EntityConfigurations;

public class AgencyAgreementEntityTypeConfiguration : TenantBaseConfiguration<AgencyAgreement>,
        IEntityTypeConfiguration<AgencyAgreement>
{
    public void Configure(EntityTypeBuilder<AgencyAgreement> builder)
    {
        base.ConfigureBase(builder);

        builder.Property(s => s.AgreementType)
            .HasColumnType("tinyint");
        builder.Property(s => s.Enabled)
            .HasColumnType("tinyint(1)");
        builder.Property(s => s.Content)
            .HasColumnType("mediumtext");
        builder.Property(p => p.LoginImagePath)
            .HasColumnType("varchar(2000)");
        builder.Property(p => p.Language)
            .HasColumnType("varchar(32)");
        builder.Property(s => s.CreateTime)
            .HasColumnType("datetime");
        builder.Property(s => s.UpdateTime)
            .HasColumnType("datetime");

        builder.Property(s => s.CheckOtaPriceGroupId)
           .HasColumnType("bigint");

        builder.Property(s => s.NotCheckOtaPriceGroupId)
          .HasColumnType("bigint");

        builder.Property(s => s.Level)
          .HasColumnType("int");

        builder.HasIndex(s => new { s.AgreementType, s.TenantId, s.Language }).IsUnique();
    }
}
