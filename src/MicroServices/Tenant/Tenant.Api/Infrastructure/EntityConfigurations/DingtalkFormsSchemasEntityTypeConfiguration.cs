using EfCoreExtensions.EntityConfigurationsBase;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Tenant.Api.Infrastructure.EntityConfigurations;

public class DingtalkFormsSchemasEntityTypeConfiguration : TenantBaseConfiguration<Model.DingtalkFormsSchemas>, IEntityTypeConfiguration<Model.DingtalkFormsSchemas>
{
    public void Configure(EntityTypeBuilder<Model.DingtalkFormsSchemas> builder)
    {
        base.ConfigureBase(builder);

        builder.Property(s => s.ProcessCode)
           .HasColumnType("varchar(128)")
           .IsRequired();

        builder.Property(s => s.Result)
          .HasColumnType("mediumtext");

        builder.Property(s => s.CreateTime)
              .HasColumnType("datetime");

        builder.Property(s => s.UpdateTime)
              .HasColumnType("datetime");

    }
}
