using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using EfCoreExtensions.EntityConfigurationsBase;

namespace Tenant.Api.Infrastructure.EntityConfigurations
{
    public class B2BPopUpLogEntityTypeConfiguration : TenantBaseConfiguration<Model.B2BPopUpLog>, IEntityTypeConfiguration<Model.B2BPopUpLog>
    {
        public void Configure(EntityTypeBuilder<Model.B2BPopUpLog> builder)
        {
            base.ConfigureBase(builder);

            builder.Property(s => s.B2BPopUpId)
                .HasColumnType("bigint");

            builder.Property(s => s.AgencyId)
                .HasColumnType("bigint");

            builder.Property(s => s.UserId)
                .HasColumnType("bigint");

            builder.Property(s => s.RecordDate)
                .HasColumnType("datetime");

            builder.Property(s => s.CreateTime)
                .HasColumnType("datetime");

            builder.HasIndex(s => s.B2BPopUpId);
        }
    }
}
