using Contracts.Common.Tenant.DTOs.FinancialSetting;
using Microsoft.AspNetCore.Mvc;
using Tenant.Api.Services.Interfaces;

namespace Tenant.Api.Controllers;
[Route("[controller]/[action]")]
[ApiController]
public class FinancialSettingController : ControllerBase
{
    private readonly IFinancialSettingService _financialSettingService;

    public FinancialSettingController(IFinancialSettingService financialSettingService)
    {
        _financialSettingService = financialSettingService;
    }

    /// <summary>
    /// 获取财务设置
    /// </summary>
    /// <returns></returns>
    [HttpGet]
    [ProducesResponseType(typeof(FinancialSettingDto), (int)HttpStatusCode.OK)]
    public async Task<IActionResult> Get()
    {
        var result = await _financialSettingService.Get();
        return Ok(result);
    }

    /// <summary>
    /// 添加或更新财务设置
    /// </summary>
    /// <param name="dto"></param>
    /// <returns></returns>
    [HttpPost]
    [ProducesResponseType((int)HttpStatusCode.OK)]
    public async Task<IActionResult> AddOrUpdate(FinancialSettingDto dto)
    {
        await _financialSettingService.AddOrUpdate(dto);
        return Ok();
    }
}
