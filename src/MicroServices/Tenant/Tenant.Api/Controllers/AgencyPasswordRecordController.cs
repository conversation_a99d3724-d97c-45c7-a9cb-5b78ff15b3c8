using Common.Swagger;
using Contracts.Common.Tenant.DTOs.AgencyPasswordRecords;
using Contracts.Common.User.DTOs.AgencyUser;
using EfCoreExtensions.Abstract;
using Microsoft.AspNetCore.Mvc;
using Tenant.Api.Services.Interfaces;

namespace User.Api.Controllers;


[Route("[controller]/[action]")]
[ApiController]
public class AgencyPasswordRecordController : ControllerBase
{
    private readonly IAgencyPasswordRecordService _agencyPasswordRecordService;

    public AgencyPasswordRecordController(IAgencyPasswordRecordService agencyPasswordRecordService)
    {
        _agencyPasswordRecordService = agencyPasswordRecordService;
    }

    [HttpPost]
    [ProducesResponseType((int)HttpStatusCode.OK)]
    public async Task<IActionResult> Add(AgencyPasswordRecordInput input)
    {
        var entity = new AgencyPasswordRecord(input.AgencyId, input.AgencyUserId);
        await _agencyPasswordRecordService.AddAsync(entity);
        return Ok();
    }

    [HttpPost]
    [ProducesResponseType(typeof(PagingModel<AgencyPasswordRecordDto>), (int)HttpStatusCode.OK)]
    public async Task<IActionResult> Search(AgencyPasswordRecordPageInput input)
    {
        var result = await _agencyPasswordRecordService.Search(input);
        return Ok(result);
    }


}
