using Common.Swagger;
using Contracts.Common.Tenant.DTOs.AgencyLevelConfig;
using Contracts.Common.Tenant.DTOs.AgencyLevelDetail;
using Contracts.Common.Tenant.Messages;
using DotNetCore.CAP;
using Microsoft.AspNetCore.Mvc;
using Tenant.Api.Services;
using Tenant.Api.Services.Interfaces;

namespace Tenant.Api.Controllers;

/// <summary>
/// 分销商等级明细
/// </summary>
[ApiController]
[Route("[controller]/[action]")]
public class AgencyLevelDetailController : ControllerBase
{

    private readonly IAgencyLevelDetailService _agencyLevelDetailService;
    public AgencyLevelDetailController(IAgencyLevelDetailService agencyLevelDetailService)
    {
        _agencyLevelDetailService = agencyLevelDetailService;
    }

    /// <summary>
    /// 调整分销商成长值
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ProducesResponseType(typeof(bool), (int)HttpStatusCode.OK)]
    [SwaggerResponseExt(default, ErrorTypes.Tenant.NoVipAgency,
                                 ErrorTypes.Tenant.AgencyLevelDetailNotExist)]
    public async Task<IActionResult> IncreaseAgencyGrowUpValue(IncreaseAgencyGrowUpValueInput input)
    {
        var result = await _agencyLevelDetailService.IncreaseAgencyGrowUpValue(input);
        return Ok(result);
    }


    #region CapSubscribe

    /// <summary>
    /// 调整分销商成长值
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [NonAction]
    [CapSubscribe(CapTopics.Tenant.IncreaseAgencyGrowUpValue)]
    public async Task<IActionResult> IncreaseAgencyGrowUpValueCap(IncreaseAgencyGrowUpValueInput input)
    {
        var result = await _agencyLevelDetailService.IncreaseAgencyGrowUpValue(input);
        return Ok(result);
    }


    /// <summary>
    /// 分销商等级开启初始化
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [NonAction]
    [CapSubscribe(CapTopics.Tenant.InitAgencyLevel)]
    public async Task CreateAgencyLevelDetailByLevelConfigId(OpenAgencyLevelInput input)
    {
        await _agencyLevelDetailService.InitAgencyLevelDetailByLevelConfigId(input);

    }

    /// <summary>
    /// 等级变化重新核算分销商等级-消费执行
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [NonAction]
    [CapSubscribe(CapTopics.Tenant.ReAgencyLevelCalculate)]
    public async Task ReAgencyLevelCalculate(ReAgencyLevelCalculateTaskInput input)
    {
        await _agencyLevelDetailService.ReAgencyLevelCalculate(input);

    }

    /// <summary>
    /// 周期核算分销商等级-消费执行
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [NonAction]
    [CapSubscribe(CapTopics.Tenant.CycleAgencyLevelCalculate)]
    public async Task CycleAgencyLevelCalculate(ReAgencyLevelCalculateTaskInput input)
    {
        await _agencyLevelDetailService.CycleAgencyLevelCalculate(input);

    }

    /// <summary>
    /// 订单转换分销商成长值-消费执行
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [NonAction]
    [CapSubscribe(CapTopics.Tenant.SyncOrderChangeGrowUpValue)]
    public async Task SyncOrderChangeGrowUpValue(SyncOrderChangeGrowUpValueInput input)
    {
        await _agencyLevelDetailService.SyncOrderChangeGrowUpValue(input);

    }

    /// <summary>
    /// 新增分销商初始化分销商等级明细
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [NonAction]
    [CapSubscribe(CapTopics.Tenant.SyncAddAgencyInitAgencyLevelDetail)]
    public async Task SyncAddAgencyInitAgencyLevelDetail(SyncAgencyDetail input)
    {
        await _agencyLevelDetailService.SyncAddAgencyInitAgencyLevelDetail(input);

    }

    /// <summary>
    /// 认证分销商成功改变分销商等级明细
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [NonAction]
    [CapSubscribe(CapTopics.Tenant.SyncAgencyLevelDetailByCertified)]
    public async Task SyncAgencyLevelDetailByCertified(SyncAgencyDetail input)
    {
        await _agencyLevelDetailService.SyncAgencyLevelDetailByCertified(input);
    }

    /// <summary>
    /// 分销商等级价格分组变化
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [NonAction]
    [CapSubscribe(CapTopics.Tenant.AgencyLevelPriceGroupIdChange)]
    public async Task SyncAgencyLevelPriceGroupIdChange(AgencyLevelConfigMessage input)
    {
        await _agencyLevelDetailService.SyncAgencyLevelPriceGroupIdChange(input);

    }

    #endregion
}
