using Contracts.Common.Tenant.Enums;
using EfCoreExtensions.EntityBase;

namespace Tenant.Api.Model;

public class B2BIndexPageComponent : TenantBase
{
    /// <summary>
    /// 类型
    /// </summary>
    public B2BComponentType B2BComponentType { get; set; }

    /// <summary>
    /// 序号
    /// </summary>
    public int Index { get; set; }

    /// <summary>
    /// 类型
    /// </summary>
    public B2BIndexPageType B2BIndexPageType { get; set; } = B2BIndexPageType.B2B;
}
