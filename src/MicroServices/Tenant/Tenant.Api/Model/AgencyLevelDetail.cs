using EfCoreExtensions.EntityBase;

namespace Tenant.Api.Model;

/// <summary>
/// 分销商等级详情
/// </summary>
public class AgencyLevelDetail : TenantBase
{
    /// <summary>
    /// 分销商id
    /// </summary>
    public long AgencyId { get; set; }

    /// <summary>
    /// 提交时间
    /// </summary>
    public DateTime CreateTime { get; set; } = DateTime.Now;

    /// <summary>
    /// 等级
    /// </summary>
    public int Level { get; set; } = 1;

    /// <summary>
    /// 周期成长值
    /// </summary>
    public int CycleGrowUpValue { get; set; }

    /// <summary>
    /// 修改时间
    /// </summary>
    public DateTime? UpdateTime { get; set; }
}
