using Contracts.Common.Tenant.Enums;
using EfCoreExtensions.EntityBase;

namespace Tenant.Api.Model;

/// <summary>
/// B2B弹窗
/// </summary>
public class B2BPopUp : TenantBase
{
    /// <summary>
    /// 弹窗名称
    /// </summary>
    public string Name { get; set; }

    /// <summary>
    /// 是否启用
    /// </summary>
    public bool Enable { get; set; } = true;

    /// <summary>
    /// 生效时间
    /// </summary>
    public DateTime BeginTime { get; set; }

    /// <summary>
    /// 结束时间
    /// </summary>
    public DateTime EndTime { get; set; }

    /// <summary>
    /// 触达客户
    /// </summary>
    public B2BPopUpAgencyType B2BPopUpAgencyType { get; set; } = B2BPopUpAgencyType.All;

    /// <summary>
    /// 展示次数
    /// </summary>
    public B2BPopUpShowType B2BPopUpShowType { get; set; } = B2BPopUpShowType.DayOnce;

    /// <summary>
    /// 操作人名字
    /// </summary>
    public string OperatorUserName { get; set; }

    /// <summary>
    /// 操作人id
    /// </summary>
    public long OperatorUserId { get; set; }

    public DateTime CreateTime { get; set; } = DateTime.Now;

    public DateTime UpdateTime { get; set; }
}
