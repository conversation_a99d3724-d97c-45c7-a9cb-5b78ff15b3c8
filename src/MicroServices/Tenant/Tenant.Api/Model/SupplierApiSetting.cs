using Contracts.Common.Tenant.Enums;
using EfCoreExtensions.EntityBase;

namespace Tenant.Api.Model;

/// <summary>
/// API供应商配置
/// </summary>
public class SupplierApiSetting : TenantBase
{
    public long SupplierId { get; set; }

    /// <summary>
    /// API业务类型
    /// </summary>
    public SupplierApiParentType SupplierApiParentType { get; set; }

    /// <summary>
    /// API类型
    /// </summary>
    public SupplierApiType SupplierApiType { get; set; }

    /// <summary>
    /// 合作模式
    /// </summary>
    public CooperationModeType CooperationMode { get; set; }


    #region 弃用
    public string? AppKey { get; set; }

    public string? AppSecret { get; set; }

    public string? Email { get; set; }

    #region Globaltix配置/平安希望保配置
    public string? AuthToken { get; set; }

    public string? UserName { get; set; }

    public string? Password { get; set; }

    public string? NotificationSecret { get; set; }
    #endregion

    /// <summary>
    /// 供应商API 配置Json
    /// </summary>
    public string? SettingJson { get; set; } 
    #endregion

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreateTime { get; set; } = DateTime.Now;
    /// <summary>
    /// 更新时间
    /// </summary>
    public DateTime? UpdateTime { get; set; } = DateTime.Now;
}
