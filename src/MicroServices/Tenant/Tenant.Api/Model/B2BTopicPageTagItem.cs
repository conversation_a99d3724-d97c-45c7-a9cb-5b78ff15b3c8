using EfCoreExtensions.EntityBase;

namespace Tenant.Api.Model;

public class B2BTopicPageTagItem : TenantBase
{
    /// <summary>
    /// 专题页Id
    /// </summary>
    public long B2BTopicPageId { get; set; }
    /// <summary>
    /// 标签id
    /// </summary>
    public long B2BTopicPageTagId { get; set; }

    public DateTime CreateTime { get; set; } = DateTime.Now;

    /// <summary>
    /// 语言
    /// </summary>
    public string Language { get; set; } = "zh";
}
