using Contracts.Common.Hotel.Enums;
using Contracts.Common.Tenant.Enums;
using EfCoreExtensions.EntityBase;
using System.ComponentModel.DataAnnotations.Schema;

namespace Tenant.Api.Model;

/// <summary>
/// 管理后台租户 - api供应商配置
/// </summary>
public class TenantApiSupplierConfig : TenantBase
{
    /// <summary>
    /// API供应商类型
    /// </summary>
    public SupplierApiType SupplierApiType { get; set; }

    /// <summary>
    /// 是否可用
    /// </summary>
    public bool Enabled { get; set; } = true;

    /// <summary>
    /// 扩展映射
    /// </summary>
    public ExtendMapping? ExtendMapping { get; set; }

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreateTime { get; set; } = DateTime.Now;

    public DateTime? UpdateTime { get; set; }
}

[NotMapped]
public class ExtendMapping
{
    /// <summary>
    /// 是否全部酒店
    /// </summary>
    public bool IsAllHotel { get; set; }

    /// <summary>
    /// 酒店价格类型
    /// </summary>
    public List<HotelPriceChannelType> HotelPriceChannelTypes { get; set; }
}