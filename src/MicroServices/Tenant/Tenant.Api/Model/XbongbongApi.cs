using EfCoreExtensions.EntityBase;
using System.ComponentModel.DataAnnotations.Schema;

namespace Tenant.Api.Model;

public class XbongbongApi : TenantBase
{
    /// <summary>
    /// 公司Id
    /// </summary>
    public string Corpid { get; set; } = null!;

    /// <summary>
    /// 操作人Id
    /// </summary>
    public string? UserId { get; set; }

    public string ApiToken { get; set; } = null!;

    public string WebHookToken { get; set; } = null!;

    /// <summary>
    /// 是否可用
    /// </summary>
    public bool Enabled { get; set; } = true;

    public FromMapping? FromMapping { get; set; }

    public DateTime CreateTime { get; set; } = DateTime.Now;

    public DateTime? UpdateTime { get; set; } = DateTime.Now;
}

[NotMapped]
public class FromMapping
{
    public string FullName { get; set; } = null!;
    public string ShortName { get; set; } = null!;
    public string Contact { get; set; } = null!;
    public string ContactNumber { get; set; } = null!;
}
