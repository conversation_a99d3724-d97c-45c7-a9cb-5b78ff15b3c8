using Contracts.Common.Resource.Enums;
using Contracts.Common.Tenant.Enums;
using EfCoreExtensions.EntityBase;
using NetTopologySuite.Geometries;

namespace Tenant.Api.Model;

public class B2BHotHotel : TenantBase
{
    /// <summary>
    /// 酒店id
    /// </summary>
    public long? HotelId { get; set; }

    /// <summary>
    /// 资源酒店id
    /// </summary>
    public long ResourceHotelId { get; set; }
    /// <summary>
    /// HopId
    /// </summary>
    public long HopId { get; set; }

    /// <summary>
    /// 热门酒店类型
    /// </summary>
    public B2BIndexPageHotHotelType B2BHotHotelType { get; set; }

    /// <summary>
    /// 图片
    /// </summary>
    public string? Path { get; set; }

    public string ZHName { get; set; }
    public string ENName { get; set; }

    /// <summary>
    /// 国家代码
    /// </summary>
    public int CountryCode { get; set; }
    /// <summary>
    /// 国家名
    /// </summary>
    public string CountryName { get; set; }

    /// <summary>
    /// 国家英文名
    /// </summary>
    public string? EnCountryName { get; set; }

    /// <summary>
    /// 城市代码
    /// </summary>
    public int CityCode { get; set; }

    /// <summary>
    /// 城市
    /// </summary>
    public string CityName { get; set; }

    /// <summary>
    /// 城市英文名
    /// </summary>
    public string? EnCityName { get; set; }

    /// <summary>
    /// 排序
    /// </summary>
    public int Sort { get; set; }

    /// <summary>
    /// 经纬度坐标类型
    /// </summary>
    public CoordinateType? CoordinateType { get; set; }

    /// <summary>
    /// 经纬度坐标
    /// </summary>
    public Point? Location { get; private set; }

    /// <summary>
    /// 星级
    /// </summary>
    public decimal StarLevel { get; set; }

    public DateTime CreateTime { get; set; } = DateTime.Now;
}
