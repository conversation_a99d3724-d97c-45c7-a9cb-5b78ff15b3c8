using AutoMapper;
using Contracts.Common.Tenant.DTOs.B2BWebConfiguration;
using Contracts.Common.Tenant.Enums;
using Microsoft.EntityFrameworkCore;
using Tenant.Api.Services.Interfaces;

namespace Tenant.Api.Services;

public class B2BWebConfigurationService : IB2BWebConfigurationService
{
    //define defalut
    private static readonly List<B2BMenuConfigDto> _menuConfigs = Enum
        .GetValues<B2BMenuCategory>()
        .Select(x => new B2BMenuConfigDto()
        {
            Menu = x,
            IsDisplayed = true
        })
        .ToList();
    private static readonly List<B2BLanguageConfigDto> _languageConfig = new()
    {
        new() { Language = "zh", Enabled = true, IsDefalut = true },
        new() { Language = "en", Enabled = true, IsDefalut = false }
    };

    private readonly CustomDbContext _dbContext;
    private readonly IMapper _mapper;

    public B2BWebConfigurationService(
        CustomDbContext dbContext,
        IMapper mapper)
    {
        _dbContext = dbContext;
        _mapper = mapper;
    }

    public async Task<B2BWebConfigurationDto> Get()
    {
        var config = await _dbContext.B2BWebConfigurations
            .FirstOrDefaultAsync();
        B2BWebConfigurationDto dto = new();
        if (config != null)
        {
            dto.B2BLogoConfigs = config.B2BLogoConfigs.Select(x => new B2BLogoConfigDto
            {
                TopLogo = x.TopLogo,
                BottomLogo = x.BottomLogo,
                Language = x.Language,
                WebLogo = x.WebLogo,
            });
            dto.B2BTitleConfigs = config.B2BTitleConfigs.Select(x => new B2BTitleConfigDto
            {
                Language = x.Language,
                BrandName = x.BrandName,
                Solgon = x.Solgon,
                WebTitle = x.WebTitle,
            });
        };
        dto.B2BMenuConfigs = config?.B2BMenuConfigs?.Any() is not true
            ? _menuConfigs
            : _mapper.Map<List<B2BMenuConfigDto>>(config!.B2BMenuConfigs);
        dto.B2bLanguageConfigs = config?.B2BLanguageConfigs?.Any() is not true
            ? _languageConfig
            : _mapper.Map<List<B2BLanguageConfigDto>>(config!.B2BLanguageConfigs);
        return dto;
    }

    public async Task Save(B2BWebConfigurationSaveInput input)
    {
        var config = await _dbContext.B2BWebConfigurations
            .FirstOrDefaultAsync();
        var isInnitConfig = config is null;
        if (isInnitConfig)
            config = new B2BWebConfiguration();

        if (input.B2BLogoConfigs is not null)
        {
            config.B2BLogoConfigs.RemoveAll(x => x.Language == input.B2BLogoConfigs.Language);
            config.B2BLogoConfigs.Add(_mapper.Map<B2BLogoConfig>(input.B2BLogoConfigs));
        }
        if (input.B2BTitleConfigs is not null)
        {
            config.B2BTitleConfigs.RemoveAll(x => x.Language == input.B2BTitleConfigs.Language);
            config.B2BTitleConfigs.Add(_mapper.Map<B2BTitleConfig>(input.B2BTitleConfigs));
        }
        if (input.B2BMenuConfigs is not null)
        {
            config.B2BMenuConfigs = _mapper.Map<List<B2BMenuConfig>>(input.B2BMenuConfigs);
        }
        if (input.B2bLanguageConfigs is not null)
        {
            config.B2BLanguageConfigs = _mapper.Map<List<B2BLanguageConfig>>(input.B2bLanguageConfigs);
        }
        if (isInnitConfig)
        {
            await _dbContext.B2BWebConfigurations.AddAsync(config!);
        }
        await _dbContext.SaveChangesAsync();
    }

}
