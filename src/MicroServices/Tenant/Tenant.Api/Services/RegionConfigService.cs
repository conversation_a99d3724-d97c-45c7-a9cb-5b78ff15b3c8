using AutoMapper;
using Common.GlobalException;
using Contracts.Common.Payment.DTOs;
using Contracts.Common.Tenant.DTOs.AgencyBankCard;
using Contracts.Common.Tenant.DTOs.RegionConfig;
using Contracts.Common.Tenant.DTOs.Supplier;
using EfCoreExtensions.Abstract;
using EfCoreExtensions.Extensions;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Internal;
using System.Collections.Generic;
using System.Linq;
using System.Text.RegularExpressions;
using Tenant.Api.Model;
using Tenant.Api.Services.Interfaces;

namespace Tenant.Api.Services;

public class RegionConfigService : IRegionConfigService
{
    private readonly IMapper _mapper;
    private readonly CustomDbContext _dbContext;
    public RegionConfigService(
        IMapper mapper,
        CustomDbContext dbContext)
    {
        _mapper = mapper;
        _dbContext = dbContext;
    }

    /// <summary>
    /// 保存区域数据(添加或修改)
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    public async Task Save(List<SaveRegionConfigInput> input)
    {
        if (!Validate(input))
            return;
        var sort = 0;

        var removeObj = await _dbContext.RegionConfigs.ToListAsync();
        foreach (var item in input)
        {
            int citySort = 0, userSort = 0;
            var regionConfig = item.Id.HasValue ? null : await _dbContext.RegionConfigs.FirstOrDefaultAsync(x => x.Id == item.Id);
            if (regionConfig is null)
            {
                regionConfig = new RegionConfig();
                var id = regionConfig.Id;
                _mapper.Map(item, regionConfig);
                regionConfig.Id = id;
                regionConfig.Sort = sort;
                regionConfig.Type = item.Type;

                await _dbContext.RegionConfigs.AddAsync(regionConfig);
            }
            else
            {
                regionConfig.Sort = sort;
                regionConfig.Name = item.Name;
                regionConfig.UpdateTime = DateTime.Now;

                var regionConfigCities = await _dbContext.RegionConfigScopes
                    .Where(x => x.RegionConfigId == item.Id)
                    .ToListAsync();
                if (regionConfigCities.Any())
                    _dbContext.RegionConfigScopes.RemoveRange(regionConfigCities);

                var regionConfigUsers = await _dbContext.RegionConfigUsers
                    .Where(x => x.RegionConfigId == item.Id)
                    .ToListAsync();
                if (regionConfigUsers.Any())
                    _dbContext.RegionConfigUsers.RemoveRange(regionConfigUsers);

                removeObj.Remove(regionConfig);
            }

            var cities = _mapper.Map<List<RegionConfigScope>>(item.Provincies);
            cities.ForEach(city => {
                city.RegionConfigId = regionConfig.Id;
                city.Sort = citySort++;
            });
            var users = _mapper.Map<List<RegionConfigUser>>(item.Users);
            users.ForEach(user => {
                user.RegionConfigId = regionConfig.Id;
                user.Sort = userSort++; 
            });

            await _dbContext.RegionConfigScopes.AddRangeAsync(cities);
            await _dbContext.RegionConfigUsers.AddRangeAsync(users);

            sort++;
        }

        if (removeObj.Count > 0)
        {
            var regionConfigIds = removeObj.Select(x => x.Id).ToList();
            _dbContext.RegionConfigs.RemoveRange(removeObj);
            var citys = _dbContext.RegionConfigScopes.Where(x => regionConfigIds.Contains(x.RegionConfigId));
            _dbContext.RegionConfigScopes.RemoveRange(citys);
            var users = _dbContext.RegionConfigUsers.Where(x => regionConfigIds.Contains(x.RegionConfigId));
            _dbContext.RegionConfigUsers.RemoveRange(users);
        }

        var result = await _dbContext.SaveChangesAsync();
    }

    private bool Validate(List<SaveRegionConfigInput> input)
    {
        var regionConfigSaveCheckResult = input.Where(x => x.Users.Count() <= 0 || 
                        (x.Type != Contracts.Common.Tenant.Enums.RegionConfigType.Other && x.Provincies.Count <= 0)).Any();
        if(regionConfigSaveCheckResult)
            throw new BusinessException(ErrorTypes.Tenant.RegionConfigSaveCheck);

        var provinciesExist = input.SelectMany(x => x.Provincies)
            .GroupBy(x => new { x.CountryCode,x.ProvinceCode })
            .Where(x => x.Count() > 1)
            .Any();

        var countryExist = input.SelectMany(x => x.Provincies)
            .GroupBy(x => x.CountryCode)
            .Where(x => x.Where(x => !x.ProvinceCode.HasValue).Any() && x.Count() > 1)
            .Any();

        if (provinciesExist || countryExist)
            throw new BusinessException(ErrorTypes.Tenant.RegionConfigProvinciesExist);

        return true;
    }

    /// <summary>
    /// 获取所有区域数据
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    public async Task<List<GetRegionConfigOutput>> GetAll()
    {
        var result = new List<GetRegionConfigOutput>();

        var query = _dbContext.RegionConfigs.AsNoTracking()
            .OrderBy(x => x.Sort);
        var regionConfigs = await query.ToListAsync();
        if (regionConfigs is null || regionConfigs.Count <= 0)
            return result;
        var regionConfigIds = regionConfigs.Select(o => o.Id);

        var regionConfigCities = await _dbContext.RegionConfigScopes.AsNoTracking()
            .Where(x => regionConfigIds.Contains(x.RegionConfigId))
            .OrderByDescending(x => x.Sort)
            .ToListAsync();

        var regionConfigUsers = await _dbContext.RegionConfigUsers.AsNoTracking()
            .Where(x => regionConfigIds.Contains(x.RegionConfigId))
            .OrderByDescending(x => x.Sort)
            .ToListAsync();

        regionConfigs.ForEach(regionConfig =>
        {
            var output = _mapper.Map<GetRegionConfigOutput>(regionConfig);
            output.Users = _mapper.Map<List<RegionConfigUserInfo>>(regionConfigUsers.Where(x => x.RegionConfigId == regionConfig.Id));
            output.CountryProvincies = _mapper.Map<List<RegionConfigScopeInfo>>(regionConfigCities.Where(x => x.RegionConfigId == regionConfig.Id));
            result.Add(output);
        });

        return result;
    }

    /// <summary>
    /// 查询区域配置的用户信息
    /// </summary>
    /// <param name="agencyId"></param>
    /// <returns></returns>
    public async Task<PagingModel<SearchRegionConfigUserOutput>> SearchUserByScope(SearchRegionConfigUserInput input)
    {
        var result = await _dbContext.RegionConfigUsers.AsNoTracking()
            .Join(_dbContext.RegionConfigScopes.AsNoTracking(), u => u.RegionConfigId, c => c.RegionConfigId, (u, c) => new { u, c })
            .Where(x => x.c.CountryCode == input.CountryCode && x.c.ProvinceCode == input.ProvinceCode ||
                (x.c.CountryCode == input.CountryCode && x.c.ProvinceCode == null))
            .OrderByDescending(x => x.u.Sort)
            .PagingAsync(input.PageIndex, input.PageSize, x => new SearchRegionConfigUserOutput
            { 
                UserId = x.u.UserId,
                Sort = x.u.Sort,
            });

        //如果没匹配到 返回其他类型用户
        if (result.Total <= 0)
        {
            result = await _dbContext.RegionConfigUsers.AsNoTracking()
            .Join(_dbContext.RegionConfigs.AsNoTracking(), u => u.RegionConfigId, c => c.Id, (u, c) => new { u, c })
            .Where(x => x.c.Type == Contracts.Common.Tenant.Enums.RegionConfigType.Other)
            .OrderByDescending(x => x.u.Sort)
            .PagingAsync(input.PageIndex, input.PageSize, x => new SearchRegionConfigUserOutput
            {
                UserId = x.u.UserId,
                Sort = x.u.Sort,
            });
        }

        return result;
    }

    public async Task<List<SelectionRegionConfigOutput>> GetSelection()
    {
        var result = await _dbContext.RegionConfigs.AsNoTracking().OrderBy(x => x.Sort).ToListAsync();

        return _mapper.Map<List<SelectionRegionConfigOutput>>(result);
    }
}