using Cit.OpenAPI.Weiling.Cache;
using Cit.Storage.Redis;

namespace Tenant.Api.Services;

public class WeilingRedisCache : IDistributedCache
{
    private static readonly string _cacheKeyPrefix = "sdkdata:";

    private readonly IRedisClient _redisClient;

    public WeilingRedisCache(IRedisClient redisClient)
    {
        _redisClient = redisClient;
    }

    public bool Set<T>(string key, T value, TimeSpan expiry)
    {
        return _redisClient.StringSet(_cacheKeyPrefix + key, value, expiry);
    }

    public bool TryGetValue<T>(string key, out T value)
    {
        var result = false;
        value = default;
        try
        {
            value = _redisClient.StringGet<T>(_cacheKeyPrefix + key);
            if (value is not null)
                result = true;
        }
        catch { }
        return result;
    }
}
