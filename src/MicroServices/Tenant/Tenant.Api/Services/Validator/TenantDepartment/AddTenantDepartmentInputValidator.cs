using Contracts.Common.Tenant.DTOs.Tenant;
using Contracts.Common.Tenant.DTOs.TenantDepartment;
using FluentValidation;

namespace Tenant.Api.Services.Validator.TenantDepartment;

public class AddTenantDepartmentInputValidator : AbstractValidator<AddTenantDepartmentInput>
{
    public AddTenantDepartmentInputValidator()
    {
        RuleFor(x => x.Name).NotNull().Length(1, 10);

        RuleFor(x => x.Remark).Length(0, 100);
    }
}
