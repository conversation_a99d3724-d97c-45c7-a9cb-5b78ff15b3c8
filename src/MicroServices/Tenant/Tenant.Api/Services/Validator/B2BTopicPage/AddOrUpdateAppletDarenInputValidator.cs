using Contracts.Common.Tenant.DTOs.B2BTopicPage;
using FluentValidation;

namespace Tenant.Api.Services.Validator.B2BTopicPage;

public class AddOrUpdateAppletDarenInputValidator : AbstractValidator<AddOrUpdateAppletDarenInput>
{
    public AddOrUpdateAppletDarenInputValidator()
    {

        RuleFor(x => x.Title).NotEmpty().Length(1, 30);

        RuleFor(x => x.AppletDarenDescription).Length(1, 30).When(x => !string.IsNullOrEmpty(x.Description));

        RuleFor(x => x.Language).NotEmpty();

       // RuleFor(x => x.SortIndex).NotEmpty();

        RuleFor(x => x.Description).Length(1, 200).When(x => !string.IsNullOrEmpty(x.Description));

    }
}
