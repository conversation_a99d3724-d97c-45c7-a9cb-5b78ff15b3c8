using Contracts.Common.Tenant.DTOs.DingtalkApi;
using Contracts.Common.Tenant.Messages;
using Contracts.Common.User.Messages;

namespace Tenant.Api.Services.Interfaces;

public interface IDingtalkApiService
{
    /// <summary>
    /// 获取钉钉用户可见表单列表
    /// </summary>
    /// <returns></returns>
    Task<List<WorkflowFormsOuput>> WorkflowForms(WorkflowFormsInput input);

    /// <summary>
    /// 获取钉钉用户可见表单详情
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<GetWorkflowFormInfoOuput> GetWorkflowFormInfo(GetWorkflowFormInfoInput input);


    Task<GetWorkflowFormInfoOuput> GetWorkflowFormInfoByProcessCode(string processCode);


    Task WorkflowFormInfoChange(WorkflowFormInfoChangeInput input);

    /// <summary>
    /// 用户详情
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<List<GetUserOutput>> GetUser(GetUserInput input);

    Task CreateProcessInstances(DingtalkApplySubmitMessage input);

    Task DepartmentList(SyncDingtalkDepartmentMessage input);

    Task CallbackProcessInstances(DingtalkApplyCallBackMessage input);
}
