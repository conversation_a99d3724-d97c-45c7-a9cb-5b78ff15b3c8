using Contracts.Common.Payment.DTOs;
using Contracts.Common.Tenant.DTOs.AgencyBankCard;
using Contracts.Common.Tenant.DTOs.RegionConfig;
using EfCoreExtensions.Abstract;

namespace Tenant.Api.Services.Interfaces;

public interface IRegionConfigService
{
    /// <summary>
    /// 保存区域配置
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task Save(List<SaveRegionConfigInput> input);

    /// <summary>
    /// 获取所有区域配置数据
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<List<GetRegionConfigOutput>> GetAll();

    /// <summary>
    /// 查询区域的用户信息
    /// </summary>
    /// <param name="agencyId"></param>
    /// <returns></returns>
    Task<PagingModel<SearchRegionConfigUserOutput>> SearchUserByScope(SearchRegionConfigUserInput input);


    /// <summary>
    /// 区域下拉框
    /// </summary>
    /// <returns></returns>
    Task<List<SelectionRegionConfigOutput>> GetSelection();
}