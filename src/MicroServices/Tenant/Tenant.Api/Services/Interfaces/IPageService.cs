using Contracts.Common.Tenant.DTOs.Page;

namespace Tenant.Api.Services.Interfaces;

public interface IPageService
{
    /// <summary>
    /// 搜索
    /// </summary>
    Task<List<SearchOutput>> Search(SearchInput input);

    /// <summary>
    /// 详情
    /// </summary>
    Task<DetailOutput> Detail(DetailInput input);

    /// <summary>
    /// 删除
    /// </summary>
    Task Delete(DetailInput input);

    /// <summary>
    /// 设置首页
    /// </summary>
    Task SetHomePage(long id);

    /// <summary>
    /// 新增
    /// </summary>
    Task<long> Add(AddInput input);

    /// <summary>
    /// 修改
    /// </summary>
    Task Edit(EditInput input);

    /// <summary>
    /// 发布
    /// </summary>
    Task Publish(PublishInput input,long tenantId);
}