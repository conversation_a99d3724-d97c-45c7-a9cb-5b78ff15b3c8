using AutoMapper;
using Contracts.Common.Tenant.DTOs.AgencyLevelConfig;

namespace Tenant.Api.Services.MappingProfiles;

public class AgencyLevelConfigOvertimeHotelProfiles : Profile
{
    public AgencyLevelConfigOvertimeHotelProfiles()
    {
        CreateMap<AgencyLevelConfigOvertimeHotelDto, AgencyLevelConfigOvertimeHotel>()
          .ForMember(x => x.Id, x => x.Ignore());
        CreateMap<AgencyLevelConfigOvertimeHotel, AgencyLevelConfigOvertimeHotelDto>();
    }
}
