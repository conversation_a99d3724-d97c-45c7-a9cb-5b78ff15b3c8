using AutoMapper;
using Contracts.Common.Tenant.DTOs.AgencyLevelConfig;
using Contracts.Common.Tenant.DTOs.AgencyLevelConfigHotel;

namespace Tenant.Api.Services.MappingProfiles;

public class AgencyLevelConfigHotelProfiles : Profile
{
    public AgencyLevelConfigHotelProfiles()
    {
        CreateMap<AgencyLevelConfigHotelDto, AgencyLevelConfigHotel>()
            .ForMember(x => x.Id, x => x.Ignore());
        CreateMap<AgencyLevelConfigHotel,AgencyLevelConfigHotelDto>();

    }

}
