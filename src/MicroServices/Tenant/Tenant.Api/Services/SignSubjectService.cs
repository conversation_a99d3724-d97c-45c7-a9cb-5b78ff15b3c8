using AutoMapper;
using Common.GlobalException;
using Contracts.Common.Tenant.DTOs.SignSubject;
using EfCoreExtensions.Abstract;
using EfCoreExtensions.Extensions;
using Microsoft.EntityFrameworkCore;
using Tenant.Api.Services.Interfaces;

namespace Tenant.Api.Services;

public class SignSubjectService : ISignSubjectService
{
    private readonly CustomDbContext _dbContext;
    private readonly IMapper _mapper;

    public SignSubjectService(CustomDbContext dbContext, IMapper mapper)
    {
        _dbContext = dbContext;
        _mapper = mapper;
    }

    public async Task<GetSignSubjectOutput> Detail(GetSignSubjectInput input)
    {
        var signSubject = await _dbContext.SignSubjects.Where(s => s.Id == input.Id)
            .Select(s => new GetSignSubjectOutput
            {
                Id = s.Id,
                Name = s.Name,
                CurrencyCode = s.CurrencyCode,
                CountryCode = s.CountryCode,
                CountryName = s.CountryName,
                ProvinceCode = s.ProvinceCode,
                ProvinceName = s.ProvinceName,
                CityCode = s.CityCode,
                CityName = s.CityName,
            })
            .FirstOrDefaultAsync();
        return signSubject;
    }

    public async Task<long> Add(SignSubjectDto dto)
    {
        var signSubject = _mapper.Map<SignSubject>(dto);
        if (await _dbContext.SignSubjects.AnyAsync(x => x.Name.Equals(dto.Name)))
            throw new BusinessException(ErrorTypes.Tenant.SignSubjectNameExist);

        await _dbContext.AddAsync(signSubject);
        await _dbContext.SaveChangesAsync();
        return signSubject.Id;
    }

    public async Task Update(SignSubjectUpdateInput input)
    {
        var signSubject = await _dbContext.SignSubjects.Where(s => s.Id == input.Id).FirstOrDefaultAsync();
        signSubject.Name = input.Name;
        signSubject.CurrencyCode = input.CurrencyCode;
        signSubject.CountryCode = input.CountryCode;
        signSubject.CountryName = input.CountryName;
        signSubject.ProvinceCode = input.ProvinceCode;
        signSubject.ProvinceName = input.ProvinceName;
        signSubject.CityCode = input.CityCode;
        signSubject.CityName = input.CityName;
        await _dbContext.SaveChangesAsync();
    }

    public async Task<PagingModel<GetSignSubjectOutput>> Search(SignSubjectSearchInput input)
    {
        var signSubjects = await _dbContext.SignSubjects
            .WhereIF(!string.IsNullOrWhiteSpace(input.Name), s => s.Name.Contains(input.Name!))
            .WhereIF(!string.IsNullOrWhiteSpace(input.CurrencyCode), s => s.CurrencyCode == input.CurrencyCode)
            .WhereIF(input.CountryCode.HasValue, s => s.CountryCode == input.CountryCode)
            .WhereIF(input.ProvinceCode.HasValue, s => s.ProvinceCode == input.ProvinceCode)
            .WhereIF(input.CityCode.HasValue, s => s.CityCode == input.CityCode)
            .Select(s => new GetSignSubjectOutput
            {
                Id = s.Id,
                Name = s.Name,
                CurrencyCode = s.CurrencyCode,
                CountryCode = s.CountryCode,
                CountryName = s.CountryName,
                ProvinceCode = s.ProvinceCode,
                ProvinceName = s.ProvinceName,
                CityCode = s.CityCode,
                CityName = s.CityName,
            })
            .OrderByDescending(s => s.Id)
            .PagingAsync(input.PageIndex, input.PageSize);
        return signSubjects;
    }

    public async Task<List<GetSignSubjectOutput>> GetList()
    {
        var signSubjects = await _dbContext.SignSubjects.OrderByDescending(s => s.Id).ToListAsync();
        var result = _mapper.Map<List<GetSignSubjectOutput>>(signSubjects);
        return result;
    }

    public async Task Delete(long id)
    {
        var signSubject = await _dbContext.SignSubjects.FindAsync(id);
        if (signSubject is null) return;

        _dbContext.Remove(signSubject);
        await _dbContext.SaveChangesAsync();
    }
}
