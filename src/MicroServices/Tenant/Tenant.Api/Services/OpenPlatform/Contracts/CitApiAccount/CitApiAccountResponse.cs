namespace Tenant.Api.Services.OpenPlatform.Contracts.CitApiAccount;

public class CitApiAccountResponse
{
    
}

#region api账号信息查询

/// <summary>
/// api账号信息查询响应Data
/// </summary>
public class CitApiAccountGetInfoResponse
{
    /// <summary>
    /// API名称
    /// klook , globaltix ,treep ,ca, experienceoz
    /// </summary>
    public string ApiName { get; set; }

    /// <summary>
    /// api类型
    /// 供应：supplier , 渠道：channel
    /// </summary>
    public string ApiType { get; set; }

    /// <summary>
    /// 配置json字符串
    /// </summary>
    public string ConfigJson { get; set; }
}

#endregion

#region api账号开通

/// <summary>
/// api账号开通响应Data
/// </summary>
public class CitApiAccountCreateResponse
{
    
}
#endregion

#region api账号更新

/// <summary>
/// api账号更新响应Data
/// </summary>
public class CitApiAccountUpdateResponse
{
    
}

#endregion
