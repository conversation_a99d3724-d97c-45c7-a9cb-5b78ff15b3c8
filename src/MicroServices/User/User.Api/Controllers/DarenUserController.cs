using Common.Jwt;
using Common.Swagger;
using Contracts.Common.User.DTOs.DarenUser;
using Contracts.Common.User.Messages;
using DotNetCore.CAP;
using EfCoreExtensions.Abstract;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using User.Api.Services.Interfaces;

namespace User.Api.Controllers
{
    [Route("[controller]/[action]")]
    [ApiController]
    public class DarenUserController : ControllerBase
    {
        private readonly IDarenUserService _darenUserService;
        private readonly IHttpContextAccessor _httpContextAccessor;
        public DarenUserController(
            IDarenUserService darenUserService, 
            IHttpContextAccessor httpContextAccessor)
        {
            _darenUserService = darenUserService;
            _httpContextAccessor = httpContextAccessor;
        }

        /// <summary>
        /// 新增达人
        /// </summary>
        [HttpPost]
        public async Task<IActionResult> Add(AddInput input)
        {
            AddDarenUserInput addDarenUserInput = new()
            {
                SuperiorUserId = input.SuperiorUserId,
                TenantId = HttpContext.GetTenantId(),
                UserId = HttpContext.GetCurrentUser().userid
            };
            await _darenUserService.Add(addDarenUserInput);
            return Ok();
        }

        [HttpPost]
        public async Task<IActionResult> Install(AddDarenUserInput input)
        {
            await _darenUserService.Add(input);
            return Ok();
        }

        /// <summary>
        /// 搜索达人列表
        /// </summary>
        [HttpPost]
        [ProducesResponseType(typeof(PagingModel<SearchDarenUserOutput>), (int)HttpStatusCode.OK)]
        public async Task<IActionResult> Search(SearchDarenUserInput input) 
        {
            var result = await _darenUserService.Search(input);
            return Ok(result);
        }

        /// <summary>
        /// 取消达人
        /// </summary>
        [HttpPost]
        public async Task<IActionResult> Cancel(long userId)
        {
            await _darenUserService.Cancel(userId);
            return Ok();
        }

        /// <summary>
        /// 获取下线
        /// </summary>
        [HttpPost]
        [ProducesResponseType(typeof(PagingModel<GetUnderUsersOutput>), (int)HttpStatusCode.OK)]
        public async Task<IActionResult> GetUnderUsers(PagingInput input)
        {
            var user = _httpContextAccessor.HttpContext.GetCurrentUser();
            var result = await _darenUserService.GetUnderUsers(input, user.userid);
            return Ok(result);
        }

        /// <summary>
        /// 是否为达人
        /// </summary>
        [HttpGet]
        [ProducesResponseType(typeof(bool), (int)HttpStatusCode.OK)]
        public async Task<IActionResult> IsDaren()
        {
            var user = _httpContextAccessor.HttpContext.GetCurrentUser();
            var darenUser = (await _darenUserService.Get(user.userid)).FirstOrDefault();
            var result = darenUser is not null;
            return Ok(result);
        }

        [HttpPost]
        [ProducesResponseType(typeof(List<GetDarenOutput>), (int)HttpStatusCode.OK)]
        public async Task<IActionResult> GetDaren(params long[] userIds)
        {
            var result = await _darenUserService.Get(userIds);
            return Ok(result);
        }

        /// <summary>
        /// 达人中心 组员、客户数统计数据
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [ProducesResponseType(typeof(DarenUnderlingSummaryOutput), (int)HttpStatusCode.OK)]
        public async Task<IActionResult> Summary()
        {
            var userId = _httpContextAccessor.HttpContext.GetCurrentUser().userid;
            var result = await _darenUserService.Summary(userId);
            return Ok(result);
        }

        /// <summary>
        /// 组员列表
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost]
        [ProducesResponseType(typeof(PagingModel<GetDevelopUserOutput>), (int)HttpStatusCode.OK)]
        public async Task<IActionResult> GetDevelopUsers(GetDevelopUserInput input)
        {
            var userId = _httpContextAccessor.HttpContext.GetCurrentUser().userid;
            var result = await _darenUserService.GetDevelopUsers(input, userId);
            return Ok(result);
        }

        #region CapSubscribe

        [NonAction]
        [CapSubscribe(CapTopics.User.BindUnderling)]
        public async Task BindUnderling(BindUnderlingMessage receive)
        {
            await _darenUserService.BindUnderling(receive);
        }

        #endregion
    }
}
