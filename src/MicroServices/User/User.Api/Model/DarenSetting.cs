using EfCoreExtensions.EntityBase;

namespace User.Api.Model
{
    public class DarenSetting : TenantBase
    {
        /// <summary>
        /// 是否公开展示
        /// </summary>
        public bool IsPublicDisplay { get; set; }

        /// <summary>
        /// 绑定规则 DarenBindRule
        /// </summary>
        public int BindRule { get; set; }

        /// <summary>
        /// 介绍
        /// </summary>
        public string? Intro { get; set; }

        public DateTime CreateTime { get; set; } = DateTime.Now;

        /// <summary>
        /// 达人招募组员 是否开启
        /// </summary>
        public bool IsDarenDevelopOpen { get; set; }
    }
}
