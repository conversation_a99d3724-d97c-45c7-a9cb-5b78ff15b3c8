using EfCoreExtensions.EntityBase;

namespace User.Api.Model
{
    /// <summary>
    /// 达人银行卡信息
    /// </summary>
    public class DarenUserBankCard : TenantBase
    {
        public long UserId { get; set; }

        /// <summary>
        /// 银行编号(易宝)
        /// </summary>
        public string BankCode { get; set; }

        /// <summary>
        /// 银行名称
        /// </summary>
        public string BankName { get; set; }

        /// <summary>
        /// 银行卡号
        /// </summary>
        public string AccountNumber { get; set; }

        /// <summary>
        /// 开户名
        /// </summary>
        public string AccountName { get; set; }

        public DateTime CreateTime { get; set; } = DateTime.Now;
    }
}
