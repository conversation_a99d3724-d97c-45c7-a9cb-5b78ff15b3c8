using EfCoreExtensions.EntityBase;

namespace User.Api.Model
{
    public class CustomerUserVip : TenantBase
    {
        public long CustomerUserId { get; set; }

        /// <summary>
        /// 会员等级Id
        /// </summary>
        public long VipLevelId { get; set; }

        /// <summary>
        /// 订单Id
        /// </summary>
        public long BaseOrderId { get; set; }

        /// <summary>
        /// 到期时间
        /// </summary>
        public DateTime ExpireDate { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreateTime { get; set; } = DateTime.Now;
    }
}
