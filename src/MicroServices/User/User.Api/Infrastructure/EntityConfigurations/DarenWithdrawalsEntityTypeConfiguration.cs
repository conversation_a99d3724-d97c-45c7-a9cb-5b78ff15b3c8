using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using EfCoreExtensions.EntityConfigurationsBase;

namespace User.Api.Infrastructure.EntityConfigurations
{
    public class DarenWithdrawalsEntityTypeConfiguration : TenantBaseConfiguration<Model.DarenWithdrawals>, IEntityTypeConfiguration<Model.DarenWithdrawals>
    {
        public void Configure(EntityTypeBuilder<Model.DarenWithdrawals> builder)
        {
            base.ConfigureBase(builder);

            builder.Property(s => s.UserId)
                .HasColumnType("bigint");
            
            builder.Property(s => s.PhoneNumber)
                .HasColumnType("varchar(50)")
                .IsRequired();

            builder.Property(s => s.Amount)
                .HasColumnType("decimal(18,4)");
            
            builder.Property(s => s.BankFees)
                .HasColumnType("decimal(18,4)");
            
            builder.Property(s => s.BankCode)
                .HasColumnType("varchar(50)")
                .IsRequired();

            builder.Property(s => s.BankName)
                .HasColumnType("varchar(50)")
                .IsRequired();

            builder.Property(s => s.AccountNumber)
                .HasColumnType("varchar(50)")
                .IsRequired();

            builder.Property(s => s.AccountName)
                .HasColumnType("varchar(20)")
                .IsRequired();

            builder.Property(s => s.Status)
                .HasColumnType("tinyint")
                .IsConcurrencyToken();
            
            builder.Property(s => s.FailureReason)
                .HasColumnType("varchar(100)");
            
            builder.Property(s => s.CreateTime)
                .HasColumnType("datetime");

            builder.HasIndex(s => new { s.TenantId, s.UserId });
            builder.HasIndex(s => new { s.TenantId, s.PhoneNumber });
        }
    }
}
