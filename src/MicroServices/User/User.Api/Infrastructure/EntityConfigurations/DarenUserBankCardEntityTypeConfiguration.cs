using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using EfCoreExtensions.EntityConfigurationsBase;

namespace User.Api.Infrastructure.EntityConfigurations
{
    public class DarenUserBankCardEntityTypeConfiguration : TenantBaseConfiguration<Model.DarenUserBankCard>, IEntityTypeConfiguration<Model.DarenUserBankCard>
    {
        public void Configure(EntityTypeBuilder<Model.DarenUserBankCard> builder)
        {
            base.ConfigureBase(builder);

            builder.Property(s => s.UserId)
                .HasColumnType("bigint");
            
            builder.Property(s => s.BankCode)
                .HasColumnType("varchar(50)")
                .IsRequired();

            builder.Property(s => s.BankName)
                .HasColumnType("varchar(50)")
                .IsRequired();

            builder.Property(s => s.AccountNumber)
                .HasColumnType("varchar(50)")
                .IsRequired();

            builder.Property(s => s.AccountName)
                .HasColumnType("varchar(20)")
                .IsRequired();

            builder.Property(s => s.CreateTime)
                .HasColumnType("datetime");
                    }
    }
}
