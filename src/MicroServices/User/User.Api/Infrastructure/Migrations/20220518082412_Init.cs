using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace User.Api.Infrastructure.Migrations
{
    public partial class Init : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AlterDatabase()
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.CreateTable(
                name: "CustomerUser",
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false),
                    PhoneNumber = table.Column<string>(type: "varchar(32)", nullable: false)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    NickName = table.Column<string>(type: "varchar(64)", nullable: true)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    Avatar = table.Column<string>(type: "varchar(128)", nullable: true)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    Gender = table.Column<sbyte>(type: "tinyint", nullable: false),
                    Birthday = table.Column<DateTime>(type: "datetime", nullable: true),
                    RegisterSource = table.Column<sbyte>(type: "tinyint", nullable: false),
                    Enabled = table.Column<bool>(type: "tinyint(1)", nullable: false),
                    CreateTime = table.Column<DateTime>(type: "datetime", nullable: false),
                    TenantId = table.Column<long>(type: "bigint", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_CustomerUser", x => x.Id);
                })
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.CreateTable(
                name: "CustomerUserVip",
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false),
                    CustomerUserId = table.Column<long>(type: "bigint", nullable: false),
                    VipLevelId = table.Column<long>(type: "bigint", nullable: false),
                    BaseOrderId = table.Column<long>(type: "bigint", nullable: false),
                    ExpireDate = table.Column<DateTime>(type: "datetime", nullable: false),
                    CreateTime = table.Column<DateTime>(type: "datetime", nullable: false),
                    TenantId = table.Column<long>(type: "bigint", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_CustomerUserVip", x => x.Id);
                })
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.CreateTable(
                name: "DarenBonus",
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false),
                    BaseOrderId = table.Column<long>(type: "bigint", nullable: false),
                    SubOrderId = table.Column<long>(type: "bigint", nullable: false),
                    OrderType = table.Column<int>(type: "int", nullable: false),
                    IsFixedAmount = table.Column<sbyte>(type: "tinyint", nullable: false),
                    BonusValue = table.Column<decimal>(type: "decimal(18,4)", nullable: false),
                    UserId = table.Column<long>(type: "bigint", nullable: false),
                    PhoneNumber = table.Column<string>(type: "varchar(50)", nullable: false)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    CreateOrderTime = table.Column<DateTime>(type: "datetime", nullable: false),
                    ProductName = table.Column<string>(type: "varchar(50)", nullable: false)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    ProductSkuName = table.Column<string>(type: "varchar(50)", nullable: false)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    AmountReceivable = table.Column<decimal>(type: "decimal(18,4)", nullable: false),
                    AmountReceived = table.Column<decimal>(type: "decimal(18,4)", nullable: false),
                    InvoiceAmount = table.Column<decimal>(type: "decimal(18,4)", nullable: false),
                    BonusType = table.Column<sbyte>(type: "tinyint", nullable: false),
                    Status = table.Column<sbyte>(type: "tinyint", nullable: false),
                    TenantId = table.Column<long>(type: "bigint", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_DarenBonus", x => x.Id);
                })
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.CreateTable(
                name: "DarenProductCommission",
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false),
                    ProductId = table.Column<long>(type: "bigint", nullable: false),
                    ProductSkuId = table.Column<long>(type: "bigint", nullable: false),
                    ProductType = table.Column<sbyte>(type: "tinyint", nullable: false),
                    IsFixedAmount = table.Column<bool>(type: "tinyint(1)", nullable: false),
                    ShareBonus = table.Column<decimal>(type: "decimal(18,4)", nullable: false),
                    DevelopBonus = table.Column<decimal>(type: "decimal(18,4)", nullable: false),
                    TenantId = table.Column<long>(type: "bigint", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_DarenProductCommission", x => x.Id);
                })
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.CreateTable(
                name: "DarenSetting",
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false),
                    IsPublicDisplay = table.Column<bool>(type: "tinyint(1)", nullable: false),
                    BindRule = table.Column<int>(type: "int", nullable: false),
                    Intro = table.Column<string>(type: "text", nullable: true)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    CreateTime = table.Column<DateTime>(type: "datetime", nullable: false),
                    TenantId = table.Column<long>(type: "bigint", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_DarenSetting", x => x.Id);
                })
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.CreateTable(
                name: "DarenShareFollow",
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false),
                    SuperiorUserId = table.Column<long>(type: "bigint", nullable: false),
                    FollowerOpenId = table.Column<string>(type: "varchar(64)", nullable: true)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    CreateTime = table.Column<DateTime>(type: "datetime", nullable: false),
                    TenantId = table.Column<long>(type: "bigint", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_DarenShareFollow", x => x.Id);
                })
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.CreateTable(
                name: "DarenUnderling",
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false),
                    UserId = table.Column<long>(type: "bigint", nullable: false),
                    SuperiorUserId = table.Column<long>(type: "bigint", nullable: false),
                    BindSource = table.Column<sbyte>(type: "tinyint", nullable: false),
                    CreateTime = table.Column<DateTime>(type: "datetime", nullable: false),
                    TenantId = table.Column<long>(type: "bigint", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_DarenUnderling", x => x.Id);
                })
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.CreateTable(
                name: "DarenUser",
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false),
                    UserId = table.Column<long>(type: "bigint", nullable: false),
                    SuperiorUserId = table.Column<long>(type: "bigint", nullable: false),
                    CreateTime = table.Column<DateTime>(type: "datetime", nullable: false),
                    TenantId = table.Column<long>(type: "bigint", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_DarenUser", x => x.Id);
                })
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.CreateTable(
                name: "DarenUserBankCard",
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false),
                    UserId = table.Column<long>(type: "bigint", nullable: false),
                    BankCode = table.Column<string>(type: "varchar(50)", nullable: false)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    BankName = table.Column<string>(type: "varchar(50)", nullable: false)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    AccountNumber = table.Column<string>(type: "varchar(50)", nullable: false)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    AccountName = table.Column<string>(type: "varchar(20)", nullable: false)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    CreateTime = table.Column<DateTime>(type: "datetime", nullable: false),
                    TenantId = table.Column<long>(type: "bigint", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_DarenUserBankCard", x => x.Id);
                })
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.CreateTable(
                name: "DarenWithdrawals",
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false),
                    UserId = table.Column<long>(type: "bigint", nullable: false),
                    PhoneNumber = table.Column<string>(type: "varchar(50)", nullable: false)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    Amount = table.Column<decimal>(type: "decimal(18,4)", nullable: false),
                    BankFees = table.Column<decimal>(type: "decimal(18,4)", nullable: false),
                    BankCode = table.Column<string>(type: "varchar(50)", nullable: false)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    BankName = table.Column<string>(type: "varchar(50)", nullable: false)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    AccountNumber = table.Column<string>(type: "varchar(50)", nullable: false)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    AccountName = table.Column<string>(type: "varchar(20)", nullable: false)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    Status = table.Column<sbyte>(type: "tinyint", nullable: false),
                    FailureReason = table.Column<string>(type: "varchar(100)", nullable: true)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    CreateTime = table.Column<DateTime>(type: "datetime", nullable: false),
                    TenantId = table.Column<long>(type: "bigint", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_DarenWithdrawals", x => x.Id);
                })
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.CreateTable(
                name: "DarenWithdrawalsBonus",
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false),
                    DarenWithdrawalsId = table.Column<long>(type: "bigint", nullable: false),
                    DarenBonusId = table.Column<long>(type: "bigint", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_DarenWithdrawalsBonus", x => x.Id);
                })
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.CreateTable(
                name: "HuiZhiBinding",
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false),
                    SupplierId = table.Column<long>(type: "bigint", nullable: false),
                    UserId = table.Column<long>(type: "bigint", nullable: false),
                    Code = table.Column<string>(type: "varchar(64)", nullable: false)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    SysRole = table.Column<sbyte>(type: "tinyint", nullable: false),
                    CreateTime = table.Column<DateTime>(type: "datetime", nullable: false),
                    TenantId = table.Column<long>(type: "bigint", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_HuiZhiBinding", x => x.Id);
                })
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.CreateTable(
                name: "ManageUser",
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false),
                    Account = table.Column<string>(type: "varchar(50)", nullable: false)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    Password = table.Column<string>(type: "varchar(100)", nullable: false)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    Name = table.Column<string>(type: "varchar(50)", nullable: false)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    NickName = table.Column<string>(type: "varchar(50)", nullable: true)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    PhoneNumber = table.Column<string>(type: "varchar(50)", nullable: true)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    Enabled = table.Column<bool>(type: "tinyint(1)", nullable: false),
                    CreateTime = table.Column<DateTime>(type: "datetime", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_ManageUser", x => x.Id);
                })
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.CreateTable(
                name: "ReceiverAddress",
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false),
                    UserId = table.Column<long>(type: "bigint", nullable: false),
                    Name = table.Column<string>(type: "varchar(32)", nullable: true)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    PhoneNumber = table.Column<string>(type: "varchar(32)", nullable: true)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    ProvinceCode = table.Column<int>(type: "int", nullable: false),
                    ProvinceName = table.Column<string>(type: "varchar(32)", nullable: true)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    CityCode = table.Column<int>(type: "int", nullable: false),
                    CityName = table.Column<string>(type: "varchar(32)", nullable: true)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    DistrictCode = table.Column<int>(type: "int", nullable: false),
                    DistrictName = table.Column<string>(type: "varchar(32)", nullable: true)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    Address = table.Column<string>(type: "varchar(64)", nullable: true)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    CreateTime = table.Column<DateTime>(type: "datetime", nullable: false),
                    UpdateTime = table.Column<DateTime>(type: "datetime", nullable: false),
                    TenantId = table.Column<long>(type: "bigint", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_ReceiverAddress", x => x.Id);
                })
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.CreateTable(
                name: "SupplierUser",
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false),
                    SupplierId = table.Column<long>(type: "bigint", nullable: false),
                    UserName = table.Column<string>(type: "varchar(16)", nullable: false)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    RealName = table.Column<string>(type: "varchar(16)", nullable: true)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    PhoneNumber = table.Column<string>(type: "varchar(32)", nullable: true)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    CreateTime = table.Column<DateTime>(type: "datetime", nullable: false),
                    TenantId = table.Column<long>(type: "bigint", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_SupplierUser", x => x.Id);
                })
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.CreateTable(
                name: "TenantUser",
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false),
                    Account = table.Column<string>(type: "varchar(50)", nullable: false)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    Password = table.Column<string>(type: "varchar(100)", nullable: false)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    Name = table.Column<string>(type: "varchar(50)", nullable: false)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    NickName = table.Column<string>(type: "varchar(50)", nullable: true)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    PhoneNumber = table.Column<string>(type: "varchar(50)", nullable: true)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    Enabled = table.Column<bool>(type: "tinyint(1)", nullable: false),
                    CreateTime = table.Column<DateTime>(type: "datetime", nullable: false),
                    TenantId = table.Column<long>(type: "bigint", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_TenantUser", x => x.Id);
                })
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.CreateTable(
                name: "UserBinding",
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false),
                    UserId = table.Column<long>(type: "bigint", nullable: false),
                    Code = table.Column<string>(type: "varchar(100)", nullable: false)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    PlatformType = table.Column<int>(type: "int", nullable: false),
                    SysRole = table.Column<int>(type: "int", nullable: false),
                    Enabled = table.Column<bool>(type: "tinyint(1)", nullable: false),
                    CreateTime = table.Column<DateTime>(type: "datetime", nullable: false),
                    UpdateTime = table.Column<DateTime>(type: "datetime", nullable: true),
                    TenantId = table.Column<long>(type: "bigint", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_UserBinding", x => x.Id);
                })
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.CreateTable(
                name: "Vip",
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false),
                    Enabled = table.Column<bool>(type: "tinyint(1)", nullable: false),
                    Intro = table.Column<string>(type: "text", nullable: true)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    CreateTime = table.Column<DateTime>(type: "datetime", nullable: false),
                    UpdateTime = table.Column<DateTime>(type: "datetime", nullable: true),
                    TenantId = table.Column<long>(type: "bigint", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Vip", x => x.Id);
                })
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.CreateTable(
                name: "VipLevel",
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false),
                    Level = table.Column<int>(type: "int", nullable: false),
                    Name = table.Column<string>(type: "varchar(20)", nullable: false)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    Cover = table.Column<string>(type: "varchar(200)", nullable: false)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    FontColor = table.Column<string>(type: "varchar(20)", nullable: true)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    CreateTime = table.Column<DateTime>(type: "datetime", nullable: false),
                    TenantId = table.Column<long>(type: "bigint", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_VipLevel", x => x.Id);
                })
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.CreateTable(
                name: "VipLevelRights",
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false),
                    VipRightsId = table.Column<long>(type: "bigint", nullable: false),
                    VipLevelId = table.Column<long>(type: "bigint", nullable: false),
                    SimpleIntro = table.Column<string>(type: "varchar(50)", nullable: true)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    DetailIntro = table.Column<string>(type: "varchar(500)", nullable: true)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    DiscountValue = table.Column<decimal>(type: "decimal(2,1)", nullable: false),
                    Enabled = table.Column<bool>(type: "tinyint(1)", nullable: false),
                    TenantId = table.Column<long>(type: "bigint", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_VipLevelRights", x => x.Id);
                })
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.CreateTable(
                name: "VipLevelRightsCoupon",
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false),
                    VipLevelRightsId = table.Column<long>(type: "bigint", nullable: false),
                    CouponId = table.Column<long>(type: "bigint", nullable: false),
                    Amount = table.Column<int>(type: "int", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_VipLevelRightsCoupon", x => x.Id);
                })
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.CreateTable(
                name: "VipRights",
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false),
                    Sort = table.Column<int>(type: "int", nullable: false),
                    Name = table.Column<string>(type: "varchar(20)", nullable: true)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    Cover = table.Column<string>(type: "varchar(200)", nullable: true)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    RightsType = table.Column<sbyte>(type: "tinyint", nullable: false),
                    CreateTime = table.Column<DateTime>(type: "datetime", nullable: false),
                    TenantId = table.Column<long>(type: "bigint", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_VipRights", x => x.Id);
                })
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.CreateTable(
                name: "VipRightsRange",
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false),
                    VipRightsId = table.Column<long>(type: "bigint", nullable: false),
                    ProductType = table.Column<int>(type: "int", nullable: false),
                    ParticipateType = table.Column<sbyte>(type: "tinyint", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_VipRightsRange", x => x.Id);
                })
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.CreateTable(
                name: "VipRightsRangeValue",
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false),
                    VipRightsRangeId = table.Column<long>(type: "bigint", nullable: false),
                    GroupId = table.Column<long>(type: "bigint", nullable: false),
                    HotelId = table.Column<long>(type: "bigint", nullable: false),
                    PriceStrategyIds = table.Column<string>(type: "varchar(200)", nullable: true)
                        .Annotation("MySql:CharSet", "utf8mb4")
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_VipRightsRangeValue", x => x.Id);
                })
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.InsertData(
                table: "ManageUser",
                columns: new[] { "Id", "Account", "CreateTime", "Enabled", "Name", "NickName", "Password", "PhoneNumber" },
                values: new object[] { 901019344460090000L, "admin", new DateTime(2021, 10, 22, 0, 0, 0, 0, DateTimeKind.Unspecified), true, "管理员", null, "E19D5CD5AF0378DA05F63F891C7467AF", null });

            migrationBuilder.CreateIndex(
                name: "IX_CustomerUser_PhoneNumber",
                table: "CustomerUser",
                column: "PhoneNumber",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_CustomerUser_TenantId",
                table: "CustomerUser",
                column: "TenantId");

            migrationBuilder.CreateIndex(
                name: "IX_CustomerUserVip_CustomerUserId",
                table: "CustomerUserVip",
                column: "CustomerUserId");

            migrationBuilder.CreateIndex(
                name: "IX_CustomerUserVip_TenantId",
                table: "CustomerUserVip",
                column: "TenantId");

            migrationBuilder.CreateIndex(
                name: "IX_DarenBonus_TenantId",
                table: "DarenBonus",
                column: "TenantId");

            migrationBuilder.CreateIndex(
                name: "IX_DarenBonus_TenantId_BaseOrderId",
                table: "DarenBonus",
                columns: new[] { "TenantId", "BaseOrderId" });

            migrationBuilder.CreateIndex(
                name: "IX_DarenBonus_TenantId_PhoneNumber",
                table: "DarenBonus",
                columns: new[] { "TenantId", "PhoneNumber" });

            migrationBuilder.CreateIndex(
                name: "IX_DarenProductCommission_ProductId_ProductSkuId",
                table: "DarenProductCommission",
                columns: new[] { "ProductId", "ProductSkuId" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_DarenProductCommission_TenantId",
                table: "DarenProductCommission",
                column: "TenantId");

            migrationBuilder.CreateIndex(
                name: "IX_DarenSetting_TenantId",
                table: "DarenSetting",
                column: "TenantId");

            migrationBuilder.CreateIndex(
                name: "IX_DarenShareFollow_TenantId",
                table: "DarenShareFollow",
                column: "TenantId");

            migrationBuilder.CreateIndex(
                name: "IX_DarenUnderling_TenantId",
                table: "DarenUnderling",
                column: "TenantId");

            migrationBuilder.CreateIndex(
                name: "IX_DarenUser_TenantId",
                table: "DarenUser",
                column: "TenantId");

            migrationBuilder.CreateIndex(
                name: "IX_DarenUserBankCard_TenantId",
                table: "DarenUserBankCard",
                column: "TenantId");

            migrationBuilder.CreateIndex(
                name: "IX_DarenWithdrawals_TenantId",
                table: "DarenWithdrawals",
                column: "TenantId");

            migrationBuilder.CreateIndex(
                name: "IX_DarenWithdrawals_TenantId_PhoneNumber",
                table: "DarenWithdrawals",
                columns: new[] { "TenantId", "PhoneNumber" });

            migrationBuilder.CreateIndex(
                name: "IX_DarenWithdrawals_TenantId_UserId",
                table: "DarenWithdrawals",
                columns: new[] { "TenantId", "UserId" });

            migrationBuilder.CreateIndex(
                name: "IX_HuiZhiBinding_Code",
                table: "HuiZhiBinding",
                column: "Code");

            migrationBuilder.CreateIndex(
                name: "IX_HuiZhiBinding_TenantId",
                table: "HuiZhiBinding",
                column: "TenantId");

            migrationBuilder.CreateIndex(
                name: "IX_HuiZhiBinding_UserId",
                table: "HuiZhiBinding",
                column: "UserId");

            migrationBuilder.CreateIndex(
                name: "IX_ManageUser_Account",
                table: "ManageUser",
                column: "Account",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_ManageUser_CreateTime",
                table: "ManageUser",
                column: "CreateTime");

            migrationBuilder.CreateIndex(
                name: "IX_ReceiverAddress_TenantId",
                table: "ReceiverAddress",
                column: "TenantId");

            migrationBuilder.CreateIndex(
                name: "IX_SupplierUser_SupplierId_UserName",
                table: "SupplierUser",
                columns: new[] { "SupplierId", "UserName" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_SupplierUser_TenantId",
                table: "SupplierUser",
                column: "TenantId");

            migrationBuilder.CreateIndex(
                name: "IX_TenantUser_Account_TenantId",
                table: "TenantUser",
                columns: new[] { "Account", "TenantId" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_TenantUser_CreateTime",
                table: "TenantUser",
                column: "CreateTime");

            migrationBuilder.CreateIndex(
                name: "IX_TenantUser_TenantId",
                table: "TenantUser",
                column: "TenantId");

            migrationBuilder.CreateIndex(
                name: "IX_UserBinding_Code",
                table: "UserBinding",
                column: "Code");

            migrationBuilder.CreateIndex(
                name: "IX_UserBinding_CreateTime",
                table: "UserBinding",
                column: "CreateTime");

            migrationBuilder.CreateIndex(
                name: "IX_UserBinding_TenantId",
                table: "UserBinding",
                column: "TenantId");

            migrationBuilder.CreateIndex(
                name: "IX_UserBinding_UserId",
                table: "UserBinding",
                column: "UserId");

            migrationBuilder.CreateIndex(
                name: "IX_Vip_TenantId",
                table: "Vip",
                column: "TenantId");

            migrationBuilder.CreateIndex(
                name: "IX_VipLevel_TenantId",
                table: "VipLevel",
                column: "TenantId");

            migrationBuilder.CreateIndex(
                name: "IX_VipLevelRights_TenantId",
                table: "VipLevelRights",
                column: "TenantId");

            migrationBuilder.CreateIndex(
                name: "IX_VipLevelRightsCoupon_VipLevelRightsId",
                table: "VipLevelRightsCoupon",
                column: "VipLevelRightsId");

            migrationBuilder.CreateIndex(
                name: "IX_VipRights_TenantId",
                table: "VipRights",
                column: "TenantId");

            migrationBuilder.CreateIndex(
                name: "IX_VipRightsRange_VipRightsId",
                table: "VipRightsRange",
                column: "VipRightsId");

            migrationBuilder.CreateIndex(
                name: "IX_VipRightsRangeValue_VipRightsRangeId",
                table: "VipRightsRangeValue",
                column: "VipRightsRangeId");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "CustomerUser");

            migrationBuilder.DropTable(
                name: "CustomerUserVip");

            migrationBuilder.DropTable(
                name: "DarenBonus");

            migrationBuilder.DropTable(
                name: "DarenProductCommission");

            migrationBuilder.DropTable(
                name: "DarenSetting");

            migrationBuilder.DropTable(
                name: "DarenShareFollow");

            migrationBuilder.DropTable(
                name: "DarenUnderling");

            migrationBuilder.DropTable(
                name: "DarenUser");

            migrationBuilder.DropTable(
                name: "DarenUserBankCard");

            migrationBuilder.DropTable(
                name: "DarenWithdrawals");

            migrationBuilder.DropTable(
                name: "DarenWithdrawalsBonus");

            migrationBuilder.DropTable(
                name: "HuiZhiBinding");

            migrationBuilder.DropTable(
                name: "ManageUser");

            migrationBuilder.DropTable(
                name: "ReceiverAddress");

            migrationBuilder.DropTable(
                name: "SupplierUser");

            migrationBuilder.DropTable(
                name: "TenantUser");

            migrationBuilder.DropTable(
                name: "UserBinding");

            migrationBuilder.DropTable(
                name: "Vip");

            migrationBuilder.DropTable(
                name: "VipLevel");

            migrationBuilder.DropTable(
                name: "VipLevelRights");

            migrationBuilder.DropTable(
                name: "VipLevelRightsCoupon");

            migrationBuilder.DropTable(
                name: "VipRights");

            migrationBuilder.DropTable(
                name: "VipRightsRange");

            migrationBuilder.DropTable(
                name: "VipRightsRangeValue");
        }
    }
}
