using Contracts.Common.Inventory.DTOs;
using Contracts.Common.Inventory.Enums;
using Contracts.Common.Inventory.Messages;
using Inventory.Api.Infrastructure;
using Inventory.Api.Model;
using Inventory.Api.Services;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using UnitTest.Base;
using Xunit;

namespace Inventory.UnitTest
{
    public class ProductInventoryConfigServiceTest : TestBase<CustomDbContext>
    {
        #region 获取产品库存配置

        [Fact(DisplayName = "获取产品库存配置_成功")]
        public async Task GetProductInventoryConfig_Success()
        {
            //arrange
            var tenantId = 897049819561590784;
            var dbContext = GetNewDbContext(tenantId);
            await dbContext.AddAsync(new ProductInventoryConfig
            {
                ItemId = 1,
                ProductId = 1,
                ProductInventoryType = ProductInventoryType.Share,
                OverSaleable = true,
                TotalQuantity = 10
            });
            var rows = await dbContext.SaveChangesAsync();

            //act
            var input = new GetProductInventoryConfigInput()
            {
                ItemId = 1,
                ProductId = 1,
            };
            var service = new ProductInventoryConfigService(dbContext);
            var output = await service.GetConfig(input);

            //assert
            Assert.NotNull(output);
        }

        #endregion

        #region 新增产品库存配置

        [Fact(DisplayName = "新增产品库存配置_成功")]
        public async Task AddProductInventoryConfig_Success()
        {
            //arrange
            var tenantId = 897049819561590784;
            var dbContext = GetNewDbContext();
            //act

            var receive = new ProductInventoryConfigMessage
            {
                ItemId = 1,
                ProductId = 1,
                TenantId = tenantId,
                OverSaleable = true,
                ProductInventoryType = ProductInventoryType.Share,
                TotalQuantity = null
            };

            var service = new ProductInventoryConfigService(dbContext);
            await service.AddConfig(receive);

            //fake data
            await service.AddConfig(receive);

            //assert
            var count = await dbContext.ProductInventoryConfigs.IgnoreQueryFilters()
                        .CountAsync(c => c.ProductId.Equals(receive.ProductId) && c.ItemId.Equals(receive.ItemId)
                        && c.TenantId.Equals(receive.TenantId));
            Assert.Equal(1, count);
        }

        #endregion

        #region 更新产品库存配置

        [Fact(DisplayName = "更新产品库存配置_成功")]
        public async Task UpdateProductInventoryConfig_Success()
        {
            //arrange
            var tenantId = 897049819561590784;
            var dbContext = GetNewDbContext();
            var productInventoryConfig = new ProductInventoryConfig
            {
                TotalQuantity = 10,
                ProductInventoryType = ProductInventoryType.Single,
                ProductId = 1,
                ItemId = 1,
                OverSaleable = false
            };
            await dbContext.AddAsync(productInventoryConfig);
            var rows = await dbContext.SetTenantId(tenantId).SaveChangesAsync();

            //act
            var receive = new ProductInventoryConfigMessage
            {
                TenantId = tenantId,
                ItemId = productInventoryConfig.ItemId,
                ProductId = productInventoryConfig.ProductId,
                OverSaleable = true,
                TotalQuantity = 20,
                ProductInventoryType = ProductInventoryType.Share
            };
            var service = new ProductInventoryConfigService(dbContext);
            await service.UpdateConfig(receive);

            //assert
            productInventoryConfig = dbContext.ProductInventoryConfigs.IgnoreQueryFilters().FirstOrDefault();
            var r = productInventoryConfig.TotalQuantity == receive.TotalQuantity
                && productInventoryConfig.OverSaleable == receive.OverSaleable
                && productInventoryConfig.ProductInventoryType == receive.ProductInventoryType;
            Assert.True(r);
        }

        #endregion

        [Theory(DisplayName = "更新库存配置总库存_成功")]
        [InlineData(ProductInventoryType.Single, 20)]
        [InlineData(ProductInventoryType.Share, 20)]
        public async Task UpdateTotalQuantity_Success(ProductInventoryType productInventoryType, int totalQuantity)
        {
            //arrange
            var tenantId = 897049819561590784;
            var dbContext = GetNewDbContext();
            var defaultFrozenQuantity = 1;//默认冻结库存数
            var defaultTotalQuantity = 100;//默认总库存
            var productInventoryConfig = new ProductInventoryConfig
            {
                TotalQuantity = defaultTotalQuantity,
                ProductInventoryType = productInventoryType,
                ProductId = 1,
                ItemId = 1,
            };

            var startDate = DateTime.Today.AddDays(-1);
            var endDate = DateTime.Today.AddDays(5);
            var days = endDate.Subtract(startDate).TotalDays;

            List<ProductCalendarInventory> productCalendarInventories = new();
            List<Api.Model.Inventory> inventories = new();
            for (int i = 0; i < days; i++)
            {
                var total = productInventoryConfig.ProductInventoryType == ProductInventoryType.Share ?
                    productInventoryConfig.TotalQuantity : defaultTotalQuantity;

                var inventory = new Api.Model.Inventory()
                {
                    AvailableQuantity = total - defaultFrozenQuantity,
                    FrozenQuantity = defaultFrozenQuantity,
                    Enabled = true,
                    TotalQuantity = total
                };

                inventories.Add(inventory);
                productCalendarInventories.Add(new ProductCalendarInventory
                {
                    InventoryId = inventory.Id,
                    ItemId = productInventoryConfig.ItemId,
                    ProductId = productInventoryConfig.ProductId,
                    Date = startDate.AddDays(i),
                });
            }
            await dbContext.AddAsync(productInventoryConfig);
            await dbContext.AddRangeAsync(productCalendarInventories);
            await dbContext.AddRangeAsync(inventories);
            var rows = await dbContext.SetTenantId(tenantId).SaveChangesAsync();

            //act
            var receive = new UpdateTotalQuantityMessage
            {
                TenantId = tenantId,
                ProductId = 1,
                TotalQuantity = totalQuantity
            };
            var service = new ProductInventoryConfigService(dbContext);

            await service.UpdateTotalQuantity(receive);

            //assert
            var result = dbContext.ProductInventoryConfigs.IgnoreQueryFilters()
                .Single()?.TotalQuantity
                .Equals(receive.TotalQuantity) ?? false;

            switch (productInventoryType)
            {
                case ProductInventoryType.Single:
                    result = result && dbContext.Inventories.IgnoreQueryFilters().All(i => i.TotalQuantity.Equals(defaultTotalQuantity))
                        && dbContext.Inventories.IgnoreQueryFilters().All(i => i.AvailableQuantity.Equals(i.TotalQuantity - i.FrozenQuantity));
                    break;
                case ProductInventoryType.Share:
                    result = result && dbContext.Inventories.IgnoreQueryFilters().All(i => i.TotalQuantity.Equals(totalQuantity))
                        && dbContext.Inventories.IgnoreQueryFilters().All(i => i.AvailableQuantity.Equals(i.TotalQuantity - i.FrozenQuantity));
                    break;
            }

            Assert.True(result);
        }
    }
}
