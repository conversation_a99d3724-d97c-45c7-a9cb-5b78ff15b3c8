using Common.GlobalException;
using Common.Swagger;
using Contracts.Common.Payment.DTOs;
using Contracts.Common.Payment.Enums;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Payment.Api.ConfigModel;
using Payment.Api.Services;
using Payment.Api.Services.Interfaces;
using System.Text.Json.Serialization;

namespace Payment.Api.Controllers
{
    [Route("[controller]/[action]")]
    [ApiController]
    public class OrderController : ControllerBase
    {
        private readonly IOrderPayService _orderPayService;
        private readonly IYeeMerConfigService _yeeMerConfigService;
        private readonly HuiZhiWeiXinConfig _huiZhiWeiXinConfig;

        public OrderController(IOrderPayService orderPayService,
            IYeeMerConfigService yeeMerConfigService,
            IOptions<HuiZhiWeiXinConfig> huiZhiWeiXinConfig)
        {
            _orderPayService = orderPayService;
            _yeeMerConfigService = yeeMerConfigService;
            _huiZhiWeiXinConfig = huiZhiWeiXinConfig.Value;
        }

        [HttpPost]
        [ProducesResponseType(typeof(GetPaymentInfoOutput), (int)HttpStatusCode.OK)]
        public async Task<IActionResult> GetPaymentInfo([FromBody] GetPaymentInfoInput input)
        {
            var result = await _orderPayService.GetPaymentInfo(input);
            return Ok(result);
        }

        /// <summary>
        /// 订单支付
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost]
        [ProducesResponseType(typeof(OrderPayOutput), (int)HttpStatusCode.OK)]
        [SwaggerResponseExt(default, ErrorTypes.Order.OrderNotFind,
            ErrorTypes.Payment.OrderHasPaid,
            ErrorTypes.Payment.YeeMerNotConfig,
            ErrorTypes.Payment.CurrencyNonsupport)]
        public async Task<IActionResult> Pay([FromBody] OrderPaymentPayInput input)
        {
            OrderPayInput orderPayInput = new()
            {
                OrderPaymentType = input.OrderPaymentType,
                PayWay = input.PayWay,
                PayType = input.PayType,
                PayChannel = input.PayChannel,
                OrderId = input.OrderId,
                RedirectUrl = input.RedirectUrl,
                UserStoredValueCardId = input.UserStoredValueCardId,
            };
            var result = await _orderPayService.Pay(orderPayInput);
            return Ok(result);
        }

        /// <summary>
        /// 额度支付
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost]
        [SwaggerResponseExt(default, ErrorTypes.Order.OrderNotFind,
            ErrorTypes.Payment.OrderHasPaid,
            ErrorTypes.Payment.CurrencyNonsupport,
            ErrorTypes.Payment.CreditPayFail)]
        public async Task<IActionResult> AgencyCreditPay([FromBody] AgencyCreditOrderPayInput input)
        {
            var result = await _orderPayService.Pay(new OrderPayInput
            {
                OrderId = input.OrderId,
                OrderPaymentType = input.OrderPaymentType,
                PayType = PayType.AgencyCreditPay
            });
            if (result.PayStatus != PayStatus.Paid)
                throw new BusinessException(ErrorTypes.Payment.CreditPayFail);
            return Ok();
        }

        /// <summary>
        /// 预收款 支付
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost]
        [ProducesResponseType(typeof(OrderPayOutput), (int)HttpStatusCode.OK)]
        [SwaggerResponseExt(default, ErrorTypes.Order.OrderNotFind,
            ErrorTypes.Payment.OrderHasPaid,
            ErrorTypes.Payment.ReceiptPrepaymentInsufficientBalance)]
        public async Task<IActionResult> ReceiptPrepaymentPay([FromBody] ReceiptPrepaymentPayInput input)
        {
            OrderPayInput orderPayInput = new()
            {
                OrderPaymentType = input.OrderPaymentType,
                OrderId = input.OrderId,
                PayType = PayType.AdvancePayment,
            };
            var result = await _orderPayService.Pay(orderPayInput);
            return Ok(result);
        }

        /// <summary>
        /// Onerway 境外信用卡支付
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public async Task<OrderPayOutput> OnerwayTxnPay(OnerwayTxnPayInput input)
        {
            OrderPayInput orderPayInput = new()
            {
                OrderPaymentType = input.OrderPaymentType,
                OrderId = input.OrderId,
                PayType = PayType.Onerway,
                RedirectUrl = input.RedirectUrl,
                TxnPaymentInfo = input.TxnPaymentInfo,
            };
            var result = await _orderPayService.Pay(orderPayInput);
            return result;
        }

        /// <summary>
        /// 微信公众号静默登录支付
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost]
        [ProducesResponseType(typeof(OrderPayOutput), (int)HttpStatusCode.OK)]
        [SwaggerResponseExt(default, ErrorTypes.Order.OrderNotFind,
            ErrorTypes.Payment.OrderHasPaid,
            ErrorTypes.Payment.YeeMerNotConfig,
            ErrorTypes.Payment.CurrencyNonsupport)]
        public async Task<IActionResult> WechatPay([FromBody] WechatPayInput input)
        {
            var merConfig = await _yeeMerConfigService.GetCurrentYeeMerConfig();
            var openId = merConfig.MerchantNoType switch
            {
                YeePaymentMerchantNoType.Agent => (await _orderPayService.GetHuizhiOauth2AccessToken(new Contracts.Common.WeChat.DTOs.Huizhi.GetOauth2AccessTokenInput
                {
                    Code = input.Code,
                    Type = Contracts.Common.WeChat.Enums.AuthType.WechatMp
                })).OpenId,
                _ => (await _orderPayService.GetWechatAccessToken(input.Code)).OpenId
            };
            OrderPayInput orderPayInput = new()
            {
                OrderId = input.OrderId,
                OrderPaymentType = input.OrderPaymentType,
                PayType = PayType.YeePay,
                PayChannel = PayChannel.WECHAT,
                PayWay = PayWay.WECHAT_OFFIACCOUNT,
                UserId = openId
            };
            var result = await _orderPayService.Pay(orderPayInput);
            return Ok(result);
        }

        /// <summary>
        /// 汇智微信支付
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        /// <exception cref="NotImplementedException"></exception>
        [HttpPost]
        [ProducesResponseType(typeof(OrderPayOutput), (int)HttpStatusCode.OK)]
        [SwaggerResponseExt(default, ErrorTypes.Order.OrderNotFind,
        ErrorTypes.Payment.OrderHasPaid,
        ErrorTypes.Payment.YeeMerNotConfig,
        ErrorTypes.Payment.CurrencyNonsupport)]
        public async Task<IActionResult> HuizhiWechatPay([FromBody] HuizhiWechatPayInput input)
        {
            var appId = input.PayWay switch
            {
                PayWay.WECHAT_OFFIACCOUNT => _huiZhiWeiXinConfig.WeixinAppId,
                PayWay.MINI_PROGRAM => input.IsProxy is not true ? _huiZhiWeiXinConfig.AppletAppId : _huiZhiWeiXinConfig.ProxyAppletAppId,
                _ => throw new NotImplementedException()
            };
            var accessToken = await _orderPayService.GetHuizhiOauth2AccessToken(
                new Contracts.Common.WeChat.DTOs.Huizhi.GetOauth2AccessTokenInput
                {
                    Code = input.Code,
                    Type = input.PayWay == PayWay.MINI_PROGRAM ? Contracts.Common.WeChat.Enums.AuthType.WechatApplet : Contracts.Common.WeChat.Enums.AuthType.WechatMp,
                    IsProxy = input.IsProxy,
                });
            var openId = accessToken.OpenId!;

            OrderPayInput orderPayInput = new()
            {
                OrderId = input.OrderId,
                OrderPaymentType = input.OrderPaymentType,
                PayType = PayType.YeePay,
                PayChannel = PayChannel.WECHAT,
                PayWay = input.PayWay,
                AppId = appId,
                UserId = openId
            };
            var result = await _orderPayService.Pay(orderPayInput);
            return Ok(result);
        }


        /// <summary>
        /// 信用卡担保支付
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost]
        [SwaggerResponseExt(default, ErrorTypes.Order.OrderNotFind,
            ErrorTypes.Payment.OrderHasPaid,
            ErrorTypes.Payment.CurrencyNonsupport,
            ErrorTypes.Payment.CreditPayFail)]
        public async Task<IActionResult> CreditCardGuaranteePay([FromBody] CreditCardOrderPayInput input)
        {
            var result = await _orderPayService.Pay(new OrderPayInput
            {
                OrderId = input.OrderId,
                OrderPaymentType = input.OrderPaymentType,
                PayType = PayType.CreditCardGuarantee
            });
            if (result.PayStatus != PayStatus.Paid)
                throw new BusinessException(ErrorTypes.Payment.CreditPayFail);
            return Ok();
        }

        /// <summary>
        /// 易宝海外银行账户收款支付
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost]
        [ProducesResponseType(typeof(YeepayCollectionPayOutput), (int)HttpStatusCode.OK)]
        [SwaggerResponseExt(default, ErrorTypes.Order.OrderNotFind, ErrorTypes.Payment.OrderHasPaid)]
        public async Task<IActionResult> YeepayCollectionPay([FromBody] YeepayCollectionPayInput input)
        {
            var result = await _orderPayService.Pay(new OrderPayInput
            {
                OrderId = input.OrderId,
                OrderPaymentType = input.OrderPaymentType,
                TenantBankAccountId = input.TenantBankAccountId,
                PayType = PayType.YeepayCollection
            });
            YeepayCollectionPayOutput output = new()
            {
                PayStatus = result.PayStatus,
                CollectionOrder = JObject.FromObject(result.PrePayTn).ToObject<CollectionOrderPayDto>(),
                Message = result.Message
            };
            return Ok(output);
        }

        /// <summary>
        /// 易宝小程序托管支付
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        [ProducesResponseType(typeof(YeeTutelagePayOutput), (int)HttpStatusCode.OK)]
        [SwaggerResponseExt(default, ErrorTypes.Order.OrderNotFind,
            ErrorTypes.Payment.OrderHasPaid,
            ErrorTypes.Payment.YeeMerNotConfig,
            ErrorTypes.Payment.CurrencyNonsupport)]
        public async Task<IActionResult> YeeTutelagePay([FromBody] YeeTutelagePayInput input)
        {
            var result = await _orderPayService.Pay(new OrderPayInput
            {
                OrderId = input.OrderId,
                OrderPaymentType = input.OrderPaymentType,
                PayType = PayType.YeePay,
                PayChannel = PayChannel.WECHAT,
                PayWay = PayWay.MINI_PROGRAM,
                IsTutelage = true,
            });
            YeeTutelagePayOutput output = new()
            {
                PayStatus = result.PayStatus,
                YeeTutelagePay = JObject.FromObject(result.PrePayTn).ToObject<YeeTutelagePayDto>(),
                Message = result.Message
            };
            return Ok(output);
        }

    }
}
