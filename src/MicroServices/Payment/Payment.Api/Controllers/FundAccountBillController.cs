using Common.GlobalException;
using Common.Swagger;
using Contracts.Common.Payment.DTOs.FundAccountBill;
using Contracts.Common.Payment.Messages;
using DotNetCore.CAP;
using EfCoreExtensions.Abstract;
using Microsoft.AspNetCore.Mvc;
using Payment.Api.Services.Interfaces;

namespace Payment.Api.Controllers;
[Route("[controller]/[action]")]
[ApiController]
public class FundAccountBillController : ControllerBase
{
    private readonly IFundAccountBillService _fundAccountBillService;

    public FundAccountBillController(IFundAccountBillService fundAccountBillService)
    {
        _fundAccountBillService = fundAccountBillService;
    }

    /// <summary>
    /// SaaS账户流水
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ProducesResponseType(typeof(PagingModel<SearchFundAccountBillFlowOutput, FundAccountBillFlowStat>), (int)HttpStatusCode.OK)]
    public async Task<ActionResult> SearchFlows(SearchFundAccountBillFlowInput input)
    {
        var result = await _fundAccountBillService.SearchFundAccountBillFlows(input);
        return Ok(result);
    }

    /// <summary>
    /// SaaS账户流水-导出
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    /// <exception cref="BusinessException"></exception>
    [HttpPost]
    [ProducesResponseType(typeof(IEnumerable<SearchFundAccountBillFlowOutput>), (int)HttpStatusCode.OK)]
    public ActionResult FlowsExport(FundAccountBillFlowQueryInput input)
    {
        var query = _fundAccountBillService.FundAccountBillFlowQuery(input);
        if (!input.UnLimitedSize && query.Count() > 5000)
            throw new BusinessException(ErrorTypes.Payment.Exceedance);//数据超过了5000行，请修改查询条件
        var result = query.ToList();
        return Ok(result);
    }

    /// <summary>
    /// 需要处理的失败账单流水
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ProducesResponseType(typeof(PagingModel<SearchFundAccountBillFlowOutput>), (int)HttpStatusCode.OK)]
    public async Task<ActionResult> SearchFailBillFlows(SearchFailBillFlowInput input)
    {
        var result = await _fundAccountBillService.SearchFailBillFlows(input);
        return Ok(result);
    }

    /// <summary>
    /// 账单流水失败重试
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [SwaggerResponseExt(default,ErrorTypes.Common.NotSupportedOperation)]
    public async Task<ActionResult> FailBillFlowRetry(FailBillFlowRetryInput input)
    {
        await _fundAccountBillService.FailBillFlowRetry(input);
        return Ok();
    }

    #region CapSubscribe

    [NonAction]
    [CapSubscribe(CapTopics.Payment.AccountTransferBillFlow)]
    public async Task AccountTransferBillFlow(AccountTransferBillFlowMessage command)
    {
        await _fundAccountBillService.AccountTransferBillFlow(command);
    }

    [NonAction]
    [CapSubscribe(CapTopics.Payment.AccountPayOrderBillFlow)]
    public async Task AccountPayOrderBillFlow(AccountPayOrderBillFlowMessage command)
    {
        await _fundAccountBillService.AccountPayOrderBillFlow(command);
    }

    [NonAction]
    [CapSubscribe(CapTopics.Payment.ProfitDivideBillFlow)]
    public async Task OrderProfitDivideBillFlow(OrderProfitDivideBillFlowMessage command)
    {
        await _fundAccountBillService.OrderProfitDivideBillFlow(command);
    }

    [NonAction]
    [CapSubscribe(CapTopics.Payment.ProfitDivideBackBillFlow)]
    public async Task OrderProfitDivideBackBillFlow(OrderProfitDivideBackBillFlowMessage command)
    {
        await _fundAccountBillService.OrderProfitDivideBackBillFlow(command);
    }

    #endregion
}
