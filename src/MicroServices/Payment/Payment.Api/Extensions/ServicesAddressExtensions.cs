using Common.ServicesHttpClient;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Payment.Api.Extensions
{
    public static class ServicesAddressExtensions
    {
        #region Tenant

        public static string Tenant_GetTenantSysConfig(this ServicesAddress address)
             => $"{address.Tenant}/Tenant/GetTenantSysConfig";

        public static string Tenant_GetSupplier(this ServicesAddress address, long supplierId)
               => $"{address.Tenant}/Supplier/Get?supplierId={supplierId}";

        public static string Tenant_GetSysConfigByTenantId(this ServicesAddress address, long tenantId)
            => $"{address.Tenant}/Tenant/GetSysConfigByTenantId?tenantId={tenantId}";

        public static string Tenant_AgencyCreditRecord_OrderPay(this ServicesAddress address)
            => $"{address.Tenant}/AgencyCreditRecord/OrderPay";

        public static string Tenant_AgencyCreditCharge_GetPaymentInfo(this ServicesAddress address)
            => $"{address.Tenant}/AgencyCreditCharge/GetPaymentInfo";

        public static string Tenant_AgencyCreditChargeDetails(this ServicesAddress address)
            => $"{address.Tenant}/AgencyCreditCharge/Details";

        #endregion

        #region Wechat

        public static string Wechat_GetTenantWechatConfig(this ServicesAddress address)
            => $"{address.WeChat}/WechatConfiguration/GetWechatConfiguration";

        public static string Wechat_GetAccessToken(this ServicesAddress address, string code)
            => $"{address.WeChat}/PlatformAuthorize/GetAccessToken?code={code}";

        public static string Wechat_GetUserInfo(this ServicesAddress address, string code)
           => $"{address.WeChat}/PlatformAuthorize/GetUserInfo?code={code}";

        public static string Wechat_GetHuizhiOauth2AccessToken(this ServicesAddress address)
           => $"{address.WeChat}/Huizhi/GetOauth2AccessToken";

        #endregion

        #region Order

        public static string Order_BaseOrder_PaymentInfo(this ServicesAddress address)
          => $"{address.Order}/BaseOrder/PaymentInfo";

        public static string Order_ReservationOrder_PaymentInfo(this ServicesAddress address)
          => $"{address.Order}/ReservationOrder/PaymentInfo";

        public static string Payment_OfflineReceiptOrder_PaymentInfo(this ServicesAddress address)
         => $"{address.Payment}/OfflineReceiptOrder/PaymentInfo";

        public static string Order_HotelGroupBookingOrder_PaymentInfo(this ServicesAddress address)
         => $"{address.Order}/HotelGroupBookingOrder/PaymentInfo";

        #endregion

        #region User

        public static string User_GetBindingCode(this ServicesAddress address, long userId, int userBindPlatformType)
        => $"{address.User}/CustomerUser/GetBindingCode?userId={userId}&userBindPlatformType={userBindPlatformType}";

        #endregion

        #region Markting

        public static string Markting_CheckUserStoredValueCard(this ServicesAddress address)
            => $"{address.Marketing}/UserStoredValueCard/CheckUserStoredValueCard";

        public static string Markting_UserSroredValueCardOrderPay(this ServicesAddress address)
             => $"{address.Marketing}/UserStoredValueCard/OrderPay";

        public static string Markting_UserSroredValueCardOrderRefund(this ServicesAddress address)
            => $"{address.Marketing}/UserStoredValueCard/OrderRefund";

        public static string Markting_GetByOfflineReceipt(this ServicesAddress address)
            => $"{address.Marketing}/UserCoupon/GetByOfflineReceipt";

        #endregion
    }
}
