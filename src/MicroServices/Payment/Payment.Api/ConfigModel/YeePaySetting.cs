namespace Payment.Api.ConfigModel
{
    /// <summary>
    /// 易宝配置
    /// </summary>
    public class YeePaySetting
    {
        /// <summary>
        /// 商户注册通知
        /// </summary>
        public string MerRegisterNotifyUri { get; set; }

        /// <summary>
        /// 清算结果通知
        /// </summary>
        public string YeeCsNotifyUri { get; set; }

        /// <summary>
        /// 支付结果通知
        /// </summary>
        public string YeePayNotifyUri { get; set; }

        /// <summary>
        /// 退款结果通知
        /// </summary>
        public string YeeRefundNotifyUri { get; set; }

        /// <summary>
        /// 付款结果通知
        /// </summary>
        public string YeePayOrderNotifyUri { get; set; }

        /// <summary>
        /// 转账结果通知
        /// </summary>
        public string YeeTransferOrderNotifyUri { get; set; }

        /// <summary>
        /// 汇智代收代付的易宝支付商户号
        /// </summary>
        public string YeeAgentBusinessMerchantNo { get; set; }

        /// <summary>
        /// 代收代付商户 B2B支付页面host
        /// </summary>
        public string YeeAgentBusinessPaymentHost { get; set; }
    }
}
