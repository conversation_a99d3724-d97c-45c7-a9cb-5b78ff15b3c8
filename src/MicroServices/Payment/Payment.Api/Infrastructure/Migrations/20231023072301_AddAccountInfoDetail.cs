using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Payment.Api.Infrastructure.Migrations
{
    public partial class AddAccountInfoDetail : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<long>(
                name: "RefundOrderId",
                table: "ProfitDivideBack",
                type: "bigint",
                nullable: true);

            migrationBuilder.CreateTable(
                name: "AccountInfoDetail",
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false),
                    AccountInfoId = table.Column<long>(type: "bigint", nullable: false),
                    BusinessType = table.Column<sbyte>(type: "tinyint", nullable: false),
                    OrderType = table.Column<sbyte>(type: "tinyint", nullable: true),
                    BusinessOrderId = table.Column<long>(type: "bigint", nullable: false),
                    ExtNo = table.Column<string>(type: "varchar(64)", nullable: true)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    Income = table.Column<decimal>(type: "decimal(18,2)", nullable: false),
                    Expenditure = table.Column<decimal>(type: "decimal(18,2)", nullable: false),
                    Balance = table.Column<decimal>(type: "decimal(18,2)", nullable: false),
                    AccountTime = table.Column<DateTime>(type: "datetime", nullable: false),
                    UpdateTime = table.Column<DateTime>(type: "datetime", nullable: true),
                    TenantId = table.Column<long>(type: "bigint", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_AccountInfoDetail", x => x.Id);
                })
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.CreateIndex(
                name: "IX_AccountInfoDetail_TenantId",
                table: "AccountInfoDetail",
                column: "TenantId");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "AccountInfoDetail");

            migrationBuilder.DropColumn(
                name: "RefundOrderId",
                table: "ProfitDivideBack");
        }
    }
}
