using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using EfCoreExtensions.EntityConfigurationsBase;

namespace Payment.Api.Infrastructure.EntityConfigurations
{
    public class TenantReceiptFlowEntityTypeConfiguration : TenantBaseConfiguration<Model.TenantReceiptFlow>, IEntityTypeConfiguration<Model.TenantReceiptFlow>
    {
        public void Configure(EntityTypeBuilder<Model.TenantReceiptFlow> builder)
        {
            base.ConfigureBase(builder);

            builder.Property(s => s.AgencyId)
                .HasColumnType("bigint");

            builder.Property(s => s.TenantReceiptFlowType)
                .HasColumnType("tinyint");

            builder.Property(s => s.BusinessOrderId)
                .HasColumnType("bigint");

            builder.Property(s => s.Amount)
                .HasColumnType("decimal(18,2)");

            builder.Property(s => s.PayType)
                .HasColumnType("tinyint");

            builder.OwnsOne(x => x.TenantBankAccount, x =>
            {
                x.Property(s => s.Id).HasColumnType("bigint");
                x.Property(s => s.BankName).HasColumnType("varchar(100)");
                x.Property(s => s.AccountNo).HasColumnType("varchar(100)");
            });

            builder.Property(s => s.CreateTime)
                .HasColumnType("datetime");
        }
    }
}
