using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using EfCoreExtensions.EntityConfigurationsBase;

namespace Payment.Api.Infrastructure.EntityConfigurations
{
    public class ReceiptPrepaymentFlowEntityTypeConfiguration : TenantBaseConfiguration<Model.ReceiptPrepaymentFlow>, IEntityTypeConfiguration<Model.ReceiptPrepaymentFlow>
    {
        public void Configure(EntityTypeBuilder<Model.ReceiptPrepaymentFlow> builder)
        {
            base.ConfigureBase(builder);

            builder.Property(s => s.ReceiptPrepaymentId)
                .HasColumnType("bigint");

            builder.Property(s => s.BusinessType)
                .HasColumnType("tinyint");

            builder.Property(s => s.UniqueOrderId)
                .HasColumnType("bigint");

            builder.Property(s => s.OrderId)
                .HasColumnType("bigint");

            builder.Property(s => s.OrderType)
                .HasColumnType("tinyint");

            builder.Property(s => s.Income)
                .HasColumnType("decimal(18,2)");

            builder.Property(s => s.Expenditure)
                .HasColumnType("decimal(18,2)");

            builder.Property(s => s.Balance)
                .HasColumnType("decimal(18,2)");

            builder.Property(s => s.CreateTime)
                .HasColumnType("datetime");

            builder.HasIndex(s => s.UniqueOrderId).IsUnique();
        }
    }
}
