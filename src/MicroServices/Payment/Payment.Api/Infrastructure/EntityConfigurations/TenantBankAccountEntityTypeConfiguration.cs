using Contracts.Common.Payment.Enums;
using EfCoreExtensions.EntityConfigurationsBase;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Payment.Api.Infrastructure.EntityConfigurations;

public class TenantBankAccountEntityTypeConfiguration : KeyBaseConfiguration<TenantBankAccount>, IEntityTypeConfiguration<TenantBankAccount>
{
    public void Configure(EntityTypeBuilder<TenantBankAccount> builder)
    {
        base.ConfigureBase(builder);

        builder.Property(s => s.TenantBankAccountType)
            .HasColumnType("tinyint")
            .IsRequired()
            .HasDefaultValue(TenantBankAccountType.Domestic);

        builder.Property(s => s.AccountName)
            .HasColumnType("varchar(50)")
            .IsRequired();

        builder.Property(s => s.BankCode)
            .HasColumnType("varchar(32)")
            .IsRequired(false);
        
        builder.Property(s => s.BranchCode)
            .HasColumnType("varchar(32)");

        builder.Property(s => s.BranchName)
            .HasColumnType("varchar(50)");

        builder.Property(s => s.BankAccountType)
            .HasColumnType("tinyint")
            .HasDefaultValue(BankAccountType.PUBLIC_CARD);//商户账户 默认对公卡

        builder.Property(s => s.AccountNo)
           .HasColumnType("varchar(50)")
           .IsRequired();

        builder.Property(s => s.Remark)
            .HasColumnType("varchar(100)");

        builder.Property(s => s.CreateTime)
            .HasColumnType("datetime")
            .IsRequired();

        builder.Property(s => s.UpdateTime)
            .HasColumnType("datetime");

        builder.Property(s => s.Enabled)
            .HasColumnType("tinyint(1)");

        builder.Property(s => s.OpeningBankCode)
            .HasColumnType("varchar(30)");

        builder.Property(s => s.TenantBankAccountVccSettingId)
            .HasColumnType("bigint");
    }
}