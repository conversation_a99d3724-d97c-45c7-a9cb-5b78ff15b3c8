using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using EfCoreExtensions.EntityConfigurationsBase;
using Contracts.Common.Payment.Enums;

namespace Payment.Api.Infrastructure.EntityConfigurations
{
    public class WithdrawOrderEntityTypeConfiguration : TenantBaseConfiguration<Model.WithdrawOrder>, IEntityTypeConfiguration<Model.WithdrawOrder>
    {
        public void Configure(EntityTypeBuilder<Model.WithdrawOrder> builder)
        {
            base.ConfigureBase(builder);

            builder.Property(s => s.OrderAmount)
                .HasColumnType("decimal(18,2)");

            builder.Property(s => s.ReceiveType)
                .HasColumnType("tinyint");

            builder.Property(s => s.ReceiveAmount)
                .HasColumnType("decimal(18,2)");

            builder.Property(s => s.DebitAmount)
                .HasColumnType("decimal(18,2)");

            builder.Property(s => s.OrderTime)
                .HasColumnType("datetime");

            builder.Property(s => s.FinishTime)
                .HasColumnType("datetime");

            builder.Property(s => s.AccountName)
                .HasColumnType("varchar(64)");

            builder.Property(s => s.AccountNo)
                .HasColumnType("varchar(32)");

            builder.Property(s => s.BankCode)
                .HasColumnType("varchar(32)");

            builder.Property(s => s.BankName)
                .HasColumnType("varchar(64)");

            builder.Property(s => s.BankAccountType)
                .HasColumnType("tinyint");

            builder.Property(s => s.Remark)
                .HasColumnType("varchar(256)");

            builder.Property(s => s.OperatorId)
                .HasColumnType("bigint");

            builder.Property(s => s.Operator)
                .HasColumnType("varchar(200)");

            builder.Property(s => s.Status)
                .HasColumnType("tinyint");

            builder.Property(s => s.FailReason)
                .HasColumnType("varchar(500)");

            builder.Property(s => s.Fee)
                .HasColumnType("decimal(18,2)");

            builder.Property(s => s.TransferOrderNo)
                .HasColumnType("bigint");

            builder.Property(s => s.PayOrderNo)
                .HasColumnType("bigint");

            builder.Property(s => s.BackTransferOrderNo)
                .HasColumnType("bigint");

            builder.Property(s => s.BranchName)
            .HasColumnType("varchar(50)");

            builder.Property(s => s.UpdateTime)
                .HasColumnType("datetime");

            builder.Property(s => s.WithdrawOrderType)
                .HasColumnType("tinyint")
                .HasDefaultValue(WithdrawOrderType.Online);

            builder.Property(s => s.Address)
                .HasColumnType("varchar(100)");

            builder.Property(s => s.SwiftCode)
                .HasColumnType("varchar(20)");

            builder.Property(s => s.ReceiveCurrencyCode)
                .HasColumnType("varchar(20)")
                .HasDefaultValue(Currency.CNY.ToString());

            builder.OwnsOne(s => s.PaymentBill, x =>
            {
                x.Property(a => a.Id).HasColumnType("bigint");
                x.Property(a => a.BeginDate).HasColumnType("datetime");
                x.Property(a => a.EndDate).HasColumnType("datetime");
            });
        }
    }
}
