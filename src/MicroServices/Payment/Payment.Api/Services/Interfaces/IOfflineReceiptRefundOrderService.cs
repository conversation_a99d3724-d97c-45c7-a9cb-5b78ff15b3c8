using Common.Jwt;
using Contracts.Common.Payment.DTOs.OfflineReceipt;
using Contracts.Common.Payment.Messages;
using EfCoreExtensions.Abstract;

namespace Payment.Api.Services.Interfaces
{
    public interface IOfflineReceiptRefundOrderService
    {
        /// <summary>
        /// 线下收款单退款
        /// </summary>
        /// <param name="input"></param>
        /// <param name="user"></param>
        /// <param name="supplierId"></param>
        /// <returns></returns>
        Task Refund(OfflineReceiptOrderRefundInput input, CurrentUser user, long? supplierId);

        /// <summary>
        /// 退款结果处理
        /// </summary>
        /// <param name="command"></param>
        /// <returns></returns>
        Task RefundResult(OfflineReceiptOrderRefundResultMessage command);

        /// <summary>
        /// 获取线下收款单退款状态信息
        /// </summary>
        /// <param name="offlineReceiptOrderId"></param>
        /// <returns></returns>
        Task<GetRefundStatusOutput> GetRefundStatus(long offlineReceiptOrderId);

        /// <summary>
        /// 线下收款退款统计列表
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<PagingModel<OfflineReceiptOrderRefundSearchOutput>> Search(OfflineReceiptOrderRefundSearchInput input);
    }
}
