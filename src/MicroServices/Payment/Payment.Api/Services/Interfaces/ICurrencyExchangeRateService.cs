
using Contracts.Common.Payment.DTOs.Currency;

namespace Payment.Api.Services.Interfaces;

public interface ICurrencyExchangeRateService
{
    Task<List<GetExchangeRateOutput>> Get(List<GetExchangeRatesInput> input);
    /// <summary>
    /// 获取上一期汇率
    /// </summary>
    /// <returns></returns>
    Task<List<GetExchangeRateOutput>> GetPenultimate();
    Task<List<GetCurrencyOutput>> GetCurrencies();
    /// <summary>
    /// 获取每日货币汇率 v6.exchangerate-api.com
    /// </summary>
    /// <returns></returns>
    Task<List<CovertChinaMoneyRate>> GetChinaMoneyCurrencyRate();
    Task<int> DailyUpdate(List<CovertChinaMoneyRate> input);
    Task ExpireExchangeRateCache();
}