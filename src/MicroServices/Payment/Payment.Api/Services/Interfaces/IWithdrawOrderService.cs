using Contracts.Common.Payment.DTOs;
using Contracts.Common.Payment.DTOs.WithdrawOrder;
using Contracts.Common.Payment.Messages;
using EfCoreExtensions.Abstract;

namespace Payment.Api.Services.Interfaces;

public interface IWithdrawOrderService
{
    /// <summary>
    /// 获取商户提现记录
    /// </summary>
    /// <param name="input"></param>
    /// <param name="tenantId"></param>
    /// <returns></returns>
    Task<PagingModel<GetWithdrawOrderOutput>> Search(WithdrawOrderSearchInput input, long tenantId);

    /// <summary>
    /// 商户提现下单
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    /// <exception cref="ErrorTypes.Payment.WithdrawalDailyLimit"></exception>
    /// <exception cref=" ErrorTypes.Payment.WithdrawalInsufficientAmount"></exception>
    Task Withdraw(WithdrawOrderInput input);

    /// <summary>
    /// 转账结果处理
    /// </summary>
    /// <param name="command"></param>
    /// <returns></returns>
    Task TransferOrderResult(TransferOrderResultMessage command);

    /// <summary>
    /// 提现付款商户结果处理
    /// </summary>
    /// <param name="command"></param>
    /// <returns></returns>
    Task AccountPayOrderResult(AccountPayOrderResultMessage command);

    /// <summary>
    /// 商户线下提现列表
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<PagingModel<SearchAuditsOutput>> SearchAudits(SearchAuditsInput input);

    /// <summary>
    /// 提现单详情
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<WithdrawOrderDetailOutput> AuditDetail(DetailInput input);

    /// <summary>
    /// 处理提现
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task AuditHandle(AuditHandleInput input);

    /// <summary>
    /// 撤销提现
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task AuditCancel(AuditCancelInput input);
}
