using Contracts.Common.Payment.Messages;

namespace Payment.Api.Services.Interfaces;

public interface ISettlementPayOrderService
{
    /// <summary>
    /// 结算单付款
    /// </summary>
    /// <param name="receive"></param>
    /// <returns></returns>
    Task PayOrder(SettlementPayOrderMessage receive);

    /// <summary>
    /// 转账结果处理
    /// </summary>
    /// <param name="command"></param>
    /// <returns></returns>
    Task TransferOrderResultHandler(TransferOrderResultMessage command);

    /// <summary>
    /// 付款结果处理
    /// </summary>
    /// <param name="command"></param>
    /// <returns></returns>
    Task AccountPayOrderResultHandler(AccountPayOrderResultMessage command);
}
