using AutoMapper;
using Cit.Payment.Yeepay.Service.Request.Merchant;
using Contracts.Common.Payment.DTOs;

namespace Payment.Api.Services.MappingProfiles
{
    public class YeeWechatAuthApplyProfile : Profile
    {
        public YeeWechatAuthApplyProfile()
        {
            CreateMap<YeeWechatAuth, WechatAuthApplyRequest>()
                .ForMember(x => x.TransactorInfo, x => x.Ignore());

            CreateMap<YeeWechatAuth, WechatAuthQueryOutput>();

            CreateMap<YeeWechatAuth, YeeWechatAuthOutput>()
                .ForMember(x => x.TransactorInfo, x => x.Ignore());
        }
    }
}
