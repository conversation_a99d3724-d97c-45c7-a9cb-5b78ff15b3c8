using Contracts.Common.Payment.DTOs;
using Contracts.Common.Payment.Enums;
using FluentValidation;

namespace Payment.Api.Services.Validator
{
    public class PayInputValidator : AbstractValidator<OrderPayInput>
    {
        public PayInputValidator()
        {
            RuleFor(p => p.OrderId).NotNull().NotEmpty();
            RuleFor(p => p.PayType).IsInEnum().Must(x => x == PayType.YeePay 
            || x == PayType.UserStoredValueCardPay 
            || x== PayType.AgencyCreditPay);//仅支持易宝、储值卡支付、分销商额度支付
            RuleFor(p => p.PayChannel).IsInEnum();
            RuleFor(p => p.PayWay).IsInEnum();
        }
    }
}
