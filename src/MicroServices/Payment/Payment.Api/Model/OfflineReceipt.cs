using EfCoreExtensions.EntityBase;

namespace Payment.Api.Model
{
    /// <summary>
    /// 线下收款表
    /// </summary>
    public class OfflineReceipt : TenantBase
    {
        /// <summary>
        /// 收款码名称
        /// </summary>
        public string? Title { get; set; }

        /// <summary>
        /// 关联供应商
        /// </summary>
        public long SupplierId { get; set; }

        public string? SupplierName { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        public string? Remark { get; set; }

        public DateTime CreateTime { get; set; }
    }

}
