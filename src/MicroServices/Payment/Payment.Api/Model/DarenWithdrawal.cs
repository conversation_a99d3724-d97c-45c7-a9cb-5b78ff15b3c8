using Contracts.Common.Payment.Enums;
using EfCoreExtensions.EntityBase;

namespace Payment.Api.Model
{
    public class DarenWithdrawal : TenantBase
    {
        /// <summary>
        /// 达人提现单id
        /// </summary>
        public long WithdrawApplyId { get; set; }

        /// <summary>
        /// 提现金额
        /// </summary>
        public decimal Amount { get; set; }

        /// <summary>
        /// 开票金额
        /// </summary>
        public decimal InvoiceAmount { get; set; }

        /// <summary>
        /// 银行编号
        /// </summary>
        public string? BankCode { get; set; }

        /// <summary>
        /// 银行卡号
        /// </summary>
        public string? AccountNumber { get; set; }

        /// <summary>
        /// 开户名
        /// </summary>
        public string? AccountName { get; set; }

        /// <summary>
        /// 关联付款单id
        /// </summary>
        public long PayOrderNo { get; set; }

        /// <summary>
        /// 关联转账单id
        /// </summary>
        public long TransferOrderNo { get; set; }

        /// <summary>
        /// 失败原因
        /// </summary>
        public string? FailReason { get; set; }

        /// <summary>
        /// 提现状态
        /// </summary>
        public WithdrawalStatus WithdrawalStatus { get; set; }

        public DateTime CreateTime { get; set; } = DateTime.Now;

        public DateTime? UpdateTime { get; set; } = DateTime.Now;
    }

}
