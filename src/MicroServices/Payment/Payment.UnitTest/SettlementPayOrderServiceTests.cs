using Contracts.Common.Payment.Enums;
using Contracts.Common.Payment.Messages;
using DotNetCore.CAP;
using Microsoft.EntityFrameworkCore;
using Payment.Api.Infrastructure;
using Payment.Api.Model;
using Payment.Api.Services;
using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using UnitTest.Base;
using Xunit;

namespace Payment.UnitTest;
public class SettlementPayOrderServiceTests : TestBase<CustomDbContext>
{
    private static long _tenantId = 1;
    public static SettlementPayOrderService GetService(CustomDbContext dbContext, ICapPublisher capPublisher)
    {
        return new SettlementPayOrderService(dbContext, capPublisher);
    }

    [Fact(DisplayName = "结算单付款_成功")]
    public async Task PayOrderTest_Success()
    {
        //arrange
        var dbContext = GetNewDbContext();
        //act
        var service = GetService(dbContext, GetCapPublisher());

        var command = new SettlementPayOrderMessage
        {
            AccountName = "test",
            AccountNo = "123456",
            BankCode = "CCB",
            Remark = "",
            SettlementOrderTransferRecordId = 123456,
            TenantId = _tenantId,
            TotalAmount = 1m,
            TransferFee = 0.5m
        };
        await service.PayOrder(command);
        await dbContext.SaveChangesAsync();
        //assert
        var settlementPayOrder = dbContext.SettlementPayOrders.IgnoreQueryFilters().FirstOrDefault();
        Assert.True(settlementPayOrder.Status == AccountPayOrderStatus.PENDING);
    }


    [Theory(DisplayName = "商户转账主商户结果处理")]
    [ClassData(typeof(TransferOrderResultCommandData))]
    public async Task TransferOrderResultHandler(TransferOrderResultMessage command)
    {
        //arrange
        SettlementPayOrder settlementPayOrder = new()
        {
            Status = AccountPayOrderStatus.PENDING,
            AccountName = "test",
            AccountNo = "123456",
            BankCode = "CCB",
            BankName = "",
            OrderTime = DateTime.Now,
            TransferFee = 0.5m,
            TotalAmount = 1,
            TransferOrderNo = command.TransferOrderNo
        };
        var dbContext = GetNewDbContext();
        await dbContext.AddAsync(settlementPayOrder);
        await dbContext.SetTenantId(_tenantId).SaveChangesAsync();
        //act
        var service = GetService(dbContext, GetCapPublisher());

        await service.TransferOrderResultHandler(command);
        //assert
        var model = dbContext.SettlementPayOrders
            .IgnoreQueryFilters()
            .FirstOrDefault(x => x.TenantId == command.TenantId && x.TransferOrderNo == command.TransferOrderNo);
        var result = model.Status == (command.IsSuccess ? AccountPayOrderStatus.PENDING : AccountPayOrderStatus.FAIL);
        Assert.True(result);
    }

    class TransferOrderResultCommandData : IEnumerable<object[]>
    {
        public IEnumerator<object[]> GetEnumerator()
        {
            yield return new object[] { new  TransferOrderResultMessage{
                        TransferOrderNo = 11111,
                        TenantId = _tenantId,
                        ExtOrderNo = "123456",
                        FailReason = "",
                        IsSuccess = true
                    }
                };
            yield return new object[] { new  TransferOrderResultMessage{
                        TransferOrderNo = 222222,
                        TenantId = _tenantId,
                        ExtOrderNo = "123456",
                        FailReason = "",
                        IsSuccess = false
                    }
                };
        }

        IEnumerator IEnumerable.GetEnumerator() => GetEnumerator();
    }

    [Theory(DisplayName = "付款供应商结果处理")]
    [ClassData(typeof(AccountPayOrderResultCommandData))]
    public async Task AccountPayOrderResultHandler(AccountPayOrderResultMessage command)
    {
        //arrange
        SettlementPayOrder settlementPayOrder = new()
        {
            Status = AccountPayOrderStatus.PENDING,
            AccountName = "test",
            AccountNo = "123456",
            BankCode = "CCB",
            BankName = "",
            OrderTime = DateTime.Now,
            TransferFee = 0.5m,
            TotalAmount = 1,
            PayOrderNo = command.PayOrderNo
        };
        var dbContext = GetNewDbContext();
        await dbContext.AddAsync(settlementPayOrder);
        await dbContext.SetTenantId(_tenantId).SaveChangesAsync();
        //act
        var service = GetService(dbContext, GetCapPublisher());

        await service.AccountPayOrderResultHandler(command);
        //assert
        var model = dbContext.SettlementPayOrders
            .IgnoreQueryFilters()
            .FirstOrDefault(x => x.TenantId == command.TenantId && x.PayOrderNo == command.PayOrderNo);
        var result = model.Status == (command.IsSuccess ? AccountPayOrderStatus.SUCCESS : AccountPayOrderStatus.FAIL);
        Assert.True(result);
    }

    class AccountPayOrderResultCommandData : IEnumerable<object[]>
    {
        public IEnumerator<object[]> GetEnumerator()
        {
            yield return new object[] {
                new AccountPayOrderResultMessage{
                    DebitAmount=1.5m,
                    Fee=0.5m,
                    FailReason="",
                    IsSuccess=true,
                    PayOrderNo=111111,
                    ReceiveAmount=1m,
                    TenantId=_tenantId
                }
            };
            yield return new object[] {
                new AccountPayOrderResultMessage{
                    DebitAmount=1.5m,
                    Fee=0.5m,
                    FailReason="",
                    IsSuccess=false,
                    PayOrderNo=111111,
                    ReceiveAmount=1m,
                    TenantId=_tenantId
                }
            };
        }

        IEnumerator IEnumerable.GetEnumerator() => GetEnumerator();
    }
}
