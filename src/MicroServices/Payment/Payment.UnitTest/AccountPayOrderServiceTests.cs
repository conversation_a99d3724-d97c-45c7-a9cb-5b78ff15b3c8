using Cit.Payment.Yeepay.Service;
using Cit.Payment.Yeepay.Service.Request.Account;
using Cit.Payment.Yeepay.Service.Response;
using Cit.Payment.Yeepay.Service.Response.Account;
using Contracts.Common.Payment.DTOs;
using Contracts.Common.Payment.Enums;
using Contracts.Common.Payment.Messages;
using DotNetCore.CAP;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using Moq;
using Payment.Api.ConfigModel;
using Payment.Api.Infrastructure;
using Payment.Api.Model;
using Payment.Api.Services;
using Payment.Api.Services.Interfaces;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using UnitTest.Base;
using Xunit;

namespace Payment.UnitTest
{
    public class AccountPayOrderServiceTests : TestBase<CustomDbContext>
    {
        private static readonly long _tenantId = 1;
        public static AccountPayOrderService GetService(CustomDbContext dbContext,
           IAccountService accountService,
           IYeeConfigService yeeConfigService,
           IOptions<YeePaySetting> yeePaySetting,
           ICapPublisher capPublisher)
        {
            return new AccountPayOrderService(dbContext,
                accountService,
                yeeConfigService,
                yeePaySetting,
                capPublisher);
        }

        [Fact(DisplayName = "付款下单_成功")]
        public async Task PayOrderTest_Success()
        {
            //arrange
            var dbContext = GetNewDbContext();
            var accountService = new Mock<IAccountService>();
            accountService.Setup(x => x.PayOrder(It.IsAny<PayOrderRequest>()))
                .ReturnsAsync(new YopResponse<PayOrderResponse>
                {
                    Result = new PayOrderResponse
                    {
                        ReturnCode = "UA00000",
                        ReturnMsg = "",
                        OrderNo = "123456",
                        RequestNo = "1",
                        Status = "REQUEST_ACCEPT"
                    }
                });
            var yeePaySetting = new Mock<IOptions<YeePaySetting>>();
            yeePaySetting.Setup(x => x.Value).Returns(new YeePaySetting { YeePayOrderNotifyUri = "http://localhost" });
            var capPublisher = GetCapPublisher();
            var yeeConfigService = new Mock<IYeeConfigService>();
            yeeConfigService.Setup(x => x.GetYeeConfig())
                .ReturnsAsync(new YeeConfig
                {
                    ParentMetchantNo = "123456"
                });
            //act
            var service = GetService(dbContext,
                accountService.Object,
                yeeConfigService.Object,
                yeePaySetting.Object,
                capPublisher);
            var receive = new AccountPayOrderMessage
            {
                TenantId = _tenantId,
                PayOrderNo = 1,
                ReceiverAccountName = "张三",
                ReceiverAccountNo = "***********",
                ReceiverBankCode = "ICBC",
                OrderAmount = 1,
                Comments = "银行附言"
            };
            await service.PayOrder(receive);
            //assert
            var payOrder = await dbContext.AccountPayOrders
                .IgnoreQueryFilters()
                .FirstOrDefaultAsync(x => x.PayOrderNo == receive.PayOrderNo);
            Assert.True(payOrder.Status == AccountPayOrderStatus.REQUEST_ACCEPT);
        }

        [Fact(DisplayName = "付款下单结果处理_成功")]
        public async Task PayOrderResultTest_Success()
        {
            //arrange
            var dbContext = GetNewDbContext();
            AccountPayOrder accountPayOrder = new()
            {
                PayOrderNo = 111,
                BankAccountType = BankAccountType.DEBIT_CARD,
                Comments = "",
                FeeChargeSide = FeeChargeSide.PAYEE,
                OrderAmount = 1,
                Status = AccountPayOrderStatus.REQUEST_RECEIVE,
                ReceiverAccountName = "",
                ReceiverAccountNo = "",
                ReceiverBankCode = "CCB",
                OrderTime = DateTime.Now
            };
            await dbContext.AddAsync(accountPayOrder);
            await dbContext.SetTenantId(_tenantId).SaveChangesAsync();

            var accountService = new Mock<IAccountService>();
            var yeePaySetting = new Mock<IOptions<YeePaySetting>>();
            yeePaySetting.Setup(x => x.Value).Returns(new YeePaySetting { YeePayOrderNotifyUri = "http://localhost" });
            var capPublisher = GetCapPublisher();
            var yeeConfigService = new Mock<IYeeConfigService>();
            yeeConfigService.Setup(x => x.GetYeeConfig())
                .ReturnsAsync(new YeeConfig
                {
                    ParentMetchantNo = "123456"
                });
            //act
            var service = GetService(dbContext,
                accountService.Object,
                yeeConfigService.Object,
                yeePaySetting.Object,
                capPublisher);
            AccountPayOrderResultInput input = new()
            {
                Status = AccountPayOrderStatus.SUCCESS,
                PayOrderNo = accountPayOrder.PayOrderNo,
                Fee = 0.1m,
                FailReason = "",
                DebitAmount = 1,
                ReceiveAmount = 0.9m,
                FinishTime = DateTime.Now,
                IsReversed = false,
                OrderNo = "123456"
            };
            await service.AccountPayOrderResult(input);
            //assert
            var payOrder = await dbContext.AccountPayOrders
                .IgnoreQueryFilters()
                .FirstOrDefaultAsync(x => x.PayOrderNo == input.PayOrderNo);

            Assert.True(payOrder.Status == input.Status);
        }

    }
}
