using Contracts.Common.Payment.DTOs;
using Contracts.Common.Payment.Enums;
using Contracts.Common.Payment.Messages;
using DotNetCore.CAP;
using Microsoft.EntityFrameworkCore;
using Moq;
using Payment.Api.Infrastructure;
using Payment.Api.Model;
using Payment.Api.Services;
using Payment.Api.Services.Interfaces;
using System;
using System.Linq;
using System.Threading.Tasks;
using UnitTest.Base;
using Xunit;

namespace Payment.UnitTest
{
    public class DarenWithdrawalsServiceTests : TestBase<CustomDbContext>
    {
        private readonly static long _tenantId = 1;
        private static DarenWithdrawalsService GetService(CustomDbContext dbContext,
            ICapPublisher capPublisher, IAccountInfoService accountInfoService = null)
        {
            return new DarenWithdrawalsService(dbContext, capPublisher, accountInfoService);
        }

        [Fact(DisplayName = "达人提现申请_成功")]
        public async Task WithdrawalApply_Success()
        {
            //arrange
            var dbContext = GetNewDbContext();
            var capPublisher = GetCapPublisher();
            var accountInfoService = new Mock<IAccountInfoService>();
            accountInfoService.Setup(x => x.GetTenantAccountInfo(It.IsAny<long>()))
                .ReturnsAsync(new TenantAccountInfoOutput
                {
                    FundAccountBalance = 1.1m
                });
            //act
            var service = GetService(dbContext, capPublisher, accountInfoService.Object);
            DarenWithdrawalApplyMessage receive = new()
            {
                AccountName = "",
                AccountNumber = "",
                Amount = 1,
                BankCode = "ICBC",
                InvoiceAmount = 0.1m,
                TenantId = _tenantId,
                WithdrawApplyId = 123456
            };
            await service.WithdrawalApply(receive);
            await dbContext.SaveChangesAsync();
            //asssert
            var darenWithdrawal = await dbContext.DarenWithdrawals
                .IgnoreQueryFilters()
                .Where(x => x.WithdrawApplyId == receive.WithdrawApplyId)
                .FirstOrDefaultAsync();
            Assert.True(darenWithdrawal.WithdrawalStatus == WithdrawalStatus.Processing);
            Assert.True(darenWithdrawal.PayOrderNo > 0);
        }

        [Fact(DisplayName = "达人提现结果处理_成功")]
        public async Task DarenWithdrawalResult_Test()
        {
            //arrange
            var dbContext = GetNewDbContext();
            DarenWithdrawal darenWithdrawal = new()
            {
                WithdrawApplyId = 1,
                WithdrawalStatus = WithdrawalStatus.Processing,
                AccountName = "",
                AccountNumber = "",
                Amount = 1,
                BankCode = "ICBC",
                CreateTime = DateTime.Now,
                InvoiceAmount = 0.1M,
                PayOrderNo = 111,
            };
            await dbContext.AddAsync(darenWithdrawal);
            await dbContext.SetTenantId(_tenantId).SaveChangesAsync();

            var capPublisher = GetCapPublisher();
            //act
            var service = GetService(dbContext, capPublisher);
            AccountPayOrderResultMessage command = new()
            {
                PayOrderNo = darenWithdrawal.PayOrderNo,
                FailReason = "",
                Fee = 0.5M,
                IsSuccess = true,
                TenantId = _tenantId
            };
            await service.DarenWithdrawalResult(command);
            //asssert
            Assert.True(darenWithdrawal.WithdrawalStatus == WithdrawalStatus.Succeed);
            Assert.True(darenWithdrawal.TransferOrderNo > 0);
        }
    }
}
