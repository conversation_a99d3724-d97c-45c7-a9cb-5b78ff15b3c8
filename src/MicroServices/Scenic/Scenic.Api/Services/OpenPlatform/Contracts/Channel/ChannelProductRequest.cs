using Contracts.Common.Scenic.Enums;

namespace Scenic.Api.Services.OpenPlatform.Contracts.Channel;

public class ChannelProductRequest
{
    
}

#region 价库同步

/// <summary>
/// 价库同步
/// </summary>
public class ChannelPriceStockUploadRequest
{
    /// <summary>
    /// true-增量更新，false-全量更新。不传默认为“false-全量更新”
    /// </summary>
    public bool IsCreUpdate { get; set; }
    
    /// <summary>
    /// alitrip/ctrip/meituan
    /// </summary>
    public string OtaType { get; set; }

    /// <summary>
    /// ticket/line
    /// </summary>
    public string ProductType { get; set; }

    /// <summary>
    /// 供应商产品ID
    /// [飞猪]基本信息的商家编码 / [美团]基本信息的供应商商品ID / [携程]套餐的供应商资源id
    /// </summary>
    public string OutProductId { get; set; }

    /// <summary>
    /// OTA商品id
    /// </summary>
    public string OtaProductId { get; set; }

    /// <summary>
    /// OTASkuId
    /// <remarks>
    /// <para>[飞猪] : 门票区域门票ticketArea</para>
    /// </remarks>
    /// </summary>
    public string OtaSkuId { get; set; }

    /// <summary>
    /// 排期数据类型
    /// </summary>
    public OpenChannelPriceStockUploadScheduleType ScheduleType { get; set; }
    
    /// <summary>
    /// 时间表
    /// </summary>
    public List<ChannelPriceStockUploadSchedulesItem> Schedules { get; set; }
}

public class ChannelPriceStockUploadSchedulesItem
{
    /// <summary>
    /// 商品编码
    /// </summary>
    public string OutSkuId { get; set; }

    /// <summary>
    /// 格式：2016-12-10 00:00:00
    /// </summary>
    public string Date { get; set; }

    /// <summary>
    /// 单位：分，必须大于0。
    /// </summary>
    public int Price { get; set; }

    /// <summary>
    /// 库存必须大于等于0 （0表示不可售）
    /// </summary>
    public int Stock { get; set; }
}

#endregion