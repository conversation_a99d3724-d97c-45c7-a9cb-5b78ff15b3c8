using AutoMapper;
using Contracts.Common.Scenic.DTOs.TicketsCombination;
using EfCoreExtensions.Abstract;

namespace Scenic.Api.Services.MappingProfiles;

public class TicketsCombinationProfiles : Profile
{
    public TicketsCombinationProfiles()
    {
        CreateMap<TicketsCombination, SearchTicketsCombinationOutput>();
        CreateMap<PagingModel<TicketsCombination>, PagingModel<SearchTicketsCombinationOutput>>();
        CreateMap<TicketsCombinationSettingInfo, TicketsCombinationSetting>();
        CreateMap<AddTicketsCombinationInput, TicketsCombination>();
        CreateMap<EditTicketsCombinationInput, TicketsCombination>();
        CreateMap<TicketsCombinationSetting, SearchTicketsCombinationSettingInfo>();
        CreateMap<TicketsCombination, TicketsCombinationDetailOutput>();
        CreateMap<TicketsCombination, DetailTicketsCombinationSettingInfo>();
    }
}
