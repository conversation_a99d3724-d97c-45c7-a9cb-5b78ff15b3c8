using AutoMapper;
using Cit.Storage.Redis;
using Common.Swagger;
using Contracts.Common.Product.Enums;
using Contracts.Common.Scenic.DTOs;
using Contracts.Common.Scenic.DTOs.ScenicSpot;
using Contracts.Common.Scenic.DTOs.Ticket;
using Contracts.Common.Scenic.DTOs.TicketsCalendarPrice;
using Contracts.Common.Scenic.DTOs.TicketTimeSlot;
using Contracts.Common.Scenic.Enums;
using Contracts.Common.Scenic.Messages;
using DotNetCore.CAP;
using EfCoreExtensions.Abstract;
using Microsoft.AspNetCore.Mvc;
using Scenic.Api.Services.Interfaces;
using static System.Net.HttpStatusCode;
using Contracts.Common.Order.Enums;
using Contracts.Common.Scenic.DTOs.OpenChannel;
using Contracts.Common.Scenic.DTOs.TicketChannelSetting;
using Contracts.Common.Scenic.DTOs.TicketSupplierSetting;
using Contracts.Common.Product.DTOs.ProductOperatorUser;

namespace Scenic.Api.Controllers;

[Route("[controller]/[action]")]
[ApiController]
public class TicketController : ControllerBase
{
    private readonly ITicketService _ticketService;
    private readonly IScenicSpotService _scenicService;
    private readonly IMapper _mapper;
    private readonly ITicketTimeSlotService _ticketTimeSlotService;
    private readonly IThirdCalendarPriceService _thirdCalendarPriceService;
    private readonly ICalendarPriceService _calendarPriceService;
    private readonly IBaseService _baseService;
    private readonly IRedisClient _redisClient;

    public TicketController(
        ITicketService ticketService,
        IScenicSpotService scenicService,
        IMapper mapper,
        ITicketTimeSlotService ticketTimeSlotService,
        IThirdCalendarPriceService thirdCalendarPriceService,
        ICalendarPriceService calendarPriceService,
        IBaseService baseService,
        IRedisClient redisClient)
    {
        _ticketService = ticketService;
        _scenicService = scenicService;
        _mapper = mapper;
        _ticketTimeSlotService = ticketTimeSlotService;
        _thirdCalendarPriceService = thirdCalendarPriceService;
        _calendarPriceService = calendarPriceService;
        _baseService = baseService;
        _redisClient = redisClient;
    }

    /// <summary>
    /// 分页查询景点门票分页基础数据
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ProducesResponseType(typeof(PagingModel<SearchTicketsOutput>), (int)OK)]
    public async Task<IActionResult> Search(SearchTicketsInput input)
    {
        var result = await _ticketService.Search(input);
        return Ok(result);
    }

    /// <summary>
    /// 查询门票列表
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ProducesResponseType(typeof(IEnumerable<TicketListQueryOutput>), 200)]
    public async Task<IActionResult> QueryTicketList(TicketListQueryInput input)
    {
        var result = await _ticketService.QueryTicketList(input);
        return Ok(result);
    }

    /// <summary>
    /// 查询门票基础信息详情
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    [HttpGet]
    [ProducesResponseType(typeof(GetTicketDetailOutput), (int)OK)]
    public async Task<IActionResult> Detail(long id)
    {
        var result = await _ticketService.Detail(id);
        return Ok(result);
    }

    /// <summary>
    /// 添加门票基础数据
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ProducesResponseType(typeof(AddTicketOutput), (int)OK)]
    [SwaggerResponseExt(default, ErrorTypes.Common.ResourceInvalid)]
    public async Task<IActionResult> Add(AddTicketInput input)
    {
        var result = await _ticketService.Add(input);
        return Ok(result);
    }

    /// <summary>
    /// 编辑门票基础数据
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ProducesResponseType(typeof(EditTicketOutput), (int)OK)]
    [SwaggerResponseExt(default, ErrorTypes.Common.ResourceInvalid)]
    [SwaggerResponseExt(default, ErrorTypes.Tenant.SupplierInvalid)]
    public async Task<IActionResult> Edit(EditTicketInput input)
    {
        var result = await _ticketService.Edit(input);
        return Ok(result);
    }


    /// <summary>
    /// 设置供应商配置
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ProducesResponseType(typeof(SetTicketSupplierSettingOutput), (int)OK)]
    public async Task<IActionResult> SetSupplierSetting(SetTicketSupplierSettingInput input)
    {
        var result = await _ticketService.SetSupplierSetting(input);
        return Ok(result);
    }

    /// <summary>
    /// 查询供应商配置
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    [HttpGet]
    [ProducesResponseType(typeof(GetTicketSupplierSettingOutput), (int)OK)]
    public async Task<IActionResult> GetSupplierSetting(long id)
    {
        var result = await _ticketService.GetSupplierSetting(id);
        return Ok(result);
    }

    /// <summary>
    /// 设置渠道配置
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ProducesResponseType(typeof(SetTicketChannelSettingOutput), (int)OK)]
    public async Task<IActionResult> SetChannelSetting(SetTicketChannelSettingInput input)
    {
        var result = await _ticketService.SetChannelSetting(input);
        return Ok(result);
    }


    /// <summary>
    /// 查询渠道配置
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    [HttpGet]
    [ProducesResponseType(typeof(GetTicketChannelSettingOutput), (int)OK)]
    public async Task<IActionResult> GetChannelSetting(long id)
    {
        var result = await _ticketService.GetChannelSetting(id);
        return Ok(result);
    }


    /// <summary>
    /// 门票关联api对接供应商，则同步第三方价格库存
    /// </summary>
    /// <param name="result"></param>
    /// <returns></returns>
    [HttpPost]
    public async Task SyncThirdPriceInventory(SyncThirdPriceInventoryInput result)
    {
        if (result.OpenSupplierType != OpenSupplierType.System)
        {
            var openSupplierType = result.OpenSupplierType.ToString().ToLowerInvariant();
            var lockSecret = Guid.NewGuid().ToString();
            var lockKey = $"sync:{openSupplierType}:{result.SkuId}";
            try
            {
                await _redisClient.LockTakeWaitingAsync(lockKey, lockSecret, TimeSpan.FromSeconds(10));
                await _thirdCalendarPriceService.SyncThirdPriceInventory(new SyncThirdPriceInventoryInput
                {
                    TicketId = result.TicketId,
                    SkuId = result.SkuId,
                    OpenSupplierType = result.OpenSupplierType,
                    EditTicketSyncInfo = result.EditTicketSyncInfo
                });
                await _calendarPriceService.UpdateThirdTicketMinPrice(new SetThirdTicketMinPriceInput
                {
                    TicketIds = new List<long> { result.TicketId }
                });
            }
            finally
            {
                await _redisClient.LockReleaseAsync(lockKey, lockSecret);
            }
        }
    }


    /// <summary>
    /// 删除门票
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    [HttpPost]
    [ProducesResponseType((int)OK)]
    public async Task<IActionResult> Delete(DeleteTicketInput input)
    {
        await _ticketService.Delete(input);
        return Ok();
    }

    /// <summary>
    /// 门票上下架状态切换
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ProducesResponseType((int)OK)]
    [SwaggerResponseExt(default, ErrorTypes.Common.ResourceInvalid)]
    [SwaggerResponseExt(default, ErrorTypes.Tenant.SupplierInvalid)]
    public async Task<IActionResult> SwitchEnabled(SwitchTicketEnabledInput input)
    {
        await _ticketService.SwitchEnabled(input);
        return Ok();
    }

    /// <summary>
    /// 通过景区Id列表获取门票信息(前端对接完移除)
    /// </summary>
    /// <param name="ids"></param>
    /// <returns></returns>
    [HttpPost]
    [Obsolete]
    [ProducesResponseType(typeof(IEnumerable<TicketListQueryOutput>), (int)OK)]
    public async Task<IActionResult> SearchByScenicIds(List<long> ids)
    {
        var result = await _ticketService.QueryTicketList(new TicketListQueryInput
        {
            ScenicSpotIds = ids
        });
        return Ok(result);
    }

    /// <summary>
    /// 通过景区Id获取门票信息(前端对接完移除)
    /// </summary>
    /// <param name="ids"></param>
    /// <returns></returns>
    [HttpGet]
    [Obsolete]
    [ProducesResponseType(typeof(TicketListQueryOutput), (int)OK)]
    public async Task<IActionResult> SearchByScenicId(long id)
    {
        var result = await _ticketService.QueryTicketList(new TicketListQueryInput
        {
            ScenicSpotIds = new List<long> { id }
        });
        return Ok(result.FirstOrDefault());
    }

    [HttpGet]
    [ProducesResponseType(typeof(TicketListQueryOutput), (int)OK)]
    public async Task<IActionResult> MallGetByScenicId(long id)
    {
        var scenicTickets = await _ticketService.QueryTicketList(
            new TicketListQueryInput
            {
                ScenicSpotIds = new List<long> { id }
            });
        var scenicTicket = scenicTickets.FirstOrDefault();
        if (scenicTicket.TicketInfos.Any() is false) return Ok(scenicTicket);
        var result = new SearchByScenicIdOutput
        {
            ScenicSpotId = scenicTicket.ScenicSpotId
        };
        var ticketInfos = new List<TicketSimpleInfo>();
        foreach (var item in scenicTicket.TicketInfos
                     .Where(x => x.PriceInventorySource == PriceInventorySource.System)) //因为价库同步产品没有微商城售卖渠道价格
        {
            //过滤掉过期期票
            if (item is
                {
                    TicketsType: ScenicTicketsType.PromissoryNote,
                    PriceInventoryType: PriceInventoryType.General,
                    ValidityType: ProductValidityType.DesignatedDate
                })
            {
                if (item.ValidityEnd < DateTime.Now) continue;
            }
            ticketInfos.Add(item);
        }

        result.TicketInfos = ticketInfos;
        return Ok(result);
    }


    [HttpPost]
    [ProducesResponseType(typeof(List<TicketSimpleInfo>), (int)OK)]
    public async Task<IActionResult> GetByIds(params long[] ids)
    {
        var tickets = await _ticketService.GetByIds(new GetTicketsByIdsInput
        {
            TicketIds = ids,
            IsValidity = false
        });
        var result = _mapper.Map<List<TicketSimpleInfo>>(tickets);
        var ticketTimeSlotInfos = await _ticketTimeSlotService.GetByIds(new GetTicketTimeSlotInput
        {
            TicketIds = ids
        });
        foreach (var item in result)
        {
            item.TimeSlotInfos = ticketTimeSlotInfos.Where(x => x.TicketId == item.Id)
                .ToList();
        }
        return Ok(result);
    }

    [HttpPost]
    [ProducesResponseType(typeof(PagingModel<GetScenicAndTicketsOutput>), (int)HttpStatusCode.OK)]
    public async Task<IActionResult> GetScenicAndTickets(GetScenicAndTicketsInput input)
    {
        var result = await _ticketService.GetScenicAndTickets(input);
        if (result.Data.Any())
        {
            //处理时段
            var ticketIds = result.Data
                .Where(x => x.PriceInventoryType == PriceInventoryType.TimeSlotCalendar)
                .Select(x => x.TicketsId).ToList();
            var timeSlotInfos = await _ticketTimeSlotService.GetByIds(new GetTicketTimeSlotInput
            {
                TicketIds = ticketIds
            });

            foreach (var item in result.Data)
            {
                item.CostPrice = item.SupplierId > 0 ? item.CostPrice : null;

                var itemTimeSlotInfos = timeSlotInfos.Where(x => x.TicketId == item.TicketsId)
                    .ToList();
                item.TimeSlotInfos = itemTimeSlotInfos;
            }

        }
        return Ok(result);
    }

    [HttpPost]
    [ProducesResponseType(typeof(List<GetScenicAndTicketsOutput>), (int)HttpStatusCode.OK)]
    public async Task<IActionResult> GetScenicAndTicketsByIds(params long[] ids)
    {
        var result = await _ticketService.GetScenicAndTicketsByIds(ids);
        //处理api价库的价格
        var apiPriceSourceTicketIds = result
            .Where(x => x.PriceInventorySource != PriceInventorySource.System)
            .Select(x => x.TicketsId)
            .ToList();
        if (apiPriceSourceTicketIds.Any())
        {
            var recentPrices = await _calendarPriceService.GetRecentMinOrMaxPrice(
                new GetRecentMinOrMaxPriceInput
                {
                    TicketIds = apiPriceSourceTicketIds,
                    PriceChannelType = CalendarPriceChannelType.B2BWeb,
                    InspectionForSale = false
                });
            foreach (var item in result.Where(x => x.PriceInventorySource != PriceInventorySource.System))
            {
                var recentPrice = recentPrices.FirstOrDefault(x => x.TicketId == item.TicketsId);
                item.SellingPrice = recentPrice?.MinPrice;
                item.CostPrice = item.SupplierId > 0 ? recentPrice?.MinCostPrice : null;
            }
        }
        return Ok(result);
    }

    /// <summary>
    /// 设置B2B售卖状态
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ProducesResponseType((int)OK)]
    public async Task<IActionResult> SetB2bSellingStatus(SetB2bSellingStatusInput input)
    {
        await _ticketService.SetB2bSellingStatus(input);
        return Ok();
    }

    [HttpPost]
    [ProducesResponseType(typeof(List<TicketTimeSlotInfo>), (int)OK)]
    public async Task<IActionResult> GetTimeSlots(GetTimeSlotsInput input)
    {
        var result = await _ticketService.GetTimeSlots(input);
        return Ok(result);
    }

    /// <summary>
    /// 通用库存查询
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ProducesResponseType(typeof(List<GenericScenicInventoryQueryOutput>), 200)]
    public async Task<IActionResult> GenericScenicInventoryQuery(GenericScenicInventoryQueryInput input)
    {
        var result = await _baseService.GenericScenicInventoryQuery(input);
        return Ok(result);
    }

    /// <summary>
    /// 开放平台-渠道产品校验
    /// </summary>
    /// <param name="channelProductSkuId"></param>
    /// <returns></returns>
    [HttpPost]
    [ProducesResponseType(typeof(List<OpenChannelCheckProductOutput>), 200)]
    public async Task<IActionResult> CheckOpenChannelProduct(params long[] channelProductSkuIds)
    {
        var result = await _ticketService.CheckOpenChannelProduct(channelProductSkuIds);
        return Ok(result);
    }

    /// <summary>
    /// 开放平台-查询产品库存轮询规则信息
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ProducesResponseType(typeof(List<OpenSupplierQueryProductStockPollOutput>), 200)]
    public async Task<IActionResult> QueryProductStockPoll(OpenSupplierQueryProductStockPollInput input)
    {
        var result = await _ticketService.QueryProductStockPoll(input);
        return Ok(result);
    }

    [HttpPost]
    public async Task<IActionResult> UpdateCostDiscountRate(UpdateTicketCostDiscountRateInput input)
    {
        await _ticketService.UpdateCostDiscountRate(input);
        return Ok();
    }

    [HttpGet]
    [ProducesResponseType(typeof(GetCompensationTicketOutput), 200)]
    public async Task<IActionResult> GetCompensationTicket()
    {
        var result = await _ticketService.GetCompensationTicket();
        return Ok(result);
    }

    /// <summary>
    /// 批量修改运营人、产品人
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ProducesResponseType((int)OK)]
    public async Task<IActionResult> BatchUpdateOperatorUser(BatchUpdateOperatorUserInput input)
    {
        await _ticketService.BatchUpdateOperatorUser(input);
        return Ok();
    }

    #region Agency Web

    /// <summary>
    /// 景点门票列表
    /// </summary>
    [HttpPost]
    [ProducesResponseType(typeof(PagingModel<AgencySearchOutput>), (int)HttpStatusCode.OK)]
    public async Task<IActionResult> AgencySearch(AgencySearchInput input)
    {
        var result = new PagingModel<AgencySearchOutput>();

        //查询景点分页列表
        var pageModel = await _scenicService.Search(new SearchScenicSpotsInput
        {
            PageIndex = input.PageIndex,
            PageSize = input.PageSize,
            ScenicSpotIds = input.ScenicSpotIds.ToList(),
            TicketIds = input.TicketIds,
            CityCode = input.CityCode,
            CountryCode = input.CountryCode,
            KeyWord = input.Name,
            Enabled = true,
            IsValidity = true
        });
        if (pageModel.Data.Any() is false) return Ok(result);

        //查询对应门票数据
        var newScenicSpotIds = pageModel.Data.Select(x => x.Id).ToList();
        var tickets = await _ticketService.GetByIds(new GetTicketsByIdsInput
        {
            TicketIds = input.TicketIds,
            ScenicSpotIds = newScenicSpotIds,
            Enable = true,
            IsValidity = true
        });
        var tags = await _scenicService.GetTags(newScenicSpotIds);
        var photos = await _scenicService.GetPhotos(newScenicSpotIds);

        result = new PagingModel<AgencySearchOutput>
        {
            Total = pageModel.Total,
            PageIndex = pageModel.PageIndex,
            PageSize = pageModel.PageSize,
            Data = pageModel.Data.Select(x =>
            {
                return new AgencySearchOutput
                {
                    ScenicSpotId = x.Id,
                    ScenicSpotName = x.Name,
                    OpeningTime = x.OpeningTime,
                    CountryName = x.CountryName,
                    ProviceName = x.ProvinceName,
                    CityName = x.CityName,
                    FirstPhoto = photos.FirstOrDefault(p => p.ScenicSpotId == x.Id)?.Path ?? "",
                    Tags = tags.FirstOrDefault(t => t.ScenicSpotId == x.Id)?.FeatureTags.Select(t => t.Name) ??
                           Enumerable.Empty<string>(),
                    Tickets = tickets.Where(t => t.ScenicSpotId == x.Id)
                        .Select(t => new AgencySearchTicketInfo
                        {
                            TicketId = t.Id,
                            TicketsType = t.TicketsType,
                            TicketName = t.Name,
                            NeedToExchange = t.NeedToExchange,
                            IsSupportRefund = t.IsSupportRefund,
                            ValidityType = t.ValidityType,
                            ValidityBegin = t.ValidityBegin,
                            ValidityEnd = t.ValidityEnd,
                            AfterPurchaseDays = t.AfterPurchaseDays,
                            CostPrice = t.CostPrice,
                            SellingPrice = t.SellingPrice,
                            CostCurrencyCode = t.CostCurrencyCode,
                            SaleCurrencyCode = t.SaleCurrencyCode,
                            PriceInventoryType = t.PriceInventoryType,
                            CredentialSourceType = t.CredentialSourceType
                        })
                };
            })
        };

        return Ok(result);
    }

    /// <summary>
    /// 查询门票详情
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    [HttpPost]
    [ProducesResponseType(typeof(List<AgencyGetOutput>), (int)HttpStatusCode.OK)]
    public async Task<IActionResult> AgencyGet(AgencyGetInput input)
    {
        var result = new List<AgencyGetOutput>();

        #region 查询门票信息

        var tickets = await _ticketService.GetByIds(new GetTicketsByIdsInput
        {
            TicketIds = input.TicketId,
            ScenicSpotIds = new[] { input.ScenicSpotId },
            Enable = true,
            IsValidity = input.IsValidity,
            TravelDate = input.TravelDate
        });
        if (tickets.Any() is false) return Ok(result);

        var ticketIds = tickets.Where(x => x.PriceInventoryType == PriceInventoryType.TimeSlotCalendar)
            .Select(x => x.Id).ToList();
        var ticketTimeSlotInfos = await _ticketTimeSlotService.GetByIds(new GetTicketTimeSlotInput
        {
            TicketIds = ticketIds
        });

        foreach (var ticket in tickets)
        {
            var item = _mapper.Map<AgencyGetOutput>(ticket);
            var dbEnum = (ExchangeProofType)Enum.ToObject(typeof(ExchangeProofType), ticket.ExchangeProof);
            item.ExchangeProofs = Enum.GetValues<ExchangeProofType>()
                .Where(x => (x & dbEnum) == x).ToList();

            item.TimeSlotInfos = ticketTimeSlotInfos.Where(x => x.TicketId == ticket.Id)
                .ToList();

            result.Add(item);
        }

        #endregion

        return Ok(result);
    }

    #endregion

    #region Vebk Web

    /// <summary>
    /// 查询门票下拉框（带景点条件）
    /// </summary>
    /// <param name="input"></param>
    [HttpPost]
    [ProducesResponseType(typeof(List<TicketSelectionByVebkOutput>), 200)]
    public async Task<IActionResult> SelectionByVebk(TicketSelectionByVebkInput input)
    {
        var result = await _ticketService.SelectionByVebk(input);
        return Ok(result);
    }
    #endregion

    #region CapSubscribe

    [NonAction]
    [CapSubscribe(CapTopics.Scenic.SetScenicTicketDisabled)]
    public async Task SetTicketDisabled(SetProductDisabledMessage receive)
    {
        await _ticketService.SetTicketDisabled(receive);
    }

    [NonAction]
    [CapSubscribe(CapTopics.Scenic.SetScenicTicketMinPrice)]
    public async Task SetTicketMinPrice(SetTicketMinPriceMessage receive)
    {
        await _ticketService.SetTicketMinPrice(receive);
    }
    #endregion
}