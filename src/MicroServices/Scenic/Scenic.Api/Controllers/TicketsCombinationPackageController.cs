using Contracts.Common.Scenic.DTOs.TicketsCombinationPackage;
using Microsoft.AspNetCore.Mvc;
using Scenic.Api.Services.Interfaces;

namespace Scenic.Api.Controllers;

[Route("[controller]/[action]")]
[ApiController]
public class TicketsCombinationPackageController : ControllerBase
{
    private readonly ITicketsCombinationPackageService _ticketsCombinationPackageService;
    public TicketsCombinationPackageController(
        ITicketsCombinationPackageService ticketsCombinationPackageService)
    {
        _ticketsCombinationPackageService = ticketsCombinationPackageService;
    }

    [HttpPost]
    [ProducesResponseType(typeof(List<GetTicketsCombinationPackageOutput>),200)]
    public async Task<IActionResult> Query(GetTicketsCombinationPackageInput input)
    {
        var result = await _ticketsCombinationPackageService.Query(input);
        return Ok(result);
    }

    [HttpPost]
    [ProducesResponseType(typeof(List<QueryTicketsCombinationPackageItemOutput>),200)]
    public async Task<IActionResult> QueryItems(QueryTicketsCombinationPackageItemInput input)
    {
        var result = await _ticketsCombinationPackageService.QueryItems(input);
        return Ok(result);
    }

    [HttpPost]
    public async Task<IActionResult> Update(UpdateTicketsCombinationPackageSettingInput input)
    {
        await _ticketsCombinationPackageService.Update(input);
        return Ok();
    }

    [HttpPost]
    public async Task<IActionResult> Delete(DeleteTicketsCombinationPackageInput input)
    {
        await _ticketsCombinationPackageService.Delete(input);
        return Ok();
    }
}