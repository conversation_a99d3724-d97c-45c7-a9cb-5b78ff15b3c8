using Common.Jwt;
using Contracts.Common.Scenic.DTOs.OpenSupplier;
using Microsoft.AspNetCore.Mvc;
using Scenic.Api.Services.Interfaces;

namespace Scenic.Api.Controllers;

[Route("[controller]/[action]")]
[ApiController]
public class ThirdTicketController : ControllerBase
{
    private readonly IThirdTicketService _thirdTicketService;
    
    public ThirdTicketController(
        IThirdTicketService thirdTicketService)
    {
        _thirdTicketService = thirdTicketService;
    }
    
    [HttpPost]
    [ProducesResponseType(typeof(QueryTenantIdListOutput),(int)HttpStatusCode.OK)]
    public async Task<IActionResult> QueryTenantIdList(QueryTenantIdListInput input)
    {
        var result = await _thirdTicketService.QueryTenantIdList(input);
        return Ok(result);
    }
}