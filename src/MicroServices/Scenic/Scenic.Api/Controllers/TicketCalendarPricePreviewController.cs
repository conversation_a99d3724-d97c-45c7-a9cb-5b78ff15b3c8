using Common.Swagger;
using Contracts.Common.Scenic.DTOs;
using Microsoft.AspNetCore.Mvc;
using Scenic.Api.Services.Interfaces;

namespace Scenic.Api.Controllers
{
    [Route("[controller]/[action]")]
    [ApiController]
    public class TicketCalendarPricePreviewController : ControllerBase
    {
        private readonly ICalendarPriceService _calendarPriceService;

        public TicketCalendarPricePreviewController(ICalendarPriceService calendarPriceService)
        {
            _calendarPriceService = calendarPriceService;
        }

        /// <summary>
        /// 获取预订票日历价格设置预览
        /// </summary>
        /// <param name="ticketsId"></param>
        /// <returns></returns>
        [HttpGet]
        [ProducesResponseType(typeof(IEnumerable<CalendarPricePreviewOutput>), (int)HttpStatusCode.OK)]
        public async Task<IActionResult> Search(long ticketsId)
        {
            var data = await _calendarPriceService.GetPreviews(new long[] { ticketsId });
            return Ok(data);
        }

        /// <summary>
        /// 获取预订票日历价格设置预览
        /// </summary>
        /// <param name="ticketsId"></param>
        /// <returns></returns>
        [HttpPost]
        [ProducesResponseType(typeof(IEnumerable<CalendarPricePreviewOutput>), (int)HttpStatusCode.OK)]
        public async Task<IActionResult> Search(long[] ticketIds)
        {
            var data = await _calendarPriceService.GetPreviews(ticketIds);
            return Ok(data);
        }

        /// <summary>
        /// 添加价格设置预览
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost]
        [SwaggerResponseExt(default, ErrorTypes.Scenic.PriceDuplicationOfDate)]
        public async Task<IActionResult> Add(CalendarPricePreviewInput input)
        {
            await _calendarPriceService.AddCalendarPricePreview(input);
            return Ok();
        }

        /// <summary>
        /// 更新价格设置预览
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost]
        [SwaggerResponseExt(default, ErrorTypes.Scenic.PriceDuplicationOfDate)]
        public async Task<IActionResult> Update(UpdateCalendarPricePreviewInput input)
        {
            await _calendarPriceService.UpdateCalendarPricePreview(input);
            return Ok();
        }

        /// <summary>
        /// 删除价格设置预览
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<IActionResult> Remove(RemoveCalendarPricePreviewInput input)
        {
            await _calendarPriceService.RemoveCalendarPricePreview(input);
            return Ok();
        }
    }
}
