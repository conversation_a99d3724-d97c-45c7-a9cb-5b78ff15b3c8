using EfCoreExtensions.Abstract;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Design;

namespace Scenic.Api.Infrastructure;

public class CustomDbContextContextDesignFactory : IDesignTimeDbContextFactory<CustomDbContext>
{
    public CustomDbContext CreateDbContext(string[] args)
    {
        var connectionString = "Server=47.106.23.65;Database=scenic;Uid=developer;Pwd=*****************;Character Set=utf8mb4;persist security info=True;";
        if (args.FirstOrDefault() == "Testing")
            connectionString = "Server=172.18.198.153;Database=scenic;Uid=developer;Pwd=saas@developer;Character Set=utf8mb4;persist security info=True;";
        var optionsBuilder = new DbContextOptionsBuilder<CustomDbContext>();
        optionsBuilder.UseMySql(connectionString, ServerVersion.AutoDetect(connectionString), option =>
        {
            option.UseNetTopologySuite();
        });
        return new CustomDbContext(optionsBuilder.Options, new TenantIdentify(1));
    }
}
