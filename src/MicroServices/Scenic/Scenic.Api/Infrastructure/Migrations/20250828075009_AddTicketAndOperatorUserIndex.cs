using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Scenic.Api.Infrastructure.Migrations
{
    public partial class AddTicketAndOperatorUserIndex : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateIndex(
                name: "IX_Tickets_TenantId_DevelopUserId_ScenicSpotId",
                table: "Tickets",
                columns: new[] { "TenantId", "DevelopUserId", "ScenicSpotId" });

            migrationBuilder.CreateIndex(
                name: "IX_Tickets_TenantId_SupplierId_ScenicSpotId",
                table: "Tickets",
                columns: new[] { "TenantId", "SupplierId", "ScenicSpotId" });

            migrationBuilder.CreateIndex(
                name: "IX_ProductOperatorUser_TenantId_ProductType_OperatorAssistantUs~",
                table: "ProductOperatorUser",
                columns: new[] { "TenantId", "ProductType", "OperatorAssistantUserId", "ProductId" });

            migrationBuilder.CreateIndex(
                name: "IX_ProductOperatorUser_TenantId_ProductType_OperatorUserId_Prod~",
                table: "ProductOperatorUser",
                columns: new[] { "TenantId", "ProductType", "OperatorUserId", "ProductId" });
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropIndex(
                name: "IX_Tickets_TenantId_DevelopUserId_ScenicSpotId",
                table: "Tickets");

            migrationBuilder.DropIndex(
                name: "IX_Tickets_TenantId_SupplierId_ScenicSpotId",
                table: "Tickets");

            migrationBuilder.DropIndex(
                name: "IX_ProductOperatorUser_TenantId_ProductType_OperatorAssistantUs~",
                table: "ProductOperatorUser");

            migrationBuilder.DropIndex(
                name: "IX_ProductOperatorUser_TenantId_ProductType_OperatorUserId_Prod~",
                table: "ProductOperatorUser");
        }
    }
}
