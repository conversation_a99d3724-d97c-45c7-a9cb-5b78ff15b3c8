using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Product.Api.Infrastructure.Migrations
{
    public partial class UpdateFieldV4 : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.UpdateData(
                table: "Fields",
                keyColumn: "Id",
                keyValue: 1281498705313329030L,
                column: "DefaultValue",
                value: "{\"address\":null,\"hasAddress\":false,\"detail\":null}");

            migrationBuilder.UpdateData(
                table: "Fields",
                keyColumn: "Id",
                keyValue: 1281498705313329064L,
                column: "DefaultValue",
                value: "{\"address\":null,\"hasAddress\":true,\"detail\":null}");

            migrationBuilder.UpdateData(
                table: "Fields",
                keyColumn: "Id",
                keyValue: 1281498705313388072L,
                column: "DefaultValue",
                value: "{\"address\":null,\"hasAddress\":true,\"detail\":null}");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.UpdateData(
                table: "Fields",
                keyColumn: "Id",
                keyValue: 1281498705313329030L,
                column: "DefaultValue",
                value: "{\"resourceType\":null,\"resourceSubType\":null,\"resourceZhName\":null,\"resourceEnName\":null,\"countryCode\":null,\"provinceCode\":null,\"cityCode\":null,\"relatedId\":null,\"countryZhName\":null,\"countryEnName\":null,\"provinceZhName\":null,\"provinceEnName\":null,\"cityZhName\":null,\"cityEnName\":null,\"iata\":null,\"icao\":null,\"highLightContent\":null,\"countryHighLightContent\":null,\"provinceHighLightContent\":null,\"cityHighLightContent\":null,\"iataHighLightContent\":null,\"icaoHighLightContent\":null,\"address\":null,\"longitude\":null,\"latitude\":null}");

            migrationBuilder.UpdateData(
                table: "Fields",
                keyColumn: "Id",
                keyValue: 1281498705313329064L,
                column: "DefaultValue",
                value: "{\"resourceType\":null,\"resourceSubType\":null,\"resourceZhName\":null,\"resourceEnName\":null,\"countryCode\":null,\"provinceCode\":null,\"cityCode\":null,\"relatedId\":null,\"countryZhName\":null,\"countryEnName\":null,\"provinceZhName\":null,\"provinceEnName\":null,\"cityZhName\":null,\"cityEnName\":null,\"iata\":null,\"icao\":null,\"highLightContent\":null,\"countryHighLightContent\":null,\"provinceHighLightContent\":null,\"cityHighLightContent\":null,\"iataHighLightContent\":null,\"icaoHighLightContent\":null,\"address\":null,\"longitude\":null,\"latitude\":null}");

            migrationBuilder.UpdateData(
                table: "Fields",
                keyColumn: "Id",
                keyValue: 1281498705313388072L,
                column: "DefaultValue",
                value: "{\"resourceType\":null,\"resourceSubType\":null,\"resourceZhName\":null,\"resourceEnName\":null,\"countryCode\":null,\"provinceCode\":null,\"cityCode\":null,\"relatedId\":null,\"countryZhName\":null,\"countryEnName\":null,\"provinceZhName\":null,\"provinceEnName\":null,\"cityZhName\":null,\"cityEnName\":null,\"iata\":null,\"icao\":null,\"highLightContent\":null,\"countryHighLightContent\":null,\"provinceHighLightContent\":null,\"cityHighLightContent\":null,\"iataHighLightContent\":null,\"icaoHighLightContent\":null,\"address\":null,\"longitude\":null,\"latitude\":null}");
        }
    }
}
