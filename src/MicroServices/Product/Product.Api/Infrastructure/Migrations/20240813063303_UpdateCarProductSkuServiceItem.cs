using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Product.Api.Infrastructure.Migrations
{
    public partial class UpdateCarProductSkuServiceItem : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "IsRequired",
                table: "CarProductSkuServiceItem");

            migrationBuilder.AddColumn<sbyte>(
                name: "ServiceOption",
                table: "CarProductSkuServiceItem",
                type: "tinyint",
                nullable: false,
                defaultValue: (sbyte)0);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "ServiceOption",
                table: "CarProductSkuServiceItem");

            migrationBuilder.AddColumn<bool>(
                name: "IsRequired",
                table: "CarProductSkuServiceItem",
                type: "tinyint(1)",
                nullable: false,
                defaultValue: false);
        }
    }
}
