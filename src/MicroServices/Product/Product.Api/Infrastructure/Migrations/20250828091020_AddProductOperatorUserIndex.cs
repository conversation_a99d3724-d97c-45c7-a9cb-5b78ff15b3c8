using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Product.Api.Infrastructure.Migrations
{
    public partial class AddProductOperatorUserIndex : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateIndex(
                name: "IX_ProductOperatorUser_TenantId_ProductType_OperatorAssistantUs~",
                table: "ProductOperatorUser",
                columns: new[] { "TenantId", "ProductType", "OperatorAssistantUserId", "ProductId" });

            migrationBuilder.CreateIndex(
                name: "IX_ProductOperatorUser_TenantId_ProductType_OperatorUserId_Prod~",
                table: "ProductOperatorUser",
                columns: new[] { "TenantId", "ProductType", "OperatorUserId", "ProductId" });
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropIndex(
                name: "IX_ProductOperatorUser_TenantId_ProductType_OperatorAssistantUs~",
                table: "ProductOperatorUser");

            migrationBuilder.DropIndex(
                name: "IX_ProductOperatorUser_TenantId_ProductType_OperatorUserId_Prod~",
                table: "ProductOperatorUser");
        }
    }
}
