// <auto-generated />
using System;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using NetTopologySuite.Geometries;
using Product.Api.Infrastructure;

#nullable disable

namespace Product.Api.Infrastructure.Migrations
{
    [DbContext(typeof(CustomDbContext))]
    [Migration("20250325061954_UpdateLineProduct_IsCompensation")]
    partial class UpdateLineProduct_IsCompensation
    {
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "6.0.5")
                .HasAnnotation("Relational:MaxIdentifierLength", 64);

            modelBuilder.Entity("Product.Api.Model.AgencyChannelPriceSettings", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<sbyte>("BasePriceType")
                        .HasColumnType("tinyint");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<sbyte>("HotelSaleType")
                        .HasColumnType("tinyint");

                    b.Property<bool>("IsApplyGroupRoom")
                        .HasColumnType("tinyint(1)");

                    b.Property<long>("PriceGroupId")
                        .HasColumnType("bigint");

                    b.Property<sbyte>("PriceSettingType")
                        .HasColumnType("tinyint");

                    b.Property<decimal>("PriceSettingValue")
                        .HasColumnType("decimal(18,2)");

                    b.Property<long>("ProductId")
                        .HasColumnType("bigint");

                    b.Property<sbyte>("ProductType")
                        .HasColumnType("tinyint");

                    b.Property<decimal?>("RedundantMinPrice")
                        .HasColumnType("decimal(18,2)");

                    b.Property<long>("SkuId")
                        .HasColumnType("bigint");

                    b.Property<int>("SkuSubClass")
                        .HasColumnType("int");

                    b.Property<long?>("SkuSubItemId")
                        .HasColumnType("bigint");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.HasIndex("TenantId", "PriceGroupId", "ProductType");

                    b.ToTable("AgencyChannelPriceSettings", (string)null);
                });

            modelBuilder.Entity("Product.Api.Model.CarHailingPoint", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<int>("CityCode")
                        .HasColumnType("int");

                    b.Property<string>("CityName")
                        .IsRequired()
                        .HasColumnType("varchar(50)");

                    b.Property<int>("CountryCode")
                        .HasColumnType("int");

                    b.Property<string>("CountryName")
                        .IsRequired()
                        .HasColumnType("varchar(50)");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<string>("PointName")
                        .IsRequired()
                        .HasColumnType("varchar(50)");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("CarHailingPoint", (string)null);
                });

            modelBuilder.Entity("Product.Api.Model.CarHailingProduct", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<sbyte>("CarHailingType")
                        .HasColumnType("tinyint");

                    b.Property<string>("Content")
                        .HasColumnType("mediumtext");

                    b.Property<string>("CostCurrencyCode")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("varchar(20)")
                        .HasDefaultValue("CNY");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<long>("DeparturePointId")
                        .HasColumnType("bigint");

                    b.Property<long>("DestinationPointId")
                        .HasColumnType("bigint");

                    b.Property<long?>("DevelopUserId")
                        .HasColumnType("bigint");

                    b.Property<bool>("Enabled")
                        .HasColumnType("tinyint(1)");

                    b.Property<string>("Instructions")
                        .HasColumnType("varchar(500)");

                    b.Property<bool>("IsSupportRefund")
                        .HasColumnType("tinyint(1)");

                    b.Property<string>("KindReminder")
                        .HasColumnType("varchar(500)");

                    b.Property<sbyte>("LanguageType")
                        .HasColumnType("tinyint");

                    b.Property<long?>("OperatorUserId")
                        .HasColumnType("bigint");

                    b.Property<int?>("RefundBeforeTravelDateDay")
                        .HasColumnType("int");

                    b.Property<decimal?>("RefundRate")
                        .HasColumnType("decimal(8,4)");

                    b.Property<TimeSpan?>("RefundTravelDateTime")
                        .HasColumnType("time");

                    b.Property<int>("ReservationDaysInAdvance")
                        .HasColumnType("int");

                    b.Property<string>("SaleCurrencyCode")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("varchar(20)")
                        .HasDefaultValue("CNY");

                    b.Property<DateTime?>("SellingDateBegin")
                        .HasColumnType("datetime");

                    b.Property<DateTime?>("SellingDateEnd")
                        .HasColumnType("datetime");

                    b.Property<long>("SupplierId")
                        .HasColumnType("bigint");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<string>("Title")
                        .IsRequired()
                        .HasColumnType("varchar(50)");

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("CarHailingProduct", (string)null);
                });

            modelBuilder.Entity("Product.Api.Model.CarHailingProductSku", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<long>("CarHailingProductId")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<bool>("Enabled")
                        .HasColumnType("tinyint(1)");

                    b.Property<string>("Images")
                        .IsRequired()
                        .HasColumnType("varchar(1000)");

                    b.Property<string>("Name")
                        .HasColumnType("varchar(50)");

                    b.Property<int>("Passengers")
                        .HasColumnType("int");

                    b.Property<int>("Seats")
                        .HasColumnType("int");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("CarHailingProductSku", (string)null);
                });

            modelBuilder.Entity("Product.Api.Model.CarHailingProductSkuCalendarPrice", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<long>("CarHailingProductId")
                        .HasColumnType("bigint");

                    b.Property<long>("CarHailingProductSkuId")
                        .HasColumnType("bigint");

                    b.Property<decimal>("CostPrice")
                        .HasColumnType("decimal(18,2)");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<DateTime>("Date")
                        .HasColumnType("datetime");

                    b.Property<decimal>("SalePrice")
                        .HasColumnType("decimal(18,2)");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("CarHailingProductSkuCalendarPrice", (string)null);
                });

            modelBuilder.Entity("Product.Api.Model.CarProduct", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<long?>("AirportId")
                        .HasColumnType("bigint");

                    b.Property<string>("AirportName")
                        .HasColumnType("varchar(255)");

                    b.Property<bool>("AutoConfirm")
                        .HasColumnType("tinyint(1)");

                    b.Property<sbyte>("B2bSellingStatusType")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("tinyint")
                        .HasDefaultValue((sbyte)0);

                    b.Property<string>("CancelRule")
                        .HasColumnType("varchar(1000)");

                    b.Property<sbyte>("CancelType")
                        .HasColumnType("tinyint");

                    b.Property<sbyte>("CarProductType")
                        .HasColumnType("tinyint");

                    b.Property<int>("CityCode")
                        .HasColumnType("int");

                    b.Property<string>("CityName")
                        .IsRequired()
                        .HasColumnType("varchar(128)");

                    b.Property<sbyte?>("CoordinateType")
                        .HasColumnType("tinyint");

                    b.Property<string>("CostCurrencyCode")
                        .IsRequired()
                        .HasColumnType("varchar(32)");

                    b.Property<int>("CountryCode")
                        .HasColumnType("int");

                    b.Property<string>("CountryName")
                        .IsRequired()
                        .HasColumnType("varchar(64)");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<long?>("DevelopUserId")
                        .HasColumnType("bigint");

                    b.Property<string>("EnTitle")
                        .HasColumnType("varchar(100)");

                    b.Property<bool>("Enabled")
                        .HasColumnType("tinyint(1)");

                    b.Property<string>("FeeExclude")
                        .HasColumnType("mediumtext");

                    b.Property<string>("FeeInclude")
                        .HasColumnType("mediumtext");

                    b.Property<string>("GooglePalceId")
                        .HasColumnType("varchar(100)");

                    b.Property<string>("Instructions")
                        .HasColumnType("mediumtext");

                    b.Property<Point>("Location")
                        .HasColumnType("point");

                    b.Property<string>("MobileDesc")
                        .HasColumnType("text");

                    b.Property<string>("PCDesc")
                        .HasColumnType("text");

                    b.Property<sbyte>("PurchaseSourceType")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("tinyint")
                        .HasDefaultValue((sbyte)1);

                    b.Property<int>("ReservationDaysInAdvance")
                        .HasColumnType("int");

                    b.Property<string>("SaleCurrencyCode")
                        .IsRequired()
                        .HasColumnType("varchar(32)");

                    b.Property<DateTime?>("SellingDateBegin")
                        .HasColumnType("datetime");

                    b.Property<DateTime?>("SellingDateEnd")
                        .HasColumnType("datetime");

                    b.Property<long>("SupplierId")
                        .HasColumnType("bigint");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<string>("Title")
                        .IsRequired()
                        .HasColumnType("varchar(50)");

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("CarProduct", (string)null);
                });

            modelBuilder.Entity("Product.Api.Model.CarProductSku", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<sbyte?>("AirportTransferType")
                        .HasColumnType("tinyint");

                    b.Property<bool>("B2bSellingStatus")
                        .HasColumnType("tinyint(1)");

                    b.Property<long>("CarProductId")
                        .HasColumnType("bigint");

                    b.Property<long>("CarTypeGradeId")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<string>("Description")
                        .HasColumnType("varchar(125)");

                    b.Property<bool>("Enabled")
                        .HasColumnType("tinyint(1)");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("tinyint(1)");

                    b.Property<int>("MaxBaggages")
                        .HasColumnType("int");

                    b.Property<int>("MaxPassengers")
                        .HasColumnType("int");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("varchar(50)");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime(6)");

                    b.HasKey("Id");

                    b.HasIndex("CarProductId");

                    b.HasIndex("CarTypeGradeId");

                    b.HasIndex("TenantId");

                    b.ToTable("CarProductSku", (string)null);
                });

            modelBuilder.Entity("Product.Api.Model.CarProductSkuCalendarPrice", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<long>("CarProductId")
                        .HasColumnType("bigint");

                    b.Property<long>("CarProductSkuId")
                        .HasColumnType("bigint");

                    b.Property<decimal>("CostPrice")
                        .HasColumnType("decimal(18,2)");

                    b.Property<DateTime>("Date")
                        .HasColumnType("datetime");

                    b.Property<decimal?>("MinProfit")
                        .HasColumnType("decimal(18,2)");

                    b.Property<sbyte>("PriceAdjustmentType")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("tinyint")
                        .HasDefaultValue((sbyte)0);

                    b.Property<decimal?>("PriceAdjustmentValue")
                        .HasColumnType("decimal(18,2)");

                    b.Property<sbyte>("PriceBasisType")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("tinyint")
                        .HasDefaultValue((sbyte)0);

                    b.Property<decimal>("SalePrice")
                        .HasColumnType("decimal(18,2)");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("UpdateTime")
                        .HasColumnType("datetime");

                    b.HasKey("Id");

                    b.HasIndex("CarProductSkuId");

                    b.HasIndex("Date");

                    b.HasIndex("TenantId");

                    b.ToTable("CarProductSkuCalendarPrice", (string)null);
                });

            modelBuilder.Entity("Product.Api.Model.CarProductSkuServiceItem", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<long>("CarProductSkuId")
                        .HasColumnType("bigint");

                    b.Property<long>("CarServiceItemId")
                        .HasColumnType("bigint");

                    b.Property<sbyte>("ServiceOption")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("tinyint")
                        .HasDefaultValue((sbyte)0);

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("CarProductSkuServiceItem", (string)null);
                });

            modelBuilder.Entity("Product.Api.Model.CarProductSkuServiceLanguage", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<long>("CarProductId")
                        .HasColumnType("bigint");

                    b.Property<long>("CarProductSkuId")
                        .HasColumnType("bigint");

                    b.Property<sbyte>("Language")
                        .HasColumnType("tinyint");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("CarProductSkuId");

                    b.HasIndex("TenantId");

                    b.ToTable("CarProductSkuServiceLanguage", (string)null);
                });

            modelBuilder.Entity("Product.Api.Model.CarServiceItem", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<bool>("B2bSellingStatus")
                        .HasColumnType("tinyint(1)");

                    b.Property<long>("CarProductId")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<string>("Description")
                        .HasColumnType("varchar(500)");

                    b.Property<bool>("Enabled")
                        .HasColumnType("tinyint(1)");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("tinyint(1)");

                    b.Property<bool>("IsNeedCharge")
                        .HasColumnType("tinyint(1)");

                    b.Property<string>("ItemName")
                        .IsRequired()
                        .HasColumnType("varchar(50)");

                    b.Property<int>("Maximum")
                        .HasColumnType("int");

                    b.Property<string>("PlatformMapKey")
                        .HasColumnType("varchar(100)");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime");

                    b.HasKey("Id");

                    b.HasIndex("CarProductId");

                    b.HasIndex("TenantId");

                    b.ToTable("CarServiceItem", (string)null);
                });

            modelBuilder.Entity("Product.Api.Model.CarServiceItemCalendarPrice", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<long>("CarProductId")
                        .HasColumnType("bigint");

                    b.Property<long>("CarServiceItemId")
                        .HasColumnType("bigint");

                    b.Property<decimal>("CostPrice")
                        .HasColumnType("decimal(18,2)");

                    b.Property<DateTime>("Date")
                        .HasColumnType("datetime");

                    b.Property<bool>("Enabled")
                        .HasColumnType("tinyint(1)");

                    b.Property<sbyte>("PriceAdjustmentType")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("tinyint")
                        .HasDefaultValue((sbyte)0);

                    b.Property<decimal?>("PriceAdjustmentValue")
                        .HasColumnType("decimal(18,2)");

                    b.Property<sbyte>("PriceBasisType")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("tinyint")
                        .HasDefaultValue((sbyte)0);

                    b.Property<decimal>("SalePrice")
                        .HasColumnType("decimal(18,2)");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("UpdateTime")
                        .HasColumnType("datetime");

                    b.HasKey("Id");

                    b.HasIndex("CarServiceItemId");

                    b.HasIndex("Date");

                    b.HasIndex("TenantId");

                    b.ToTable("CarServiceItemCalendarPrice", (string)null);
                });

            modelBuilder.Entity("Product.Api.Model.CarTypeGrade", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<string>("GradeName")
                        .IsRequired()
                        .HasColumnType("varchar(50)");

                    b.Property<int>("Seating")
                        .HasColumnType("int");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("CarTypeGrade", (string)null);
                });

            modelBuilder.Entity("Product.Api.Model.CarTypeGradePlatformMap", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<long>("CarTypeGradeId")
                        .HasColumnType("bigint");

                    b.Property<string>("PlatformMapKey")
                        .IsRequired()
                        .HasColumnType("varchar(128)");

                    b.Property<int>("SupplierApiType")
                        .HasColumnType("int");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("CarTypeGradePlatformMap", (string)null);
                });

            modelBuilder.Entity("Product.Api.Model.Disclaimer", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<string>("Content")
                        .HasColumnType("mediumtext");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<bool>("Enabled")
                        .HasColumnType("tinyint(1)")
                        .HasDefaultValue(true);

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<string>("Title")
                        .IsRequired()
                        .HasColumnType("varchar(50)");

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("Disclaimer", (string)null);
                });

            modelBuilder.Entity("Product.Api.Model.DisclaimerUsedDetails", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<long>("DisclaimerId")
                        .HasColumnType("bigint");

                    b.Property<long>("ProductId")
                        .HasColumnType("bigint");

                    b.Property<sbyte>("ProductType")
                        .HasColumnType("tinyint");

                    b.HasKey("Id");

                    b.ToTable("DisclaimerUsedDetails", (string)null);
                });

            modelBuilder.Entity("Product.Api.Model.Fields", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<string>("Append")
                        .HasColumnType("varchar(20)");

                    b.Property<bool?>("Controls")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("tinyint(1)")
                        .HasDefaultValue(false);

                    b.Property<string>("DefaultValue")
                        .HasColumnType("text");

                    b.Property<string>("EnPlaceholder")
                        .HasColumnType("varchar(250)");

                    b.Property<string>("EsResourceParentType")
                        .HasColumnType("varchar(500)");

                    b.Property<string>("FieldCode")
                        .IsRequired()
                        .HasColumnType("varchar(50)");

                    b.Property<string>("FieldComponentType")
                        .HasColumnType("varchar(50)");

                    b.Property<string>("FieldEnName")
                        .HasColumnType("varchar(100)");

                    b.Property<string>("FieldName")
                        .IsRequired()
                        .HasColumnType("varchar(100)");

                    b.Property<sbyte>("FieldType")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("tinyint")
                        .HasDefaultValue((sbyte)0);

                    b.Property<sbyte>("FieldsGroupType")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("tinyint")
                        .HasDefaultValue((sbyte)0);

                    b.Property<string>("Group")
                        .HasColumnType("varchar(64)");

                    b.Property<string>("GroupName")
                        .HasColumnType("varchar(20)");

                    b.Property<sbyte?>("GroupSort")
                        .HasColumnType("tinyint");

                    b.Property<bool?>("IsMultiple")
                        .HasColumnType("tinyint(1)");

                    b.Property<DateTime?>("MaxDate")
                        .HasColumnType("datetime(6)");

                    b.Property<decimal?>("Maxlength")
                        .HasColumnType("decimal(18,6)");

                    b.Property<DateTime?>("MinDate")
                        .HasColumnType("datetime(6)");

                    b.Property<decimal?>("MinLength")
                        .HasColumnType("decimal(18,6)");

                    b.Property<bool?>("Number")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("tinyint(1)")
                        .HasDefaultValue(false);

                    b.Property<string>("Placeholder")
                        .HasColumnType("varchar(100)");

                    b.Property<sbyte?>("Precision")
                        .HasColumnType("tinyint");

                    b.Property<bool?>("Radio")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("tinyint(1)")
                        .HasDefaultValue(false);

                    b.Property<string>("Remark")
                        .HasColumnType("varchar(100)");

                    b.Property<string>("RulerFilter")
                        .HasColumnType("text");

                    b.Property<bool?>("Textarea")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("tinyint(1)")
                        .HasDefaultValue(false);

                    b.Property<string>("UIExtend")
                        .HasColumnType("text");

                    b.Property<string>("Unit")
                        .HasColumnType("varchar(20)");

                    b.Property<string>("ValueRange")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("FieldCode")
                        .IsUnique();

                    b.ToTable("Fields", (string)null);

                    b.HasData(
                        new
                        {
                            Id = 1281498705313329020L,
                            Controls = false,
                            DefaultValue = "",
                            EnPlaceholder = "Please enter",
                            FieldCode = "Name",
                            FieldComponentType = "FieldInput",
                            FieldEnName = "Name",
                            FieldName = "姓名",
                            FieldType = (sbyte)0,
                            FieldsGroupType = (sbyte)0,
                            Maxlength = 60m,
                            Number = false,
                            Placeholder = "请输入",
                            Radio = false,
                            Textarea = false
                        },
                        new
                        {
                            Id = 1281498705313329021L,
                            Controls = false,
                            DefaultValue = "",
                            EnPlaceholder = "First name in Pinyin or English",
                            FieldCode = "FirstName",
                            FieldComponentType = "FieldInput",
                            FieldEnName = "English Name",
                            FieldName = "英文名",
                            FieldType = (sbyte)0,
                            FieldsGroupType = (sbyte)0,
                            Maxlength = 30m,
                            Number = false,
                            Placeholder = "名 拼音或英文",
                            Radio = false,
                            RulerFilter = "[{\"code\":null,\"name\":\"英文名\",\"msg\":\"请输入拼音或英文，多个词可空格隔开\",\"pattern\":\"[a-zA-Z]+[a-zA-Z ]*$\",\"enName\":\"English Name\",\"enMsg\":\"Please enter Pinyin or English, multiple words can be separated by spaces\",\"zhName\":\"英文名\",\"zhMsg\":\"请输入拼音或英文，多个词可空格隔开\"}]",
                            Textarea = false
                        },
                        new
                        {
                            Id = 1281498705313329022L,
                            Controls = false,
                            DefaultValue = "",
                            EnPlaceholder = "Last name pinyin or English",
                            FieldCode = "LastName",
                            FieldComponentType = "FieldInput",
                            FieldEnName = "English Surname",
                            FieldName = "英文姓",
                            FieldType = (sbyte)0,
                            FieldsGroupType = (sbyte)0,
                            Maxlength = 30m,
                            Number = false,
                            Placeholder = "姓 拼音或英文",
                            Radio = false,
                            RulerFilter = "[{\"code\":null,\"name\":\"英文姓\",\"msg\":\"请输入拼音或英文，多个词可空格隔开\",\"pattern\":\"[a-zA-Z]+[a-zA-Z ]*$\",\"enName\":\"English Surname\",\"enMsg\":\"Please enter pinyin or English, multiple words can be separated by spaces\",\"zhName\":\"英文姓\",\"zhMsg\":\"请输入拼音或英文，多个词可空格隔开\"}]",
                            Textarea = false
                        },
                        new
                        {
                            Id = 1281498705313329023L,
                            Controls = false,
                            DefaultValue = "1",
                            EnPlaceholder = "Please enter",
                            FieldCode = "Gender",
                            FieldComponentType = "FieldSelect",
                            FieldEnName = "Gender",
                            FieldName = "性别",
                            FieldType = (sbyte)1,
                            FieldsGroupType = (sbyte)0,
                            Number = false,
                            Placeholder = "请输入",
                            Radio = true,
                            Textarea = false,
                            ValueRange = "[{\"code\":\"1\",\"name\":\"男\",\"zhName\":\"男\",\"enName\":\"Male\"},{\"code\":\"2\",\"name\":\"女\",\"zhName\":\"女\",\"enName\":\"Female\"}]"
                        },
                        new
                        {
                            Id = 1281498705313329024L,
                            Controls = false,
                            EnPlaceholder = "Please enter",
                            FieldCode = "LeftEyeVision",
                            FieldComponentType = "FieldInput",
                            FieldEnName = "Left Eye Vision",
                            FieldName = "左眼视力",
                            FieldType = (sbyte)3,
                            FieldsGroupType = (sbyte)0,
                            Maxlength = 5.3m,
                            MinLength = 0.1m,
                            Number = true,
                            Placeholder = "请输入",
                            Precision = (sbyte)2,
                            Radio = false,
                            Remark = "兼容1.0和5分视力值",
                            Textarea = false
                        },
                        new
                        {
                            Id = 1281498705313329025L,
                            Controls = false,
                            EnPlaceholder = "Please enter",
                            FieldCode = "RightEyeVision",
                            FieldComponentType = "FieldInput",
                            FieldEnName = "Right Eye Vision",
                            FieldName = "右眼视力",
                            FieldType = (sbyte)3,
                            FieldsGroupType = (sbyte)0,
                            Maxlength = 5.3m,
                            MinLength = 0.1m,
                            Number = true,
                            Placeholder = "请输入",
                            Precision = (sbyte)2,
                            Radio = false,
                            Remark = "兼容1.0和5分视力值",
                            Textarea = false
                        },
                        new
                        {
                            Id = 1281498705313329026L,
                            Controls = false,
                            EnPlaceholder = "Please enter",
                            FieldCode = "BirthPlace",
                            FieldComponentType = "FieldInput",
                            FieldEnName = "Place of Birth",
                            FieldName = "出生地",
                            FieldType = (sbyte)0,
                            FieldsGroupType = (sbyte)0,
                            Maxlength = 50m,
                            Number = false,
                            Placeholder = "请输入",
                            Radio = false,
                            Textarea = false
                        },
                        new
                        {
                            Id = 1281498705313329030L,
                            Controls = false,
                            DefaultValue = "{\"address\":null,\"hasAddress\":false,\"detail\":{\"resourceType\":null,\"resourceSubType\":null,\"resourceZhName\":null,\"resourceEnName\":null,\"countryCode\":null,\"provinceCode\":null,\"cityCode\":null,\"relatedId\":null,\"countryZhName\":null,\"countryEnName\":null,\"provinceZhName\":null,\"provinceEnName\":null,\"cityZhName\":null,\"cityEnName\":null,\"iata\":null,\"icao\":null,\"highLightContent\":null,\"countryHighLightContent\":null,\"provinceHighLightContent\":null,\"cityHighLightContent\":null,\"iataHighLightContent\":null,\"icaoHighLightContent\":null,\"longitude\":null,\"latitude\":null,\"value\":null}}",
                            EnPlaceholder = "Please enter your country",
                            EsResourceParentType = "[1]",
                            FieldCode = "Nationality",
                            FieldComponentType = "FieldDestinationSearch",
                            FieldEnName = "Nationality",
                            FieldName = "国籍",
                            FieldType = (sbyte)9,
                            FieldsGroupType = (sbyte)0,
                            Number = false,
                            Placeholder = "请输入国家",
                            Radio = false,
                            Textarea = false
                        },
                        new
                        {
                            Id = 1281498705313329031L,
                            Controls = false,
                            EnPlaceholder = "Please enter",
                            FieldCode = "BirthDate",
                            FieldComponentType = "FieldDate",
                            FieldEnName = "Date of Birth",
                            FieldName = "出生日期",
                            FieldType = (sbyte)5,
                            FieldsGroupType = (sbyte)0,
                            MinDate = new DateTime(1900, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            Number = false,
                            Placeholder = "请输入",
                            Radio = false,
                            Textarea = false,
                            UIExtend = "{\"dateType\":\"date\",\"dateFormat\":\"YYYY-MM-DD\",\"valueFormat\":\"yyyy-MM-dd\"}"
                        },
                        new
                        {
                            Id = 1281498705313329032L,
                            Append = "厘米",
                            Controls = false,
                            EnPlaceholder = "Please enter",
                            FieldCode = "Height",
                            FieldComponentType = "FieldInput",
                            FieldEnName = "Height",
                            FieldName = "身高",
                            FieldType = (sbyte)1,
                            FieldsGroupType = (sbyte)0,
                            Maxlength = 300m,
                            MinLength = 0m,
                            Number = true,
                            Placeholder = "请输入",
                            Radio = false,
                            Textarea = false,
                            Unit = "cm"
                        },
                        new
                        {
                            Id = 1281498705313329033L,
                            Append = "千克",
                            Controls = false,
                            EnPlaceholder = "Please enter",
                            FieldCode = "Weight",
                            FieldComponentType = "FieldInput",
                            FieldEnName = "Weight",
                            FieldName = "体重",
                            FieldType = (sbyte)1,
                            FieldsGroupType = (sbyte)0,
                            Maxlength = 9999m,
                            MinLength = 0m,
                            Number = true,
                            Placeholder = "请输入",
                            Radio = false,
                            Textarea = false,
                            Unit = "kg"
                        },
                        new
                        {
                            Id = 1281498705313329034L,
                            Append = "码",
                            Controls = false,
                            EnPlaceholder = "Please enter",
                            FieldCode = "ShoeSize",
                            FieldComponentType = "FieldInput",
                            FieldEnName = "Shoe Size",
                            FieldName = "鞋码",
                            FieldType = (sbyte)3,
                            FieldsGroupType = (sbyte)0,
                            Maxlength = 50m,
                            MinLength = 0m,
                            Number = true,
                            Placeholder = "请输入",
                            Precision = (sbyte)1,
                            Radio = false,
                            Textarea = false,
                            Unit = "yards"
                        },
                        new
                        {
                            Id = 1281498705313329035L,
                            Controls = false,
                            EnPlaceholder = "Please enter",
                            FieldCode = "Ethnicity",
                            FieldComponentType = "FieldInput",
                            FieldEnName = "Ethnicity",
                            FieldName = "民族",
                            FieldType = (sbyte)0,
                            FieldsGroupType = (sbyte)0,
                            Maxlength = 30m,
                            Number = false,
                            Placeholder = "请输入",
                            Radio = false,
                            Textarea = false
                        },
                        new
                        {
                            Id = 1281498705313329036L,
                            Controls = false,
                            DefaultValue = "{\"type\":2,\"value\":null}",
                            EnPlaceholder = "Please enter your ID number",
                            FieldCode = "Card",
                            FieldComponentType = "FieldSelectInput",
                            FieldEnName = "ID Number",
                            FieldName = "证件号",
                            FieldType = (sbyte)9,
                            FieldsGroupType = (sbyte)1,
                            Number = false,
                            Placeholder = "请输入证件号",
                            Radio = false,
                            RulerFilter = "[{\"code\":\"1\",\"name\":\"身份证号\",\"msg\":\"请输入正确的身份证号\",\"pattern\":\"^(^[1-9]\\\\d{7}((0\\\\d)|(1[0-2]))(([0|1|2]\\\\d)|3[0-1])\\\\d{3}$)|(^[1-9]\\\\d{5}[1-9]\\\\d{3}((0\\\\d)|(1[0-2]))(([0|1|2]\\\\d)|3[0-1])((\\\\d{4})|\\\\d{3}[Xx])$)$\",\"enName\":\"ID Number\",\"enMsg\":\"Please enter the correct ID number\",\"zhName\":\"身份证号\",\"zhMsg\":\"请输入正确的身份证号\"}]",
                            Textarea = false,
                            ValueRange = "[{\"code\":1,\"name\":\"身份证\",\"zhName\":\"身份证\",\"enName\":\"ID Card\"},{\"code\":2,\"name\":\"护照\",\"zhName\":\"护照\",\"enName\":\"Passport\"},{\"code\":3,\"name\":\"学生证\",\"zhName\":\"学生证\",\"enName\":\"Student ID\"},{\"code\":4,\"name\":\"军人证\",\"zhName\":\"军人证\",\"enName\":\"Military ID\"},{\"code\":5,\"name\":\"军官证\",\"zhName\":\"军官证\",\"enName\":\"Officer ID\"},{\"code\":6,\"name\":\"驾驶证\",\"zhName\":\"驾驶证\",\"enName\":\"Driver's License\"},{\"code\":7,\"name\":\"回乡证\",\"zhName\":\"回乡证\",\"enName\":\"Home Return Permit\"},{\"code\":8,\"name\":\"台胞证\",\"zhName\":\"台胞证\",\"enName\":\"Taiwan Compatriot Pass\"},{\"code\":9,\"name\":\"警官证\",\"zhName\":\"警官证\",\"enName\":\"Police Officer ID\"},{\"code\":10,\"name\":\"港澳通行证\",\"zhName\":\"港澳通行证\",\"enName\":\"HK/Macau Pass\"},{\"code\":11,\"name\":\"国际海员证\",\"zhName\":\"国际海员证\",\"enName\":\"Seafarer's ID\"},{\"code\":12,\"name\":\"台湾通行证\",\"zhName\":\"台湾通行证\",\"enName\":\"Taiwan Travel Pass\"},{\"code\":13,\"name\":\"士兵证\",\"zhName\":\"士兵证\",\"enName\":\"Soldier ID\"},{\"code\":14,\"name\":\"外国人永久居留证\",\"zhName\":\"外国人永久居留证\",\"enName\":\"Permanent Residence\"},{\"code\":99,\"name\":\"其他\",\"zhName\":\"其他\",\"enName\":\"Other\"}]"
                        },
                        new
                        {
                            Id = 1281498705313329038L,
                            Controls = false,
                            EnPlaceholder = "Please select",
                            FieldCode = "CardEndDate",
                            FieldComponentType = "FieldDate",
                            FieldEnName = "ID Validity Period",
                            FieldName = "证件有效期",
                            FieldType = (sbyte)5,
                            FieldsGroupType = (sbyte)1,
                            Number = false,
                            Placeholder = "请选择",
                            Radio = false,
                            Textarea = false,
                            UIExtend = "{\"dateType\":\"date\",\"dateFormat\":\"YYYY-MM-DD\",\"valueFormat\":\"yyyy-MM-dd\"}"
                        },
                        new
                        {
                            Id = 1281498705313329039L,
                            Controls = false,
                            EnPlaceholder = "Please select",
                            FieldCode = "CardStartDate",
                            FieldComponentType = "FieldDate",
                            FieldEnName = "ID lssuance Date",
                            FieldName = "证件签发日期",
                            FieldType = (sbyte)5,
                            FieldsGroupType = (sbyte)1,
                            Number = false,
                            Placeholder = "请选择",
                            Radio = false,
                            Textarea = false,
                            UIExtend = "{\"dateType\":\"date\",\"dateFormat\":\"YYYY-MM-DD\",\"valueFormat\":\"yyyy-MM-dd\"}"
                        },
                        new
                        {
                            Id = 1281498705313329040L,
                            Controls = false,
                            DefaultValue = "{\"address\":null,\"hasAddress\":false,\"detail\":{\"resourceType\":null,\"resourceSubType\":null,\"resourceZhName\":null,\"resourceEnName\":null,\"countryCode\":null,\"provinceCode\":null,\"cityCode\":null,\"relatedId\":null,\"countryZhName\":null,\"countryEnName\":null,\"provinceZhName\":null,\"provinceEnName\":null,\"cityZhName\":null,\"cityEnName\":null,\"iata\":null,\"icao\":null,\"highLightContent\":null,\"countryHighLightContent\":null,\"provinceHighLightContent\":null,\"cityHighLightContent\":null,\"iataHighLightContent\":null,\"icaoHighLightContent\":null,\"longitude\":null,\"latitude\":null,\"value\":null}}",
                            EnPlaceholder = "Please enter the country",
                            EsResourceParentType = "[1]",
                            FieldCode = "CardCountry",
                            FieldComponentType = "FieldDestinationSearch",
                            FieldEnName = "ID lssuing Country",
                            FieldName = "证件签发国",
                            FieldType = (sbyte)9,
                            FieldsGroupType = (sbyte)1,
                            Number = false,
                            Placeholder = "请输入国家",
                            Radio = false,
                            Textarea = false
                        },
                        new
                        {
                            Id = 1281498705313329041L,
                            Controls = false,
                            EnPlaceholder = "Please enter",
                            FieldCode = "CardPlace",
                            FieldComponentType = "FieldInput",
                            FieldEnName = "ID lssuing Place",
                            FieldName = "证件签发地",
                            FieldType = (sbyte)0,
                            FieldsGroupType = (sbyte)1,
                            Maxlength = 30m,
                            Number = false,
                            Placeholder = "请输入",
                            Radio = false,
                            Textarea = false
                        },
                        new
                        {
                            Id = 1281498705313372342L,
                            Controls = false,
                            EnPlaceholder = "Please enter",
                            FieldCode = "ContactName",
                            FieldComponentType = "FieldInput",
                            FieldEnName = "Contact Person",
                            FieldName = "联系人",
                            FieldType = (sbyte)0,
                            FieldsGroupType = (sbyte)2,
                            Maxlength = 30m,
                            Number = false,
                            Placeholder = "请输入",
                            Radio = false,
                            Textarea = false
                        },
                        new
                        {
                            Id = 1281498705313372344L,
                            Controls = false,
                            DefaultValue = "{\"type\":\"[+86]\",\"value\":null}",
                            EnPlaceholder = "Please enter",
                            FieldCode = "ContactPhone",
                            FieldComponentType = "FieldPhoneInput",
                            FieldEnName = "Mobile Number",
                            FieldName = "手机号",
                            FieldType = (sbyte)9,
                            FieldsGroupType = (sbyte)2,
                            Maxlength = 16m,
                            Number = false,
                            Placeholder = "请输入",
                            Radio = false,
                            RulerFilter = "[{\"code\":\"[+86]\",\"name\":\"手机号\",\"msg\":\"请输入正确的手机号\",\"pattern\":\"^(0|86|17951)?(1)[0-9]{10}$\",\"enName\":\"Mobile Number\",\"enMsg\":\"Please enter the correct mobile phone number\",\"zhName\":\"手机号\",\"zhMsg\":\"请输入正确的手机号\"}]",
                            Textarea = false,
                            ValueRange = "[{\"code\":\"[+244]\",\"name\":\"安哥拉 +244\",\"zhName\":\"安哥拉 +244\",\"enName\":\"Angola +244\"},{\"code\":\"[+93]\",\"name\":\"阿富汗 +93\",\"zhName\":\"阿富汗 +93\",\"enName\":\"Afghanistan +93\"},{\"code\":\"[+1907]\",\"name\":\"阿拉斯加 +1907\",\"zhName\":\"阿拉斯加 +1907\",\"enName\":\"Alaska +1907\"},{\"code\":\"[+355]\",\"name\":\"阿尔巴尼亚 +355\",\"zhName\":\"阿尔巴尼亚 +355\",\"enName\":\"Albania +355\"},{\"code\":\"[+213]\",\"name\":\"阿尔及利亚 +213\",\"zhName\":\"阿尔及利亚 +213\",\"enName\":\"Algeria +213\"},{\"code\":\"[+376]\",\"name\":\"安道尔共和国 +376\",\"zhName\":\"安道尔共和国 +376\",\"enName\":\"Andorra +376\"},{\"code\":\"[+1254]\",\"name\":\"安圭拉岛 +1254\",\"zhName\":\"安圭拉岛 +1254\",\"enName\":\"Anguilla +1254\"},{\"code\":\"[+1268]\",\"name\":\"安提瓜和巴布达 +1268\",\"zhName\":\"安提瓜和巴布达 +1268\",\"enName\":\"Antigua and Barbuda +1268\"},{\"code\":\"[+54]\",\"name\":\"阿根廷 +54\",\"zhName\":\"阿根廷 +54\",\"enName\":\"Argentina +54\"},{\"code\":\"[+374]\",\"name\":\"亚美尼亚 +374\",\"zhName\":\"亚美尼亚 +374\",\"enName\":\"Armenia +374\"},{\"code\":\"[+297]\",\"name\":\"阿鲁巴岛 +297\",\"zhName\":\"阿鲁巴岛 +297\",\"enName\":\"Aruba +297\"},{\"code\":\"[+247]\",\"name\":\"阿森松 +247\",\"zhName\":\"阿森松 +247\",\"enName\":\"Ascension +247\"},{\"code\":\"[+61]\",\"name\":\"澳大利亚 +61\",\"zhName\":\"澳大利亚 +61\",\"enName\":\"Australia +61\"},{\"code\":\"[+43]\",\"name\":\"奥地利 +43\",\"zhName\":\"奥地利 +43\",\"enName\":\"Austria +43\"},{\"code\":\"[+994]\",\"name\":\"阿塞拜疆 +994\",\"zhName\":\"阿塞拜疆 +994\",\"enName\":\"Azerbaijan +994\"},{\"code\":\"[+1242]\",\"name\":\"巴哈马 +1242\",\"zhName\":\"巴哈马 +1242\",\"enName\":\"Bahamas +1242\"},{\"code\":\"[+973]\",\"name\":\"巴林 +973\",\"zhName\":\"巴林 +973\",\"enName\":\"Bahrain +973\"},{\"code\":\"[+880]\",\"name\":\"孟加拉国 +880\",\"zhName\":\"孟加拉国 +880\",\"enName\":\"Bangladesh +880\"},{\"code\":\"[+1246]\",\"name\":\"巴巴多斯 +1246\",\"zhName\":\"巴巴多斯 +1246\",\"enName\":\"Barbados +1246\"},{\"code\":\"[+375]\",\"name\":\"白俄罗斯 +375\",\"zhName\":\"白俄罗斯 +375\",\"enName\":\"Belarus +375\"},{\"code\":\"[+32]\",\"name\":\"比利时 +32\",\"zhName\":\"比利时 +32\",\"enName\":\"Belgium +32\"},{\"code\":\"[+501]\",\"name\":\"伯利兹 +501\",\"zhName\":\"伯利兹 +501\",\"enName\":\"Belize +501\"},{\"code\":\"[+229]\",\"name\":\"贝宁 +229\",\"zhName\":\"贝宁 +229\",\"enName\":\"Benin +229\"},{\"code\":\"[+1441]\",\"name\":\"百慕大群岛 +1441\",\"zhName\":\"百慕大群岛 +1441\",\"enName\":\"Bermuda +1441\"},{\"code\":\"[+975]\",\"name\":\"不丹 +975\",\"zhName\":\"不丹 +975\",\"enName\":\"Bhutan +975\"},{\"code\":\"[+591]\",\"name\":\"玻利维亚 +591\",\"zhName\":\"玻利维亚 +591\",\"enName\":\"Bolivia +591\"},{\"code\":\"[+387]\",\"name\":\"波斯尼亚和黑塞哥维那 +387\",\"zhName\":\"波斯尼亚和黑塞哥维那 +387\",\"enName\":\"Bosnia and Herzegovina +387\"},{\"code\":\"[+267]\",\"name\":\"博茨瓦纳 +267\",\"zhName\":\"博茨瓦纳 +267\",\"enName\":\"Botswana +267\"},{\"code\":\"[+55]\",\"name\":\"巴西 +55\",\"zhName\":\"巴西 +55\",\"enName\":\"Brazil +55\"},{\"code\":\"[+673]\",\"name\":\"文莱 +673\",\"zhName\":\"文莱 +673\",\"enName\":\"Brunei +673\"},{\"code\":\"[+359]\",\"name\":\"保加利亚 +359\",\"zhName\":\"保加利亚 +359\",\"enName\":\"Bulgaria +359\"},{\"code\":\"[+226]\",\"name\":\"布基纳法索 +226\",\"zhName\":\"布基纳法索 +226\",\"enName\":\"Burkina Faso +226\"},{\"code\":\"[+95]\",\"name\":\"缅甸 +95\",\"zhName\":\"缅甸 +95\",\"enName\":\"Myanmar +95\"},{\"code\":\"[+257]\",\"name\":\"布隆迪 +257\",\"zhName\":\"布隆迪 +257\",\"enName\":\"Burundi +257\"},{\"code\":\"[+237]\",\"name\":\"喀麦隆 +237\",\"zhName\":\"喀麦隆 +237\",\"enName\":\"Cameroon +237\"},{\"code\":\"[+1]\",\"name\":\"加拿大/美国 +1\",\"zhName\":\"加拿大/美国 +1\",\"enName\":\"Canada/USA +1\"},{\"code\":\"[+238]\",\"name\":\"佛得角 +238\",\"zhName\":\"佛得角 +238\",\"enName\":\"Cape Verde +238\"},{\"code\":\"[+1345]\",\"name\":\"开曼群岛 +1345\",\"zhName\":\"开曼群岛 +1345\",\"enName\":\"Cayman Islands +1345\"},{\"code\":\"[+236]\",\"name\":\"中非共和国 +236\",\"zhName\":\"中非共和国 +236\",\"enName\":\"Central African Republic +236\"},{\"code\":\"[+235]\",\"name\":\"乍得 +235\",\"zhName\":\"乍得 +235\",\"enName\":\"Chad +235\"},{\"code\":\"[+56]\",\"name\":\"智利 +56\",\"zhName\":\"智利 +56\",\"enName\":\"Chile +56\"},{\"code\":\"[+86]\",\"name\":\"中国大陆 +86\",\"zhName\":\"中国大陆 +86\",\"enName\":\"Mainland China +86\"},{\"code\":\"[+57]\",\"name\":\"哥伦比亚 +57\",\"zhName\":\"哥伦比亚 +57\",\"enName\":\"Colombia +57\"},{\"code\":\"[+242]\",\"name\":\"刚果 +242\",\"zhName\":\"刚果 +242\",\"enName\":\"Congo +242\"},{\"code\":\"[+682]\",\"name\":\"库克群岛 +682\",\"zhName\":\"库克群岛 +682\",\"enName\":\"Cook Islands +682\"},{\"code\":\"[+506]\",\"name\":\"哥斯达黎加 +506\",\"zhName\":\"哥斯达黎加 +506\",\"enName\":\"Costa Rica +506\"},{\"code\":\"[+53]\",\"name\":\"古巴 +53\",\"zhName\":\"古巴 +53\",\"enName\":\"Cuba +53\"},{\"code\":\"[+357]\",\"name\":\"塞浦路斯 +357\",\"zhName\":\"塞浦路斯 +357\",\"enName\":\"Cyprus +357\"},{\"code\":\"[+420]\",\"name\":\"捷克 +420\",\"zhName\":\"捷克 +420\",\"enName\":\"Czech Republic +420\"},{\"code\":\"[+385]\",\"name\":\"克罗地亚共和国 +385\",\"zhName\":\"克罗地亚共和国 +385\",\"enName\":\"Croatia +385\"},{\"code\":\"[+45]\",\"name\":\"丹麦 +45\",\"zhName\":\"丹麦 +45\",\"enName\":\"Denmark +45\"},{\"code\":\"[+253]\",\"name\":\"吉布提 +253\",\"zhName\":\"吉布提 +253\",\"enName\":\"Djibouti +253\"},{\"code\":\"[+1890]\",\"name\":\"多米尼加共和国 +1890\",\"zhName\":\"多米尼加共和国 +1890\",\"enName\":\"Dominican Republic +1890\"},{\"code\":\"[+593]\",\"name\":\"厄瓜多尔 +593\",\"zhName\":\"厄瓜多尔 +593\",\"enName\":\"Ecuador +593\"},{\"code\":\"[+20]\",\"name\":\"埃及 +20\",\"zhName\":\"埃及 +20\",\"enName\":\"Egypt +20\"},{\"code\":\"[+503]\",\"name\":\"萨尔瓦多 +503\",\"zhName\":\"萨尔瓦多 +503\",\"enName\":\"El Salvador +503\"},{\"code\":\"[+372]\",\"name\":\"爱沙尼亚 +372\",\"zhName\":\"爱沙尼亚 +372\",\"enName\":\"Estonia +372\"},{\"code\":\"[+251]\",\"name\":\"埃塞俄比亚 +251\",\"zhName\":\"埃塞俄比亚 +251\",\"enName\":\"Ethiopia +251\"},{\"code\":\"[+679]\",\"name\":\"斐济 +679\",\"zhName\":\"斐济 +679\",\"enName\":\"Fiji +679\"},{\"code\":\"[+358]\",\"name\":\"芬兰 +358\",\"zhName\":\"芬兰 +358\",\"enName\":\"Finland +358\"},{\"code\":\"[+33]\",\"name\":\"法国 +33\",\"zhName\":\"法国 +33\",\"enName\":\"France +33\"},{\"code\":\"[+594]\",\"name\":\"法属圭亚那 +594\",\"zhName\":\"法属圭亚那 +594\",\"enName\":\"French Guiana +594\"},{\"code\":\"[+689]\",\"name\":\"法属玻利尼西亚 +689\",\"zhName\":\"法属玻利尼西亚 +689\",\"enName\":\"French Polynesia +689\"},{\"code\":\"[+241]\",\"name\":\"加蓬 +241\",\"zhName\":\"加蓬 +241\",\"enName\":\"Gabon +241\"},{\"code\":\"[+220]\",\"name\":\"冈比亚 +220\",\"zhName\":\"冈比亚 +220\",\"enName\":\"Gambia +220\"},{\"code\":\"[+995]\",\"name\":\"格鲁吉亚 +995\",\"zhName\":\"格鲁吉亚 +995\",\"enName\":\"Georgia +995\"},{\"code\":\"[+49]\",\"name\":\"德国 +49\",\"zhName\":\"德国 +49\",\"enName\":\"Germany +49\"},{\"code\":\"[+233]\",\"name\":\"加纳/乌兹别克斯坦 +233\",\"zhName\":\"加纳/乌兹别克斯坦 +233\",\"enName\":\"Ghana/Uzbekistan +233\"},{\"code\":\"[+350]\",\"name\":\"直布罗陀 +350\",\"zhName\":\"直布罗陀 +350\",\"enName\":\"Gibraltar +350\"},{\"code\":\"[+30]\",\"name\":\"希腊 +30\",\"zhName\":\"希腊 +30\",\"enName\":\"Greece +30\"},{\"code\":\"[+1809]\",\"name\":\"格林纳达/特立尼达和多巴哥 +1809\",\"zhName\":\"格林纳达/特立尼达和多巴哥 +1809\",\"enName\":\"Grenada/Trinidad and Tobago +1809\"},{\"code\":\"[+1671]\",\"name\":\"关岛 +1671\",\"zhName\":\"关岛 +1671\",\"enName\":\"Guam +1671\"},{\"code\":\"[+502]\",\"name\":\"危地马拉 +502\",\"zhName\":\"危地马拉 +502\",\"enName\":\"Guatemala +502\"},{\"code\":\"[+224]\",\"name\":\"几内亚 +224\",\"zhName\":\"几内亚 +224\",\"enName\":\"Guinea +224\"},{\"code\":\"[+592]\",\"name\":\"圭亚那 +592\",\"zhName\":\"圭亚那 +592\",\"enName\":\"Guyana +592\"},{\"code\":\"[+509]\",\"name\":\"海地 +509\",\"zhName\":\"海地 +509\",\"enName\":\"Haiti +509\"},{\"code\":\"[+504]\",\"name\":\"洪都拉斯 +504\",\"zhName\":\"洪都拉斯 +504\",\"enName\":\"Honduras +504\"},{\"code\":\"[+852]\",\"name\":\"中国香港 +852\",\"zhName\":\"中国香港 +852\",\"enName\":\"Hong Kong +852\"},{\"code\":\"[+36]\",\"name\":\"匈牙利 +36\",\"zhName\":\"匈牙利 +36\",\"enName\":\"Hungary +36\"},{\"code\":\"[+354]\",\"name\":\"冰岛 +354\",\"zhName\":\"冰岛 +354\",\"enName\":\"Iceland +354\"},{\"code\":\"[+91]\",\"name\":\"印度 +91\",\"zhName\":\"印度 +91\",\"enName\":\"India +91\"},{\"code\":\"[+62]\",\"name\":\"印度尼西亚 +62\",\"zhName\":\"印度尼西亚 +62\",\"enName\":\"Indonesia +62\"},{\"code\":\"[+98]\",\"name\":\"伊朗 +98\",\"zhName\":\"伊朗 +98\",\"enName\":\"Iran +98\"},{\"code\":\"[+964]\",\"name\":\"伊拉克 +964\",\"zhName\":\"伊拉克 +964\",\"enName\":\"Iraq +964\"},{\"code\":\"[+353]\",\"name\":\"爱尔兰 +353\",\"zhName\":\"爱尔兰 +353\",\"enName\":\"Ireland +353\"},{\"code\":\"[+972]\",\"name\":\"以色列 +972\",\"zhName\":\"以色列 +972\",\"enName\":\"Israel +972\"},{\"code\":\"[+39]\",\"name\":\"意大利 +39\",\"zhName\":\"意大利 +39\",\"enName\":\"Italy +39\"},{\"code\":\"[+225]\",\"name\":\"科特迪瓦 +225\",\"zhName\":\"科特迪瓦 +225\",\"enName\":\"Côte d'Ivoire +225\"},{\"code\":\"[+1876]\",\"name\":\"牙买加 +1876\",\"zhName\":\"牙买加 +1876\",\"enName\":\"Jamaica +1876\"},{\"code\":\"[+81]\",\"name\":\"日本 +81\",\"zhName\":\"日本 +81\",\"enName\":\"Japan +81\"},{\"code\":\"[+962]\",\"name\":\"约旦 +962\",\"zhName\":\"约旦 +962\",\"enName\":\"Jordan +962\"},{\"code\":\"[+855]\",\"name\":\"柬埔寨 +855\",\"zhName\":\"柬埔寨 +855\",\"enName\":\"Cambodia +855\"},{\"code\":\"[+7]\",\"name\":\"哈萨克斯坦/俄罗斯 +7\",\"zhName\":\"哈萨克斯坦/俄罗斯 +7\",\"enName\":\"Kazakhstan/Russia +7\"},{\"code\":\"[+254]\",\"name\":\"肯尼亚 +254\",\"zhName\":\"肯尼亚 +254\",\"enName\":\"Kenya +254\"},{\"code\":\"[+82]\",\"name\":\"韩国 +82\",\"zhName\":\"韩国 +82\",\"enName\":\"South Korea +82\"},{\"code\":\"[+965]\",\"name\":\"科威特 +965\",\"zhName\":\"科威特 +965\",\"enName\":\"Kuwait +965\"},{\"code\":\"[+331]\",\"name\":\"吉尔吉斯坦 +331\",\"zhName\":\"吉尔吉斯坦 +331\",\"enName\":\"Kyrgyzstan +331\"},{\"code\":\"[+856]\",\"name\":\"老挝 +856\",\"zhName\":\"老挝 +856\",\"enName\":\"Laos +856\"},{\"code\":\"[+371]\",\"name\":\"拉脱维亚 +371\",\"zhName\":\"拉脱维亚 +371\",\"enName\":\"Latvia +371\"},{\"code\":\"[+961]\",\"name\":\"黎巴嫩 +961\",\"zhName\":\"黎巴嫩 +961\",\"enName\":\"Lebanon +961\"},{\"code\":\"[+266]\",\"name\":\"莱索托 +266\",\"zhName\":\"莱索托 +266\",\"enName\":\"Lesotho +266\"},{\"code\":\"[+231]\",\"name\":\"利比里亚 +231\",\"zhName\":\"利比里亚 +231\",\"enName\":\"Liberia +231\"},{\"code\":\"[+218]\",\"name\":\"利比亚 +218\",\"zhName\":\"利比亚 +218\",\"enName\":\"Libya +218\"},{\"code\":\"[+423]\",\"name\":\"列支敦士登 +423\",\"zhName\":\"列支敦士登 +423\",\"enName\":\"Liechtenstein +423\"},{\"code\":\"[+370]\",\"name\":\"立陶宛 +370\",\"zhName\":\"立陶宛 +370\",\"enName\":\"Lithuania +370\"},{\"code\":\"[+352]\",\"name\":\"卢森堡 +352\",\"zhName\":\"卢森堡 +352\",\"enName\":\"Luxembourg +352\"},{\"code\":\"[+853]\",\"name\":\"中国澳门 +853\",\"zhName\":\"中国澳门 +853\",\"enName\":\"Macau +853\"},{\"code\":\"[+261]\",\"name\":\"马达加斯加 +261\",\"zhName\":\"马达加斯加 +261\",\"enName\":\"Madagascar +261\"},{\"code\":\"[+265]\",\"name\":\"马拉维 +265\",\"zhName\":\"马拉维 +265\",\"enName\":\"Malawi +265\"},{\"code\":\"[+60]\",\"name\":\"马来西亚 +60\",\"zhName\":\"马来西亚 +60\",\"enName\":\"Malaysia +60\"},{\"code\":\"[+960]\",\"name\":\"马尔代夫 +960\",\"zhName\":\"马尔代夫 +960\",\"enName\":\"Maldives +960\"},{\"code\":\"[+223]\",\"name\":\"马里 +223\",\"zhName\":\"马里 +223\",\"enName\":\"Mali +223\"},{\"code\":\"[+356]\",\"name\":\"马耳他 +356\",\"zhName\":\"马耳他 +356\",\"enName\":\"Malta +356\"},{\"code\":\"[+1670]\",\"name\":\"马里亚那群岛 +1670\",\"zhName\":\"马里亚那群岛 +1670\",\"enName\":\"Mariana Islands +1670\"},{\"code\":\"[+596]\",\"name\":\"马提尼克 +596\",\"zhName\":\"马提尼克 +596\",\"enName\":\"Martinique +596\"},{\"code\":\"[+230]\",\"name\":\"毛里求斯 +230\",\"zhName\":\"毛里求斯 +230\",\"enName\":\"Mauritius +230\"},{\"code\":\"[+52]\",\"name\":\"墨西哥 +52\",\"zhName\":\"墨西哥 +52\",\"enName\":\"Mexico +52\"},{\"code\":\"[+373]\",\"name\":\"摩尔多瓦 +373\",\"zhName\":\"摩尔多瓦 +373\",\"enName\":\"Moldova +373\"},{\"code\":\"[+377]\",\"name\":\"摩纳哥 +377\",\"zhName\":\"摩纳哥 +377\",\"enName\":\"Monaco +377\"},{\"code\":\"[+976]\",\"name\":\"蒙古 +976\",\"zhName\":\"蒙古 +976\",\"enName\":\"Mongolia +976\"},{\"code\":\"[+1664]\",\"name\":\"蒙特塞拉特岛 +1664\",\"zhName\":\"蒙特塞拉特岛 +1664\",\"enName\":\"Montserrat +1664\"},{\"code\":\"[+212]\",\"name\":\"摩洛哥 +212\",\"zhName\":\"摩洛哥 +212\",\"enName\":\"Morocco +212\"},{\"code\":\"[+258]\",\"name\":\"莫桑比克 +258\",\"zhName\":\"莫桑比克 +258\",\"enName\":\"Mozambique +258\"},{\"code\":\"[+264]\",\"name\":\"纳米比亚 +264\",\"zhName\":\"纳米比亚 +264\",\"enName\":\"Namibia +264\"},{\"code\":\"[+674]\",\"name\":\"瑙鲁 +674\",\"zhName\":\"瑙鲁 +674\",\"enName\":\"Nauru +674\"},{\"code\":\"[+977]\",\"name\":\"尼泊尔 +977\",\"zhName\":\"尼泊尔 +977\",\"enName\":\"Nepal +977\"},{\"code\":\"[+599]\",\"name\":\"荷属安的列斯 +599\",\"zhName\":\"荷属安的列斯 +599\",\"enName\":\"Netherlands Antilles +599\"},{\"code\":\"[+31]\",\"name\":\"荷兰 +31\",\"zhName\":\"荷兰 +31\",\"enName\":\"Netherlands +31\"},{\"code\":\"[+64]\",\"name\":\"新西兰 +64\",\"zhName\":\"新西兰 +64\",\"enName\":\"New Zealand +64\"},{\"code\":\"[+505]\",\"name\":\"尼加拉瓜 +505\",\"zhName\":\"尼加拉瓜 +505\",\"enName\":\"Nicaragua +505\"},{\"code\":\"[+227]\",\"name\":\"尼日尔 +227\",\"zhName\":\"尼日尔 +227\",\"enName\":\"Niger +227\"},{\"code\":\"[+234]\",\"name\":\"尼日利亚 +234\",\"zhName\":\"尼日利亚 +234\",\"enName\":\"Nigeria +234\"},{\"code\":\"[+850]\",\"name\":\"朝鲜 +850\",\"zhName\":\"朝鲜 +850\",\"enName\":\"North Korea +850\"},{\"code\":\"[+47]\",\"name\":\"挪威 +47\",\"zhName\":\"挪威 +47\",\"enName\":\"Norway +47\"},{\"code\":\"[+968]\",\"name\":\"阿曼 +968\",\"zhName\":\"阿曼 +968\",\"enName\":\"Oman +968\"},{\"code\":\"[+92]\",\"name\":\"巴基斯坦 +92\",\"zhName\":\"巴基斯坦 +92\",\"enName\":\"Pakistan +92\"},{\"code\":\"[+507]\",\"name\":\"巴拿马 +507\",\"zhName\":\"巴拿马 +507\",\"enName\":\"Panama +507\"},{\"code\":\"[+675]\",\"name\":\"巴布亚新几内亚 +675\",\"zhName\":\"巴布亚新几内亚 +675\",\"enName\":\"Papua New Guinea +675\"},{\"code\":\"[+595]\",\"name\":\"巴拉圭 +595\",\"zhName\":\"巴拉圭 +595\",\"enName\":\"Paraguay +595\"},{\"code\":\"[+51]\",\"name\":\"秘鲁 +51\",\"zhName\":\"秘鲁 +51\",\"enName\":\"Peru +51\"},{\"code\":\"[+63]\",\"name\":\"菲律宾 +63\",\"zhName\":\"菲律宾 +63\",\"enName\":\"Philippines +63\"},{\"code\":\"[+48]\",\"name\":\"波兰 +48\",\"zhName\":\"波兰 +48\",\"enName\":\"Poland +48\"},{\"code\":\"[+351]\",\"name\":\"葡萄牙 +351\",\"zhName\":\"葡萄牙 +351\",\"enName\":\"Portugal +351\"},{\"code\":\"[+1787]\",\"name\":\"波多黎各 +1787\",\"zhName\":\"波多黎各 +1787\",\"enName\":\"Puerto Rico +1787\"},{\"code\":\"[+974]\",\"name\":\"卡塔尔 +974\",\"zhName\":\"卡塔尔 +974\",\"enName\":\"Qatar +974\"},{\"code\":\"[+262]\",\"name\":\"留尼旺 +262\",\"zhName\":\"留尼旺 +262\",\"enName\":\"Reunion +262\"},{\"code\":\"[+40]\",\"name\":\"罗马尼亚 +40\",\"zhName\":\"罗马尼亚 +40\",\"enName\":\"Romania +40\"},{\"code\":\"[+1784]\",\"name\":\"圣文森特岛/圣文森特 +1784\",\"zhName\":\"圣文森特岛/圣文森特 +1784\",\"enName\":\"St. Vincent Island/St. Vincent +1784\"},{\"code\":\"[+684]\",\"name\":\"东萨摩亚(美) +684\",\"zhName\":\"东萨摩亚(美) +684\",\"enName\":\"Eastern Samoa (US) +684\"},{\"code\":\"[+685]\",\"name\":\"西萨摩亚 +685\",\"zhName\":\"西萨摩亚 +685\",\"enName\":\"Western Samoa +685\"},{\"code\":\"[+378]\",\"name\":\"圣马力诺 +378\",\"zhName\":\"圣马力诺 +378\",\"enName\":\"San Marino +378\"},{\"code\":\"[+239]\",\"name\":\"圣多美和普林西比 +239\",\"zhName\":\"圣多美和普林西比 +239\",\"enName\":\"Sao Tome and Principe +239\"},{\"code\":\"[+966]\",\"name\":\"沙特阿拉伯 +966\",\"zhName\":\"沙特阿拉伯 +966\",\"enName\":\"Saudi Arabia +966\"},{\"code\":\"[+221]\",\"name\":\"塞内加尔 +221\",\"zhName\":\"塞内加尔 +221\",\"enName\":\"Senegal +221\"},{\"code\":\"[+248]\",\"name\":\"塞舌尔 +248\",\"zhName\":\"塞舌尔 +248\",\"enName\":\"Seychelles +248\"},{\"code\":\"[+232]\",\"name\":\"塞拉利昂 +232\",\"zhName\":\"塞拉利昂 +232\",\"enName\":\"Sierra Leone +232\"},{\"code\":\"[+65]\",\"name\":\"新加坡 +65\",\"zhName\":\"新加坡 +65\",\"enName\":\"Singapore +65\"},{\"code\":\"[+421]\",\"name\":\"斯洛伐克 +421\",\"zhName\":\"斯洛伐克 +421\",\"enName\":\"Slovakia +421\"},{\"code\":\"[+386]\",\"name\":\"斯洛文尼亚 +386\",\"zhName\":\"斯洛文尼亚 +386\",\"enName\":\"Slovenia +386\"},{\"code\":\"[+677]\",\"name\":\"所罗门群岛 +677\",\"zhName\":\"所罗门群岛 +677\",\"enName\":\"Solomon Islands +677\"},{\"code\":\"[+252]\",\"name\":\"索马里 +252\",\"zhName\":\"索马里 +252\",\"enName\":\"Somalia +252\"},{\"code\":\"[+27]\",\"name\":\"南非 +27\",\"zhName\":\"南非 +27\",\"enName\":\"South Africa +27\"},{\"code\":\"[+34]\",\"name\":\"西班牙 +34\",\"zhName\":\"西班牙 +34\",\"enName\":\"Spain +34\"},{\"code\":\"[+94]\",\"name\":\"斯里兰卡 +94\",\"zhName\":\"斯里兰卡 +94\",\"enName\":\"Sri Lanka +94\"},{\"code\":\"[+1758]\",\"name\":\"圣卢西亚 +1758\",\"zhName\":\"圣卢西亚 +1758\",\"enName\":\"Saint Lucia +1758\"},{\"code\":\"[+249]\",\"name\":\"苏丹 +249\",\"zhName\":\"苏丹 +249\",\"enName\":\"Sudan +249\"},{\"code\":\"[+597]\",\"name\":\"苏里南 +597\",\"zhName\":\"苏里南 +597\",\"enName\":\"Suriname +597\"},{\"code\":\"[+268]\",\"name\":\"斯威士兰 +268\",\"zhName\":\"斯威士兰 +268\",\"enName\":\"Swaziland +268\"},{\"code\":\"[+46]\",\"name\":\"瑞典 +46\",\"zhName\":\"瑞典 +46\",\"enName\":\"Sweden +46\"},{\"code\":\"[+41]\",\"name\":\"瑞士 +41\",\"zhName\":\"瑞士 +41\",\"enName\":\"Switzerland +41\"},{\"code\":\"[+963]\",\"name\":\"叙利亚 +963\",\"zhName\":\"叙利亚 +963\",\"enName\":\"Syria +963\"},{\"code\":\"[+886]\",\"name\":\"中国台湾 +886\",\"zhName\":\"中国台湾 +886\",\"enName\":\"Taiwan +886\"},{\"code\":\"[+992]\",\"name\":\"塔吉克斯坦 +992\",\"zhName\":\"塔吉克斯坦 +992\",\"enName\":\"Tajikistan +992\"},{\"code\":\"[+255]\",\"name\":\"坦桑尼亚 +255\",\"zhName\":\"坦桑尼亚 +255\",\"enName\":\"Tanzania +255\"},{\"code\":\"[+66]\",\"name\":\"泰国 +66\",\"zhName\":\"泰国 +66\",\"enName\":\"Thailand +66\"},{\"code\":\"[+228]\",\"name\":\"多哥 +228\",\"zhName\":\"多哥 +228\",\"enName\":\"Togo +228\"},{\"code\":\"[+676]\",\"name\":\"汤加 +676\",\"zhName\":\"汤加 +676\",\"enName\":\"Tonga +676\"},{\"code\":\"[+216]\",\"name\":\"突尼斯 +216\",\"zhName\":\"突尼斯 +216\",\"enName\":\"Tunisia +216\"},{\"code\":\"[+90]\",\"name\":\"土耳其 +90\",\"zhName\":\"土耳其 +90\",\"enName\":\"Turkey +90\"},{\"code\":\"[+993]\",\"name\":\"土库曼斯坦 +993\",\"zhName\":\"土库曼斯坦 +993\",\"enName\":\"Turkmenistan +993\"},{\"code\":\"[+256]\",\"name\":\"乌干达 +256\",\"zhName\":\"乌干达 +256\",\"enName\":\"Uganda +256\"},{\"code\":\"[+380]\",\"name\":\"乌克兰 +380\",\"zhName\":\"乌克兰 +380\",\"enName\":\"Ukraine +380\"},{\"code\":\"[+971]\",\"name\":\"阿拉伯联合酋长国 +971\",\"zhName\":\"阿拉伯联合酋长国 +971\",\"enName\":\"United Arab Emirates +971\"},{\"code\":\"[+44]\",\"name\":\"英国 +44\",\"zhName\":\"英国 +44\",\"enName\":\"United Kingdom +44\"},{\"code\":\"[+598]\",\"name\":\"乌拉圭 +598\",\"zhName\":\"乌拉圭 +598\",\"enName\":\"Uruguay +598\"},{\"code\":\"[+58]\",\"name\":\"委内瑞拉 +58\",\"zhName\":\"委内瑞拉 +58\",\"enName\":\"Venezuela +58\"},{\"code\":\"[+84]\",\"name\":\"越南 +84\",\"zhName\":\"越南 +84\",\"enName\":\"Vietnam +84\"},{\"code\":\"[+967]\",\"name\":\"也门 +967\",\"zhName\":\"也门 +967\",\"enName\":\"Yemen +967\"},{\"code\":\"[+381]\",\"name\":\"南斯拉夫 +381\",\"zhName\":\"南斯拉夫 +381\",\"enName\":\"Yugoslavia +381\"},{\"code\":\"[+263]\",\"name\":\"津巴布韦 +263\",\"zhName\":\"津巴布韦 +263\",\"enName\":\"Zimbabwe +263\"},{\"code\":\"[+243]\",\"name\":\"扎伊尔 +243\",\"zhName\":\"扎伊尔 +243\",\"enName\":\"Zaire +243\"},{\"code\":\"[+260]\",\"name\":\"赞比亚 +260\",\"zhName\":\"赞比亚 +260\",\"enName\":\"Zambia +260\"}]"
                        },
                        new
                        {
                            Id = 1281498705313372346L,
                            Controls = false,
                            DefaultValue = "{\"type\":\"[+86]\",\"value\":null}",
                            EnPlaceholder = "Please enter",
                            FieldCode = "LocalPhone",
                            FieldComponentType = "FieldPhoneInput",
                            FieldEnName = "Local Mobile Number",
                            FieldName = "当地手机号",
                            FieldType = (sbyte)9,
                            FieldsGroupType = (sbyte)2,
                            Maxlength = 16m,
                            Number = false,
                            Placeholder = "请输入",
                            Radio = false,
                            RulerFilter = "[{\"code\":\"[+86]\",\"name\":\"手机号\",\"msg\":\"请输入正确的手机号\",\"pattern\":\"^(0|86|17951)?(1)[0-9]{10}$\",\"enName\":\"Local Mobile Number\",\"enMsg\":\"Please enter the correct mobile phone number\",\"zhName\":\"手机号\",\"zhMsg\":\"请输入正确的手机号\"}]",
                            Textarea = false,
                            ValueRange = "[{\"code\":\"[+244]\",\"name\":\"安哥拉 +244\",\"zhName\":\"安哥拉 +244\",\"enName\":\"Angola +244\"},{\"code\":\"[+93]\",\"name\":\"阿富汗 +93\",\"zhName\":\"阿富汗 +93\",\"enName\":\"Afghanistan +93\"},{\"code\":\"[+1907]\",\"name\":\"阿拉斯加 +1907\",\"zhName\":\"阿拉斯加 +1907\",\"enName\":\"Alaska +1907\"},{\"code\":\"[+355]\",\"name\":\"阿尔巴尼亚 +355\",\"zhName\":\"阿尔巴尼亚 +355\",\"enName\":\"Albania +355\"},{\"code\":\"[+213]\",\"name\":\"阿尔及利亚 +213\",\"zhName\":\"阿尔及利亚 +213\",\"enName\":\"Algeria +213\"},{\"code\":\"[+376]\",\"name\":\"安道尔共和国 +376\",\"zhName\":\"安道尔共和国 +376\",\"enName\":\"Andorra +376\"},{\"code\":\"[+1254]\",\"name\":\"安圭拉岛 +1254\",\"zhName\":\"安圭拉岛 +1254\",\"enName\":\"Anguilla +1254\"},{\"code\":\"[+1268]\",\"name\":\"安提瓜和巴布达 +1268\",\"zhName\":\"安提瓜和巴布达 +1268\",\"enName\":\"Antigua and Barbuda +1268\"},{\"code\":\"[+54]\",\"name\":\"阿根廷 +54\",\"zhName\":\"阿根廷 +54\",\"enName\":\"Argentina +54\"},{\"code\":\"[+374]\",\"name\":\"亚美尼亚 +374\",\"zhName\":\"亚美尼亚 +374\",\"enName\":\"Armenia +374\"},{\"code\":\"[+297]\",\"name\":\"阿鲁巴岛 +297\",\"zhName\":\"阿鲁巴岛 +297\",\"enName\":\"Aruba +297\"},{\"code\":\"[+247]\",\"name\":\"阿森松 +247\",\"zhName\":\"阿森松 +247\",\"enName\":\"Ascension +247\"},{\"code\":\"[+61]\",\"name\":\"澳大利亚 +61\",\"zhName\":\"澳大利亚 +61\",\"enName\":\"Australia +61\"},{\"code\":\"[+43]\",\"name\":\"奥地利 +43\",\"zhName\":\"奥地利 +43\",\"enName\":\"Austria +43\"},{\"code\":\"[+994]\",\"name\":\"阿塞拜疆 +994\",\"zhName\":\"阿塞拜疆 +994\",\"enName\":\"Azerbaijan +994\"},{\"code\":\"[+1242]\",\"name\":\"巴哈马 +1242\",\"zhName\":\"巴哈马 +1242\",\"enName\":\"Bahamas +1242\"},{\"code\":\"[+973]\",\"name\":\"巴林 +973\",\"zhName\":\"巴林 +973\",\"enName\":\"Bahrain +973\"},{\"code\":\"[+880]\",\"name\":\"孟加拉国 +880\",\"zhName\":\"孟加拉国 +880\",\"enName\":\"Bangladesh +880\"},{\"code\":\"[+1246]\",\"name\":\"巴巴多斯 +1246\",\"zhName\":\"巴巴多斯 +1246\",\"enName\":\"Barbados +1246\"},{\"code\":\"[+375]\",\"name\":\"白俄罗斯 +375\",\"zhName\":\"白俄罗斯 +375\",\"enName\":\"Belarus +375\"},{\"code\":\"[+32]\",\"name\":\"比利时 +32\",\"zhName\":\"比利时 +32\",\"enName\":\"Belgium +32\"},{\"code\":\"[+501]\",\"name\":\"伯利兹 +501\",\"zhName\":\"伯利兹 +501\",\"enName\":\"Belize +501\"},{\"code\":\"[+229]\",\"name\":\"贝宁 +229\",\"zhName\":\"贝宁 +229\",\"enName\":\"Benin +229\"},{\"code\":\"[+1441]\",\"name\":\"百慕大群岛 +1441\",\"zhName\":\"百慕大群岛 +1441\",\"enName\":\"Bermuda +1441\"},{\"code\":\"[+975]\",\"name\":\"不丹 +975\",\"zhName\":\"不丹 +975\",\"enName\":\"Bhutan +975\"},{\"code\":\"[+591]\",\"name\":\"玻利维亚 +591\",\"zhName\":\"玻利维亚 +591\",\"enName\":\"Bolivia +591\"},{\"code\":\"[+387]\",\"name\":\"波斯尼亚和黑塞哥维那 +387\",\"zhName\":\"波斯尼亚和黑塞哥维那 +387\",\"enName\":\"Bosnia and Herzegovina +387\"},{\"code\":\"[+267]\",\"name\":\"博茨瓦纳 +267\",\"zhName\":\"博茨瓦纳 +267\",\"enName\":\"Botswana +267\"},{\"code\":\"[+55]\",\"name\":\"巴西 +55\",\"zhName\":\"巴西 +55\",\"enName\":\"Brazil +55\"},{\"code\":\"[+673]\",\"name\":\"文莱 +673\",\"zhName\":\"文莱 +673\",\"enName\":\"Brunei +673\"},{\"code\":\"[+359]\",\"name\":\"保加利亚 +359\",\"zhName\":\"保加利亚 +359\",\"enName\":\"Bulgaria +359\"},{\"code\":\"[+226]\",\"name\":\"布基纳法索 +226\",\"zhName\":\"布基纳法索 +226\",\"enName\":\"Burkina Faso +226\"},{\"code\":\"[+95]\",\"name\":\"缅甸 +95\",\"zhName\":\"缅甸 +95\",\"enName\":\"Myanmar +95\"},{\"code\":\"[+257]\",\"name\":\"布隆迪 +257\",\"zhName\":\"布隆迪 +257\",\"enName\":\"Burundi +257\"},{\"code\":\"[+237]\",\"name\":\"喀麦隆 +237\",\"zhName\":\"喀麦隆 +237\",\"enName\":\"Cameroon +237\"},{\"code\":\"[+1]\",\"name\":\"加拿大/美国 +1\",\"zhName\":\"加拿大/美国 +1\",\"enName\":\"Canada/USA +1\"},{\"code\":\"[+238]\",\"name\":\"佛得角 +238\",\"zhName\":\"佛得角 +238\",\"enName\":\"Cape Verde +238\"},{\"code\":\"[+1345]\",\"name\":\"开曼群岛 +1345\",\"zhName\":\"开曼群岛 +1345\",\"enName\":\"Cayman Islands +1345\"},{\"code\":\"[+236]\",\"name\":\"中非共和国 +236\",\"zhName\":\"中非共和国 +236\",\"enName\":\"Central African Republic +236\"},{\"code\":\"[+235]\",\"name\":\"乍得 +235\",\"zhName\":\"乍得 +235\",\"enName\":\"Chad +235\"},{\"code\":\"[+56]\",\"name\":\"智利 +56\",\"zhName\":\"智利 +56\",\"enName\":\"Chile +56\"},{\"code\":\"[+86]\",\"name\":\"中国大陆 +86\",\"zhName\":\"中国大陆 +86\",\"enName\":\"Mainland China +86\"},{\"code\":\"[+57]\",\"name\":\"哥伦比亚 +57\",\"zhName\":\"哥伦比亚 +57\",\"enName\":\"Colombia +57\"},{\"code\":\"[+242]\",\"name\":\"刚果 +242\",\"zhName\":\"刚果 +242\",\"enName\":\"Congo +242\"},{\"code\":\"[+682]\",\"name\":\"库克群岛 +682\",\"zhName\":\"库克群岛 +682\",\"enName\":\"Cook Islands +682\"},{\"code\":\"[+506]\",\"name\":\"哥斯达黎加 +506\",\"zhName\":\"哥斯达黎加 +506\",\"enName\":\"Costa Rica +506\"},{\"code\":\"[+53]\",\"name\":\"古巴 +53\",\"zhName\":\"古巴 +53\",\"enName\":\"Cuba +53\"},{\"code\":\"[+357]\",\"name\":\"塞浦路斯 +357\",\"zhName\":\"塞浦路斯 +357\",\"enName\":\"Cyprus +357\"},{\"code\":\"[+420]\",\"name\":\"捷克 +420\",\"zhName\":\"捷克 +420\",\"enName\":\"Czech Republic +420\"},{\"code\":\"[+385]\",\"name\":\"克罗地亚共和国 +385\",\"zhName\":\"克罗地亚共和国 +385\",\"enName\":\"Croatia +385\"},{\"code\":\"[+45]\",\"name\":\"丹麦 +45\",\"zhName\":\"丹麦 +45\",\"enName\":\"Denmark +45\"},{\"code\":\"[+253]\",\"name\":\"吉布提 +253\",\"zhName\":\"吉布提 +253\",\"enName\":\"Djibouti +253\"},{\"code\":\"[+1890]\",\"name\":\"多米尼加共和国 +1890\",\"zhName\":\"多米尼加共和国 +1890\",\"enName\":\"Dominican Republic +1890\"},{\"code\":\"[+593]\",\"name\":\"厄瓜多尔 +593\",\"zhName\":\"厄瓜多尔 +593\",\"enName\":\"Ecuador +593\"},{\"code\":\"[+20]\",\"name\":\"埃及 +20\",\"zhName\":\"埃及 +20\",\"enName\":\"Egypt +20\"},{\"code\":\"[+503]\",\"name\":\"萨尔瓦多 +503\",\"zhName\":\"萨尔瓦多 +503\",\"enName\":\"El Salvador +503\"},{\"code\":\"[+372]\",\"name\":\"爱沙尼亚 +372\",\"zhName\":\"爱沙尼亚 +372\",\"enName\":\"Estonia +372\"},{\"code\":\"[+251]\",\"name\":\"埃塞俄比亚 +251\",\"zhName\":\"埃塞俄比亚 +251\",\"enName\":\"Ethiopia +251\"},{\"code\":\"[+679]\",\"name\":\"斐济 +679\",\"zhName\":\"斐济 +679\",\"enName\":\"Fiji +679\"},{\"code\":\"[+358]\",\"name\":\"芬兰 +358\",\"zhName\":\"芬兰 +358\",\"enName\":\"Finland +358\"},{\"code\":\"[+33]\",\"name\":\"法国 +33\",\"zhName\":\"法国 +33\",\"enName\":\"France +33\"},{\"code\":\"[+594]\",\"name\":\"法属圭亚那 +594\",\"zhName\":\"法属圭亚那 +594\",\"enName\":\"French Guiana +594\"},{\"code\":\"[+689]\",\"name\":\"法属玻利尼西亚 +689\",\"zhName\":\"法属玻利尼西亚 +689\",\"enName\":\"French Polynesia +689\"},{\"code\":\"[+241]\",\"name\":\"加蓬 +241\",\"zhName\":\"加蓬 +241\",\"enName\":\"Gabon +241\"},{\"code\":\"[+220]\",\"name\":\"冈比亚 +220\",\"zhName\":\"冈比亚 +220\",\"enName\":\"Gambia +220\"},{\"code\":\"[+995]\",\"name\":\"格鲁吉亚 +995\",\"zhName\":\"格鲁吉亚 +995\",\"enName\":\"Georgia +995\"},{\"code\":\"[+49]\",\"name\":\"德国 +49\",\"zhName\":\"德国 +49\",\"enName\":\"Germany +49\"},{\"code\":\"[+233]\",\"name\":\"加纳/乌兹别克斯坦 +233\",\"zhName\":\"加纳/乌兹别克斯坦 +233\",\"enName\":\"Ghana/Uzbekistan +233\"},{\"code\":\"[+350]\",\"name\":\"直布罗陀 +350\",\"zhName\":\"直布罗陀 +350\",\"enName\":\"Gibraltar +350\"},{\"code\":\"[+30]\",\"name\":\"希腊 +30\",\"zhName\":\"希腊 +30\",\"enName\":\"Greece +30\"},{\"code\":\"[+1809]\",\"name\":\"格林纳达/特立尼达和多巴哥 +1809\",\"zhName\":\"格林纳达/特立尼达和多巴哥 +1809\",\"enName\":\"Grenada/Trinidad and Tobago +1809\"},{\"code\":\"[+1671]\",\"name\":\"关岛 +1671\",\"zhName\":\"关岛 +1671\",\"enName\":\"Guam +1671\"},{\"code\":\"[+502]\",\"name\":\"危地马拉 +502\",\"zhName\":\"危地马拉 +502\",\"enName\":\"Guatemala +502\"},{\"code\":\"[+224]\",\"name\":\"几内亚 +224\",\"zhName\":\"几内亚 +224\",\"enName\":\"Guinea +224\"},{\"code\":\"[+592]\",\"name\":\"圭亚那 +592\",\"zhName\":\"圭亚那 +592\",\"enName\":\"Guyana +592\"},{\"code\":\"[+509]\",\"name\":\"海地 +509\",\"zhName\":\"海地 +509\",\"enName\":\"Haiti +509\"},{\"code\":\"[+504]\",\"name\":\"洪都拉斯 +504\",\"zhName\":\"洪都拉斯 +504\",\"enName\":\"Honduras +504\"},{\"code\":\"[+852]\",\"name\":\"中国香港 +852\",\"zhName\":\"中国香港 +852\",\"enName\":\"Hong Kong +852\"},{\"code\":\"[+36]\",\"name\":\"匈牙利 +36\",\"zhName\":\"匈牙利 +36\",\"enName\":\"Hungary +36\"},{\"code\":\"[+354]\",\"name\":\"冰岛 +354\",\"zhName\":\"冰岛 +354\",\"enName\":\"Iceland +354\"},{\"code\":\"[+91]\",\"name\":\"印度 +91\",\"zhName\":\"印度 +91\",\"enName\":\"India +91\"},{\"code\":\"[+62]\",\"name\":\"印度尼西亚 +62\",\"zhName\":\"印度尼西亚 +62\",\"enName\":\"Indonesia +62\"},{\"code\":\"[+98]\",\"name\":\"伊朗 +98\",\"zhName\":\"伊朗 +98\",\"enName\":\"Iran +98\"},{\"code\":\"[+964]\",\"name\":\"伊拉克 +964\",\"zhName\":\"伊拉克 +964\",\"enName\":\"Iraq +964\"},{\"code\":\"[+353]\",\"name\":\"爱尔兰 +353\",\"zhName\":\"爱尔兰 +353\",\"enName\":\"Ireland +353\"},{\"code\":\"[+972]\",\"name\":\"以色列 +972\",\"zhName\":\"以色列 +972\",\"enName\":\"Israel +972\"},{\"code\":\"[+39]\",\"name\":\"意大利 +39\",\"zhName\":\"意大利 +39\",\"enName\":\"Italy +39\"},{\"code\":\"[+225]\",\"name\":\"科特迪瓦 +225\",\"zhName\":\"科特迪瓦 +225\",\"enName\":\"Côte d'Ivoire +225\"},{\"code\":\"[+1876]\",\"name\":\"牙买加 +1876\",\"zhName\":\"牙买加 +1876\",\"enName\":\"Jamaica +1876\"},{\"code\":\"[+81]\",\"name\":\"日本 +81\",\"zhName\":\"日本 +81\",\"enName\":\"Japan +81\"},{\"code\":\"[+962]\",\"name\":\"约旦 +962\",\"zhName\":\"约旦 +962\",\"enName\":\"Jordan +962\"},{\"code\":\"[+855]\",\"name\":\"柬埔寨 +855\",\"zhName\":\"柬埔寨 +855\",\"enName\":\"Cambodia +855\"},{\"code\":\"[+7]\",\"name\":\"哈萨克斯坦/俄罗斯 +7\",\"zhName\":\"哈萨克斯坦/俄罗斯 +7\",\"enName\":\"Kazakhstan/Russia +7\"},{\"code\":\"[+254]\",\"name\":\"肯尼亚 +254\",\"zhName\":\"肯尼亚 +254\",\"enName\":\"Kenya +254\"},{\"code\":\"[+82]\",\"name\":\"韩国 +82\",\"zhName\":\"韩国 +82\",\"enName\":\"South Korea +82\"},{\"code\":\"[+965]\",\"name\":\"科威特 +965\",\"zhName\":\"科威特 +965\",\"enName\":\"Kuwait +965\"},{\"code\":\"[+331]\",\"name\":\"吉尔吉斯坦 +331\",\"zhName\":\"吉尔吉斯坦 +331\",\"enName\":\"Kyrgyzstan +331\"},{\"code\":\"[+856]\",\"name\":\"老挝 +856\",\"zhName\":\"老挝 +856\",\"enName\":\"Laos +856\"},{\"code\":\"[+371]\",\"name\":\"拉脱维亚 +371\",\"zhName\":\"拉脱维亚 +371\",\"enName\":\"Latvia +371\"},{\"code\":\"[+961]\",\"name\":\"黎巴嫩 +961\",\"zhName\":\"黎巴嫩 +961\",\"enName\":\"Lebanon +961\"},{\"code\":\"[+266]\",\"name\":\"莱索托 +266\",\"zhName\":\"莱索托 +266\",\"enName\":\"Lesotho +266\"},{\"code\":\"[+231]\",\"name\":\"利比里亚 +231\",\"zhName\":\"利比里亚 +231\",\"enName\":\"Liberia +231\"},{\"code\":\"[+218]\",\"name\":\"利比亚 +218\",\"zhName\":\"利比亚 +218\",\"enName\":\"Libya +218\"},{\"code\":\"[+423]\",\"name\":\"列支敦士登 +423\",\"zhName\":\"列支敦士登 +423\",\"enName\":\"Liechtenstein +423\"},{\"code\":\"[+370]\",\"name\":\"立陶宛 +370\",\"zhName\":\"立陶宛 +370\",\"enName\":\"Lithuania +370\"},{\"code\":\"[+352]\",\"name\":\"卢森堡 +352\",\"zhName\":\"卢森堡 +352\",\"enName\":\"Luxembourg +352\"},{\"code\":\"[+853]\",\"name\":\"中国澳门 +853\",\"zhName\":\"中国澳门 +853\",\"enName\":\"Macau +853\"},{\"code\":\"[+261]\",\"name\":\"马达加斯加 +261\",\"zhName\":\"马达加斯加 +261\",\"enName\":\"Madagascar +261\"},{\"code\":\"[+265]\",\"name\":\"马拉维 +265\",\"zhName\":\"马拉维 +265\",\"enName\":\"Malawi +265\"},{\"code\":\"[+60]\",\"name\":\"马来西亚 +60\",\"zhName\":\"马来西亚 +60\",\"enName\":\"Malaysia +60\"},{\"code\":\"[+960]\",\"name\":\"马尔代夫 +960\",\"zhName\":\"马尔代夫 +960\",\"enName\":\"Maldives +960\"},{\"code\":\"[+223]\",\"name\":\"马里 +223\",\"zhName\":\"马里 +223\",\"enName\":\"Mali +223\"},{\"code\":\"[+356]\",\"name\":\"马耳他 +356\",\"zhName\":\"马耳他 +356\",\"enName\":\"Malta +356\"},{\"code\":\"[+1670]\",\"name\":\"马里亚那群岛 +1670\",\"zhName\":\"马里亚那群岛 +1670\",\"enName\":\"Mariana Islands +1670\"},{\"code\":\"[+596]\",\"name\":\"马提尼克 +596\",\"zhName\":\"马提尼克 +596\",\"enName\":\"Martinique +596\"},{\"code\":\"[+230]\",\"name\":\"毛里求斯 +230\",\"zhName\":\"毛里求斯 +230\",\"enName\":\"Mauritius +230\"},{\"code\":\"[+52]\",\"name\":\"墨西哥 +52\",\"zhName\":\"墨西哥 +52\",\"enName\":\"Mexico +52\"},{\"code\":\"[+373]\",\"name\":\"摩尔多瓦 +373\",\"zhName\":\"摩尔多瓦 +373\",\"enName\":\"Moldova +373\"},{\"code\":\"[+377]\",\"name\":\"摩纳哥 +377\",\"zhName\":\"摩纳哥 +377\",\"enName\":\"Monaco +377\"},{\"code\":\"[+976]\",\"name\":\"蒙古 +976\",\"zhName\":\"蒙古 +976\",\"enName\":\"Mongolia +976\"},{\"code\":\"[+1664]\",\"name\":\"蒙特塞拉特岛 +1664\",\"zhName\":\"蒙特塞拉特岛 +1664\",\"enName\":\"Montserrat +1664\"},{\"code\":\"[+212]\",\"name\":\"摩洛哥 +212\",\"zhName\":\"摩洛哥 +212\",\"enName\":\"Morocco +212\"},{\"code\":\"[+258]\",\"name\":\"莫桑比克 +258\",\"zhName\":\"莫桑比克 +258\",\"enName\":\"Mozambique +258\"},{\"code\":\"[+264]\",\"name\":\"纳米比亚 +264\",\"zhName\":\"纳米比亚 +264\",\"enName\":\"Namibia +264\"},{\"code\":\"[+674]\",\"name\":\"瑙鲁 +674\",\"zhName\":\"瑙鲁 +674\",\"enName\":\"Nauru +674\"},{\"code\":\"[+977]\",\"name\":\"尼泊尔 +977\",\"zhName\":\"尼泊尔 +977\",\"enName\":\"Nepal +977\"},{\"code\":\"[+599]\",\"name\":\"荷属安的列斯 +599\",\"zhName\":\"荷属安的列斯 +599\",\"enName\":\"Netherlands Antilles +599\"},{\"code\":\"[+31]\",\"name\":\"荷兰 +31\",\"zhName\":\"荷兰 +31\",\"enName\":\"Netherlands +31\"},{\"code\":\"[+64]\",\"name\":\"新西兰 +64\",\"zhName\":\"新西兰 +64\",\"enName\":\"New Zealand +64\"},{\"code\":\"[+505]\",\"name\":\"尼加拉瓜 +505\",\"zhName\":\"尼加拉瓜 +505\",\"enName\":\"Nicaragua +505\"},{\"code\":\"[+227]\",\"name\":\"尼日尔 +227\",\"zhName\":\"尼日尔 +227\",\"enName\":\"Niger +227\"},{\"code\":\"[+234]\",\"name\":\"尼日利亚 +234\",\"zhName\":\"尼日利亚 +234\",\"enName\":\"Nigeria +234\"},{\"code\":\"[+850]\",\"name\":\"朝鲜 +850\",\"zhName\":\"朝鲜 +850\",\"enName\":\"North Korea +850\"},{\"code\":\"[+47]\",\"name\":\"挪威 +47\",\"zhName\":\"挪威 +47\",\"enName\":\"Norway +47\"},{\"code\":\"[+968]\",\"name\":\"阿曼 +968\",\"zhName\":\"阿曼 +968\",\"enName\":\"Oman +968\"},{\"code\":\"[+92]\",\"name\":\"巴基斯坦 +92\",\"zhName\":\"巴基斯坦 +92\",\"enName\":\"Pakistan +92\"},{\"code\":\"[+507]\",\"name\":\"巴拿马 +507\",\"zhName\":\"巴拿马 +507\",\"enName\":\"Panama +507\"},{\"code\":\"[+675]\",\"name\":\"巴布亚新几内亚 +675\",\"zhName\":\"巴布亚新几内亚 +675\",\"enName\":\"Papua New Guinea +675\"},{\"code\":\"[+595]\",\"name\":\"巴拉圭 +595\",\"zhName\":\"巴拉圭 +595\",\"enName\":\"Paraguay +595\"},{\"code\":\"[+51]\",\"name\":\"秘鲁 +51\",\"zhName\":\"秘鲁 +51\",\"enName\":\"Peru +51\"},{\"code\":\"[+63]\",\"name\":\"菲律宾 +63\",\"zhName\":\"菲律宾 +63\",\"enName\":\"Philippines +63\"},{\"code\":\"[+48]\",\"name\":\"波兰 +48\",\"zhName\":\"波兰 +48\",\"enName\":\"Poland +48\"},{\"code\":\"[+351]\",\"name\":\"葡萄牙 +351\",\"zhName\":\"葡萄牙 +351\",\"enName\":\"Portugal +351\"},{\"code\":\"[+1787]\",\"name\":\"波多黎各 +1787\",\"zhName\":\"波多黎各 +1787\",\"enName\":\"Puerto Rico +1787\"},{\"code\":\"[+974]\",\"name\":\"卡塔尔 +974\",\"zhName\":\"卡塔尔 +974\",\"enName\":\"Qatar +974\"},{\"code\":\"[+262]\",\"name\":\"留尼旺 +262\",\"zhName\":\"留尼旺 +262\",\"enName\":\"Reunion +262\"},{\"code\":\"[+40]\",\"name\":\"罗马尼亚 +40\",\"zhName\":\"罗马尼亚 +40\",\"enName\":\"Romania +40\"},{\"code\":\"[+1784]\",\"name\":\"圣文森特岛/圣文森特 +1784\",\"zhName\":\"圣文森特岛/圣文森特 +1784\",\"enName\":\"St. Vincent Island/St. Vincent +1784\"},{\"code\":\"[+684]\",\"name\":\"东萨摩亚(美) +684\",\"zhName\":\"东萨摩亚(美) +684\",\"enName\":\"Eastern Samoa (US) +684\"},{\"code\":\"[+685]\",\"name\":\"西萨摩亚 +685\",\"zhName\":\"西萨摩亚 +685\",\"enName\":\"Western Samoa +685\"},{\"code\":\"[+378]\",\"name\":\"圣马力诺 +378\",\"zhName\":\"圣马力诺 +378\",\"enName\":\"San Marino +378\"},{\"code\":\"[+239]\",\"name\":\"圣多美和普林西比 +239\",\"zhName\":\"圣多美和普林西比 +239\",\"enName\":\"Sao Tome and Principe +239\"},{\"code\":\"[+966]\",\"name\":\"沙特阿拉伯 +966\",\"zhName\":\"沙特阿拉伯 +966\",\"enName\":\"Saudi Arabia +966\"},{\"code\":\"[+221]\",\"name\":\"塞内加尔 +221\",\"zhName\":\"塞内加尔 +221\",\"enName\":\"Senegal +221\"},{\"code\":\"[+248]\",\"name\":\"塞舌尔 +248\",\"zhName\":\"塞舌尔 +248\",\"enName\":\"Seychelles +248\"},{\"code\":\"[+232]\",\"name\":\"塞拉利昂 +232\",\"zhName\":\"塞拉利昂 +232\",\"enName\":\"Sierra Leone +232\"},{\"code\":\"[+65]\",\"name\":\"新加坡 +65\",\"zhName\":\"新加坡 +65\",\"enName\":\"Singapore +65\"},{\"code\":\"[+421]\",\"name\":\"斯洛伐克 +421\",\"zhName\":\"斯洛伐克 +421\",\"enName\":\"Slovakia +421\"},{\"code\":\"[+386]\",\"name\":\"斯洛文尼亚 +386\",\"zhName\":\"斯洛文尼亚 +386\",\"enName\":\"Slovenia +386\"},{\"code\":\"[+677]\",\"name\":\"所罗门群岛 +677\",\"zhName\":\"所罗门群岛 +677\",\"enName\":\"Solomon Islands +677\"},{\"code\":\"[+252]\",\"name\":\"索马里 +252\",\"zhName\":\"索马里 +252\",\"enName\":\"Somalia +252\"},{\"code\":\"[+27]\",\"name\":\"南非 +27\",\"zhName\":\"南非 +27\",\"enName\":\"South Africa +27\"},{\"code\":\"[+34]\",\"name\":\"西班牙 +34\",\"zhName\":\"西班牙 +34\",\"enName\":\"Spain +34\"},{\"code\":\"[+94]\",\"name\":\"斯里兰卡 +94\",\"zhName\":\"斯里兰卡 +94\",\"enName\":\"Sri Lanka +94\"},{\"code\":\"[+1758]\",\"name\":\"圣卢西亚 +1758\",\"zhName\":\"圣卢西亚 +1758\",\"enName\":\"Saint Lucia +1758\"},{\"code\":\"[+249]\",\"name\":\"苏丹 +249\",\"zhName\":\"苏丹 +249\",\"enName\":\"Sudan +249\"},{\"code\":\"[+597]\",\"name\":\"苏里南 +597\",\"zhName\":\"苏里南 +597\",\"enName\":\"Suriname +597\"},{\"code\":\"[+268]\",\"name\":\"斯威士兰 +268\",\"zhName\":\"斯威士兰 +268\",\"enName\":\"Swaziland +268\"},{\"code\":\"[+46]\",\"name\":\"瑞典 +46\",\"zhName\":\"瑞典 +46\",\"enName\":\"Sweden +46\"},{\"code\":\"[+41]\",\"name\":\"瑞士 +41\",\"zhName\":\"瑞士 +41\",\"enName\":\"Switzerland +41\"},{\"code\":\"[+963]\",\"name\":\"叙利亚 +963\",\"zhName\":\"叙利亚 +963\",\"enName\":\"Syria +963\"},{\"code\":\"[+886]\",\"name\":\"中国台湾 +886\",\"zhName\":\"中国台湾 +886\",\"enName\":\"Taiwan +886\"},{\"code\":\"[+992]\",\"name\":\"塔吉克斯坦 +992\",\"zhName\":\"塔吉克斯坦 +992\",\"enName\":\"Tajikistan +992\"},{\"code\":\"[+255]\",\"name\":\"坦桑尼亚 +255\",\"zhName\":\"坦桑尼亚 +255\",\"enName\":\"Tanzania +255\"},{\"code\":\"[+66]\",\"name\":\"泰国 +66\",\"zhName\":\"泰国 +66\",\"enName\":\"Thailand +66\"},{\"code\":\"[+228]\",\"name\":\"多哥 +228\",\"zhName\":\"多哥 +228\",\"enName\":\"Togo +228\"},{\"code\":\"[+676]\",\"name\":\"汤加 +676\",\"zhName\":\"汤加 +676\",\"enName\":\"Tonga +676\"},{\"code\":\"[+216]\",\"name\":\"突尼斯 +216\",\"zhName\":\"突尼斯 +216\",\"enName\":\"Tunisia +216\"},{\"code\":\"[+90]\",\"name\":\"土耳其 +90\",\"zhName\":\"土耳其 +90\",\"enName\":\"Turkey +90\"},{\"code\":\"[+993]\",\"name\":\"土库曼斯坦 +993\",\"zhName\":\"土库曼斯坦 +993\",\"enName\":\"Turkmenistan +993\"},{\"code\":\"[+256]\",\"name\":\"乌干达 +256\",\"zhName\":\"乌干达 +256\",\"enName\":\"Uganda +256\"},{\"code\":\"[+380]\",\"name\":\"乌克兰 +380\",\"zhName\":\"乌克兰 +380\",\"enName\":\"Ukraine +380\"},{\"code\":\"[+971]\",\"name\":\"阿拉伯联合酋长国 +971\",\"zhName\":\"阿拉伯联合酋长国 +971\",\"enName\":\"United Arab Emirates +971\"},{\"code\":\"[+44]\",\"name\":\"英国 +44\",\"zhName\":\"英国 +44\",\"enName\":\"United Kingdom +44\"},{\"code\":\"[+598]\",\"name\":\"乌拉圭 +598\",\"zhName\":\"乌拉圭 +598\",\"enName\":\"Uruguay +598\"},{\"code\":\"[+58]\",\"name\":\"委内瑞拉 +58\",\"zhName\":\"委内瑞拉 +58\",\"enName\":\"Venezuela +58\"},{\"code\":\"[+84]\",\"name\":\"越南 +84\",\"zhName\":\"越南 +84\",\"enName\":\"Vietnam +84\"},{\"code\":\"[+967]\",\"name\":\"也门 +967\",\"zhName\":\"也门 +967\",\"enName\":\"Yemen +967\"},{\"code\":\"[+381]\",\"name\":\"南斯拉夫 +381\",\"zhName\":\"南斯拉夫 +381\",\"enName\":\"Yugoslavia +381\"},{\"code\":\"[+263]\",\"name\":\"津巴布韦 +263\",\"zhName\":\"津巴布韦 +263\",\"enName\":\"Zimbabwe +263\"},{\"code\":\"[+243]\",\"name\":\"扎伊尔 +243\",\"zhName\":\"扎伊尔 +243\",\"enName\":\"Zaire +243\"},{\"code\":\"[+260]\",\"name\":\"赞比亚 +260\",\"zhName\":\"赞比亚 +260\",\"enName\":\"Zambia +260\"}]"
                        },
                        new
                        {
                            Id = 1281498705313372347L,
                            Controls = false,
                            EnPlaceholder = "Please enter",
                            FieldCode = "ContactEmail",
                            FieldComponentType = "FieldInput",
                            FieldEnName = "Email",
                            FieldName = "邮箱",
                            FieldType = (sbyte)0,
                            FieldsGroupType = (sbyte)2,
                            Maxlength = 50m,
                            Number = false,
                            Placeholder = "请输入",
                            Radio = false,
                            RulerFilter = "[{\"code\":null,\"name\":\"邮箱\",\"msg\":\"请输入正确的电子邮箱\",\"pattern\":\"^([\\\\w_\\\\-\\\\.])+\\\\@([\\\\w_\\\\-\\\\.])+\\\\.(\\\\w{2,4})$\",\"enName\":\"Email\",\"enMsg\":\"Please enter the correct email address\",\"zhName\":\"邮箱\",\"zhMsg\":\"请输入正确的电子邮箱\"}]",
                            Textarea = false
                        },
                        new
                        {
                            Id = 1281498705313372349L,
                            Controls = false,
                            DefaultValue = "{\"type\":1,\"value\":null}",
                            EnPlaceholder = "Please enter",
                            FieldCode = "ContactSocialMedia",
                            FieldComponentType = "FieldSelectInput",
                            FieldEnName = "Social Media",
                            FieldName = "社交媒体",
                            FieldType = (sbyte)9,
                            FieldsGroupType = (sbyte)2,
                            Maxlength = 30m,
                            Number = false,
                            Placeholder = "请输入",
                            Radio = false,
                            Textarea = false,
                            ValueRange = "[{\"code\":1,\"name\":\"微信\",\"zhName\":\"微信\",\"enName\":\"Wechat\"},{\"code\":2,\"name\":\"Whatsapp\",\"zhName\":\"Whatsapp\",\"enName\":\"Whatsapp\"},{\"code\":3,\"name\":\"Line\",\"zhName\":\"Line\",\"enName\":\"Line\"},{\"code\":4,\"name\":\"Telegram\",\"zhName\":\"Telegram\",\"enName\":\"Telegram\"},{\"code\":5,\"name\":\"Skype\",\"zhName\":\"Skype\",\"enName\":\"Skype\"},{\"code\":6,\"name\":\"Facebook\",\"zhName\":\"Facebook\",\"enName\":\"Facebook\"},{\"code\":7,\"name\":\"旺旺\",\"zhName\":\"旺旺\",\"enName\":\"Wangwang\"},{\"code\":8,\"name\":\"KakaoTalk\",\"zhName\":\"KakaoTalk\",\"enName\":\"KakaoTalk\"}]"
                        },
                        new
                        {
                            Id = 1281498705313372350L,
                            Controls = false,
                            EnPlaceholder = "Please enter",
                            FieldCode = "TourGuideName",
                            FieldComponentType = "FieldInput",
                            FieldEnName = "Tour Guide Name",
                            FieldName = "导游姓名",
                            FieldType = (sbyte)0,
                            FieldsGroupType = (sbyte)2,
                            Maxlength = 30m,
                            Number = false,
                            Placeholder = "请输入",
                            Radio = false,
                            Textarea = false
                        },
                        new
                        {
                            Id = 1281498705313372352L,
                            Controls = false,
                            DefaultValue = "{\"type\":\"[+86]\",\"value\":null}",
                            EnPlaceholder = "Please enter",
                            FieldCode = "TourGuidePhone",
                            FieldComponentType = "FieldPhoneInput",
                            FieldEnName = "Tour Guide Phone",
                            FieldName = "导游电话",
                            FieldType = (sbyte)9,
                            FieldsGroupType = (sbyte)2,
                            Maxlength = 16m,
                            Number = false,
                            Placeholder = "请输入",
                            Radio = false,
                            RulerFilter = "[{\"code\":\"[+86]\",\"name\":\"导游电话\",\"msg\":\"请输入正确的手机号\",\"pattern\":\"^(0|86|17951)?(1)[0-9]{10}$\",\"enName\":\"Tour Guide Phone\",\"enMsg\":\"Please enter the correct mobile phone number\",\"zhName\":\"导游电话\",\"zhMsg\":\"请输入正确的手机号\"}]",
                            Textarea = false,
                            ValueRange = "[{\"code\":\"[+244]\",\"name\":\"安哥拉 +244\",\"zhName\":\"安哥拉 +244\",\"enName\":\"Angola +244\"},{\"code\":\"[+93]\",\"name\":\"阿富汗 +93\",\"zhName\":\"阿富汗 +93\",\"enName\":\"Afghanistan +93\"},{\"code\":\"[+1907]\",\"name\":\"阿拉斯加 +1907\",\"zhName\":\"阿拉斯加 +1907\",\"enName\":\"Alaska +1907\"},{\"code\":\"[+355]\",\"name\":\"阿尔巴尼亚 +355\",\"zhName\":\"阿尔巴尼亚 +355\",\"enName\":\"Albania +355\"},{\"code\":\"[+213]\",\"name\":\"阿尔及利亚 +213\",\"zhName\":\"阿尔及利亚 +213\",\"enName\":\"Algeria +213\"},{\"code\":\"[+376]\",\"name\":\"安道尔共和国 +376\",\"zhName\":\"安道尔共和国 +376\",\"enName\":\"Andorra +376\"},{\"code\":\"[+1254]\",\"name\":\"安圭拉岛 +1254\",\"zhName\":\"安圭拉岛 +1254\",\"enName\":\"Anguilla +1254\"},{\"code\":\"[+1268]\",\"name\":\"安提瓜和巴布达 +1268\",\"zhName\":\"安提瓜和巴布达 +1268\",\"enName\":\"Antigua and Barbuda +1268\"},{\"code\":\"[+54]\",\"name\":\"阿根廷 +54\",\"zhName\":\"阿根廷 +54\",\"enName\":\"Argentina +54\"},{\"code\":\"[+374]\",\"name\":\"亚美尼亚 +374\",\"zhName\":\"亚美尼亚 +374\",\"enName\":\"Armenia +374\"},{\"code\":\"[+297]\",\"name\":\"阿鲁巴岛 +297\",\"zhName\":\"阿鲁巴岛 +297\",\"enName\":\"Aruba +297\"},{\"code\":\"[+247]\",\"name\":\"阿森松 +247\",\"zhName\":\"阿森松 +247\",\"enName\":\"Ascension +247\"},{\"code\":\"[+61]\",\"name\":\"澳大利亚 +61\",\"zhName\":\"澳大利亚 +61\",\"enName\":\"Australia +61\"},{\"code\":\"[+43]\",\"name\":\"奥地利 +43\",\"zhName\":\"奥地利 +43\",\"enName\":\"Austria +43\"},{\"code\":\"[+994]\",\"name\":\"阿塞拜疆 +994\",\"zhName\":\"阿塞拜疆 +994\",\"enName\":\"Azerbaijan +994\"},{\"code\":\"[+1242]\",\"name\":\"巴哈马 +1242\",\"zhName\":\"巴哈马 +1242\",\"enName\":\"Bahamas +1242\"},{\"code\":\"[+973]\",\"name\":\"巴林 +973\",\"zhName\":\"巴林 +973\",\"enName\":\"Bahrain +973\"},{\"code\":\"[+880]\",\"name\":\"孟加拉国 +880\",\"zhName\":\"孟加拉国 +880\",\"enName\":\"Bangladesh +880\"},{\"code\":\"[+1246]\",\"name\":\"巴巴多斯 +1246\",\"zhName\":\"巴巴多斯 +1246\",\"enName\":\"Barbados +1246\"},{\"code\":\"[+375]\",\"name\":\"白俄罗斯 +375\",\"zhName\":\"白俄罗斯 +375\",\"enName\":\"Belarus +375\"},{\"code\":\"[+32]\",\"name\":\"比利时 +32\",\"zhName\":\"比利时 +32\",\"enName\":\"Belgium +32\"},{\"code\":\"[+501]\",\"name\":\"伯利兹 +501\",\"zhName\":\"伯利兹 +501\",\"enName\":\"Belize +501\"},{\"code\":\"[+229]\",\"name\":\"贝宁 +229\",\"zhName\":\"贝宁 +229\",\"enName\":\"Benin +229\"},{\"code\":\"[+1441]\",\"name\":\"百慕大群岛 +1441\",\"zhName\":\"百慕大群岛 +1441\",\"enName\":\"Bermuda +1441\"},{\"code\":\"[+975]\",\"name\":\"不丹 +975\",\"zhName\":\"不丹 +975\",\"enName\":\"Bhutan +975\"},{\"code\":\"[+591]\",\"name\":\"玻利维亚 +591\",\"zhName\":\"玻利维亚 +591\",\"enName\":\"Bolivia +591\"},{\"code\":\"[+387]\",\"name\":\"波斯尼亚和黑塞哥维那 +387\",\"zhName\":\"波斯尼亚和黑塞哥维那 +387\",\"enName\":\"Bosnia and Herzegovina +387\"},{\"code\":\"[+267]\",\"name\":\"博茨瓦纳 +267\",\"zhName\":\"博茨瓦纳 +267\",\"enName\":\"Botswana +267\"},{\"code\":\"[+55]\",\"name\":\"巴西 +55\",\"zhName\":\"巴西 +55\",\"enName\":\"Brazil +55\"},{\"code\":\"[+673]\",\"name\":\"文莱 +673\",\"zhName\":\"文莱 +673\",\"enName\":\"Brunei +673\"},{\"code\":\"[+359]\",\"name\":\"保加利亚 +359\",\"zhName\":\"保加利亚 +359\",\"enName\":\"Bulgaria +359\"},{\"code\":\"[+226]\",\"name\":\"布基纳法索 +226\",\"zhName\":\"布基纳法索 +226\",\"enName\":\"Burkina Faso +226\"},{\"code\":\"[+95]\",\"name\":\"缅甸 +95\",\"zhName\":\"缅甸 +95\",\"enName\":\"Myanmar +95\"},{\"code\":\"[+257]\",\"name\":\"布隆迪 +257\",\"zhName\":\"布隆迪 +257\",\"enName\":\"Burundi +257\"},{\"code\":\"[+237]\",\"name\":\"喀麦隆 +237\",\"zhName\":\"喀麦隆 +237\",\"enName\":\"Cameroon +237\"},{\"code\":\"[+1]\",\"name\":\"加拿大/美国 +1\",\"zhName\":\"加拿大/美国 +1\",\"enName\":\"Canada/USA +1\"},{\"code\":\"[+238]\",\"name\":\"佛得角 +238\",\"zhName\":\"佛得角 +238\",\"enName\":\"Cape Verde +238\"},{\"code\":\"[+1345]\",\"name\":\"开曼群岛 +1345\",\"zhName\":\"开曼群岛 +1345\",\"enName\":\"Cayman Islands +1345\"},{\"code\":\"[+236]\",\"name\":\"中非共和国 +236\",\"zhName\":\"中非共和国 +236\",\"enName\":\"Central African Republic +236\"},{\"code\":\"[+235]\",\"name\":\"乍得 +235\",\"zhName\":\"乍得 +235\",\"enName\":\"Chad +235\"},{\"code\":\"[+56]\",\"name\":\"智利 +56\",\"zhName\":\"智利 +56\",\"enName\":\"Chile +56\"},{\"code\":\"[+86]\",\"name\":\"中国大陆 +86\",\"zhName\":\"中国大陆 +86\",\"enName\":\"Mainland China +86\"},{\"code\":\"[+57]\",\"name\":\"哥伦比亚 +57\",\"zhName\":\"哥伦比亚 +57\",\"enName\":\"Colombia +57\"},{\"code\":\"[+242]\",\"name\":\"刚果 +242\",\"zhName\":\"刚果 +242\",\"enName\":\"Congo +242\"},{\"code\":\"[+682]\",\"name\":\"库克群岛 +682\",\"zhName\":\"库克群岛 +682\",\"enName\":\"Cook Islands +682\"},{\"code\":\"[+506]\",\"name\":\"哥斯达黎加 +506\",\"zhName\":\"哥斯达黎加 +506\",\"enName\":\"Costa Rica +506\"},{\"code\":\"[+53]\",\"name\":\"古巴 +53\",\"zhName\":\"古巴 +53\",\"enName\":\"Cuba +53\"},{\"code\":\"[+357]\",\"name\":\"塞浦路斯 +357\",\"zhName\":\"塞浦路斯 +357\",\"enName\":\"Cyprus +357\"},{\"code\":\"[+420]\",\"name\":\"捷克 +420\",\"zhName\":\"捷克 +420\",\"enName\":\"Czech Republic +420\"},{\"code\":\"[+385]\",\"name\":\"克罗地亚共和国 +385\",\"zhName\":\"克罗地亚共和国 +385\",\"enName\":\"Croatia +385\"},{\"code\":\"[+45]\",\"name\":\"丹麦 +45\",\"zhName\":\"丹麦 +45\",\"enName\":\"Denmark +45\"},{\"code\":\"[+253]\",\"name\":\"吉布提 +253\",\"zhName\":\"吉布提 +253\",\"enName\":\"Djibouti +253\"},{\"code\":\"[+1890]\",\"name\":\"多米尼加共和国 +1890\",\"zhName\":\"多米尼加共和国 +1890\",\"enName\":\"Dominican Republic +1890\"},{\"code\":\"[+593]\",\"name\":\"厄瓜多尔 +593\",\"zhName\":\"厄瓜多尔 +593\",\"enName\":\"Ecuador +593\"},{\"code\":\"[+20]\",\"name\":\"埃及 +20\",\"zhName\":\"埃及 +20\",\"enName\":\"Egypt +20\"},{\"code\":\"[+503]\",\"name\":\"萨尔瓦多 +503\",\"zhName\":\"萨尔瓦多 +503\",\"enName\":\"El Salvador +503\"},{\"code\":\"[+372]\",\"name\":\"爱沙尼亚 +372\",\"zhName\":\"爱沙尼亚 +372\",\"enName\":\"Estonia +372\"},{\"code\":\"[+251]\",\"name\":\"埃塞俄比亚 +251\",\"zhName\":\"埃塞俄比亚 +251\",\"enName\":\"Ethiopia +251\"},{\"code\":\"[+679]\",\"name\":\"斐济 +679\",\"zhName\":\"斐济 +679\",\"enName\":\"Fiji +679\"},{\"code\":\"[+358]\",\"name\":\"芬兰 +358\",\"zhName\":\"芬兰 +358\",\"enName\":\"Finland +358\"},{\"code\":\"[+33]\",\"name\":\"法国 +33\",\"zhName\":\"法国 +33\",\"enName\":\"France +33\"},{\"code\":\"[+594]\",\"name\":\"法属圭亚那 +594\",\"zhName\":\"法属圭亚那 +594\",\"enName\":\"French Guiana +594\"},{\"code\":\"[+689]\",\"name\":\"法属玻利尼西亚 +689\",\"zhName\":\"法属玻利尼西亚 +689\",\"enName\":\"French Polynesia +689\"},{\"code\":\"[+241]\",\"name\":\"加蓬 +241\",\"zhName\":\"加蓬 +241\",\"enName\":\"Gabon +241\"},{\"code\":\"[+220]\",\"name\":\"冈比亚 +220\",\"zhName\":\"冈比亚 +220\",\"enName\":\"Gambia +220\"},{\"code\":\"[+995]\",\"name\":\"格鲁吉亚 +995\",\"zhName\":\"格鲁吉亚 +995\",\"enName\":\"Georgia +995\"},{\"code\":\"[+49]\",\"name\":\"德国 +49\",\"zhName\":\"德国 +49\",\"enName\":\"Germany +49\"},{\"code\":\"[+233]\",\"name\":\"加纳/乌兹别克斯坦 +233\",\"zhName\":\"加纳/乌兹别克斯坦 +233\",\"enName\":\"Ghana/Uzbekistan +233\"},{\"code\":\"[+350]\",\"name\":\"直布罗陀 +350\",\"zhName\":\"直布罗陀 +350\",\"enName\":\"Gibraltar +350\"},{\"code\":\"[+30]\",\"name\":\"希腊 +30\",\"zhName\":\"希腊 +30\",\"enName\":\"Greece +30\"},{\"code\":\"[+1809]\",\"name\":\"格林纳达/特立尼达和多巴哥 +1809\",\"zhName\":\"格林纳达/特立尼达和多巴哥 +1809\",\"enName\":\"Grenada/Trinidad and Tobago +1809\"},{\"code\":\"[+1671]\",\"name\":\"关岛 +1671\",\"zhName\":\"关岛 +1671\",\"enName\":\"Guam +1671\"},{\"code\":\"[+502]\",\"name\":\"危地马拉 +502\",\"zhName\":\"危地马拉 +502\",\"enName\":\"Guatemala +502\"},{\"code\":\"[+224]\",\"name\":\"几内亚 +224\",\"zhName\":\"几内亚 +224\",\"enName\":\"Guinea +224\"},{\"code\":\"[+592]\",\"name\":\"圭亚那 +592\",\"zhName\":\"圭亚那 +592\",\"enName\":\"Guyana +592\"},{\"code\":\"[+509]\",\"name\":\"海地 +509\",\"zhName\":\"海地 +509\",\"enName\":\"Haiti +509\"},{\"code\":\"[+504]\",\"name\":\"洪都拉斯 +504\",\"zhName\":\"洪都拉斯 +504\",\"enName\":\"Honduras +504\"},{\"code\":\"[+852]\",\"name\":\"中国香港 +852\",\"zhName\":\"中国香港 +852\",\"enName\":\"Hong Kong +852\"},{\"code\":\"[+36]\",\"name\":\"匈牙利 +36\",\"zhName\":\"匈牙利 +36\",\"enName\":\"Hungary +36\"},{\"code\":\"[+354]\",\"name\":\"冰岛 +354\",\"zhName\":\"冰岛 +354\",\"enName\":\"Iceland +354\"},{\"code\":\"[+91]\",\"name\":\"印度 +91\",\"zhName\":\"印度 +91\",\"enName\":\"India +91\"},{\"code\":\"[+62]\",\"name\":\"印度尼西亚 +62\",\"zhName\":\"印度尼西亚 +62\",\"enName\":\"Indonesia +62\"},{\"code\":\"[+98]\",\"name\":\"伊朗 +98\",\"zhName\":\"伊朗 +98\",\"enName\":\"Iran +98\"},{\"code\":\"[+964]\",\"name\":\"伊拉克 +964\",\"zhName\":\"伊拉克 +964\",\"enName\":\"Iraq +964\"},{\"code\":\"[+353]\",\"name\":\"爱尔兰 +353\",\"zhName\":\"爱尔兰 +353\",\"enName\":\"Ireland +353\"},{\"code\":\"[+972]\",\"name\":\"以色列 +972\",\"zhName\":\"以色列 +972\",\"enName\":\"Israel +972\"},{\"code\":\"[+39]\",\"name\":\"意大利 +39\",\"zhName\":\"意大利 +39\",\"enName\":\"Italy +39\"},{\"code\":\"[+225]\",\"name\":\"科特迪瓦 +225\",\"zhName\":\"科特迪瓦 +225\",\"enName\":\"Côte d'Ivoire +225\"},{\"code\":\"[+1876]\",\"name\":\"牙买加 +1876\",\"zhName\":\"牙买加 +1876\",\"enName\":\"Jamaica +1876\"},{\"code\":\"[+81]\",\"name\":\"日本 +81\",\"zhName\":\"日本 +81\",\"enName\":\"Japan +81\"},{\"code\":\"[+962]\",\"name\":\"约旦 +962\",\"zhName\":\"约旦 +962\",\"enName\":\"Jordan +962\"},{\"code\":\"[+855]\",\"name\":\"柬埔寨 +855\",\"zhName\":\"柬埔寨 +855\",\"enName\":\"Cambodia +855\"},{\"code\":\"[+7]\",\"name\":\"哈萨克斯坦/俄罗斯 +7\",\"zhName\":\"哈萨克斯坦/俄罗斯 +7\",\"enName\":\"Kazakhstan/Russia +7\"},{\"code\":\"[+254]\",\"name\":\"肯尼亚 +254\",\"zhName\":\"肯尼亚 +254\",\"enName\":\"Kenya +254\"},{\"code\":\"[+82]\",\"name\":\"韩国 +82\",\"zhName\":\"韩国 +82\",\"enName\":\"South Korea +82\"},{\"code\":\"[+965]\",\"name\":\"科威特 +965\",\"zhName\":\"科威特 +965\",\"enName\":\"Kuwait +965\"},{\"code\":\"[+331]\",\"name\":\"吉尔吉斯坦 +331\",\"zhName\":\"吉尔吉斯坦 +331\",\"enName\":\"Kyrgyzstan +331\"},{\"code\":\"[+856]\",\"name\":\"老挝 +856\",\"zhName\":\"老挝 +856\",\"enName\":\"Laos +856\"},{\"code\":\"[+371]\",\"name\":\"拉脱维亚 +371\",\"zhName\":\"拉脱维亚 +371\",\"enName\":\"Latvia +371\"},{\"code\":\"[+961]\",\"name\":\"黎巴嫩 +961\",\"zhName\":\"黎巴嫩 +961\",\"enName\":\"Lebanon +961\"},{\"code\":\"[+266]\",\"name\":\"莱索托 +266\",\"zhName\":\"莱索托 +266\",\"enName\":\"Lesotho +266\"},{\"code\":\"[+231]\",\"name\":\"利比里亚 +231\",\"zhName\":\"利比里亚 +231\",\"enName\":\"Liberia +231\"},{\"code\":\"[+218]\",\"name\":\"利比亚 +218\",\"zhName\":\"利比亚 +218\",\"enName\":\"Libya +218\"},{\"code\":\"[+423]\",\"name\":\"列支敦士登 +423\",\"zhName\":\"列支敦士登 +423\",\"enName\":\"Liechtenstein +423\"},{\"code\":\"[+370]\",\"name\":\"立陶宛 +370\",\"zhName\":\"立陶宛 +370\",\"enName\":\"Lithuania +370\"},{\"code\":\"[+352]\",\"name\":\"卢森堡 +352\",\"zhName\":\"卢森堡 +352\",\"enName\":\"Luxembourg +352\"},{\"code\":\"[+853]\",\"name\":\"中国澳门 +853\",\"zhName\":\"中国澳门 +853\",\"enName\":\"Macau +853\"},{\"code\":\"[+261]\",\"name\":\"马达加斯加 +261\",\"zhName\":\"马达加斯加 +261\",\"enName\":\"Madagascar +261\"},{\"code\":\"[+265]\",\"name\":\"马拉维 +265\",\"zhName\":\"马拉维 +265\",\"enName\":\"Malawi +265\"},{\"code\":\"[+60]\",\"name\":\"马来西亚 +60\",\"zhName\":\"马来西亚 +60\",\"enName\":\"Malaysia +60\"},{\"code\":\"[+960]\",\"name\":\"马尔代夫 +960\",\"zhName\":\"马尔代夫 +960\",\"enName\":\"Maldives +960\"},{\"code\":\"[+223]\",\"name\":\"马里 +223\",\"zhName\":\"马里 +223\",\"enName\":\"Mali +223\"},{\"code\":\"[+356]\",\"name\":\"马耳他 +356\",\"zhName\":\"马耳他 +356\",\"enName\":\"Malta +356\"},{\"code\":\"[+1670]\",\"name\":\"马里亚那群岛 +1670\",\"zhName\":\"马里亚那群岛 +1670\",\"enName\":\"Mariana Islands +1670\"},{\"code\":\"[+596]\",\"name\":\"马提尼克 +596\",\"zhName\":\"马提尼克 +596\",\"enName\":\"Martinique +596\"},{\"code\":\"[+230]\",\"name\":\"毛里求斯 +230\",\"zhName\":\"毛里求斯 +230\",\"enName\":\"Mauritius +230\"},{\"code\":\"[+52]\",\"name\":\"墨西哥 +52\",\"zhName\":\"墨西哥 +52\",\"enName\":\"Mexico +52\"},{\"code\":\"[+373]\",\"name\":\"摩尔多瓦 +373\",\"zhName\":\"摩尔多瓦 +373\",\"enName\":\"Moldova +373\"},{\"code\":\"[+377]\",\"name\":\"摩纳哥 +377\",\"zhName\":\"摩纳哥 +377\",\"enName\":\"Monaco +377\"},{\"code\":\"[+976]\",\"name\":\"蒙古 +976\",\"zhName\":\"蒙古 +976\",\"enName\":\"Mongolia +976\"},{\"code\":\"[+1664]\",\"name\":\"蒙特塞拉特岛 +1664\",\"zhName\":\"蒙特塞拉特岛 +1664\",\"enName\":\"Montserrat +1664\"},{\"code\":\"[+212]\",\"name\":\"摩洛哥 +212\",\"zhName\":\"摩洛哥 +212\",\"enName\":\"Morocco +212\"},{\"code\":\"[+258]\",\"name\":\"莫桑比克 +258\",\"zhName\":\"莫桑比克 +258\",\"enName\":\"Mozambique +258\"},{\"code\":\"[+264]\",\"name\":\"纳米比亚 +264\",\"zhName\":\"纳米比亚 +264\",\"enName\":\"Namibia +264\"},{\"code\":\"[+674]\",\"name\":\"瑙鲁 +674\",\"zhName\":\"瑙鲁 +674\",\"enName\":\"Nauru +674\"},{\"code\":\"[+977]\",\"name\":\"尼泊尔 +977\",\"zhName\":\"尼泊尔 +977\",\"enName\":\"Nepal +977\"},{\"code\":\"[+599]\",\"name\":\"荷属安的列斯 +599\",\"zhName\":\"荷属安的列斯 +599\",\"enName\":\"Netherlands Antilles +599\"},{\"code\":\"[+31]\",\"name\":\"荷兰 +31\",\"zhName\":\"荷兰 +31\",\"enName\":\"Netherlands +31\"},{\"code\":\"[+64]\",\"name\":\"新西兰 +64\",\"zhName\":\"新西兰 +64\",\"enName\":\"New Zealand +64\"},{\"code\":\"[+505]\",\"name\":\"尼加拉瓜 +505\",\"zhName\":\"尼加拉瓜 +505\",\"enName\":\"Nicaragua +505\"},{\"code\":\"[+227]\",\"name\":\"尼日尔 +227\",\"zhName\":\"尼日尔 +227\",\"enName\":\"Niger +227\"},{\"code\":\"[+234]\",\"name\":\"尼日利亚 +234\",\"zhName\":\"尼日利亚 +234\",\"enName\":\"Nigeria +234\"},{\"code\":\"[+850]\",\"name\":\"朝鲜 +850\",\"zhName\":\"朝鲜 +850\",\"enName\":\"North Korea +850\"},{\"code\":\"[+47]\",\"name\":\"挪威 +47\",\"zhName\":\"挪威 +47\",\"enName\":\"Norway +47\"},{\"code\":\"[+968]\",\"name\":\"阿曼 +968\",\"zhName\":\"阿曼 +968\",\"enName\":\"Oman +968\"},{\"code\":\"[+92]\",\"name\":\"巴基斯坦 +92\",\"zhName\":\"巴基斯坦 +92\",\"enName\":\"Pakistan +92\"},{\"code\":\"[+507]\",\"name\":\"巴拿马 +507\",\"zhName\":\"巴拿马 +507\",\"enName\":\"Panama +507\"},{\"code\":\"[+675]\",\"name\":\"巴布亚新几内亚 +675\",\"zhName\":\"巴布亚新几内亚 +675\",\"enName\":\"Papua New Guinea +675\"},{\"code\":\"[+595]\",\"name\":\"巴拉圭 +595\",\"zhName\":\"巴拉圭 +595\",\"enName\":\"Paraguay +595\"},{\"code\":\"[+51]\",\"name\":\"秘鲁 +51\",\"zhName\":\"秘鲁 +51\",\"enName\":\"Peru +51\"},{\"code\":\"[+63]\",\"name\":\"菲律宾 +63\",\"zhName\":\"菲律宾 +63\",\"enName\":\"Philippines +63\"},{\"code\":\"[+48]\",\"name\":\"波兰 +48\",\"zhName\":\"波兰 +48\",\"enName\":\"Poland +48\"},{\"code\":\"[+351]\",\"name\":\"葡萄牙 +351\",\"zhName\":\"葡萄牙 +351\",\"enName\":\"Portugal +351\"},{\"code\":\"[+1787]\",\"name\":\"波多黎各 +1787\",\"zhName\":\"波多黎各 +1787\",\"enName\":\"Puerto Rico +1787\"},{\"code\":\"[+974]\",\"name\":\"卡塔尔 +974\",\"zhName\":\"卡塔尔 +974\",\"enName\":\"Qatar +974\"},{\"code\":\"[+262]\",\"name\":\"留尼旺 +262\",\"zhName\":\"留尼旺 +262\",\"enName\":\"Reunion +262\"},{\"code\":\"[+40]\",\"name\":\"罗马尼亚 +40\",\"zhName\":\"罗马尼亚 +40\",\"enName\":\"Romania +40\"},{\"code\":\"[+1784]\",\"name\":\"圣文森特岛/圣文森特 +1784\",\"zhName\":\"圣文森特岛/圣文森特 +1784\",\"enName\":\"St. Vincent Island/St. Vincent +1784\"},{\"code\":\"[+684]\",\"name\":\"东萨摩亚(美) +684\",\"zhName\":\"东萨摩亚(美) +684\",\"enName\":\"Eastern Samoa (US) +684\"},{\"code\":\"[+685]\",\"name\":\"西萨摩亚 +685\",\"zhName\":\"西萨摩亚 +685\",\"enName\":\"Western Samoa +685\"},{\"code\":\"[+378]\",\"name\":\"圣马力诺 +378\",\"zhName\":\"圣马力诺 +378\",\"enName\":\"San Marino +378\"},{\"code\":\"[+239]\",\"name\":\"圣多美和普林西比 +239\",\"zhName\":\"圣多美和普林西比 +239\",\"enName\":\"Sao Tome and Principe +239\"},{\"code\":\"[+966]\",\"name\":\"沙特阿拉伯 +966\",\"zhName\":\"沙特阿拉伯 +966\",\"enName\":\"Saudi Arabia +966\"},{\"code\":\"[+221]\",\"name\":\"塞内加尔 +221\",\"zhName\":\"塞内加尔 +221\",\"enName\":\"Senegal +221\"},{\"code\":\"[+248]\",\"name\":\"塞舌尔 +248\",\"zhName\":\"塞舌尔 +248\",\"enName\":\"Seychelles +248\"},{\"code\":\"[+232]\",\"name\":\"塞拉利昂 +232\",\"zhName\":\"塞拉利昂 +232\",\"enName\":\"Sierra Leone +232\"},{\"code\":\"[+65]\",\"name\":\"新加坡 +65\",\"zhName\":\"新加坡 +65\",\"enName\":\"Singapore +65\"},{\"code\":\"[+421]\",\"name\":\"斯洛伐克 +421\",\"zhName\":\"斯洛伐克 +421\",\"enName\":\"Slovakia +421\"},{\"code\":\"[+386]\",\"name\":\"斯洛文尼亚 +386\",\"zhName\":\"斯洛文尼亚 +386\",\"enName\":\"Slovenia +386\"},{\"code\":\"[+677]\",\"name\":\"所罗门群岛 +677\",\"zhName\":\"所罗门群岛 +677\",\"enName\":\"Solomon Islands +677\"},{\"code\":\"[+252]\",\"name\":\"索马里 +252\",\"zhName\":\"索马里 +252\",\"enName\":\"Somalia +252\"},{\"code\":\"[+27]\",\"name\":\"南非 +27\",\"zhName\":\"南非 +27\",\"enName\":\"South Africa +27\"},{\"code\":\"[+34]\",\"name\":\"西班牙 +34\",\"zhName\":\"西班牙 +34\",\"enName\":\"Spain +34\"},{\"code\":\"[+94]\",\"name\":\"斯里兰卡 +94\",\"zhName\":\"斯里兰卡 +94\",\"enName\":\"Sri Lanka +94\"},{\"code\":\"[+1758]\",\"name\":\"圣卢西亚 +1758\",\"zhName\":\"圣卢西亚 +1758\",\"enName\":\"Saint Lucia +1758\"},{\"code\":\"[+249]\",\"name\":\"苏丹 +249\",\"zhName\":\"苏丹 +249\",\"enName\":\"Sudan +249\"},{\"code\":\"[+597]\",\"name\":\"苏里南 +597\",\"zhName\":\"苏里南 +597\",\"enName\":\"Suriname +597\"},{\"code\":\"[+268]\",\"name\":\"斯威士兰 +268\",\"zhName\":\"斯威士兰 +268\",\"enName\":\"Swaziland +268\"},{\"code\":\"[+46]\",\"name\":\"瑞典 +46\",\"zhName\":\"瑞典 +46\",\"enName\":\"Sweden +46\"},{\"code\":\"[+41]\",\"name\":\"瑞士 +41\",\"zhName\":\"瑞士 +41\",\"enName\":\"Switzerland +41\"},{\"code\":\"[+963]\",\"name\":\"叙利亚 +963\",\"zhName\":\"叙利亚 +963\",\"enName\":\"Syria +963\"},{\"code\":\"[+886]\",\"name\":\"中国台湾 +886\",\"zhName\":\"中国台湾 +886\",\"enName\":\"Taiwan +886\"},{\"code\":\"[+992]\",\"name\":\"塔吉克斯坦 +992\",\"zhName\":\"塔吉克斯坦 +992\",\"enName\":\"Tajikistan +992\"},{\"code\":\"[+255]\",\"name\":\"坦桑尼亚 +255\",\"zhName\":\"坦桑尼亚 +255\",\"enName\":\"Tanzania +255\"},{\"code\":\"[+66]\",\"name\":\"泰国 +66\",\"zhName\":\"泰国 +66\",\"enName\":\"Thailand +66\"},{\"code\":\"[+228]\",\"name\":\"多哥 +228\",\"zhName\":\"多哥 +228\",\"enName\":\"Togo +228\"},{\"code\":\"[+676]\",\"name\":\"汤加 +676\",\"zhName\":\"汤加 +676\",\"enName\":\"Tonga +676\"},{\"code\":\"[+216]\",\"name\":\"突尼斯 +216\",\"zhName\":\"突尼斯 +216\",\"enName\":\"Tunisia +216\"},{\"code\":\"[+90]\",\"name\":\"土耳其 +90\",\"zhName\":\"土耳其 +90\",\"enName\":\"Turkey +90\"},{\"code\":\"[+993]\",\"name\":\"土库曼斯坦 +993\",\"zhName\":\"土库曼斯坦 +993\",\"enName\":\"Turkmenistan +993\"},{\"code\":\"[+256]\",\"name\":\"乌干达 +256\",\"zhName\":\"乌干达 +256\",\"enName\":\"Uganda +256\"},{\"code\":\"[+380]\",\"name\":\"乌克兰 +380\",\"zhName\":\"乌克兰 +380\",\"enName\":\"Ukraine +380\"},{\"code\":\"[+971]\",\"name\":\"阿拉伯联合酋长国 +971\",\"zhName\":\"阿拉伯联合酋长国 +971\",\"enName\":\"United Arab Emirates +971\"},{\"code\":\"[+44]\",\"name\":\"英国 +44\",\"zhName\":\"英国 +44\",\"enName\":\"United Kingdom +44\"},{\"code\":\"[+598]\",\"name\":\"乌拉圭 +598\",\"zhName\":\"乌拉圭 +598\",\"enName\":\"Uruguay +598\"},{\"code\":\"[+58]\",\"name\":\"委内瑞拉 +58\",\"zhName\":\"委内瑞拉 +58\",\"enName\":\"Venezuela +58\"},{\"code\":\"[+84]\",\"name\":\"越南 +84\",\"zhName\":\"越南 +84\",\"enName\":\"Vietnam +84\"},{\"code\":\"[+967]\",\"name\":\"也门 +967\",\"zhName\":\"也门 +967\",\"enName\":\"Yemen +967\"},{\"code\":\"[+381]\",\"name\":\"南斯拉夫 +381\",\"zhName\":\"南斯拉夫 +381\",\"enName\":\"Yugoslavia +381\"},{\"code\":\"[+263]\",\"name\":\"津巴布韦 +263\",\"zhName\":\"津巴布韦 +263\",\"enName\":\"Zimbabwe +263\"},{\"code\":\"[+243]\",\"name\":\"扎伊尔 +243\",\"zhName\":\"扎伊尔 +243\",\"enName\":\"Zaire +243\"},{\"code\":\"[+260]\",\"name\":\"赞比亚 +260\",\"zhName\":\"赞比亚 +260\",\"enName\":\"Zambia +260\"}]"
                        },
                        new
                        {
                            Id = 1281498705313372353L,
                            Controls = false,
                            EnPlaceholder = "Please enter",
                            FieldCode = "TourGuideEmail",
                            FieldComponentType = "FieldInput",
                            FieldEnName = "Tour Guide Email",
                            FieldName = "导游邮箱",
                            FieldType = (sbyte)0,
                            FieldsGroupType = (sbyte)2,
                            Maxlength = 50m,
                            Number = false,
                            Placeholder = "请输入",
                            Radio = false,
                            RulerFilter = "[{\"code\":null,\"name\":\"邮箱\",\"msg\":\"请输入正确的电子邮箱\",\"pattern\":\"^([\\\\w_\\\\-\\\\.])+\\\\@([\\\\w_\\\\-\\\\.])+\\\\.(\\\\w{2,4})$\",\"enName\":\"Tour Guide Email\",\"enMsg\":\"Please enter the correct email address\",\"zhName\":\"邮箱\",\"zhMsg\":\"请输入正确的电子邮箱\"}]",
                            Textarea = false
                        },
                        new
                        {
                            Id = 1281498705313372355L,
                            Controls = false,
                            DefaultValue = "{\"type\":1,\"value\":null}",
                            EnPlaceholder = "Please enter",
                            FieldCode = "TourGuideSocialMedia",
                            FieldComponentType = "FieldSelectInput",
                            FieldEnName = "Tour Guide Social Media",
                            FieldName = "导游社交媒体",
                            FieldType = (sbyte)9,
                            FieldsGroupType = (sbyte)2,
                            Maxlength = 30m,
                            Number = false,
                            Placeholder = "请输入",
                            Radio = false,
                            Textarea = false,
                            ValueRange = "[{\"code\":1,\"name\":\"微信\",\"zhName\":\"微信\",\"enName\":\"Wechat\"},{\"code\":2,\"name\":\"Whatsapp\",\"zhName\":\"Whatsapp\",\"enName\":\"Whatsapp\"},{\"code\":3,\"name\":\"Line\",\"zhName\":\"Line\",\"enName\":\"Line\"},{\"code\":4,\"name\":\"Telegram\",\"zhName\":\"Telegram\",\"enName\":\"Telegram\"},{\"code\":5,\"name\":\"Skype\",\"zhName\":\"Skype\",\"enName\":\"Skype\"},{\"code\":6,\"name\":\"Facebook\",\"zhName\":\"Facebook\",\"enName\":\"Facebook\"},{\"code\":7,\"name\":\"旺旺\",\"zhName\":\"旺旺\",\"enName\":\"Wangwang\"},{\"code\":8,\"name\":\"KakaoTalk\",\"zhName\":\"KakaoTalk\",\"enName\":\"KakaoTalk\"}]"
                        },
                        new
                        {
                            Id = 1281498705313372356L,
                            Controls = false,
                            EnPlaceholder = "Please enter",
                            FieldCode = "DriverName",
                            FieldComponentType = "FieldInput",
                            FieldEnName = "Driver Name",
                            FieldName = "司机姓名",
                            FieldType = (sbyte)0,
                            FieldsGroupType = (sbyte)2,
                            Maxlength = 30m,
                            Number = false,
                            Placeholder = "请输入",
                            Radio = false,
                            Textarea = false
                        },
                        new
                        {
                            Id = 1281498705313372358L,
                            Controls = false,
                            DefaultValue = "{\"type\":\"[+86]\",\"value\":null}",
                            EnPlaceholder = "Please enter",
                            FieldCode = "DriverPhone",
                            FieldComponentType = "FieldPhoneInput",
                            FieldEnName = "Driver Phone",
                            FieldName = "司机电话",
                            FieldType = (sbyte)9,
                            FieldsGroupType = (sbyte)2,
                            Maxlength = 16m,
                            Number = false,
                            Placeholder = "请输入",
                            Radio = false,
                            RulerFilter = "[{\"code\":\"[+86]\",\"name\":\"司机电话\",\"msg\":\"请输入正确的手机号\",\"pattern\":\"^(0|86|17951)?(1)[0-9]{10}$\",\"enName\":\"Driver Phone\",\"enMsg\":\"Please enter the correct mobile phone number\",\"zhName\":\"司机电话\",\"zhMsg\":\"请输入正确的手机号\"}]",
                            Textarea = false,
                            ValueRange = "[{\"code\":\"[+244]\",\"name\":\"安哥拉 +244\",\"zhName\":\"安哥拉 +244\",\"enName\":\"Angola +244\"},{\"code\":\"[+93]\",\"name\":\"阿富汗 +93\",\"zhName\":\"阿富汗 +93\",\"enName\":\"Afghanistan +93\"},{\"code\":\"[+1907]\",\"name\":\"阿拉斯加 +1907\",\"zhName\":\"阿拉斯加 +1907\",\"enName\":\"Alaska +1907\"},{\"code\":\"[+355]\",\"name\":\"阿尔巴尼亚 +355\",\"zhName\":\"阿尔巴尼亚 +355\",\"enName\":\"Albania +355\"},{\"code\":\"[+213]\",\"name\":\"阿尔及利亚 +213\",\"zhName\":\"阿尔及利亚 +213\",\"enName\":\"Algeria +213\"},{\"code\":\"[+376]\",\"name\":\"安道尔共和国 +376\",\"zhName\":\"安道尔共和国 +376\",\"enName\":\"Andorra +376\"},{\"code\":\"[+1254]\",\"name\":\"安圭拉岛 +1254\",\"zhName\":\"安圭拉岛 +1254\",\"enName\":\"Anguilla +1254\"},{\"code\":\"[+1268]\",\"name\":\"安提瓜和巴布达 +1268\",\"zhName\":\"安提瓜和巴布达 +1268\",\"enName\":\"Antigua and Barbuda +1268\"},{\"code\":\"[+54]\",\"name\":\"阿根廷 +54\",\"zhName\":\"阿根廷 +54\",\"enName\":\"Argentina +54\"},{\"code\":\"[+374]\",\"name\":\"亚美尼亚 +374\",\"zhName\":\"亚美尼亚 +374\",\"enName\":\"Armenia +374\"},{\"code\":\"[+297]\",\"name\":\"阿鲁巴岛 +297\",\"zhName\":\"阿鲁巴岛 +297\",\"enName\":\"Aruba +297\"},{\"code\":\"[+247]\",\"name\":\"阿森松 +247\",\"zhName\":\"阿森松 +247\",\"enName\":\"Ascension +247\"},{\"code\":\"[+61]\",\"name\":\"澳大利亚 +61\",\"zhName\":\"澳大利亚 +61\",\"enName\":\"Australia +61\"},{\"code\":\"[+43]\",\"name\":\"奥地利 +43\",\"zhName\":\"奥地利 +43\",\"enName\":\"Austria +43\"},{\"code\":\"[+994]\",\"name\":\"阿塞拜疆 +994\",\"zhName\":\"阿塞拜疆 +994\",\"enName\":\"Azerbaijan +994\"},{\"code\":\"[+1242]\",\"name\":\"巴哈马 +1242\",\"zhName\":\"巴哈马 +1242\",\"enName\":\"Bahamas +1242\"},{\"code\":\"[+973]\",\"name\":\"巴林 +973\",\"zhName\":\"巴林 +973\",\"enName\":\"Bahrain +973\"},{\"code\":\"[+880]\",\"name\":\"孟加拉国 +880\",\"zhName\":\"孟加拉国 +880\",\"enName\":\"Bangladesh +880\"},{\"code\":\"[+1246]\",\"name\":\"巴巴多斯 +1246\",\"zhName\":\"巴巴多斯 +1246\",\"enName\":\"Barbados +1246\"},{\"code\":\"[+375]\",\"name\":\"白俄罗斯 +375\",\"zhName\":\"白俄罗斯 +375\",\"enName\":\"Belarus +375\"},{\"code\":\"[+32]\",\"name\":\"比利时 +32\",\"zhName\":\"比利时 +32\",\"enName\":\"Belgium +32\"},{\"code\":\"[+501]\",\"name\":\"伯利兹 +501\",\"zhName\":\"伯利兹 +501\",\"enName\":\"Belize +501\"},{\"code\":\"[+229]\",\"name\":\"贝宁 +229\",\"zhName\":\"贝宁 +229\",\"enName\":\"Benin +229\"},{\"code\":\"[+1441]\",\"name\":\"百慕大群岛 +1441\",\"zhName\":\"百慕大群岛 +1441\",\"enName\":\"Bermuda +1441\"},{\"code\":\"[+975]\",\"name\":\"不丹 +975\",\"zhName\":\"不丹 +975\",\"enName\":\"Bhutan +975\"},{\"code\":\"[+591]\",\"name\":\"玻利维亚 +591\",\"zhName\":\"玻利维亚 +591\",\"enName\":\"Bolivia +591\"},{\"code\":\"[+387]\",\"name\":\"波斯尼亚和黑塞哥维那 +387\",\"zhName\":\"波斯尼亚和黑塞哥维那 +387\",\"enName\":\"Bosnia and Herzegovina +387\"},{\"code\":\"[+267]\",\"name\":\"博茨瓦纳 +267\",\"zhName\":\"博茨瓦纳 +267\",\"enName\":\"Botswana +267\"},{\"code\":\"[+55]\",\"name\":\"巴西 +55\",\"zhName\":\"巴西 +55\",\"enName\":\"Brazil +55\"},{\"code\":\"[+673]\",\"name\":\"文莱 +673\",\"zhName\":\"文莱 +673\",\"enName\":\"Brunei +673\"},{\"code\":\"[+359]\",\"name\":\"保加利亚 +359\",\"zhName\":\"保加利亚 +359\",\"enName\":\"Bulgaria +359\"},{\"code\":\"[+226]\",\"name\":\"布基纳法索 +226\",\"zhName\":\"布基纳法索 +226\",\"enName\":\"Burkina Faso +226\"},{\"code\":\"[+95]\",\"name\":\"缅甸 +95\",\"zhName\":\"缅甸 +95\",\"enName\":\"Myanmar +95\"},{\"code\":\"[+257]\",\"name\":\"布隆迪 +257\",\"zhName\":\"布隆迪 +257\",\"enName\":\"Burundi +257\"},{\"code\":\"[+237]\",\"name\":\"喀麦隆 +237\",\"zhName\":\"喀麦隆 +237\",\"enName\":\"Cameroon +237\"},{\"code\":\"[+1]\",\"name\":\"加拿大/美国 +1\",\"zhName\":\"加拿大/美国 +1\",\"enName\":\"Canada/USA +1\"},{\"code\":\"[+238]\",\"name\":\"佛得角 +238\",\"zhName\":\"佛得角 +238\",\"enName\":\"Cape Verde +238\"},{\"code\":\"[+1345]\",\"name\":\"开曼群岛 +1345\",\"zhName\":\"开曼群岛 +1345\",\"enName\":\"Cayman Islands +1345\"},{\"code\":\"[+236]\",\"name\":\"中非共和国 +236\",\"zhName\":\"中非共和国 +236\",\"enName\":\"Central African Republic +236\"},{\"code\":\"[+235]\",\"name\":\"乍得 +235\",\"zhName\":\"乍得 +235\",\"enName\":\"Chad +235\"},{\"code\":\"[+56]\",\"name\":\"智利 +56\",\"zhName\":\"智利 +56\",\"enName\":\"Chile +56\"},{\"code\":\"[+86]\",\"name\":\"中国大陆 +86\",\"zhName\":\"中国大陆 +86\",\"enName\":\"Mainland China +86\"},{\"code\":\"[+57]\",\"name\":\"哥伦比亚 +57\",\"zhName\":\"哥伦比亚 +57\",\"enName\":\"Colombia +57\"},{\"code\":\"[+242]\",\"name\":\"刚果 +242\",\"zhName\":\"刚果 +242\",\"enName\":\"Congo +242\"},{\"code\":\"[+682]\",\"name\":\"库克群岛 +682\",\"zhName\":\"库克群岛 +682\",\"enName\":\"Cook Islands +682\"},{\"code\":\"[+506]\",\"name\":\"哥斯达黎加 +506\",\"zhName\":\"哥斯达黎加 +506\",\"enName\":\"Costa Rica +506\"},{\"code\":\"[+53]\",\"name\":\"古巴 +53\",\"zhName\":\"古巴 +53\",\"enName\":\"Cuba +53\"},{\"code\":\"[+357]\",\"name\":\"塞浦路斯 +357\",\"zhName\":\"塞浦路斯 +357\",\"enName\":\"Cyprus +357\"},{\"code\":\"[+420]\",\"name\":\"捷克 +420\",\"zhName\":\"捷克 +420\",\"enName\":\"Czech Republic +420\"},{\"code\":\"[+385]\",\"name\":\"克罗地亚共和国 +385\",\"zhName\":\"克罗地亚共和国 +385\",\"enName\":\"Croatia +385\"},{\"code\":\"[+45]\",\"name\":\"丹麦 +45\",\"zhName\":\"丹麦 +45\",\"enName\":\"Denmark +45\"},{\"code\":\"[+253]\",\"name\":\"吉布提 +253\",\"zhName\":\"吉布提 +253\",\"enName\":\"Djibouti +253\"},{\"code\":\"[+1890]\",\"name\":\"多米尼加共和国 +1890\",\"zhName\":\"多米尼加共和国 +1890\",\"enName\":\"Dominican Republic +1890\"},{\"code\":\"[+593]\",\"name\":\"厄瓜多尔 +593\",\"zhName\":\"厄瓜多尔 +593\",\"enName\":\"Ecuador +593\"},{\"code\":\"[+20]\",\"name\":\"埃及 +20\",\"zhName\":\"埃及 +20\",\"enName\":\"Egypt +20\"},{\"code\":\"[+503]\",\"name\":\"萨尔瓦多 +503\",\"zhName\":\"萨尔瓦多 +503\",\"enName\":\"El Salvador +503\"},{\"code\":\"[+372]\",\"name\":\"爱沙尼亚 +372\",\"zhName\":\"爱沙尼亚 +372\",\"enName\":\"Estonia +372\"},{\"code\":\"[+251]\",\"name\":\"埃塞俄比亚 +251\",\"zhName\":\"埃塞俄比亚 +251\",\"enName\":\"Ethiopia +251\"},{\"code\":\"[+679]\",\"name\":\"斐济 +679\",\"zhName\":\"斐济 +679\",\"enName\":\"Fiji +679\"},{\"code\":\"[+358]\",\"name\":\"芬兰 +358\",\"zhName\":\"芬兰 +358\",\"enName\":\"Finland +358\"},{\"code\":\"[+33]\",\"name\":\"法国 +33\",\"zhName\":\"法国 +33\",\"enName\":\"France +33\"},{\"code\":\"[+594]\",\"name\":\"法属圭亚那 +594\",\"zhName\":\"法属圭亚那 +594\",\"enName\":\"French Guiana +594\"},{\"code\":\"[+689]\",\"name\":\"法属玻利尼西亚 +689\",\"zhName\":\"法属玻利尼西亚 +689\",\"enName\":\"French Polynesia +689\"},{\"code\":\"[+241]\",\"name\":\"加蓬 +241\",\"zhName\":\"加蓬 +241\",\"enName\":\"Gabon +241\"},{\"code\":\"[+220]\",\"name\":\"冈比亚 +220\",\"zhName\":\"冈比亚 +220\",\"enName\":\"Gambia +220\"},{\"code\":\"[+995]\",\"name\":\"格鲁吉亚 +995\",\"zhName\":\"格鲁吉亚 +995\",\"enName\":\"Georgia +995\"},{\"code\":\"[+49]\",\"name\":\"德国 +49\",\"zhName\":\"德国 +49\",\"enName\":\"Germany +49\"},{\"code\":\"[+233]\",\"name\":\"加纳/乌兹别克斯坦 +233\",\"zhName\":\"加纳/乌兹别克斯坦 +233\",\"enName\":\"Ghana/Uzbekistan +233\"},{\"code\":\"[+350]\",\"name\":\"直布罗陀 +350\",\"zhName\":\"直布罗陀 +350\",\"enName\":\"Gibraltar +350\"},{\"code\":\"[+30]\",\"name\":\"希腊 +30\",\"zhName\":\"希腊 +30\",\"enName\":\"Greece +30\"},{\"code\":\"[+1809]\",\"name\":\"格林纳达/特立尼达和多巴哥 +1809\",\"zhName\":\"格林纳达/特立尼达和多巴哥 +1809\",\"enName\":\"Grenada/Trinidad and Tobago +1809\"},{\"code\":\"[+1671]\",\"name\":\"关岛 +1671\",\"zhName\":\"关岛 +1671\",\"enName\":\"Guam +1671\"},{\"code\":\"[+502]\",\"name\":\"危地马拉 +502\",\"zhName\":\"危地马拉 +502\",\"enName\":\"Guatemala +502\"},{\"code\":\"[+224]\",\"name\":\"几内亚 +224\",\"zhName\":\"几内亚 +224\",\"enName\":\"Guinea +224\"},{\"code\":\"[+592]\",\"name\":\"圭亚那 +592\",\"zhName\":\"圭亚那 +592\",\"enName\":\"Guyana +592\"},{\"code\":\"[+509]\",\"name\":\"海地 +509\",\"zhName\":\"海地 +509\",\"enName\":\"Haiti +509\"},{\"code\":\"[+504]\",\"name\":\"洪都拉斯 +504\",\"zhName\":\"洪都拉斯 +504\",\"enName\":\"Honduras +504\"},{\"code\":\"[+852]\",\"name\":\"中国香港 +852\",\"zhName\":\"中国香港 +852\",\"enName\":\"Hong Kong +852\"},{\"code\":\"[+36]\",\"name\":\"匈牙利 +36\",\"zhName\":\"匈牙利 +36\",\"enName\":\"Hungary +36\"},{\"code\":\"[+354]\",\"name\":\"冰岛 +354\",\"zhName\":\"冰岛 +354\",\"enName\":\"Iceland +354\"},{\"code\":\"[+91]\",\"name\":\"印度 +91\",\"zhName\":\"印度 +91\",\"enName\":\"India +91\"},{\"code\":\"[+62]\",\"name\":\"印度尼西亚 +62\",\"zhName\":\"印度尼西亚 +62\",\"enName\":\"Indonesia +62\"},{\"code\":\"[+98]\",\"name\":\"伊朗 +98\",\"zhName\":\"伊朗 +98\",\"enName\":\"Iran +98\"},{\"code\":\"[+964]\",\"name\":\"伊拉克 +964\",\"zhName\":\"伊拉克 +964\",\"enName\":\"Iraq +964\"},{\"code\":\"[+353]\",\"name\":\"爱尔兰 +353\",\"zhName\":\"爱尔兰 +353\",\"enName\":\"Ireland +353\"},{\"code\":\"[+972]\",\"name\":\"以色列 +972\",\"zhName\":\"以色列 +972\",\"enName\":\"Israel +972\"},{\"code\":\"[+39]\",\"name\":\"意大利 +39\",\"zhName\":\"意大利 +39\",\"enName\":\"Italy +39\"},{\"code\":\"[+225]\",\"name\":\"科特迪瓦 +225\",\"zhName\":\"科特迪瓦 +225\",\"enName\":\"Côte d'Ivoire +225\"},{\"code\":\"[+1876]\",\"name\":\"牙买加 +1876\",\"zhName\":\"牙买加 +1876\",\"enName\":\"Jamaica +1876\"},{\"code\":\"[+81]\",\"name\":\"日本 +81\",\"zhName\":\"日本 +81\",\"enName\":\"Japan +81\"},{\"code\":\"[+962]\",\"name\":\"约旦 +962\",\"zhName\":\"约旦 +962\",\"enName\":\"Jordan +962\"},{\"code\":\"[+855]\",\"name\":\"柬埔寨 +855\",\"zhName\":\"柬埔寨 +855\",\"enName\":\"Cambodia +855\"},{\"code\":\"[+7]\",\"name\":\"哈萨克斯坦/俄罗斯 +7\",\"zhName\":\"哈萨克斯坦/俄罗斯 +7\",\"enName\":\"Kazakhstan/Russia +7\"},{\"code\":\"[+254]\",\"name\":\"肯尼亚 +254\",\"zhName\":\"肯尼亚 +254\",\"enName\":\"Kenya +254\"},{\"code\":\"[+82]\",\"name\":\"韩国 +82\",\"zhName\":\"韩国 +82\",\"enName\":\"South Korea +82\"},{\"code\":\"[+965]\",\"name\":\"科威特 +965\",\"zhName\":\"科威特 +965\",\"enName\":\"Kuwait +965\"},{\"code\":\"[+331]\",\"name\":\"吉尔吉斯坦 +331\",\"zhName\":\"吉尔吉斯坦 +331\",\"enName\":\"Kyrgyzstan +331\"},{\"code\":\"[+856]\",\"name\":\"老挝 +856\",\"zhName\":\"老挝 +856\",\"enName\":\"Laos +856\"},{\"code\":\"[+371]\",\"name\":\"拉脱维亚 +371\",\"zhName\":\"拉脱维亚 +371\",\"enName\":\"Latvia +371\"},{\"code\":\"[+961]\",\"name\":\"黎巴嫩 +961\",\"zhName\":\"黎巴嫩 +961\",\"enName\":\"Lebanon +961\"},{\"code\":\"[+266]\",\"name\":\"莱索托 +266\",\"zhName\":\"莱索托 +266\",\"enName\":\"Lesotho +266\"},{\"code\":\"[+231]\",\"name\":\"利比里亚 +231\",\"zhName\":\"利比里亚 +231\",\"enName\":\"Liberia +231\"},{\"code\":\"[+218]\",\"name\":\"利比亚 +218\",\"zhName\":\"利比亚 +218\",\"enName\":\"Libya +218\"},{\"code\":\"[+423]\",\"name\":\"列支敦士登 +423\",\"zhName\":\"列支敦士登 +423\",\"enName\":\"Liechtenstein +423\"},{\"code\":\"[+370]\",\"name\":\"立陶宛 +370\",\"zhName\":\"立陶宛 +370\",\"enName\":\"Lithuania +370\"},{\"code\":\"[+352]\",\"name\":\"卢森堡 +352\",\"zhName\":\"卢森堡 +352\",\"enName\":\"Luxembourg +352\"},{\"code\":\"[+853]\",\"name\":\"中国澳门 +853\",\"zhName\":\"中国澳门 +853\",\"enName\":\"Macau +853\"},{\"code\":\"[+261]\",\"name\":\"马达加斯加 +261\",\"zhName\":\"马达加斯加 +261\",\"enName\":\"Madagascar +261\"},{\"code\":\"[+265]\",\"name\":\"马拉维 +265\",\"zhName\":\"马拉维 +265\",\"enName\":\"Malawi +265\"},{\"code\":\"[+60]\",\"name\":\"马来西亚 +60\",\"zhName\":\"马来西亚 +60\",\"enName\":\"Malaysia +60\"},{\"code\":\"[+960]\",\"name\":\"马尔代夫 +960\",\"zhName\":\"马尔代夫 +960\",\"enName\":\"Maldives +960\"},{\"code\":\"[+223]\",\"name\":\"马里 +223\",\"zhName\":\"马里 +223\",\"enName\":\"Mali +223\"},{\"code\":\"[+356]\",\"name\":\"马耳他 +356\",\"zhName\":\"马耳他 +356\",\"enName\":\"Malta +356\"},{\"code\":\"[+1670]\",\"name\":\"马里亚那群岛 +1670\",\"zhName\":\"马里亚那群岛 +1670\",\"enName\":\"Mariana Islands +1670\"},{\"code\":\"[+596]\",\"name\":\"马提尼克 +596\",\"zhName\":\"马提尼克 +596\",\"enName\":\"Martinique +596\"},{\"code\":\"[+230]\",\"name\":\"毛里求斯 +230\",\"zhName\":\"毛里求斯 +230\",\"enName\":\"Mauritius +230\"},{\"code\":\"[+52]\",\"name\":\"墨西哥 +52\",\"zhName\":\"墨西哥 +52\",\"enName\":\"Mexico +52\"},{\"code\":\"[+373]\",\"name\":\"摩尔多瓦 +373\",\"zhName\":\"摩尔多瓦 +373\",\"enName\":\"Moldova +373\"},{\"code\":\"[+377]\",\"name\":\"摩纳哥 +377\",\"zhName\":\"摩纳哥 +377\",\"enName\":\"Monaco +377\"},{\"code\":\"[+976]\",\"name\":\"蒙古 +976\",\"zhName\":\"蒙古 +976\",\"enName\":\"Mongolia +976\"},{\"code\":\"[+1664]\",\"name\":\"蒙特塞拉特岛 +1664\",\"zhName\":\"蒙特塞拉特岛 +1664\",\"enName\":\"Montserrat +1664\"},{\"code\":\"[+212]\",\"name\":\"摩洛哥 +212\",\"zhName\":\"摩洛哥 +212\",\"enName\":\"Morocco +212\"},{\"code\":\"[+258]\",\"name\":\"莫桑比克 +258\",\"zhName\":\"莫桑比克 +258\",\"enName\":\"Mozambique +258\"},{\"code\":\"[+264]\",\"name\":\"纳米比亚 +264\",\"zhName\":\"纳米比亚 +264\",\"enName\":\"Namibia +264\"},{\"code\":\"[+674]\",\"name\":\"瑙鲁 +674\",\"zhName\":\"瑙鲁 +674\",\"enName\":\"Nauru +674\"},{\"code\":\"[+977]\",\"name\":\"尼泊尔 +977\",\"zhName\":\"尼泊尔 +977\",\"enName\":\"Nepal +977\"},{\"code\":\"[+599]\",\"name\":\"荷属安的列斯 +599\",\"zhName\":\"荷属安的列斯 +599\",\"enName\":\"Netherlands Antilles +599\"},{\"code\":\"[+31]\",\"name\":\"荷兰 +31\",\"zhName\":\"荷兰 +31\",\"enName\":\"Netherlands +31\"},{\"code\":\"[+64]\",\"name\":\"新西兰 +64\",\"zhName\":\"新西兰 +64\",\"enName\":\"New Zealand +64\"},{\"code\":\"[+505]\",\"name\":\"尼加拉瓜 +505\",\"zhName\":\"尼加拉瓜 +505\",\"enName\":\"Nicaragua +505\"},{\"code\":\"[+227]\",\"name\":\"尼日尔 +227\",\"zhName\":\"尼日尔 +227\",\"enName\":\"Niger +227\"},{\"code\":\"[+234]\",\"name\":\"尼日利亚 +234\",\"zhName\":\"尼日利亚 +234\",\"enName\":\"Nigeria +234\"},{\"code\":\"[+850]\",\"name\":\"朝鲜 +850\",\"zhName\":\"朝鲜 +850\",\"enName\":\"North Korea +850\"},{\"code\":\"[+47]\",\"name\":\"挪威 +47\",\"zhName\":\"挪威 +47\",\"enName\":\"Norway +47\"},{\"code\":\"[+968]\",\"name\":\"阿曼 +968\",\"zhName\":\"阿曼 +968\",\"enName\":\"Oman +968\"},{\"code\":\"[+92]\",\"name\":\"巴基斯坦 +92\",\"zhName\":\"巴基斯坦 +92\",\"enName\":\"Pakistan +92\"},{\"code\":\"[+507]\",\"name\":\"巴拿马 +507\",\"zhName\":\"巴拿马 +507\",\"enName\":\"Panama +507\"},{\"code\":\"[+675]\",\"name\":\"巴布亚新几内亚 +675\",\"zhName\":\"巴布亚新几内亚 +675\",\"enName\":\"Papua New Guinea +675\"},{\"code\":\"[+595]\",\"name\":\"巴拉圭 +595\",\"zhName\":\"巴拉圭 +595\",\"enName\":\"Paraguay +595\"},{\"code\":\"[+51]\",\"name\":\"秘鲁 +51\",\"zhName\":\"秘鲁 +51\",\"enName\":\"Peru +51\"},{\"code\":\"[+63]\",\"name\":\"菲律宾 +63\",\"zhName\":\"菲律宾 +63\",\"enName\":\"Philippines +63\"},{\"code\":\"[+48]\",\"name\":\"波兰 +48\",\"zhName\":\"波兰 +48\",\"enName\":\"Poland +48\"},{\"code\":\"[+351]\",\"name\":\"葡萄牙 +351\",\"zhName\":\"葡萄牙 +351\",\"enName\":\"Portugal +351\"},{\"code\":\"[+1787]\",\"name\":\"波多黎各 +1787\",\"zhName\":\"波多黎各 +1787\",\"enName\":\"Puerto Rico +1787\"},{\"code\":\"[+974]\",\"name\":\"卡塔尔 +974\",\"zhName\":\"卡塔尔 +974\",\"enName\":\"Qatar +974\"},{\"code\":\"[+262]\",\"name\":\"留尼旺 +262\",\"zhName\":\"留尼旺 +262\",\"enName\":\"Reunion +262\"},{\"code\":\"[+40]\",\"name\":\"罗马尼亚 +40\",\"zhName\":\"罗马尼亚 +40\",\"enName\":\"Romania +40\"},{\"code\":\"[+1784]\",\"name\":\"圣文森特岛/圣文森特 +1784\",\"zhName\":\"圣文森特岛/圣文森特 +1784\",\"enName\":\"St. Vincent Island/St. Vincent +1784\"},{\"code\":\"[+684]\",\"name\":\"东萨摩亚(美) +684\",\"zhName\":\"东萨摩亚(美) +684\",\"enName\":\"Eastern Samoa (US) +684\"},{\"code\":\"[+685]\",\"name\":\"西萨摩亚 +685\",\"zhName\":\"西萨摩亚 +685\",\"enName\":\"Western Samoa +685\"},{\"code\":\"[+378]\",\"name\":\"圣马力诺 +378\",\"zhName\":\"圣马力诺 +378\",\"enName\":\"San Marino +378\"},{\"code\":\"[+239]\",\"name\":\"圣多美和普林西比 +239\",\"zhName\":\"圣多美和普林西比 +239\",\"enName\":\"Sao Tome and Principe +239\"},{\"code\":\"[+966]\",\"name\":\"沙特阿拉伯 +966\",\"zhName\":\"沙特阿拉伯 +966\",\"enName\":\"Saudi Arabia +966\"},{\"code\":\"[+221]\",\"name\":\"塞内加尔 +221\",\"zhName\":\"塞内加尔 +221\",\"enName\":\"Senegal +221\"},{\"code\":\"[+248]\",\"name\":\"塞舌尔 +248\",\"zhName\":\"塞舌尔 +248\",\"enName\":\"Seychelles +248\"},{\"code\":\"[+232]\",\"name\":\"塞拉利昂 +232\",\"zhName\":\"塞拉利昂 +232\",\"enName\":\"Sierra Leone +232\"},{\"code\":\"[+65]\",\"name\":\"新加坡 +65\",\"zhName\":\"新加坡 +65\",\"enName\":\"Singapore +65\"},{\"code\":\"[+421]\",\"name\":\"斯洛伐克 +421\",\"zhName\":\"斯洛伐克 +421\",\"enName\":\"Slovakia +421\"},{\"code\":\"[+386]\",\"name\":\"斯洛文尼亚 +386\",\"zhName\":\"斯洛文尼亚 +386\",\"enName\":\"Slovenia +386\"},{\"code\":\"[+677]\",\"name\":\"所罗门群岛 +677\",\"zhName\":\"所罗门群岛 +677\",\"enName\":\"Solomon Islands +677\"},{\"code\":\"[+252]\",\"name\":\"索马里 +252\",\"zhName\":\"索马里 +252\",\"enName\":\"Somalia +252\"},{\"code\":\"[+27]\",\"name\":\"南非 +27\",\"zhName\":\"南非 +27\",\"enName\":\"South Africa +27\"},{\"code\":\"[+34]\",\"name\":\"西班牙 +34\",\"zhName\":\"西班牙 +34\",\"enName\":\"Spain +34\"},{\"code\":\"[+94]\",\"name\":\"斯里兰卡 +94\",\"zhName\":\"斯里兰卡 +94\",\"enName\":\"Sri Lanka +94\"},{\"code\":\"[+1758]\",\"name\":\"圣卢西亚 +1758\",\"zhName\":\"圣卢西亚 +1758\",\"enName\":\"Saint Lucia +1758\"},{\"code\":\"[+249]\",\"name\":\"苏丹 +249\",\"zhName\":\"苏丹 +249\",\"enName\":\"Sudan +249\"},{\"code\":\"[+597]\",\"name\":\"苏里南 +597\",\"zhName\":\"苏里南 +597\",\"enName\":\"Suriname +597\"},{\"code\":\"[+268]\",\"name\":\"斯威士兰 +268\",\"zhName\":\"斯威士兰 +268\",\"enName\":\"Swaziland +268\"},{\"code\":\"[+46]\",\"name\":\"瑞典 +46\",\"zhName\":\"瑞典 +46\",\"enName\":\"Sweden +46\"},{\"code\":\"[+41]\",\"name\":\"瑞士 +41\",\"zhName\":\"瑞士 +41\",\"enName\":\"Switzerland +41\"},{\"code\":\"[+963]\",\"name\":\"叙利亚 +963\",\"zhName\":\"叙利亚 +963\",\"enName\":\"Syria +963\"},{\"code\":\"[+886]\",\"name\":\"中国台湾 +886\",\"zhName\":\"中国台湾 +886\",\"enName\":\"Taiwan +886\"},{\"code\":\"[+992]\",\"name\":\"塔吉克斯坦 +992\",\"zhName\":\"塔吉克斯坦 +992\",\"enName\":\"Tajikistan +992\"},{\"code\":\"[+255]\",\"name\":\"坦桑尼亚 +255\",\"zhName\":\"坦桑尼亚 +255\",\"enName\":\"Tanzania +255\"},{\"code\":\"[+66]\",\"name\":\"泰国 +66\",\"zhName\":\"泰国 +66\",\"enName\":\"Thailand +66\"},{\"code\":\"[+228]\",\"name\":\"多哥 +228\",\"zhName\":\"多哥 +228\",\"enName\":\"Togo +228\"},{\"code\":\"[+676]\",\"name\":\"汤加 +676\",\"zhName\":\"汤加 +676\",\"enName\":\"Tonga +676\"},{\"code\":\"[+216]\",\"name\":\"突尼斯 +216\",\"zhName\":\"突尼斯 +216\",\"enName\":\"Tunisia +216\"},{\"code\":\"[+90]\",\"name\":\"土耳其 +90\",\"zhName\":\"土耳其 +90\",\"enName\":\"Turkey +90\"},{\"code\":\"[+993]\",\"name\":\"土库曼斯坦 +993\",\"zhName\":\"土库曼斯坦 +993\",\"enName\":\"Turkmenistan +993\"},{\"code\":\"[+256]\",\"name\":\"乌干达 +256\",\"zhName\":\"乌干达 +256\",\"enName\":\"Uganda +256\"},{\"code\":\"[+380]\",\"name\":\"乌克兰 +380\",\"zhName\":\"乌克兰 +380\",\"enName\":\"Ukraine +380\"},{\"code\":\"[+971]\",\"name\":\"阿拉伯联合酋长国 +971\",\"zhName\":\"阿拉伯联合酋长国 +971\",\"enName\":\"United Arab Emirates +971\"},{\"code\":\"[+44]\",\"name\":\"英国 +44\",\"zhName\":\"英国 +44\",\"enName\":\"United Kingdom +44\"},{\"code\":\"[+598]\",\"name\":\"乌拉圭 +598\",\"zhName\":\"乌拉圭 +598\",\"enName\":\"Uruguay +598\"},{\"code\":\"[+58]\",\"name\":\"委内瑞拉 +58\",\"zhName\":\"委内瑞拉 +58\",\"enName\":\"Venezuela +58\"},{\"code\":\"[+84]\",\"name\":\"越南 +84\",\"zhName\":\"越南 +84\",\"enName\":\"Vietnam +84\"},{\"code\":\"[+967]\",\"name\":\"也门 +967\",\"zhName\":\"也门 +967\",\"enName\":\"Yemen +967\"},{\"code\":\"[+381]\",\"name\":\"南斯拉夫 +381\",\"zhName\":\"南斯拉夫 +381\",\"enName\":\"Yugoslavia +381\"},{\"code\":\"[+263]\",\"name\":\"津巴布韦 +263\",\"zhName\":\"津巴布韦 +263\",\"enName\":\"Zimbabwe +263\"},{\"code\":\"[+243]\",\"name\":\"扎伊尔 +243\",\"zhName\":\"扎伊尔 +243\",\"enName\":\"Zaire +243\"},{\"code\":\"[+260]\",\"name\":\"赞比亚 +260\",\"zhName\":\"赞比亚 +260\",\"enName\":\"Zambia +260\"}]"
                        },
                        new
                        {
                            Id = 1281498705313372359L,
                            Controls = false,
                            EnPlaceholder = "Please enter",
                            FieldCode = "DriverEmail",
                            FieldComponentType = "FieldInput",
                            FieldEnName = "Driver Email",
                            FieldName = "司机邮箱",
                            FieldType = (sbyte)0,
                            FieldsGroupType = (sbyte)2,
                            Maxlength = 50m,
                            Number = false,
                            Placeholder = "请输入",
                            Radio = false,
                            RulerFilter = "[{\"code\":null,\"name\":\"司机邮箱\",\"msg\":\"请输入正确的电子邮箱\",\"pattern\":\"^([\\\\w_\\\\-\\\\.])+\\\\@([\\\\w_\\\\-\\\\.])+\\\\.(\\\\w{2,4})$\",\"enName\":\"Driver Email\",\"enMsg\":\"Please enter the correct email address\",\"zhName\":\"司机邮箱\",\"zhMsg\":\"请输入正确的电子邮箱\"}]",
                            Textarea = false
                        },
                        new
                        {
                            Id = 1281498705313372361L,
                            Controls = false,
                            DefaultValue = "{\"type\":1,\"value\":null}",
                            EnPlaceholder = "Please enter",
                            FieldCode = "DriverSocialMedia",
                            FieldComponentType = "FieldSelectInput",
                            FieldEnName = "Driver Social Media",
                            FieldName = "司机社交媒体",
                            FieldType = (sbyte)9,
                            FieldsGroupType = (sbyte)2,
                            Maxlength = 30m,
                            Number = false,
                            Placeholder = "请输入",
                            Radio = false,
                            Textarea = false,
                            ValueRange = "[{\"code\":1,\"name\":\"微信\",\"zhName\":\"微信\",\"enName\":\"Wechat\"},{\"code\":2,\"name\":\"Whatsapp\",\"zhName\":\"Whatsapp\",\"enName\":\"Whatsapp\"},{\"code\":3,\"name\":\"Line\",\"zhName\":\"Line\",\"enName\":\"Line\"},{\"code\":4,\"name\":\"Telegram\",\"zhName\":\"Telegram\",\"enName\":\"Telegram\"},{\"code\":5,\"name\":\"Skype\",\"zhName\":\"Skype\",\"enName\":\"Skype\"},{\"code\":6,\"name\":\"Facebook\",\"zhName\":\"Facebook\",\"enName\":\"Facebook\"},{\"code\":7,\"name\":\"旺旺\",\"zhName\":\"旺旺\",\"enName\":\"Wangwang\"},{\"code\":8,\"name\":\"KakaoTalk\",\"zhName\":\"KakaoTalk\",\"enName\":\"KakaoTalk\"}]"
                        },
                        new
                        {
                            Id = 1281498705313329062L,
                            Controls = true,
                            DefaultValue = "1",
                            EnPlaceholder = "Please enter",
                            FieldCode = "Passengers",
                            FieldComponentType = "FieldInput",
                            FieldEnName = "Number of Passengers",
                            FieldName = "乘客数",
                            FieldType = (sbyte)1,
                            FieldsGroupType = (sbyte)3,
                            MinLength = 1m,
                            Number = true,
                            Placeholder = "请输入",
                            Radio = false,
                            Textarea = false
                        },
                        new
                        {
                            Id = 1281498705313329063L,
                            Controls = true,
                            DefaultValue = "0",
                            EnPlaceholder = "Please enter",
                            FieldCode = "Baggages",
                            FieldComponentType = "FieldInput",
                            FieldEnName = "Number of Luggage",
                            FieldName = "行李数",
                            FieldType = (sbyte)1,
                            FieldsGroupType = (sbyte)3,
                            MinLength = 0m,
                            Number = true,
                            Placeholder = "请输入",
                            Radio = false,
                            Textarea = false
                        },
                        new
                        {
                            Id = 1281498705313329064L,
                            Controls = false,
                            DefaultValue = "{\"address\":null,\"hasAddress\":true,\"detail\":{\"resourceType\":null,\"resourceSubType\":null,\"resourceZhName\":null,\"resourceEnName\":null,\"countryCode\":null,\"provinceCode\":null,\"cityCode\":null,\"relatedId\":null,\"countryZhName\":null,\"countryEnName\":null,\"provinceZhName\":null,\"provinceEnName\":null,\"cityZhName\":null,\"cityEnName\":null,\"iata\":null,\"icao\":null,\"highLightContent\":null,\"countryHighLightContent\":null,\"provinceHighLightContent\":null,\"cityHighLightContent\":null,\"iataHighLightContent\":null,\"icaoHighLightContent\":null,\"longitude\":null,\"latitude\":null,\"value\":null}}",
                            EsResourceParentType = "[4,6,7,2,5,9,8]",
                            FieldCode = "DepartureJson",
                            FieldComponentType = "FieldDestinationSearch",
                            FieldEnName = "Pick-up Location",
                            FieldName = "上车地点",
                            FieldType = (sbyte)9,
                            FieldsGroupType = (sbyte)3,
                            Number = false,
                            Placeholder = "请输入酒店/机场/景点名称或其他关键字",
                            Radio = false,
                            Textarea = false
                        },
                        new
                        {
                            Id = 1281498705313329071L,
                            Controls = false,
                            DefaultValue = "{\"type\":\"[+86]\",\"value\":null}",
                            EnPlaceholder = "Please enter",
                            FieldCode = "DepartureHotelPhone",
                            FieldComponentType = "FieldPhoneInput",
                            FieldEnName = "Departure Hotel Phone",
                            FieldName = "出发酒店电话",
                            FieldType = (sbyte)9,
                            FieldsGroupType = (sbyte)3,
                            Maxlength = 16m,
                            Number = false,
                            Placeholder = "请输入",
                            Radio = false,
                            Textarea = false,
                            ValueRange = "[{\"code\":\"[+244]\",\"name\":\"安哥拉 +244\",\"zhName\":\"安哥拉 +244\",\"enName\":\"Angola +244\"},{\"code\":\"[+93]\",\"name\":\"阿富汗 +93\",\"zhName\":\"阿富汗 +93\",\"enName\":\"Afghanistan +93\"},{\"code\":\"[+1907]\",\"name\":\"阿拉斯加 +1907\",\"zhName\":\"阿拉斯加 +1907\",\"enName\":\"Alaska +1907\"},{\"code\":\"[+355]\",\"name\":\"阿尔巴尼亚 +355\",\"zhName\":\"阿尔巴尼亚 +355\",\"enName\":\"Albania +355\"},{\"code\":\"[+213]\",\"name\":\"阿尔及利亚 +213\",\"zhName\":\"阿尔及利亚 +213\",\"enName\":\"Algeria +213\"},{\"code\":\"[+376]\",\"name\":\"安道尔共和国 +376\",\"zhName\":\"安道尔共和国 +376\",\"enName\":\"Andorra +376\"},{\"code\":\"[+1254]\",\"name\":\"安圭拉岛 +1254\",\"zhName\":\"安圭拉岛 +1254\",\"enName\":\"Anguilla +1254\"},{\"code\":\"[+1268]\",\"name\":\"安提瓜和巴布达 +1268\",\"zhName\":\"安提瓜和巴布达 +1268\",\"enName\":\"Antigua and Barbuda +1268\"},{\"code\":\"[+54]\",\"name\":\"阿根廷 +54\",\"zhName\":\"阿根廷 +54\",\"enName\":\"Argentina +54\"},{\"code\":\"[+374]\",\"name\":\"亚美尼亚 +374\",\"zhName\":\"亚美尼亚 +374\",\"enName\":\"Armenia +374\"},{\"code\":\"[+297]\",\"name\":\"阿鲁巴岛 +297\",\"zhName\":\"阿鲁巴岛 +297\",\"enName\":\"Aruba +297\"},{\"code\":\"[+247]\",\"name\":\"阿森松 +247\",\"zhName\":\"阿森松 +247\",\"enName\":\"Ascension +247\"},{\"code\":\"[+61]\",\"name\":\"澳大利亚 +61\",\"zhName\":\"澳大利亚 +61\",\"enName\":\"Australia +61\"},{\"code\":\"[+43]\",\"name\":\"奥地利 +43\",\"zhName\":\"奥地利 +43\",\"enName\":\"Austria +43\"},{\"code\":\"[+994]\",\"name\":\"阿塞拜疆 +994\",\"zhName\":\"阿塞拜疆 +994\",\"enName\":\"Azerbaijan +994\"},{\"code\":\"[+1242]\",\"name\":\"巴哈马 +1242\",\"zhName\":\"巴哈马 +1242\",\"enName\":\"Bahamas +1242\"},{\"code\":\"[+973]\",\"name\":\"巴林 +973\",\"zhName\":\"巴林 +973\",\"enName\":\"Bahrain +973\"},{\"code\":\"[+880]\",\"name\":\"孟加拉国 +880\",\"zhName\":\"孟加拉国 +880\",\"enName\":\"Bangladesh +880\"},{\"code\":\"[+1246]\",\"name\":\"巴巴多斯 +1246\",\"zhName\":\"巴巴多斯 +1246\",\"enName\":\"Barbados +1246\"},{\"code\":\"[+375]\",\"name\":\"白俄罗斯 +375\",\"zhName\":\"白俄罗斯 +375\",\"enName\":\"Belarus +375\"},{\"code\":\"[+32]\",\"name\":\"比利时 +32\",\"zhName\":\"比利时 +32\",\"enName\":\"Belgium +32\"},{\"code\":\"[+501]\",\"name\":\"伯利兹 +501\",\"zhName\":\"伯利兹 +501\",\"enName\":\"Belize +501\"},{\"code\":\"[+229]\",\"name\":\"贝宁 +229\",\"zhName\":\"贝宁 +229\",\"enName\":\"Benin +229\"},{\"code\":\"[+1441]\",\"name\":\"百慕大群岛 +1441\",\"zhName\":\"百慕大群岛 +1441\",\"enName\":\"Bermuda +1441\"},{\"code\":\"[+975]\",\"name\":\"不丹 +975\",\"zhName\":\"不丹 +975\",\"enName\":\"Bhutan +975\"},{\"code\":\"[+591]\",\"name\":\"玻利维亚 +591\",\"zhName\":\"玻利维亚 +591\",\"enName\":\"Bolivia +591\"},{\"code\":\"[+387]\",\"name\":\"波斯尼亚和黑塞哥维那 +387\",\"zhName\":\"波斯尼亚和黑塞哥维那 +387\",\"enName\":\"Bosnia and Herzegovina +387\"},{\"code\":\"[+267]\",\"name\":\"博茨瓦纳 +267\",\"zhName\":\"博茨瓦纳 +267\",\"enName\":\"Botswana +267\"},{\"code\":\"[+55]\",\"name\":\"巴西 +55\",\"zhName\":\"巴西 +55\",\"enName\":\"Brazil +55\"},{\"code\":\"[+673]\",\"name\":\"文莱 +673\",\"zhName\":\"文莱 +673\",\"enName\":\"Brunei +673\"},{\"code\":\"[+359]\",\"name\":\"保加利亚 +359\",\"zhName\":\"保加利亚 +359\",\"enName\":\"Bulgaria +359\"},{\"code\":\"[+226]\",\"name\":\"布基纳法索 +226\",\"zhName\":\"布基纳法索 +226\",\"enName\":\"Burkina Faso +226\"},{\"code\":\"[+95]\",\"name\":\"缅甸 +95\",\"zhName\":\"缅甸 +95\",\"enName\":\"Myanmar +95\"},{\"code\":\"[+257]\",\"name\":\"布隆迪 +257\",\"zhName\":\"布隆迪 +257\",\"enName\":\"Burundi +257\"},{\"code\":\"[+237]\",\"name\":\"喀麦隆 +237\",\"zhName\":\"喀麦隆 +237\",\"enName\":\"Cameroon +237\"},{\"code\":\"[+1]\",\"name\":\"加拿大/美国 +1\",\"zhName\":\"加拿大/美国 +1\",\"enName\":\"Canada/USA +1\"},{\"code\":\"[+238]\",\"name\":\"佛得角 +238\",\"zhName\":\"佛得角 +238\",\"enName\":\"Cape Verde +238\"},{\"code\":\"[+1345]\",\"name\":\"开曼群岛 +1345\",\"zhName\":\"开曼群岛 +1345\",\"enName\":\"Cayman Islands +1345\"},{\"code\":\"[+236]\",\"name\":\"中非共和国 +236\",\"zhName\":\"中非共和国 +236\",\"enName\":\"Central African Republic +236\"},{\"code\":\"[+235]\",\"name\":\"乍得 +235\",\"zhName\":\"乍得 +235\",\"enName\":\"Chad +235\"},{\"code\":\"[+56]\",\"name\":\"智利 +56\",\"zhName\":\"智利 +56\",\"enName\":\"Chile +56\"},{\"code\":\"[+86]\",\"name\":\"中国大陆 +86\",\"zhName\":\"中国大陆 +86\",\"enName\":\"Mainland China +86\"},{\"code\":\"[+57]\",\"name\":\"哥伦比亚 +57\",\"zhName\":\"哥伦比亚 +57\",\"enName\":\"Colombia +57\"},{\"code\":\"[+242]\",\"name\":\"刚果 +242\",\"zhName\":\"刚果 +242\",\"enName\":\"Congo +242\"},{\"code\":\"[+682]\",\"name\":\"库克群岛 +682\",\"zhName\":\"库克群岛 +682\",\"enName\":\"Cook Islands +682\"},{\"code\":\"[+506]\",\"name\":\"哥斯达黎加 +506\",\"zhName\":\"哥斯达黎加 +506\",\"enName\":\"Costa Rica +506\"},{\"code\":\"[+53]\",\"name\":\"古巴 +53\",\"zhName\":\"古巴 +53\",\"enName\":\"Cuba +53\"},{\"code\":\"[+357]\",\"name\":\"塞浦路斯 +357\",\"zhName\":\"塞浦路斯 +357\",\"enName\":\"Cyprus +357\"},{\"code\":\"[+420]\",\"name\":\"捷克 +420\",\"zhName\":\"捷克 +420\",\"enName\":\"Czech Republic +420\"},{\"code\":\"[+385]\",\"name\":\"克罗地亚共和国 +385\",\"zhName\":\"克罗地亚共和国 +385\",\"enName\":\"Croatia +385\"},{\"code\":\"[+45]\",\"name\":\"丹麦 +45\",\"zhName\":\"丹麦 +45\",\"enName\":\"Denmark +45\"},{\"code\":\"[+253]\",\"name\":\"吉布提 +253\",\"zhName\":\"吉布提 +253\",\"enName\":\"Djibouti +253\"},{\"code\":\"[+1890]\",\"name\":\"多米尼加共和国 +1890\",\"zhName\":\"多米尼加共和国 +1890\",\"enName\":\"Dominican Republic +1890\"},{\"code\":\"[+593]\",\"name\":\"厄瓜多尔 +593\",\"zhName\":\"厄瓜多尔 +593\",\"enName\":\"Ecuador +593\"},{\"code\":\"[+20]\",\"name\":\"埃及 +20\",\"zhName\":\"埃及 +20\",\"enName\":\"Egypt +20\"},{\"code\":\"[+503]\",\"name\":\"萨尔瓦多 +503\",\"zhName\":\"萨尔瓦多 +503\",\"enName\":\"El Salvador +503\"},{\"code\":\"[+372]\",\"name\":\"爱沙尼亚 +372\",\"zhName\":\"爱沙尼亚 +372\",\"enName\":\"Estonia +372\"},{\"code\":\"[+251]\",\"name\":\"埃塞俄比亚 +251\",\"zhName\":\"埃塞俄比亚 +251\",\"enName\":\"Ethiopia +251\"},{\"code\":\"[+679]\",\"name\":\"斐济 +679\",\"zhName\":\"斐济 +679\",\"enName\":\"Fiji +679\"},{\"code\":\"[+358]\",\"name\":\"芬兰 +358\",\"zhName\":\"芬兰 +358\",\"enName\":\"Finland +358\"},{\"code\":\"[+33]\",\"name\":\"法国 +33\",\"zhName\":\"法国 +33\",\"enName\":\"France +33\"},{\"code\":\"[+594]\",\"name\":\"法属圭亚那 +594\",\"zhName\":\"法属圭亚那 +594\",\"enName\":\"French Guiana +594\"},{\"code\":\"[+689]\",\"name\":\"法属玻利尼西亚 +689\",\"zhName\":\"法属玻利尼西亚 +689\",\"enName\":\"French Polynesia +689\"},{\"code\":\"[+241]\",\"name\":\"加蓬 +241\",\"zhName\":\"加蓬 +241\",\"enName\":\"Gabon +241\"},{\"code\":\"[+220]\",\"name\":\"冈比亚 +220\",\"zhName\":\"冈比亚 +220\",\"enName\":\"Gambia +220\"},{\"code\":\"[+995]\",\"name\":\"格鲁吉亚 +995\",\"zhName\":\"格鲁吉亚 +995\",\"enName\":\"Georgia +995\"},{\"code\":\"[+49]\",\"name\":\"德国 +49\",\"zhName\":\"德国 +49\",\"enName\":\"Germany +49\"},{\"code\":\"[+233]\",\"name\":\"加纳/乌兹别克斯坦 +233\",\"zhName\":\"加纳/乌兹别克斯坦 +233\",\"enName\":\"Ghana/Uzbekistan +233\"},{\"code\":\"[+350]\",\"name\":\"直布罗陀 +350\",\"zhName\":\"直布罗陀 +350\",\"enName\":\"Gibraltar +350\"},{\"code\":\"[+30]\",\"name\":\"希腊 +30\",\"zhName\":\"希腊 +30\",\"enName\":\"Greece +30\"},{\"code\":\"[+1809]\",\"name\":\"格林纳达/特立尼达和多巴哥 +1809\",\"zhName\":\"格林纳达/特立尼达和多巴哥 +1809\",\"enName\":\"Grenada/Trinidad and Tobago +1809\"},{\"code\":\"[+1671]\",\"name\":\"关岛 +1671\",\"zhName\":\"关岛 +1671\",\"enName\":\"Guam +1671\"},{\"code\":\"[+502]\",\"name\":\"危地马拉 +502\",\"zhName\":\"危地马拉 +502\",\"enName\":\"Guatemala +502\"},{\"code\":\"[+224]\",\"name\":\"几内亚 +224\",\"zhName\":\"几内亚 +224\",\"enName\":\"Guinea +224\"},{\"code\":\"[+592]\",\"name\":\"圭亚那 +592\",\"zhName\":\"圭亚那 +592\",\"enName\":\"Guyana +592\"},{\"code\":\"[+509]\",\"name\":\"海地 +509\",\"zhName\":\"海地 +509\",\"enName\":\"Haiti +509\"},{\"code\":\"[+504]\",\"name\":\"洪都拉斯 +504\",\"zhName\":\"洪都拉斯 +504\",\"enName\":\"Honduras +504\"},{\"code\":\"[+852]\",\"name\":\"中国香港 +852\",\"zhName\":\"中国香港 +852\",\"enName\":\"Hong Kong +852\"},{\"code\":\"[+36]\",\"name\":\"匈牙利 +36\",\"zhName\":\"匈牙利 +36\",\"enName\":\"Hungary +36\"},{\"code\":\"[+354]\",\"name\":\"冰岛 +354\",\"zhName\":\"冰岛 +354\",\"enName\":\"Iceland +354\"},{\"code\":\"[+91]\",\"name\":\"印度 +91\",\"zhName\":\"印度 +91\",\"enName\":\"India +91\"},{\"code\":\"[+62]\",\"name\":\"印度尼西亚 +62\",\"zhName\":\"印度尼西亚 +62\",\"enName\":\"Indonesia +62\"},{\"code\":\"[+98]\",\"name\":\"伊朗 +98\",\"zhName\":\"伊朗 +98\",\"enName\":\"Iran +98\"},{\"code\":\"[+964]\",\"name\":\"伊拉克 +964\",\"zhName\":\"伊拉克 +964\",\"enName\":\"Iraq +964\"},{\"code\":\"[+353]\",\"name\":\"爱尔兰 +353\",\"zhName\":\"爱尔兰 +353\",\"enName\":\"Ireland +353\"},{\"code\":\"[+972]\",\"name\":\"以色列 +972\",\"zhName\":\"以色列 +972\",\"enName\":\"Israel +972\"},{\"code\":\"[+39]\",\"name\":\"意大利 +39\",\"zhName\":\"意大利 +39\",\"enName\":\"Italy +39\"},{\"code\":\"[+225]\",\"name\":\"科特迪瓦 +225\",\"zhName\":\"科特迪瓦 +225\",\"enName\":\"Côte d'Ivoire +225\"},{\"code\":\"[+1876]\",\"name\":\"牙买加 +1876\",\"zhName\":\"牙买加 +1876\",\"enName\":\"Jamaica +1876\"},{\"code\":\"[+81]\",\"name\":\"日本 +81\",\"zhName\":\"日本 +81\",\"enName\":\"Japan +81\"},{\"code\":\"[+962]\",\"name\":\"约旦 +962\",\"zhName\":\"约旦 +962\",\"enName\":\"Jordan +962\"},{\"code\":\"[+855]\",\"name\":\"柬埔寨 +855\",\"zhName\":\"柬埔寨 +855\",\"enName\":\"Cambodia +855\"},{\"code\":\"[+7]\",\"name\":\"哈萨克斯坦/俄罗斯 +7\",\"zhName\":\"哈萨克斯坦/俄罗斯 +7\",\"enName\":\"Kazakhstan/Russia +7\"},{\"code\":\"[+254]\",\"name\":\"肯尼亚 +254\",\"zhName\":\"肯尼亚 +254\",\"enName\":\"Kenya +254\"},{\"code\":\"[+82]\",\"name\":\"韩国 +82\",\"zhName\":\"韩国 +82\",\"enName\":\"South Korea +82\"},{\"code\":\"[+965]\",\"name\":\"科威特 +965\",\"zhName\":\"科威特 +965\",\"enName\":\"Kuwait +965\"},{\"code\":\"[+331]\",\"name\":\"吉尔吉斯坦 +331\",\"zhName\":\"吉尔吉斯坦 +331\",\"enName\":\"Kyrgyzstan +331\"},{\"code\":\"[+856]\",\"name\":\"老挝 +856\",\"zhName\":\"老挝 +856\",\"enName\":\"Laos +856\"},{\"code\":\"[+371]\",\"name\":\"拉脱维亚 +371\",\"zhName\":\"拉脱维亚 +371\",\"enName\":\"Latvia +371\"},{\"code\":\"[+961]\",\"name\":\"黎巴嫩 +961\",\"zhName\":\"黎巴嫩 +961\",\"enName\":\"Lebanon +961\"},{\"code\":\"[+266]\",\"name\":\"莱索托 +266\",\"zhName\":\"莱索托 +266\",\"enName\":\"Lesotho +266\"},{\"code\":\"[+231]\",\"name\":\"利比里亚 +231\",\"zhName\":\"利比里亚 +231\",\"enName\":\"Liberia +231\"},{\"code\":\"[+218]\",\"name\":\"利比亚 +218\",\"zhName\":\"利比亚 +218\",\"enName\":\"Libya +218\"},{\"code\":\"[+423]\",\"name\":\"列支敦士登 +423\",\"zhName\":\"列支敦士登 +423\",\"enName\":\"Liechtenstein +423\"},{\"code\":\"[+370]\",\"name\":\"立陶宛 +370\",\"zhName\":\"立陶宛 +370\",\"enName\":\"Lithuania +370\"},{\"code\":\"[+352]\",\"name\":\"卢森堡 +352\",\"zhName\":\"卢森堡 +352\",\"enName\":\"Luxembourg +352\"},{\"code\":\"[+853]\",\"name\":\"中国澳门 +853\",\"zhName\":\"中国澳门 +853\",\"enName\":\"Macau +853\"},{\"code\":\"[+261]\",\"name\":\"马达加斯加 +261\",\"zhName\":\"马达加斯加 +261\",\"enName\":\"Madagascar +261\"},{\"code\":\"[+265]\",\"name\":\"马拉维 +265\",\"zhName\":\"马拉维 +265\",\"enName\":\"Malawi +265\"},{\"code\":\"[+60]\",\"name\":\"马来西亚 +60\",\"zhName\":\"马来西亚 +60\",\"enName\":\"Malaysia +60\"},{\"code\":\"[+960]\",\"name\":\"马尔代夫 +960\",\"zhName\":\"马尔代夫 +960\",\"enName\":\"Maldives +960\"},{\"code\":\"[+223]\",\"name\":\"马里 +223\",\"zhName\":\"马里 +223\",\"enName\":\"Mali +223\"},{\"code\":\"[+356]\",\"name\":\"马耳他 +356\",\"zhName\":\"马耳他 +356\",\"enName\":\"Malta +356\"},{\"code\":\"[+1670]\",\"name\":\"马里亚那群岛 +1670\",\"zhName\":\"马里亚那群岛 +1670\",\"enName\":\"Mariana Islands +1670\"},{\"code\":\"[+596]\",\"name\":\"马提尼克 +596\",\"zhName\":\"马提尼克 +596\",\"enName\":\"Martinique +596\"},{\"code\":\"[+230]\",\"name\":\"毛里求斯 +230\",\"zhName\":\"毛里求斯 +230\",\"enName\":\"Mauritius +230\"},{\"code\":\"[+52]\",\"name\":\"墨西哥 +52\",\"zhName\":\"墨西哥 +52\",\"enName\":\"Mexico +52\"},{\"code\":\"[+373]\",\"name\":\"摩尔多瓦 +373\",\"zhName\":\"摩尔多瓦 +373\",\"enName\":\"Moldova +373\"},{\"code\":\"[+377]\",\"name\":\"摩纳哥 +377\",\"zhName\":\"摩纳哥 +377\",\"enName\":\"Monaco +377\"},{\"code\":\"[+976]\",\"name\":\"蒙古 +976\",\"zhName\":\"蒙古 +976\",\"enName\":\"Mongolia +976\"},{\"code\":\"[+1664]\",\"name\":\"蒙特塞拉特岛 +1664\",\"zhName\":\"蒙特塞拉特岛 +1664\",\"enName\":\"Montserrat +1664\"},{\"code\":\"[+212]\",\"name\":\"摩洛哥 +212\",\"zhName\":\"摩洛哥 +212\",\"enName\":\"Morocco +212\"},{\"code\":\"[+258]\",\"name\":\"莫桑比克 +258\",\"zhName\":\"莫桑比克 +258\",\"enName\":\"Mozambique +258\"},{\"code\":\"[+264]\",\"name\":\"纳米比亚 +264\",\"zhName\":\"纳米比亚 +264\",\"enName\":\"Namibia +264\"},{\"code\":\"[+674]\",\"name\":\"瑙鲁 +674\",\"zhName\":\"瑙鲁 +674\",\"enName\":\"Nauru +674\"},{\"code\":\"[+977]\",\"name\":\"尼泊尔 +977\",\"zhName\":\"尼泊尔 +977\",\"enName\":\"Nepal +977\"},{\"code\":\"[+599]\",\"name\":\"荷属安的列斯 +599\",\"zhName\":\"荷属安的列斯 +599\",\"enName\":\"Netherlands Antilles +599\"},{\"code\":\"[+31]\",\"name\":\"荷兰 +31\",\"zhName\":\"荷兰 +31\",\"enName\":\"Netherlands +31\"},{\"code\":\"[+64]\",\"name\":\"新西兰 +64\",\"zhName\":\"新西兰 +64\",\"enName\":\"New Zealand +64\"},{\"code\":\"[+505]\",\"name\":\"尼加拉瓜 +505\",\"zhName\":\"尼加拉瓜 +505\",\"enName\":\"Nicaragua +505\"},{\"code\":\"[+227]\",\"name\":\"尼日尔 +227\",\"zhName\":\"尼日尔 +227\",\"enName\":\"Niger +227\"},{\"code\":\"[+234]\",\"name\":\"尼日利亚 +234\",\"zhName\":\"尼日利亚 +234\",\"enName\":\"Nigeria +234\"},{\"code\":\"[+850]\",\"name\":\"朝鲜 +850\",\"zhName\":\"朝鲜 +850\",\"enName\":\"North Korea +850\"},{\"code\":\"[+47]\",\"name\":\"挪威 +47\",\"zhName\":\"挪威 +47\",\"enName\":\"Norway +47\"},{\"code\":\"[+968]\",\"name\":\"阿曼 +968\",\"zhName\":\"阿曼 +968\",\"enName\":\"Oman +968\"},{\"code\":\"[+92]\",\"name\":\"巴基斯坦 +92\",\"zhName\":\"巴基斯坦 +92\",\"enName\":\"Pakistan +92\"},{\"code\":\"[+507]\",\"name\":\"巴拿马 +507\",\"zhName\":\"巴拿马 +507\",\"enName\":\"Panama +507\"},{\"code\":\"[+675]\",\"name\":\"巴布亚新几内亚 +675\",\"zhName\":\"巴布亚新几内亚 +675\",\"enName\":\"Papua New Guinea +675\"},{\"code\":\"[+595]\",\"name\":\"巴拉圭 +595\",\"zhName\":\"巴拉圭 +595\",\"enName\":\"Paraguay +595\"},{\"code\":\"[+51]\",\"name\":\"秘鲁 +51\",\"zhName\":\"秘鲁 +51\",\"enName\":\"Peru +51\"},{\"code\":\"[+63]\",\"name\":\"菲律宾 +63\",\"zhName\":\"菲律宾 +63\",\"enName\":\"Philippines +63\"},{\"code\":\"[+48]\",\"name\":\"波兰 +48\",\"zhName\":\"波兰 +48\",\"enName\":\"Poland +48\"},{\"code\":\"[+351]\",\"name\":\"葡萄牙 +351\",\"zhName\":\"葡萄牙 +351\",\"enName\":\"Portugal +351\"},{\"code\":\"[+1787]\",\"name\":\"波多黎各 +1787\",\"zhName\":\"波多黎各 +1787\",\"enName\":\"Puerto Rico +1787\"},{\"code\":\"[+974]\",\"name\":\"卡塔尔 +974\",\"zhName\":\"卡塔尔 +974\",\"enName\":\"Qatar +974\"},{\"code\":\"[+262]\",\"name\":\"留尼旺 +262\",\"zhName\":\"留尼旺 +262\",\"enName\":\"Reunion +262\"},{\"code\":\"[+40]\",\"name\":\"罗马尼亚 +40\",\"zhName\":\"罗马尼亚 +40\",\"enName\":\"Romania +40\"},{\"code\":\"[+1784]\",\"name\":\"圣文森特岛/圣文森特 +1784\",\"zhName\":\"圣文森特岛/圣文森特 +1784\",\"enName\":\"St. Vincent Island/St. Vincent +1784\"},{\"code\":\"[+684]\",\"name\":\"东萨摩亚(美) +684\",\"zhName\":\"东萨摩亚(美) +684\",\"enName\":\"Eastern Samoa (US) +684\"},{\"code\":\"[+685]\",\"name\":\"西萨摩亚 +685\",\"zhName\":\"西萨摩亚 +685\",\"enName\":\"Western Samoa +685\"},{\"code\":\"[+378]\",\"name\":\"圣马力诺 +378\",\"zhName\":\"圣马力诺 +378\",\"enName\":\"San Marino +378\"},{\"code\":\"[+239]\",\"name\":\"圣多美和普林西比 +239\",\"zhName\":\"圣多美和普林西比 +239\",\"enName\":\"Sao Tome and Principe +239\"},{\"code\":\"[+966]\",\"name\":\"沙特阿拉伯 +966\",\"zhName\":\"沙特阿拉伯 +966\",\"enName\":\"Saudi Arabia +966\"},{\"code\":\"[+221]\",\"name\":\"塞内加尔 +221\",\"zhName\":\"塞内加尔 +221\",\"enName\":\"Senegal +221\"},{\"code\":\"[+248]\",\"name\":\"塞舌尔 +248\",\"zhName\":\"塞舌尔 +248\",\"enName\":\"Seychelles +248\"},{\"code\":\"[+232]\",\"name\":\"塞拉利昂 +232\",\"zhName\":\"塞拉利昂 +232\",\"enName\":\"Sierra Leone +232\"},{\"code\":\"[+65]\",\"name\":\"新加坡 +65\",\"zhName\":\"新加坡 +65\",\"enName\":\"Singapore +65\"},{\"code\":\"[+421]\",\"name\":\"斯洛伐克 +421\",\"zhName\":\"斯洛伐克 +421\",\"enName\":\"Slovakia +421\"},{\"code\":\"[+386]\",\"name\":\"斯洛文尼亚 +386\",\"zhName\":\"斯洛文尼亚 +386\",\"enName\":\"Slovenia +386\"},{\"code\":\"[+677]\",\"name\":\"所罗门群岛 +677\",\"zhName\":\"所罗门群岛 +677\",\"enName\":\"Solomon Islands +677\"},{\"code\":\"[+252]\",\"name\":\"索马里 +252\",\"zhName\":\"索马里 +252\",\"enName\":\"Somalia +252\"},{\"code\":\"[+27]\",\"name\":\"南非 +27\",\"zhName\":\"南非 +27\",\"enName\":\"South Africa +27\"},{\"code\":\"[+34]\",\"name\":\"西班牙 +34\",\"zhName\":\"西班牙 +34\",\"enName\":\"Spain +34\"},{\"code\":\"[+94]\",\"name\":\"斯里兰卡 +94\",\"zhName\":\"斯里兰卡 +94\",\"enName\":\"Sri Lanka +94\"},{\"code\":\"[+1758]\",\"name\":\"圣卢西亚 +1758\",\"zhName\":\"圣卢西亚 +1758\",\"enName\":\"Saint Lucia +1758\"},{\"code\":\"[+249]\",\"name\":\"苏丹 +249\",\"zhName\":\"苏丹 +249\",\"enName\":\"Sudan +249\"},{\"code\":\"[+597]\",\"name\":\"苏里南 +597\",\"zhName\":\"苏里南 +597\",\"enName\":\"Suriname +597\"},{\"code\":\"[+268]\",\"name\":\"斯威士兰 +268\",\"zhName\":\"斯威士兰 +268\",\"enName\":\"Swaziland +268\"},{\"code\":\"[+46]\",\"name\":\"瑞典 +46\",\"zhName\":\"瑞典 +46\",\"enName\":\"Sweden +46\"},{\"code\":\"[+41]\",\"name\":\"瑞士 +41\",\"zhName\":\"瑞士 +41\",\"enName\":\"Switzerland +41\"},{\"code\":\"[+963]\",\"name\":\"叙利亚 +963\",\"zhName\":\"叙利亚 +963\",\"enName\":\"Syria +963\"},{\"code\":\"[+886]\",\"name\":\"中国台湾 +886\",\"zhName\":\"中国台湾 +886\",\"enName\":\"Taiwan +886\"},{\"code\":\"[+992]\",\"name\":\"塔吉克斯坦 +992\",\"zhName\":\"塔吉克斯坦 +992\",\"enName\":\"Tajikistan +992\"},{\"code\":\"[+255]\",\"name\":\"坦桑尼亚 +255\",\"zhName\":\"坦桑尼亚 +255\",\"enName\":\"Tanzania +255\"},{\"code\":\"[+66]\",\"name\":\"泰国 +66\",\"zhName\":\"泰国 +66\",\"enName\":\"Thailand +66\"},{\"code\":\"[+228]\",\"name\":\"多哥 +228\",\"zhName\":\"多哥 +228\",\"enName\":\"Togo +228\"},{\"code\":\"[+676]\",\"name\":\"汤加 +676\",\"zhName\":\"汤加 +676\",\"enName\":\"Tonga +676\"},{\"code\":\"[+216]\",\"name\":\"突尼斯 +216\",\"zhName\":\"突尼斯 +216\",\"enName\":\"Tunisia +216\"},{\"code\":\"[+90]\",\"name\":\"土耳其 +90\",\"zhName\":\"土耳其 +90\",\"enName\":\"Turkey +90\"},{\"code\":\"[+993]\",\"name\":\"土库曼斯坦 +993\",\"zhName\":\"土库曼斯坦 +993\",\"enName\":\"Turkmenistan +993\"},{\"code\":\"[+256]\",\"name\":\"乌干达 +256\",\"zhName\":\"乌干达 +256\",\"enName\":\"Uganda +256\"},{\"code\":\"[+380]\",\"name\":\"乌克兰 +380\",\"zhName\":\"乌克兰 +380\",\"enName\":\"Ukraine +380\"},{\"code\":\"[+971]\",\"name\":\"阿拉伯联合酋长国 +971\",\"zhName\":\"阿拉伯联合酋长国 +971\",\"enName\":\"United Arab Emirates +971\"},{\"code\":\"[+44]\",\"name\":\"英国 +44\",\"zhName\":\"英国 +44\",\"enName\":\"United Kingdom +44\"},{\"code\":\"[+598]\",\"name\":\"乌拉圭 +598\",\"zhName\":\"乌拉圭 +598\",\"enName\":\"Uruguay +598\"},{\"code\":\"[+58]\",\"name\":\"委内瑞拉 +58\",\"zhName\":\"委内瑞拉 +58\",\"enName\":\"Venezuela +58\"},{\"code\":\"[+84]\",\"name\":\"越南 +84\",\"zhName\":\"越南 +84\",\"enName\":\"Vietnam +84\"},{\"code\":\"[+967]\",\"name\":\"也门 +967\",\"zhName\":\"也门 +967\",\"enName\":\"Yemen +967\"},{\"code\":\"[+381]\",\"name\":\"南斯拉夫 +381\",\"zhName\":\"南斯拉夫 +381\",\"enName\":\"Yugoslavia +381\"},{\"code\":\"[+263]\",\"name\":\"津巴布韦 +263\",\"zhName\":\"津巴布韦 +263\",\"enName\":\"Zimbabwe +263\"},{\"code\":\"[+243]\",\"name\":\"扎伊尔 +243\",\"zhName\":\"扎伊尔 +243\",\"enName\":\"Zaire +243\"},{\"code\":\"[+260]\",\"name\":\"赞比亚 +260\",\"zhName\":\"赞比亚 +260\",\"enName\":\"Zambia +260\"}]"
                        },
                        new
                        {
                            Id = 1281498705313329082L,
                            Controls = false,
                            EnPlaceholder = "Please enter",
                            FieldCode = "DepartureHotelRoomNO",
                            FieldComponentType = "FieldInput",
                            FieldEnName = "Departure Hotel Room Number",
                            FieldName = "出发酒店房号",
                            FieldType = (sbyte)0,
                            FieldsGroupType = (sbyte)3,
                            Maxlength = 30m,
                            Number = false,
                            Placeholder = "请输入",
                            Radio = false,
                            Textarea = false
                        },
                        new
                        {
                            Id = 1281498705313388072L,
                            Controls = false,
                            DefaultValue = "{\"address\":null,\"hasAddress\":true,\"detail\":{\"resourceType\":null,\"resourceSubType\":null,\"resourceZhName\":null,\"resourceEnName\":null,\"countryCode\":null,\"provinceCode\":null,\"cityCode\":null,\"relatedId\":null,\"countryZhName\":null,\"countryEnName\":null,\"provinceZhName\":null,\"provinceEnName\":null,\"cityZhName\":null,\"cityEnName\":null,\"iata\":null,\"icao\":null,\"highLightContent\":null,\"countryHighLightContent\":null,\"provinceHighLightContent\":null,\"cityHighLightContent\":null,\"iataHighLightContent\":null,\"icaoHighLightContent\":null,\"longitude\":null,\"latitude\":null,\"value\":null}}",
                            EsResourceParentType = "[4,6,7,2,5,9,8]",
                            FieldCode = "DestinationJson",
                            FieldComponentType = "FieldDestinationSearch",
                            FieldEnName = "Destination",
                            FieldName = "送达地点",
                            FieldType = (sbyte)9,
                            FieldsGroupType = (sbyte)3,
                            Number = false,
                            Placeholder = "请输入酒店/机场/景点名称或其他关键字",
                            Radio = false,
                            Textarea = false
                        },
                        new
                        {
                            Id = 1281498705313388079L,
                            Controls = false,
                            DefaultValue = "{\"type\":\"[+86]\",\"value\":null}",
                            EnPlaceholder = "Please enter",
                            FieldCode = "DestinationHotelPhone",
                            FieldComponentType = "FieldPhoneInput",
                            FieldEnName = "Destination Hotel Phone",
                            FieldName = "目的酒店电话",
                            FieldType = (sbyte)9,
                            FieldsGroupType = (sbyte)3,
                            Maxlength = 16m,
                            Number = false,
                            Placeholder = "请输入",
                            Radio = false,
                            Textarea = false,
                            ValueRange = "[{\"code\":\"[+244]\",\"name\":\"安哥拉 +244\",\"zhName\":\"安哥拉 +244\",\"enName\":\"Angola +244\"},{\"code\":\"[+93]\",\"name\":\"阿富汗 +93\",\"zhName\":\"阿富汗 +93\",\"enName\":\"Afghanistan +93\"},{\"code\":\"[+1907]\",\"name\":\"阿拉斯加 +1907\",\"zhName\":\"阿拉斯加 +1907\",\"enName\":\"Alaska +1907\"},{\"code\":\"[+355]\",\"name\":\"阿尔巴尼亚 +355\",\"zhName\":\"阿尔巴尼亚 +355\",\"enName\":\"Albania +355\"},{\"code\":\"[+213]\",\"name\":\"阿尔及利亚 +213\",\"zhName\":\"阿尔及利亚 +213\",\"enName\":\"Algeria +213\"},{\"code\":\"[+376]\",\"name\":\"安道尔共和国 +376\",\"zhName\":\"安道尔共和国 +376\",\"enName\":\"Andorra +376\"},{\"code\":\"[+1254]\",\"name\":\"安圭拉岛 +1254\",\"zhName\":\"安圭拉岛 +1254\",\"enName\":\"Anguilla +1254\"},{\"code\":\"[+1268]\",\"name\":\"安提瓜和巴布达 +1268\",\"zhName\":\"安提瓜和巴布达 +1268\",\"enName\":\"Antigua and Barbuda +1268\"},{\"code\":\"[+54]\",\"name\":\"阿根廷 +54\",\"zhName\":\"阿根廷 +54\",\"enName\":\"Argentina +54\"},{\"code\":\"[+374]\",\"name\":\"亚美尼亚 +374\",\"zhName\":\"亚美尼亚 +374\",\"enName\":\"Armenia +374\"},{\"code\":\"[+297]\",\"name\":\"阿鲁巴岛 +297\",\"zhName\":\"阿鲁巴岛 +297\",\"enName\":\"Aruba +297\"},{\"code\":\"[+247]\",\"name\":\"阿森松 +247\",\"zhName\":\"阿森松 +247\",\"enName\":\"Ascension +247\"},{\"code\":\"[+61]\",\"name\":\"澳大利亚 +61\",\"zhName\":\"澳大利亚 +61\",\"enName\":\"Australia +61\"},{\"code\":\"[+43]\",\"name\":\"奥地利 +43\",\"zhName\":\"奥地利 +43\",\"enName\":\"Austria +43\"},{\"code\":\"[+994]\",\"name\":\"阿塞拜疆 +994\",\"zhName\":\"阿塞拜疆 +994\",\"enName\":\"Azerbaijan +994\"},{\"code\":\"[+1242]\",\"name\":\"巴哈马 +1242\",\"zhName\":\"巴哈马 +1242\",\"enName\":\"Bahamas +1242\"},{\"code\":\"[+973]\",\"name\":\"巴林 +973\",\"zhName\":\"巴林 +973\",\"enName\":\"Bahrain +973\"},{\"code\":\"[+880]\",\"name\":\"孟加拉国 +880\",\"zhName\":\"孟加拉国 +880\",\"enName\":\"Bangladesh +880\"},{\"code\":\"[+1246]\",\"name\":\"巴巴多斯 +1246\",\"zhName\":\"巴巴多斯 +1246\",\"enName\":\"Barbados +1246\"},{\"code\":\"[+375]\",\"name\":\"白俄罗斯 +375\",\"zhName\":\"白俄罗斯 +375\",\"enName\":\"Belarus +375\"},{\"code\":\"[+32]\",\"name\":\"比利时 +32\",\"zhName\":\"比利时 +32\",\"enName\":\"Belgium +32\"},{\"code\":\"[+501]\",\"name\":\"伯利兹 +501\",\"zhName\":\"伯利兹 +501\",\"enName\":\"Belize +501\"},{\"code\":\"[+229]\",\"name\":\"贝宁 +229\",\"zhName\":\"贝宁 +229\",\"enName\":\"Benin +229\"},{\"code\":\"[+1441]\",\"name\":\"百慕大群岛 +1441\",\"zhName\":\"百慕大群岛 +1441\",\"enName\":\"Bermuda +1441\"},{\"code\":\"[+975]\",\"name\":\"不丹 +975\",\"zhName\":\"不丹 +975\",\"enName\":\"Bhutan +975\"},{\"code\":\"[+591]\",\"name\":\"玻利维亚 +591\",\"zhName\":\"玻利维亚 +591\",\"enName\":\"Bolivia +591\"},{\"code\":\"[+387]\",\"name\":\"波斯尼亚和黑塞哥维那 +387\",\"zhName\":\"波斯尼亚和黑塞哥维那 +387\",\"enName\":\"Bosnia and Herzegovina +387\"},{\"code\":\"[+267]\",\"name\":\"博茨瓦纳 +267\",\"zhName\":\"博茨瓦纳 +267\",\"enName\":\"Botswana +267\"},{\"code\":\"[+55]\",\"name\":\"巴西 +55\",\"zhName\":\"巴西 +55\",\"enName\":\"Brazil +55\"},{\"code\":\"[+673]\",\"name\":\"文莱 +673\",\"zhName\":\"文莱 +673\",\"enName\":\"Brunei +673\"},{\"code\":\"[+359]\",\"name\":\"保加利亚 +359\",\"zhName\":\"保加利亚 +359\",\"enName\":\"Bulgaria +359\"},{\"code\":\"[+226]\",\"name\":\"布基纳法索 +226\",\"zhName\":\"布基纳法索 +226\",\"enName\":\"Burkina Faso +226\"},{\"code\":\"[+95]\",\"name\":\"缅甸 +95\",\"zhName\":\"缅甸 +95\",\"enName\":\"Myanmar +95\"},{\"code\":\"[+257]\",\"name\":\"布隆迪 +257\",\"zhName\":\"布隆迪 +257\",\"enName\":\"Burundi +257\"},{\"code\":\"[+237]\",\"name\":\"喀麦隆 +237\",\"zhName\":\"喀麦隆 +237\",\"enName\":\"Cameroon +237\"},{\"code\":\"[+1]\",\"name\":\"加拿大/美国 +1\",\"zhName\":\"加拿大/美国 +1\",\"enName\":\"Canada/USA +1\"},{\"code\":\"[+238]\",\"name\":\"佛得角 +238\",\"zhName\":\"佛得角 +238\",\"enName\":\"Cape Verde +238\"},{\"code\":\"[+1345]\",\"name\":\"开曼群岛 +1345\",\"zhName\":\"开曼群岛 +1345\",\"enName\":\"Cayman Islands +1345\"},{\"code\":\"[+236]\",\"name\":\"中非共和国 +236\",\"zhName\":\"中非共和国 +236\",\"enName\":\"Central African Republic +236\"},{\"code\":\"[+235]\",\"name\":\"乍得 +235\",\"zhName\":\"乍得 +235\",\"enName\":\"Chad +235\"},{\"code\":\"[+56]\",\"name\":\"智利 +56\",\"zhName\":\"智利 +56\",\"enName\":\"Chile +56\"},{\"code\":\"[+86]\",\"name\":\"中国大陆 +86\",\"zhName\":\"中国大陆 +86\",\"enName\":\"Mainland China +86\"},{\"code\":\"[+57]\",\"name\":\"哥伦比亚 +57\",\"zhName\":\"哥伦比亚 +57\",\"enName\":\"Colombia +57\"},{\"code\":\"[+242]\",\"name\":\"刚果 +242\",\"zhName\":\"刚果 +242\",\"enName\":\"Congo +242\"},{\"code\":\"[+682]\",\"name\":\"库克群岛 +682\",\"zhName\":\"库克群岛 +682\",\"enName\":\"Cook Islands +682\"},{\"code\":\"[+506]\",\"name\":\"哥斯达黎加 +506\",\"zhName\":\"哥斯达黎加 +506\",\"enName\":\"Costa Rica +506\"},{\"code\":\"[+53]\",\"name\":\"古巴 +53\",\"zhName\":\"古巴 +53\",\"enName\":\"Cuba +53\"},{\"code\":\"[+357]\",\"name\":\"塞浦路斯 +357\",\"zhName\":\"塞浦路斯 +357\",\"enName\":\"Cyprus +357\"},{\"code\":\"[+420]\",\"name\":\"捷克 +420\",\"zhName\":\"捷克 +420\",\"enName\":\"Czech Republic +420\"},{\"code\":\"[+385]\",\"name\":\"克罗地亚共和国 +385\",\"zhName\":\"克罗地亚共和国 +385\",\"enName\":\"Croatia +385\"},{\"code\":\"[+45]\",\"name\":\"丹麦 +45\",\"zhName\":\"丹麦 +45\",\"enName\":\"Denmark +45\"},{\"code\":\"[+253]\",\"name\":\"吉布提 +253\",\"zhName\":\"吉布提 +253\",\"enName\":\"Djibouti +253\"},{\"code\":\"[+1890]\",\"name\":\"多米尼加共和国 +1890\",\"zhName\":\"多米尼加共和国 +1890\",\"enName\":\"Dominican Republic +1890\"},{\"code\":\"[+593]\",\"name\":\"厄瓜多尔 +593\",\"zhName\":\"厄瓜多尔 +593\",\"enName\":\"Ecuador +593\"},{\"code\":\"[+20]\",\"name\":\"埃及 +20\",\"zhName\":\"埃及 +20\",\"enName\":\"Egypt +20\"},{\"code\":\"[+503]\",\"name\":\"萨尔瓦多 +503\",\"zhName\":\"萨尔瓦多 +503\",\"enName\":\"El Salvador +503\"},{\"code\":\"[+372]\",\"name\":\"爱沙尼亚 +372\",\"zhName\":\"爱沙尼亚 +372\",\"enName\":\"Estonia +372\"},{\"code\":\"[+251]\",\"name\":\"埃塞俄比亚 +251\",\"zhName\":\"埃塞俄比亚 +251\",\"enName\":\"Ethiopia +251\"},{\"code\":\"[+679]\",\"name\":\"斐济 +679\",\"zhName\":\"斐济 +679\",\"enName\":\"Fiji +679\"},{\"code\":\"[+358]\",\"name\":\"芬兰 +358\",\"zhName\":\"芬兰 +358\",\"enName\":\"Finland +358\"},{\"code\":\"[+33]\",\"name\":\"法国 +33\",\"zhName\":\"法国 +33\",\"enName\":\"France +33\"},{\"code\":\"[+594]\",\"name\":\"法属圭亚那 +594\",\"zhName\":\"法属圭亚那 +594\",\"enName\":\"French Guiana +594\"},{\"code\":\"[+689]\",\"name\":\"法属玻利尼西亚 +689\",\"zhName\":\"法属玻利尼西亚 +689\",\"enName\":\"French Polynesia +689\"},{\"code\":\"[+241]\",\"name\":\"加蓬 +241\",\"zhName\":\"加蓬 +241\",\"enName\":\"Gabon +241\"},{\"code\":\"[+220]\",\"name\":\"冈比亚 +220\",\"zhName\":\"冈比亚 +220\",\"enName\":\"Gambia +220\"},{\"code\":\"[+995]\",\"name\":\"格鲁吉亚 +995\",\"zhName\":\"格鲁吉亚 +995\",\"enName\":\"Georgia +995\"},{\"code\":\"[+49]\",\"name\":\"德国 +49\",\"zhName\":\"德国 +49\",\"enName\":\"Germany +49\"},{\"code\":\"[+233]\",\"name\":\"加纳/乌兹别克斯坦 +233\",\"zhName\":\"加纳/乌兹别克斯坦 +233\",\"enName\":\"Ghana/Uzbekistan +233\"},{\"code\":\"[+350]\",\"name\":\"直布罗陀 +350\",\"zhName\":\"直布罗陀 +350\",\"enName\":\"Gibraltar +350\"},{\"code\":\"[+30]\",\"name\":\"希腊 +30\",\"zhName\":\"希腊 +30\",\"enName\":\"Greece +30\"},{\"code\":\"[+1809]\",\"name\":\"格林纳达/特立尼达和多巴哥 +1809\",\"zhName\":\"格林纳达/特立尼达和多巴哥 +1809\",\"enName\":\"Grenada/Trinidad and Tobago +1809\"},{\"code\":\"[+1671]\",\"name\":\"关岛 +1671\",\"zhName\":\"关岛 +1671\",\"enName\":\"Guam +1671\"},{\"code\":\"[+502]\",\"name\":\"危地马拉 +502\",\"zhName\":\"危地马拉 +502\",\"enName\":\"Guatemala +502\"},{\"code\":\"[+224]\",\"name\":\"几内亚 +224\",\"zhName\":\"几内亚 +224\",\"enName\":\"Guinea +224\"},{\"code\":\"[+592]\",\"name\":\"圭亚那 +592\",\"zhName\":\"圭亚那 +592\",\"enName\":\"Guyana +592\"},{\"code\":\"[+509]\",\"name\":\"海地 +509\",\"zhName\":\"海地 +509\",\"enName\":\"Haiti +509\"},{\"code\":\"[+504]\",\"name\":\"洪都拉斯 +504\",\"zhName\":\"洪都拉斯 +504\",\"enName\":\"Honduras +504\"},{\"code\":\"[+852]\",\"name\":\"中国香港 +852\",\"zhName\":\"中国香港 +852\",\"enName\":\"Hong Kong +852\"},{\"code\":\"[+36]\",\"name\":\"匈牙利 +36\",\"zhName\":\"匈牙利 +36\",\"enName\":\"Hungary +36\"},{\"code\":\"[+354]\",\"name\":\"冰岛 +354\",\"zhName\":\"冰岛 +354\",\"enName\":\"Iceland +354\"},{\"code\":\"[+91]\",\"name\":\"印度 +91\",\"zhName\":\"印度 +91\",\"enName\":\"India +91\"},{\"code\":\"[+62]\",\"name\":\"印度尼西亚 +62\",\"zhName\":\"印度尼西亚 +62\",\"enName\":\"Indonesia +62\"},{\"code\":\"[+98]\",\"name\":\"伊朗 +98\",\"zhName\":\"伊朗 +98\",\"enName\":\"Iran +98\"},{\"code\":\"[+964]\",\"name\":\"伊拉克 +964\",\"zhName\":\"伊拉克 +964\",\"enName\":\"Iraq +964\"},{\"code\":\"[+353]\",\"name\":\"爱尔兰 +353\",\"zhName\":\"爱尔兰 +353\",\"enName\":\"Ireland +353\"},{\"code\":\"[+972]\",\"name\":\"以色列 +972\",\"zhName\":\"以色列 +972\",\"enName\":\"Israel +972\"},{\"code\":\"[+39]\",\"name\":\"意大利 +39\",\"zhName\":\"意大利 +39\",\"enName\":\"Italy +39\"},{\"code\":\"[+225]\",\"name\":\"科特迪瓦 +225\",\"zhName\":\"科特迪瓦 +225\",\"enName\":\"Côte d'Ivoire +225\"},{\"code\":\"[+1876]\",\"name\":\"牙买加 +1876\",\"zhName\":\"牙买加 +1876\",\"enName\":\"Jamaica +1876\"},{\"code\":\"[+81]\",\"name\":\"日本 +81\",\"zhName\":\"日本 +81\",\"enName\":\"Japan +81\"},{\"code\":\"[+962]\",\"name\":\"约旦 +962\",\"zhName\":\"约旦 +962\",\"enName\":\"Jordan +962\"},{\"code\":\"[+855]\",\"name\":\"柬埔寨 +855\",\"zhName\":\"柬埔寨 +855\",\"enName\":\"Cambodia +855\"},{\"code\":\"[+7]\",\"name\":\"哈萨克斯坦/俄罗斯 +7\",\"zhName\":\"哈萨克斯坦/俄罗斯 +7\",\"enName\":\"Kazakhstan/Russia +7\"},{\"code\":\"[+254]\",\"name\":\"肯尼亚 +254\",\"zhName\":\"肯尼亚 +254\",\"enName\":\"Kenya +254\"},{\"code\":\"[+82]\",\"name\":\"韩国 +82\",\"zhName\":\"韩国 +82\",\"enName\":\"South Korea +82\"},{\"code\":\"[+965]\",\"name\":\"科威特 +965\",\"zhName\":\"科威特 +965\",\"enName\":\"Kuwait +965\"},{\"code\":\"[+331]\",\"name\":\"吉尔吉斯坦 +331\",\"zhName\":\"吉尔吉斯坦 +331\",\"enName\":\"Kyrgyzstan +331\"},{\"code\":\"[+856]\",\"name\":\"老挝 +856\",\"zhName\":\"老挝 +856\",\"enName\":\"Laos +856\"},{\"code\":\"[+371]\",\"name\":\"拉脱维亚 +371\",\"zhName\":\"拉脱维亚 +371\",\"enName\":\"Latvia +371\"},{\"code\":\"[+961]\",\"name\":\"黎巴嫩 +961\",\"zhName\":\"黎巴嫩 +961\",\"enName\":\"Lebanon +961\"},{\"code\":\"[+266]\",\"name\":\"莱索托 +266\",\"zhName\":\"莱索托 +266\",\"enName\":\"Lesotho +266\"},{\"code\":\"[+231]\",\"name\":\"利比里亚 +231\",\"zhName\":\"利比里亚 +231\",\"enName\":\"Liberia +231\"},{\"code\":\"[+218]\",\"name\":\"利比亚 +218\",\"zhName\":\"利比亚 +218\",\"enName\":\"Libya +218\"},{\"code\":\"[+423]\",\"name\":\"列支敦士登 +423\",\"zhName\":\"列支敦士登 +423\",\"enName\":\"Liechtenstein +423\"},{\"code\":\"[+370]\",\"name\":\"立陶宛 +370\",\"zhName\":\"立陶宛 +370\",\"enName\":\"Lithuania +370\"},{\"code\":\"[+352]\",\"name\":\"卢森堡 +352\",\"zhName\":\"卢森堡 +352\",\"enName\":\"Luxembourg +352\"},{\"code\":\"[+853]\",\"name\":\"中国澳门 +853\",\"zhName\":\"中国澳门 +853\",\"enName\":\"Macau +853\"},{\"code\":\"[+261]\",\"name\":\"马达加斯加 +261\",\"zhName\":\"马达加斯加 +261\",\"enName\":\"Madagascar +261\"},{\"code\":\"[+265]\",\"name\":\"马拉维 +265\",\"zhName\":\"马拉维 +265\",\"enName\":\"Malawi +265\"},{\"code\":\"[+60]\",\"name\":\"马来西亚 +60\",\"zhName\":\"马来西亚 +60\",\"enName\":\"Malaysia +60\"},{\"code\":\"[+960]\",\"name\":\"马尔代夫 +960\",\"zhName\":\"马尔代夫 +960\",\"enName\":\"Maldives +960\"},{\"code\":\"[+223]\",\"name\":\"马里 +223\",\"zhName\":\"马里 +223\",\"enName\":\"Mali +223\"},{\"code\":\"[+356]\",\"name\":\"马耳他 +356\",\"zhName\":\"马耳他 +356\",\"enName\":\"Malta +356\"},{\"code\":\"[+1670]\",\"name\":\"马里亚那群岛 +1670\",\"zhName\":\"马里亚那群岛 +1670\",\"enName\":\"Mariana Islands +1670\"},{\"code\":\"[+596]\",\"name\":\"马提尼克 +596\",\"zhName\":\"马提尼克 +596\",\"enName\":\"Martinique +596\"},{\"code\":\"[+230]\",\"name\":\"毛里求斯 +230\",\"zhName\":\"毛里求斯 +230\",\"enName\":\"Mauritius +230\"},{\"code\":\"[+52]\",\"name\":\"墨西哥 +52\",\"zhName\":\"墨西哥 +52\",\"enName\":\"Mexico +52\"},{\"code\":\"[+373]\",\"name\":\"摩尔多瓦 +373\",\"zhName\":\"摩尔多瓦 +373\",\"enName\":\"Moldova +373\"},{\"code\":\"[+377]\",\"name\":\"摩纳哥 +377\",\"zhName\":\"摩纳哥 +377\",\"enName\":\"Monaco +377\"},{\"code\":\"[+976]\",\"name\":\"蒙古 +976\",\"zhName\":\"蒙古 +976\",\"enName\":\"Mongolia +976\"},{\"code\":\"[+1664]\",\"name\":\"蒙特塞拉特岛 +1664\",\"zhName\":\"蒙特塞拉特岛 +1664\",\"enName\":\"Montserrat +1664\"},{\"code\":\"[+212]\",\"name\":\"摩洛哥 +212\",\"zhName\":\"摩洛哥 +212\",\"enName\":\"Morocco +212\"},{\"code\":\"[+258]\",\"name\":\"莫桑比克 +258\",\"zhName\":\"莫桑比克 +258\",\"enName\":\"Mozambique +258\"},{\"code\":\"[+264]\",\"name\":\"纳米比亚 +264\",\"zhName\":\"纳米比亚 +264\",\"enName\":\"Namibia +264\"},{\"code\":\"[+674]\",\"name\":\"瑙鲁 +674\",\"zhName\":\"瑙鲁 +674\",\"enName\":\"Nauru +674\"},{\"code\":\"[+977]\",\"name\":\"尼泊尔 +977\",\"zhName\":\"尼泊尔 +977\",\"enName\":\"Nepal +977\"},{\"code\":\"[+599]\",\"name\":\"荷属安的列斯 +599\",\"zhName\":\"荷属安的列斯 +599\",\"enName\":\"Netherlands Antilles +599\"},{\"code\":\"[+31]\",\"name\":\"荷兰 +31\",\"zhName\":\"荷兰 +31\",\"enName\":\"Netherlands +31\"},{\"code\":\"[+64]\",\"name\":\"新西兰 +64\",\"zhName\":\"新西兰 +64\",\"enName\":\"New Zealand +64\"},{\"code\":\"[+505]\",\"name\":\"尼加拉瓜 +505\",\"zhName\":\"尼加拉瓜 +505\",\"enName\":\"Nicaragua +505\"},{\"code\":\"[+227]\",\"name\":\"尼日尔 +227\",\"zhName\":\"尼日尔 +227\",\"enName\":\"Niger +227\"},{\"code\":\"[+234]\",\"name\":\"尼日利亚 +234\",\"zhName\":\"尼日利亚 +234\",\"enName\":\"Nigeria +234\"},{\"code\":\"[+850]\",\"name\":\"朝鲜 +850\",\"zhName\":\"朝鲜 +850\",\"enName\":\"North Korea +850\"},{\"code\":\"[+47]\",\"name\":\"挪威 +47\",\"zhName\":\"挪威 +47\",\"enName\":\"Norway +47\"},{\"code\":\"[+968]\",\"name\":\"阿曼 +968\",\"zhName\":\"阿曼 +968\",\"enName\":\"Oman +968\"},{\"code\":\"[+92]\",\"name\":\"巴基斯坦 +92\",\"zhName\":\"巴基斯坦 +92\",\"enName\":\"Pakistan +92\"},{\"code\":\"[+507]\",\"name\":\"巴拿马 +507\",\"zhName\":\"巴拿马 +507\",\"enName\":\"Panama +507\"},{\"code\":\"[+675]\",\"name\":\"巴布亚新几内亚 +675\",\"zhName\":\"巴布亚新几内亚 +675\",\"enName\":\"Papua New Guinea +675\"},{\"code\":\"[+595]\",\"name\":\"巴拉圭 +595\",\"zhName\":\"巴拉圭 +595\",\"enName\":\"Paraguay +595\"},{\"code\":\"[+51]\",\"name\":\"秘鲁 +51\",\"zhName\":\"秘鲁 +51\",\"enName\":\"Peru +51\"},{\"code\":\"[+63]\",\"name\":\"菲律宾 +63\",\"zhName\":\"菲律宾 +63\",\"enName\":\"Philippines +63\"},{\"code\":\"[+48]\",\"name\":\"波兰 +48\",\"zhName\":\"波兰 +48\",\"enName\":\"Poland +48\"},{\"code\":\"[+351]\",\"name\":\"葡萄牙 +351\",\"zhName\":\"葡萄牙 +351\",\"enName\":\"Portugal +351\"},{\"code\":\"[+1787]\",\"name\":\"波多黎各 +1787\",\"zhName\":\"波多黎各 +1787\",\"enName\":\"Puerto Rico +1787\"},{\"code\":\"[+974]\",\"name\":\"卡塔尔 +974\",\"zhName\":\"卡塔尔 +974\",\"enName\":\"Qatar +974\"},{\"code\":\"[+262]\",\"name\":\"留尼旺 +262\",\"zhName\":\"留尼旺 +262\",\"enName\":\"Reunion +262\"},{\"code\":\"[+40]\",\"name\":\"罗马尼亚 +40\",\"zhName\":\"罗马尼亚 +40\",\"enName\":\"Romania +40\"},{\"code\":\"[+1784]\",\"name\":\"圣文森特岛/圣文森特 +1784\",\"zhName\":\"圣文森特岛/圣文森特 +1784\",\"enName\":\"St. Vincent Island/St. Vincent +1784\"},{\"code\":\"[+684]\",\"name\":\"东萨摩亚(美) +684\",\"zhName\":\"东萨摩亚(美) +684\",\"enName\":\"Eastern Samoa (US) +684\"},{\"code\":\"[+685]\",\"name\":\"西萨摩亚 +685\",\"zhName\":\"西萨摩亚 +685\",\"enName\":\"Western Samoa +685\"},{\"code\":\"[+378]\",\"name\":\"圣马力诺 +378\",\"zhName\":\"圣马力诺 +378\",\"enName\":\"San Marino +378\"},{\"code\":\"[+239]\",\"name\":\"圣多美和普林西比 +239\",\"zhName\":\"圣多美和普林西比 +239\",\"enName\":\"Sao Tome and Principe +239\"},{\"code\":\"[+966]\",\"name\":\"沙特阿拉伯 +966\",\"zhName\":\"沙特阿拉伯 +966\",\"enName\":\"Saudi Arabia +966\"},{\"code\":\"[+221]\",\"name\":\"塞内加尔 +221\",\"zhName\":\"塞内加尔 +221\",\"enName\":\"Senegal +221\"},{\"code\":\"[+248]\",\"name\":\"塞舌尔 +248\",\"zhName\":\"塞舌尔 +248\",\"enName\":\"Seychelles +248\"},{\"code\":\"[+232]\",\"name\":\"塞拉利昂 +232\",\"zhName\":\"塞拉利昂 +232\",\"enName\":\"Sierra Leone +232\"},{\"code\":\"[+65]\",\"name\":\"新加坡 +65\",\"zhName\":\"新加坡 +65\",\"enName\":\"Singapore +65\"},{\"code\":\"[+421]\",\"name\":\"斯洛伐克 +421\",\"zhName\":\"斯洛伐克 +421\",\"enName\":\"Slovakia +421\"},{\"code\":\"[+386]\",\"name\":\"斯洛文尼亚 +386\",\"zhName\":\"斯洛文尼亚 +386\",\"enName\":\"Slovenia +386\"},{\"code\":\"[+677]\",\"name\":\"所罗门群岛 +677\",\"zhName\":\"所罗门群岛 +677\",\"enName\":\"Solomon Islands +677\"},{\"code\":\"[+252]\",\"name\":\"索马里 +252\",\"zhName\":\"索马里 +252\",\"enName\":\"Somalia +252\"},{\"code\":\"[+27]\",\"name\":\"南非 +27\",\"zhName\":\"南非 +27\",\"enName\":\"South Africa +27\"},{\"code\":\"[+34]\",\"name\":\"西班牙 +34\",\"zhName\":\"西班牙 +34\",\"enName\":\"Spain +34\"},{\"code\":\"[+94]\",\"name\":\"斯里兰卡 +94\",\"zhName\":\"斯里兰卡 +94\",\"enName\":\"Sri Lanka +94\"},{\"code\":\"[+1758]\",\"name\":\"圣卢西亚 +1758\",\"zhName\":\"圣卢西亚 +1758\",\"enName\":\"Saint Lucia +1758\"},{\"code\":\"[+249]\",\"name\":\"苏丹 +249\",\"zhName\":\"苏丹 +249\",\"enName\":\"Sudan +249\"},{\"code\":\"[+597]\",\"name\":\"苏里南 +597\",\"zhName\":\"苏里南 +597\",\"enName\":\"Suriname +597\"},{\"code\":\"[+268]\",\"name\":\"斯威士兰 +268\",\"zhName\":\"斯威士兰 +268\",\"enName\":\"Swaziland +268\"},{\"code\":\"[+46]\",\"name\":\"瑞典 +46\",\"zhName\":\"瑞典 +46\",\"enName\":\"Sweden +46\"},{\"code\":\"[+41]\",\"name\":\"瑞士 +41\",\"zhName\":\"瑞士 +41\",\"enName\":\"Switzerland +41\"},{\"code\":\"[+963]\",\"name\":\"叙利亚 +963\",\"zhName\":\"叙利亚 +963\",\"enName\":\"Syria +963\"},{\"code\":\"[+886]\",\"name\":\"中国台湾 +886\",\"zhName\":\"中国台湾 +886\",\"enName\":\"Taiwan +886\"},{\"code\":\"[+992]\",\"name\":\"塔吉克斯坦 +992\",\"zhName\":\"塔吉克斯坦 +992\",\"enName\":\"Tajikistan +992\"},{\"code\":\"[+255]\",\"name\":\"坦桑尼亚 +255\",\"zhName\":\"坦桑尼亚 +255\",\"enName\":\"Tanzania +255\"},{\"code\":\"[+66]\",\"name\":\"泰国 +66\",\"zhName\":\"泰国 +66\",\"enName\":\"Thailand +66\"},{\"code\":\"[+228]\",\"name\":\"多哥 +228\",\"zhName\":\"多哥 +228\",\"enName\":\"Togo +228\"},{\"code\":\"[+676]\",\"name\":\"汤加 +676\",\"zhName\":\"汤加 +676\",\"enName\":\"Tonga +676\"},{\"code\":\"[+216]\",\"name\":\"突尼斯 +216\",\"zhName\":\"突尼斯 +216\",\"enName\":\"Tunisia +216\"},{\"code\":\"[+90]\",\"name\":\"土耳其 +90\",\"zhName\":\"土耳其 +90\",\"enName\":\"Turkey +90\"},{\"code\":\"[+993]\",\"name\":\"土库曼斯坦 +993\",\"zhName\":\"土库曼斯坦 +993\",\"enName\":\"Turkmenistan +993\"},{\"code\":\"[+256]\",\"name\":\"乌干达 +256\",\"zhName\":\"乌干达 +256\",\"enName\":\"Uganda +256\"},{\"code\":\"[+380]\",\"name\":\"乌克兰 +380\",\"zhName\":\"乌克兰 +380\",\"enName\":\"Ukraine +380\"},{\"code\":\"[+971]\",\"name\":\"阿拉伯联合酋长国 +971\",\"zhName\":\"阿拉伯联合酋长国 +971\",\"enName\":\"United Arab Emirates +971\"},{\"code\":\"[+44]\",\"name\":\"英国 +44\",\"zhName\":\"英国 +44\",\"enName\":\"United Kingdom +44\"},{\"code\":\"[+598]\",\"name\":\"乌拉圭 +598\",\"zhName\":\"乌拉圭 +598\",\"enName\":\"Uruguay +598\"},{\"code\":\"[+58]\",\"name\":\"委内瑞拉 +58\",\"zhName\":\"委内瑞拉 +58\",\"enName\":\"Venezuela +58\"},{\"code\":\"[+84]\",\"name\":\"越南 +84\",\"zhName\":\"越南 +84\",\"enName\":\"Vietnam +84\"},{\"code\":\"[+967]\",\"name\":\"也门 +967\",\"zhName\":\"也门 +967\",\"enName\":\"Yemen +967\"},{\"code\":\"[+381]\",\"name\":\"南斯拉夫 +381\",\"zhName\":\"南斯拉夫 +381\",\"enName\":\"Yugoslavia +381\"},{\"code\":\"[+263]\",\"name\":\"津巴布韦 +263\",\"zhName\":\"津巴布韦 +263\",\"enName\":\"Zimbabwe +263\"},{\"code\":\"[+243]\",\"name\":\"扎伊尔 +243\",\"zhName\":\"扎伊尔 +243\",\"enName\":\"Zaire +243\"},{\"code\":\"[+260]\",\"name\":\"赞比亚 +260\",\"zhName\":\"赞比亚 +260\",\"enName\":\"Zambia +260\"}]"
                        },
                        new
                        {
                            Id = 1281498705313388080L,
                            Controls = false,
                            EnPlaceholder = "Please enter",
                            FieldCode = "DestinationHotelRoomNO",
                            FieldComponentType = "FieldInput",
                            FieldEnName = "Destination Hotel Room Number",
                            FieldName = "目的酒店房号",
                            FieldType = (sbyte)0,
                            FieldsGroupType = (sbyte)3,
                            Maxlength = 30m,
                            Number = false,
                            Placeholder = "请输入",
                            Radio = false,
                            Textarea = false
                        },
                        new
                        {
                            Id = 1281498705313388081L,
                            Controls = false,
                            EnPlaceholder = "Please enter",
                            FieldCode = "FlightNumber",
                            FieldComponentType = "FieldInput",
                            FieldEnName = "Flight Number",
                            FieldName = "航班号",
                            FieldType = (sbyte)0,
                            FieldsGroupType = (sbyte)3,
                            Maxlength = 10m,
                            Number = false,
                            Placeholder = "请输入",
                            Radio = false,
                            Textarea = false
                        },
                        new
                        {
                            Id = 1281498705313388082L,
                            Controls = false,
                            EnPlaceholder = "Please enter",
                            FieldCode = "DepartureFlightNumber",
                            FieldComponentType = "FieldInput",
                            FieldEnName = "Outbound Flight Number",
                            FieldName = "去程航班号",
                            FieldType = (sbyte)0,
                            FieldsGroupType = (sbyte)3,
                            Maxlength = 10m,
                            Number = false,
                            Placeholder = "请输入",
                            Radio = false,
                            Textarea = false
                        },
                        new
                        {
                            Id = 1281498705313388083L,
                            Controls = false,
                            EnPlaceholder = "Please enter",
                            FieldCode = "ReturnFlightNumber",
                            FieldComponentType = "FieldInput",
                            FieldEnName = "Return Flight Number",
                            FieldName = "返程航班号",
                            FieldType = (sbyte)0,
                            FieldsGroupType = (sbyte)3,
                            Maxlength = 10m,
                            Number = false,
                            Placeholder = "请输入",
                            Radio = false,
                            Textarea = false
                        },
                        new
                        {
                            Id = 1281498705313388084L,
                            Controls = false,
                            EnPlaceholder = "Please enter",
                            FieldCode = "FlightSchedule",
                            FieldComponentType = "FieldInput",
                            FieldEnName = "Flight Number",
                            FieldName = "班次",
                            FieldType = (sbyte)0,
                            FieldsGroupType = (sbyte)3,
                            Maxlength = 30m,
                            Number = false,
                            Placeholder = "请输入",
                            Radio = false,
                            Textarea = false
                        },
                        new
                        {
                            Id = 1281498705313388085L,
                            Controls = false,
                            EnPlaceholder = "Please select",
                            FieldCode = "FlightDepartureTime",
                            FieldComponentType = "FieldDate",
                            FieldEnName = "Flight Departure Time (Local)",
                            FieldName = "航班起飞当地时间",
                            FieldType = (sbyte)6,
                            FieldsGroupType = (sbyte)3,
                            MinDate = new DateTime(1900, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            Number = false,
                            Placeholder = "请选择",
                            Radio = false,
                            Textarea = false,
                            UIExtend = "{\"dateType\":\"datetime\",\"dateFormat\":\"YYYY-MM-DD HH:mm\",\"valueFormat\":\"yyyy-MM-dd HH:mm\"}"
                        },
                        new
                        {
                            Id = 1281498705313388086L,
                            Controls = false,
                            EnPlaceholder = "Please select",
                            FieldCode = "FlightLandingTime",
                            FieldComponentType = "FieldDate",
                            FieldEnName = "Flight Landing Time (Local)",
                            FieldName = "航班降落当地时间",
                            FieldType = (sbyte)6,
                            FieldsGroupType = (sbyte)3,
                            MinDate = new DateTime(1900, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            Number = false,
                            Placeholder = "请选择",
                            Radio = false,
                            Textarea = false,
                            UIExtend = "{\"dateType\":\"datetime\",\"dateFormat\":\"YYYY-MM-DD HH:mm\",\"valueFormat\":\"yyyy-MM-dd HH:mm\"}"
                        },
                        new
                        {
                            Id = 1281498705313388087L,
                            Controls = false,
                            EnPlaceholder = "Please enter",
                            FieldCode = "AirportTerminal",
                            FieldComponentType = "FieldInput",
                            FieldEnName = "Terminal",
                            FieldName = "航站楼",
                            FieldType = (sbyte)0,
                            FieldsGroupType = (sbyte)3,
                            Maxlength = 10m,
                            Number = false,
                            Placeholder = "请输入",
                            Radio = false,
                            Textarea = false
                        },
                        new
                        {
                            Id = 1281498705313388088L,
                            Controls = false,
                            DefaultValue = "0",
                            FieldCode = "IsLandingVisa",
                            FieldComponentType = "FieldSelect",
                            FieldEnName = "Visa on Arrival (Y/N)",
                            FieldName = "是否落地签",
                            FieldType = (sbyte)7,
                            FieldsGroupType = (sbyte)3,
                            Number = false,
                            Radio = true,
                            Textarea = false,
                            ValueRange = "[{\"code\":true,\"name\":\"是\",\"zhName\":\"是\",\"enName\":\"Yes\"},{\"code\":false,\"name\":\"否\",\"zhName\":\"否\",\"enName\":\"No\"}]"
                        },
                        new
                        {
                            Id = 1281498705313388089L,
                            Controls = false,
                            EnPlaceholder = "Please enter",
                            FieldCode = "CarBrand",
                            FieldComponentType = "FieldInput",
                            FieldEnName = "Vehicle Brand",
                            FieldName = "车辆品牌",
                            FieldType = (sbyte)0,
                            FieldsGroupType = (sbyte)3,
                            Maxlength = 30m,
                            Number = false,
                            Placeholder = "请输入",
                            Radio = false,
                            Textarea = false
                        },
                        new
                        {
                            Id = 1281498705313388090L,
                            Controls = false,
                            EnPlaceholder = "Please enter",
                            FieldCode = "CarModelType",
                            FieldComponentType = "FieldInput",
                            FieldEnName = "Vehicle Model",
                            FieldName = "车型",
                            FieldType = (sbyte)0,
                            FieldsGroupType = (sbyte)3,
                            Maxlength = 30m,
                            Number = false,
                            Placeholder = "请输入",
                            Radio = false,
                            Textarea = false
                        },
                        new
                        {
                            Id = 1281498705313388091L,
                            Controls = false,
                            EnPlaceholder = "Please enter",
                            FieldCode = "LicensePlateNumber",
                            FieldComponentType = "FieldInput",
                            FieldEnName = "License Plate Number",
                            FieldName = "车牌号",
                            FieldType = (sbyte)0,
                            FieldsGroupType = (sbyte)3,
                            Maxlength = 30m,
                            Number = false,
                            Placeholder = "请输入",
                            Radio = false,
                            Textarea = false
                        },
                        new
                        {
                            Id = 1281498705313388092L,
                            Controls = false,
                            EnPlaceholder = "Please enter",
                            FieldCode = "DrivingLicenceNumber",
                            FieldComponentType = "FieldInput",
                            FieldEnName = "Driver's License Number",
                            FieldName = "驾照号",
                            FieldType = (sbyte)0,
                            FieldsGroupType = (sbyte)3,
                            Maxlength = 30m,
                            Number = false,
                            Placeholder = "请输入",
                            Radio = false,
                            Textarea = false
                        },
                        new
                        {
                            Id = 1281498705313388093L,
                            Controls = false,
                            EnPlaceholder = "Please enter",
                            FieldCode = "AuthorizedDrivingModel",
                            FieldComponentType = "FieldInput",
                            FieldEnName = "Approved Vehicle Model",
                            FieldName = "准驾车型",
                            FieldType = (sbyte)0,
                            FieldsGroupType = (sbyte)3,
                            Maxlength = 30m,
                            Number = false,
                            Placeholder = "请输入",
                            Radio = false,
                            Textarea = false
                        },
                        new
                        {
                            Id = 1281498705313388094L,
                            Controls = true,
                            DefaultValue = "1",
                            EnPlaceholder = "Please enter",
                            FieldCode = "CharterCarDays",
                            FieldComponentType = "FieldInput",
                            FieldEnName = "Charter Days",
                            FieldName = "包车天数",
                            FieldType = (sbyte)1,
                            FieldsGroupType = (sbyte)3,
                            MinLength = 1m,
                            Number = true,
                            Placeholder = "请输入",
                            Radio = false,
                            Textarea = false
                        },
                        new
                        {
                            Id = 1281498705313388096L,
                            Controls = false,
                            EnPlaceholder = "Please enter",
                            FieldCode = "UserRemarks",
                            FieldComponentType = "FieldInput",
                            FieldEnName = "Remarks",
                            FieldName = "备注",
                            FieldType = (sbyte)0,
                            FieldsGroupType = (sbyte)3,
                            Maxlength = 2000m,
                            Number = false,
                            Placeholder = "请输入",
                            Radio = false,
                            Textarea = true
                        },
                        new
                        {
                            Id = 1281498705313388095L,
                            Controls = false,
                            FieldCode = "TipMessage",
                            FieldComponentType = "FieldTipMessage",
                            FieldEnName = "Prompt Information",
                            FieldName = "提示信息",
                            FieldType = (sbyte)8,
                            FieldsGroupType = (sbyte)4,
                            Maxlength = 300m,
                            Number = false,
                            Radio = false,
                            Textarea = false
                        });
                });

            modelBuilder.Entity("Product.Api.Model.Group", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<string>("Name")
                        .HasColumnType("varchar(50)");

                    b.Property<string>("Remark")
                        .HasColumnType("varchar(100)");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("Group", (string)null);
                });

            modelBuilder.Entity("Product.Api.Model.GroupItems", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<long>("GroupId")
                        .HasColumnType("bigint");

                    b.Property<long>("ProductId")
                        .HasColumnType("bigint");

                    b.Property<sbyte>("ProductType")
                        .HasColumnType("tinyint");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("GroupId");

                    b.HasIndex("ProductId");

                    b.HasIndex("TenantId");

                    b.ToTable("GroupItems", (string)null);
                });

            modelBuilder.Entity("Product.Api.Model.InformationTemplate", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<bool>("IsDelete")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("tinyint(1)")
                        .HasDefaultValue(false);

                    b.Property<string>("TemplateName")
                        .IsRequired()
                        .HasColumnType("varchar(50)");

                    b.Property<sbyte>("TemplateType")
                        .HasColumnType("tinyint");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("InformationTemplate", (string)null);
                });

            modelBuilder.Entity("Product.Api.Model.InformationTemplateFields", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<string>("DefaultValue")
                        .HasColumnType("text");

                    b.Property<string>("FieldCode")
                        .IsRequired()
                        .HasColumnType("varchar(50)");

                    b.Property<bool?>("IsMultiple")
                        .HasColumnType("tinyint(1)");

                    b.Property<bool>("IsRequired")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("tinyint(1)")
                        .HasDefaultValue(false);

                    b.Property<sbyte>("Sort")
                        .HasColumnType("tinyint");

                    b.Property<long>("TemplateId")
                        .HasColumnType("bigint");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("InformationTemplateFields", (string)null);
                });

            modelBuilder.Entity("Product.Api.Model.LineProduct", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<bool>("AdultsAllowed")
                        .HasColumnType("tinyint(1)");

                    b.Property<string>("AdultsStandard")
                        .HasColumnType("varchar(50)");

                    b.Property<bool>("AutoConfirm")
                        .HasColumnType("tinyint(1)");

                    b.Property<bool>("B2bSellingStatus")
                        .HasColumnType("tinyint(1)");

                    b.Property<bool>("BabyAllowed")
                        .HasColumnType("tinyint(1)");

                    b.Property<string>("BabyStandard")
                        .HasColumnType("varchar(50)");

                    b.Property<bool>("ChildrenAllowed")
                        .HasColumnType("tinyint(1)");

                    b.Property<string>("ChildrenStandard")
                        .HasColumnType("varchar(50)");

                    b.Property<string>("Content")
                        .HasColumnType("mediumtext");

                    b.Property<string>("CostCurrencyCode")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("varchar(20)")
                        .HasDefaultValue("CNY");

                    b.Property<decimal>("CostDiscountRate")
                        .HasColumnType("decimal(18,2)");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<int>("Days")
                        .HasColumnType("int");

                    b.Property<int>("DepartureCityId")
                        .HasColumnType("int");

                    b.Property<string>("DepartureCityName")
                        .HasColumnType("varchar(50)");

                    b.Property<int>("DepartureCountryId")
                        .HasColumnType("int");

                    b.Property<string>("DepartureCountryName")
                        .HasColumnType("varchar(50)");

                    b.Property<int>("DestinationCityId")
                        .HasColumnType("int");

                    b.Property<string>("DestinationCityName")
                        .HasColumnType("varchar(50)");

                    b.Property<sbyte?>("DestinationCoordinateType")
                        .HasColumnType("tinyint");

                    b.Property<int>("DestinationCountryId")
                        .HasColumnType("int");

                    b.Property<string>("DestinationCountryName")
                        .HasColumnType("varchar(50)");

                    b.Property<string>("DestinationGooglePalceId")
                        .HasColumnType("varchar(100)");

                    b.Property<Point>("DestinationLocation")
                        .HasColumnType("point");

                    b.Property<long?>("DevelopUserId")
                        .HasColumnType("bigint");

                    b.Property<bool>("ElderlyAllowed")
                        .HasColumnType("tinyint(1)");

                    b.Property<string>("ElderlyStandard")
                        .HasColumnType("varchar(50)");

                    b.Property<string>("EnTitle")
                        .HasColumnType("varchar(100)");

                    b.Property<bool>("Enabled")
                        .HasColumnType("tinyint(1)");

                    b.Property<string>("FeeNotNote")
                        .HasColumnType("text");

                    b.Property<string>("FeeNote")
                        .HasColumnType("text");

                    b.Property<string>("Instructions")
                        .HasColumnType("varchar(500)");

                    b.Property<bool>("IsCompensation")
                        .HasColumnType("tinyint(1)")
                        .HasDefaultValue(false);

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("tinyint(1)");

                    b.Property<bool>("IsManualConfirm")
                        .HasColumnType("tinyint(1)");

                    b.Property<bool>("IsOpenTimeSlotInventory")
                        .HasColumnType("tinyint(1)");

                    b.Property<bool>("IsSupportRefund")
                        .HasColumnType("tinyint(1)");

                    b.Property<string>("KindReminder")
                        .HasColumnType("varchar(500)");

                    b.Property<int>("Nights")
                        .HasColumnType("int");

                    b.Property<long?>("OperatorUserId")
                        .HasColumnType("bigint");

                    b.Property<string>("OtherNote")
                        .HasColumnType("text");

                    b.Property<sbyte>("PriceInventorySource")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("tinyint")
                        .HasDefaultValue((sbyte)0);

                    b.Property<sbyte>("PriceInventoryType")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("tinyint")
                        .HasDefaultValue((sbyte)1);

                    b.Property<sbyte>("PurchaseSourceType")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("tinyint")
                        .HasDefaultValue((sbyte)1);

                    b.Property<int?>("RefundBeforeTravelDateDay")
                        .HasColumnType("int");

                    b.Property<decimal?>("RefundRate")
                        .HasColumnType("decimal(8,4)");

                    b.Property<TimeSpan?>("RefundTravelDateTime")
                        .HasColumnType("time");

                    b.Property<int>("ReservationDaysInAdvance")
                        .HasColumnType("int");

                    b.Property<TimeSpan>("ReservationTimeInAdvance")
                        .HasColumnType("time");

                    b.Property<string>("SaleCurrencyCode")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("varchar(20)")
                        .HasDefaultValue("CNY");

                    b.Property<string>("SellPointDescribe")
                        .HasColumnType("varchar(500)");

                    b.Property<DateTime?>("SellingDateBegin")
                        .HasColumnType("datetime");

                    b.Property<DateTime?>("SellingDateEnd")
                        .HasColumnType("datetime");

                    b.Property<int>("Sort")
                        .HasColumnType("int");

                    b.Property<long>("SupplierId")
                        .HasColumnType("bigint");

                    b.Property<string>("SupplierName")
                        .HasColumnType("varchar(128)");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<string>("Title")
                        .IsRequired()
                        .HasColumnType("varchar(50)");

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.HasIndex("Sort", "UpdateTime");

                    b.ToTable("LineProduct", (string)null);
                });

            modelBuilder.Entity("Product.Api.Model.LineProductOpenChannelSetting", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<string>("Area")
                        .HasColumnType("varchar(100)");

                    b.Property<string>("ChannelProductId")
                        .HasColumnType("varchar(100)");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<long>("LineProductId")
                        .HasColumnType("bigint");

                    b.Property<sbyte>("PriceInventorySyncChannelType")
                        .HasColumnType("tinyint");

                    b.Property<sbyte>("PriceInventorySyncType")
                        .HasColumnType("tinyint");

                    b.Property<string>("SupplierProductId")
                        .HasColumnType("varchar(100)");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime");

                    b.Property<int>("ZeroStockThreshold")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("LineProductId");

                    b.HasIndex("TenantId");

                    b.ToTable("LineProductOpenChannelSetting", (string)null);
                });

            modelBuilder.Entity("Product.Api.Model.LineProductOpenChannelSettingItem", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<long>("LineProductId")
                        .HasColumnType("bigint");

                    b.Property<long>("LineProductOpenChannelSettingId")
                        .HasColumnType("bigint");

                    b.Property<long>("SyncSkuId")
                        .HasColumnType("bigint");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("LineProductId");

                    b.HasIndex("TenantId");

                    b.ToTable("LineProductOpenChannelSettingItem", (string)null);
                });

            modelBuilder.Entity("Product.Api.Model.LineProductOpenSupplierSetting", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<string>("ActivityId")
                        .HasColumnType("varchar(500)");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<long>("LineProductId")
                        .HasColumnType("bigint");

                    b.Property<sbyte>("PriceInventorySyncType")
                        .HasColumnType("tinyint");

                    b.Property<int>("SyncDateRange")
                        .HasColumnType("int");

                    b.Property<int>("SyncInterval")
                        .HasColumnType("int");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("LineProductOpenSupplierSetting", (string)null);
                });

            modelBuilder.Entity("Product.Api.Model.LineProductRallyPoint", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<string>("Address")
                        .IsRequired()
                        .HasColumnType("varchar(100)");

                    b.Property<int>("CoordinateType")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasDefaultValue(1);

                    b.Property<long>("LineProductId")
                        .HasColumnType("bigint");

                    b.Property<Point>("Location")
                        .HasColumnType("point");

                    b.Property<TimeSpan>("Time")
                        .HasColumnType("time");

                    b.HasKey("Id");

                    b.ToTable("LineProductRallyPoint", (string)null);
                });

            modelBuilder.Entity("Product.Api.Model.LineProductSku", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<string>("ActivityId")
                        .HasColumnType("varchar(100)");

                    b.Property<bool>("B2bSellingStatus")
                        .HasColumnType("tinyint(1)");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<bool>("Enabled")
                        .HasColumnType("tinyint(1)");

                    b.Property<string>("FeeIncludes")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<bool>("IncludedAccommodation")
                        .HasColumnType("tinyint(1)");

                    b.Property<bool>("IsTimeSlot")
                        .HasColumnType("tinyint(1)");

                    b.Property<long>("LineProductId")
                        .HasColumnType("bigint");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("varchar(50)");

                    b.Property<string>("PackageId")
                        .HasColumnType("varchar(100)");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<string>("TimeSlotId")
                        .HasColumnType("varchar(100)");

                    b.Property<string>("TimeSlotName")
                        .HasColumnType("varchar(200)");

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("LineProductSku", (string)null);
                });

            modelBuilder.Entity("Product.Api.Model.LineProductSkuCalendarPrice", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<decimal?>("CostPrice")
                        .HasColumnType("decimal(18,2)");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<DateTime>("Date")
                        .HasColumnType("datetime");

                    b.Property<long>("LineProductId")
                        .HasColumnType("bigint");

                    b.Property<long>("LineProductSkuId")
                        .HasColumnType("bigint");

                    b.Property<long?>("LineProductSkuTypeItemId")
                        .HasColumnType("bigint");

                    b.Property<decimal?>("Price")
                        .HasColumnType("decimal(18,2)");

                    b.Property<sbyte>("PriceAdjustmentType")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("tinyint")
                        .HasDefaultValue((sbyte)0);

                    b.Property<decimal?>("PriceAdjustmentValue")
                        .HasColumnType("decimal(18,2)");

                    b.Property<sbyte>("PriceBasisType")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("tinyint")
                        .HasDefaultValue((sbyte)0);

                    b.Property<sbyte>("PriceChannelType")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("tinyint")
                        .HasDefaultValue((sbyte)0);

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<sbyte>("Type")
                        .HasColumnType("tinyint");

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime");

                    b.HasKey("Id");

                    b.HasIndex("LineProductSkuId");

                    b.HasIndex("TenantId");

                    b.ToTable("LineProductSkuCalendarPrice", (string)null);
                });

            modelBuilder.Entity("Product.Api.Model.LineProductSkuItinerary", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<sbyte>("ActivityType")
                        .HasColumnType("tinyint");

                    b.Property<string>("Content")
                        .HasColumnType("varchar(500)");

                    b.Property<int>("Day")
                        .HasColumnType("int");

                    b.Property<string>("ImgPath")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<long>("LineProductId")
                        .HasColumnType("bigint");

                    b.Property<long>("LineProductSkuId")
                        .HasColumnType("bigint");

                    b.Property<long>("ResourceId")
                        .HasColumnType("bigint");

                    b.Property<string>("Subject")
                        .IsRequired()
                        .HasColumnType("varchar(100)");

                    b.Property<string>("Subtitle")
                        .HasColumnType("varchar(100)");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<TimeSpan>("Time")
                        .HasColumnType("time");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("LineProductSkuItinerary", (string)null);
                });

            modelBuilder.Entity("Product.Api.Model.LineProductSkuTypeItem", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<string>("ActivityId")
                        .HasColumnType("varchar(100)");

                    b.Property<long>("BasicProductMatchId")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<long>("LineProductId")
                        .HasColumnType("bigint");

                    b.Property<long>("LineProductSkuId")
                        .HasColumnType("bigint");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("varchar(500)");

                    b.Property<string>("PackageId")
                        .HasColumnType("varchar(100)");

                    b.Property<string>("SkuId")
                        .HasColumnType("varchar(100)");

                    b.Property<sbyte>("SkuPriceType")
                        .HasColumnType("tinyint");

                    b.Property<bool>("SupplierIsSale")
                        .HasColumnType("tinyint(1)")
                        .HasDefaultValue(true);

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<string>("TimeSlotId")
                        .HasColumnType("varchar(100)");

                    b.Property<string>("TimeSlotName")
                        .HasColumnType("varchar(200)");

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("LineProductSkuTypeItem", (string)null);
                });

            modelBuilder.Entity("Product.Api.Model.MailProduct", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<string>("Content")
                        .HasColumnType("mediumtext");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<bool>("Enabled")
                        .HasColumnType("tinyint(1)");

                    b.Property<bool>("IsSupportRefund")
                        .HasColumnType("tinyint(1)");

                    b.Property<long>("PostageTemplateId")
                        .HasColumnType("bigint");

                    b.Property<string>("SellPointDescribe")
                        .HasColumnType("varchar(100)");

                    b.Property<DateTime?>("SellingDateBegin")
                        .HasColumnType("datetime");

                    b.Property<DateTime?>("SellingDateEnd")
                        .HasColumnType("datetime");

                    b.Property<int>("Sort")
                        .HasColumnType("int");

                    b.Property<long>("SupplierId")
                        .HasColumnType("bigint");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<string>("Title")
                        .HasColumnType("varchar(100)");

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.HasIndex("Sort", "UpdateTime");

                    b.ToTable("MailProduct", (string)null);
                });

            modelBuilder.Entity("Product.Api.Model.OpenPlatformPricingSyncLog", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<sbyte?>("ChannelType")
                        .HasColumnType("tinyint");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<string>("FailedMessage")
                        .HasColumnType("varchar(500)");

                    b.Property<sbyte>("PlatformType")
                        .HasColumnType("tinyint");

                    b.Property<long>("ProductId")
                        .HasColumnType("bigint");

                    b.Property<string>("ProductName")
                        .HasColumnType("varchar(100)");

                    b.Property<sbyte>("ProductType")
                        .HasColumnType("tinyint");

                    b.Property<long>("SkuId")
                        .HasColumnType("bigint");

                    b.Property<string>("SkuName")
                        .IsRequired()
                        .HasColumnType("varchar(100)");

                    b.Property<string>("SkuTypeItemName")
                        .HasColumnType("varchar(100)");

                    b.Property<DateTime>("SyncEndDate")
                        .HasColumnType("datetime");

                    b.Property<sbyte>("SyncResult")
                        .HasColumnType("tinyint");

                    b.Property<DateTime>("SyncStartDate")
                        .HasColumnType("datetime");

                    b.Property<sbyte>("SyncType")
                        .HasColumnType("tinyint");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.HasIndex("TenantId", "ProductId", "ProductType", "PlatformType");

                    b.ToTable("OpenPlatformPricingSyncLog", (string)null);
                });

            modelBuilder.Entity("Product.Api.Model.OpenSupplierBasicProduct", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<bool>("IsTimeSlot")
                        .HasColumnType("tinyint(1)");

                    b.Property<sbyte>("MatchStatus")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("tinyint")
                        .HasDefaultValue((sbyte)0);

                    b.Property<sbyte>("OpenSupplierType")
                        .HasColumnType("tinyint");

                    b.Property<string>("OptionId")
                        .HasColumnType("varchar(100)");

                    b.Property<string>("OptionName")
                        .HasColumnType("varchar(500)");

                    b.Property<string>("ProductId")
                        .IsRequired()
                        .HasColumnType("varchar(100)");

                    b.Property<string>("ProductName")
                        .IsRequired()
                        .HasColumnType("varchar(500)");

                    b.Property<string>("SkuId")
                        .HasColumnType("varchar(100)");

                    b.Property<string>("SkuName")
                        .HasColumnType("varchar(500)");

                    b.Property<sbyte>("SkuPriceType")
                        .HasColumnType("tinyint");

                    b.Property<string>("TimeSlotId")
                        .HasColumnType("varchar(100)");

                    b.Property<string>("TimeSlotName")
                        .HasColumnType("varchar(500)");

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime");

                    b.HasKey("Id");

                    b.ToTable("OpenSupplierBasicProduct", (string)null);
                });

            modelBuilder.Entity("Product.Api.Model.PostageTemplate", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<int>("DeliveryCityCode")
                        .HasColumnType("int");

                    b.Property<string>("DeliveryCityName")
                        .HasColumnType("varchar(50)");

                    b.Property<int>("DeliveryProvinceCode")
                        .HasColumnType("int");

                    b.Property<string>("DeliveryProvinceName")
                        .HasColumnType("varchar(50)");

                    b.Property<bool>("FirstChoice")
                        .HasColumnType("tinyint(1)");

                    b.Property<bool>("IsFreeShipping")
                        .HasColumnType("tinyint(1)");

                    b.Property<string>("Name")
                        .HasColumnType("varchar(50)");

                    b.Property<int>("ProcessingTime")
                        .HasColumnType("int");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("PostageTemplate", (string)null);
                });

            modelBuilder.Entity("Product.Api.Model.PostageTemplateRegion", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<int>("CityCode")
                        .HasColumnType("int");

                    b.Property<long>("ItemId")
                        .HasColumnType("bigint");

                    b.Property<int>("ProvinceCode")
                        .HasColumnType("int");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<sbyte>("Type")
                        .HasColumnType("tinyint");

                    b.HasKey("Id");

                    b.HasIndex("ItemId");

                    b.HasIndex("TenantId");

                    b.ToTable("PostageTemplateRegion", (string)null);
                });

            modelBuilder.Entity("Product.Api.Model.PostageTemplateSetting", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<decimal>("AdditionalFee")
                        .HasColumnType("decimal(18,2)");

                    b.Property<int>("AdditionalPieces")
                        .HasColumnType("int");

                    b.Property<decimal>("FreeShippingOverAmount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<int>("FreeShippingOverPieces")
                        .HasColumnType("int");

                    b.Property<long>("PostageTemplateId")
                        .HasColumnType("bigint");

                    b.Property<decimal>("StartFee")
                        .HasColumnType("decimal(18,2)");

                    b.Property<int>("StartPieces")
                        .HasColumnType("int");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<sbyte>("Type")
                        .HasColumnType("tinyint");

                    b.HasKey("Id");

                    b.HasIndex("PostageTemplateId");

                    b.HasIndex("TenantId");

                    b.ToTable("PostageTemplateSetting", (string)null);
                });

            modelBuilder.Entity("Product.Api.Model.ProductDefaultInformationTemplate", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<sbyte>("ProductTemplateType")
                        .HasColumnType("tinyint");

                    b.Property<sbyte>("ProductType")
                        .HasColumnType("tinyint");

                    b.Property<long>("TemplateId")
                        .HasColumnType("bigint");

                    b.Property<sbyte>("TemplateType")
                        .HasColumnType("tinyint");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.HasIndex("TemplateType", "ProductType", "ProductTemplateType", "TenantId")
                        .IsUnique();

                    b.ToTable("ProductDefaultInformationTemplate", (string)null);
                });

            modelBuilder.Entity("Product.Api.Model.ProductInformationTemplate", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<long>("ProductId")
                        .HasColumnType("bigint");

                    b.Property<long?>("ProductSkuId")
                        .HasColumnType("bigint");

                    b.Property<sbyte>("ProductTemplateType")
                        .HasColumnType("tinyint");

                    b.Property<sbyte>("ProductType")
                        .HasColumnType("tinyint");

                    b.Property<long>("TemplateId")
                        .HasColumnType("bigint");

                    b.Property<sbyte>("TemplateType")
                        .HasColumnType("tinyint");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("ProductInformationTemplate", (string)null);
                });

            modelBuilder.Entity("Product.Api.Model.ProductOperatorUser", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<DateTime?>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<long?>("OperatorAssistantUserId")
                        .HasColumnType("bigint");

                    b.Property<long?>("OperatorUserId")
                        .HasColumnType("bigint");

                    b.Property<long>("ProductId")
                        .HasColumnType("bigint");

                    b.Property<sbyte>("ProductType")
                        .HasColumnType("tinyint");

                    b.Property<sbyte>("SellingPlatform")
                        .HasColumnType("tinyint");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.HasIndex("ProductId", "ProductType");

                    b.ToTable("ProductOperatorUser", (string)null);
                });

            modelBuilder.Entity("Product.Api.Model.ProductPhotos", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<bool>("Enabled")
                        .HasColumnType("tinyint(1)");

                    b.Property<sbyte>("MediaType")
                        .HasColumnType("tinyint");

                    b.Property<string>("Path")
                        .HasColumnType("varchar(255)");

                    b.Property<long>("ProductId")
                        .HasColumnType("bigint");

                    b.Property<int>("Sort")
                        .HasColumnType("int");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime");

                    b.HasKey("Id");

                    b.HasIndex("ProductId");

                    b.HasIndex("TenantId");

                    b.ToTable("ProductPhotos", (string)null);
                });

            modelBuilder.Entity("Product.Api.Model.ProductRedundantData", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<int>("AvailableInventory")
                        .HasColumnType("int");

                    b.Property<bool>("HasCommission")
                        .HasColumnType("tinyint(1)");

                    b.Property<decimal?>("MaxLinePrice")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("MaxPrice")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("MinLinePrice")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("MinPrice")
                        .HasColumnType("decimal(18,2)");

                    b.Property<long>("ProductId")
                        .HasColumnType("bigint");

                    b.Property<int>("Sales")
                        .HasColumnType("int");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("UpdateTime")
                        .HasColumnType("datetime");

                    b.HasKey("Id");

                    b.HasIndex("ProductId")
                        .IsUnique();

                    b.HasIndex("TenantId");

                    b.ToTable("ProductRedundantData", (string)null);
                });

            modelBuilder.Entity("Product.Api.Model.ProductResource", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<long>("CityCode")
                        .HasColumnType("bigint");

                    b.Property<string>("CityName")
                        .HasColumnType("varchar(50)");

                    b.Property<long>("CountryCode")
                        .HasColumnType("bigint");

                    b.Property<string>("CountryName")
                        .HasColumnType("varchar(50)");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<long>("ProductId")
                        .HasColumnType("bigint");

                    b.Property<long>("ProvinceCode")
                        .HasColumnType("bigint");

                    b.Property<string>("ProvinceName")
                        .HasColumnType("varchar(50)");

                    b.Property<long>("ResourceId")
                        .HasColumnType("bigint");

                    b.Property<sbyte>("ResourceType")
                        .HasColumnType("tinyint");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<sbyte>("TicketBusinessType")
                        .HasColumnType("tinyint");

                    b.HasKey("Id");

                    b.HasIndex("ProductId");

                    b.HasIndex("TenantId");

                    b.ToTable("ProductResource", (string)null);
                });

            modelBuilder.Entity("Product.Api.Model.ProductSku", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<int>("AfterPurchaseDays")
                        .HasColumnType("int");

                    b.Property<bool>("B2bSellingStatus")
                        .HasColumnType("tinyint(1)");

                    b.Property<string>("CostDescription")
                        .HasColumnType("varchar(500)");

                    b.Property<decimal>("CostPrice")
                        .HasColumnType("decimal(18,2)");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<bool>("Enabled")
                        .HasColumnType("tinyint(1)");

                    b.Property<string>("ImagePath")
                        .HasColumnType("varchar(255)");

                    b.Property<decimal>("LinePrice")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("Name")
                        .HasColumnType("varchar(100)");

                    b.Property<int>("NumberOfNights")
                        .HasColumnType("int");

                    b.Property<long>("ProductId")
                        .HasColumnType("bigint");

                    b.Property<decimal>("SellingPrice")
                        .HasColumnType("decimal(18,2)");

                    b.Property<int>("Sort")
                        .HasColumnType("int");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime");

                    b.Property<DateTime?>("ValidityBegin")
                        .HasColumnType("datetime");

                    b.Property<DateTime?>("ValidityEnd")
                        .HasColumnType("datetime");

                    b.Property<sbyte>("ValidityType")
                        .HasColumnType("tinyint");

                    b.HasKey("Id");

                    b.HasIndex("ProductId");

                    b.HasIndex("TenantId");

                    b.ToTable("ProductSku", (string)null);
                });

            modelBuilder.Entity("Product.Api.Model.ProductSkuReservationCalendarPrice", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<decimal>("CostPrice")
                        .HasColumnType("decimal(18,2)");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<DateTime>("Date")
                        .HasColumnType("datetime");

                    b.Property<decimal>("Price")
                        .HasColumnType("decimal(18,2)");

                    b.Property<long>("ProductId")
                        .HasColumnType("bigint");

                    b.Property<long>("ProductSkuId")
                        .HasColumnType("bigint");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("ProductId");

                    b.HasIndex("ProductSkuId");

                    b.HasIndex("TenantId");

                    b.HasIndex("ProductId", "ProductSkuId", "Date")
                        .IsUnique();

                    b.ToTable("ProductSkuReservationCalendarPrice", (string)null);
                });

            modelBuilder.Entity("Product.Api.Model.Restaurant", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<string>("Address")
                        .IsRequired()
                        .HasColumnType("varchar(50)");

                    b.Property<int>("CityCode")
                        .HasColumnType("int");

                    b.Property<string>("CityName")
                        .HasColumnType("varchar(50)");

                    b.Property<int>("CoordinateType")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasDefaultValue(1);

                    b.Property<int>("CountryCode")
                        .HasColumnType("int");

                    b.Property<string>("CountryName")
                        .HasColumnType("varchar(50)");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<int>("DistrictCode")
                        .HasColumnType("int");

                    b.Property<string>("DistrictName")
                        .HasColumnType("varchar(50)");

                    b.Property<bool>("Enabled")
                        .HasColumnType("tinyint(1)");

                    b.Property<string>("HouseNumber")
                        .HasColumnType("varchar(10)");

                    b.Property<Point>("Location")
                        .IsRequired()
                        .HasColumnType("point");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("varchar(50)");

                    b.Property<sbyte>("OperatingModel")
                        .HasColumnType("tinyint");

                    b.Property<int>("ProvinceCode")
                        .HasColumnType("int");

                    b.Property<string>("ProvinceName")
                        .HasColumnType("varchar(50)");

                    b.Property<string>("ServiceTime")
                        .IsRequired()
                        .HasColumnType("varchar(100)");

                    b.Property<string>("Telephone")
                        .IsRequired()
                        .HasColumnType("varchar(50)");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime");

                    b.HasKey("Id");

                    b.HasIndex("Location");

                    b.HasIndex("TenantId");

                    b.ToTable("Restaurant", (string)null);
                });

            modelBuilder.Entity("Product.Api.Model.RestaurantPhotos", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<bool>("Enabled")
                        .HasColumnType("tinyint(1)");

                    b.Property<string>("Path")
                        .IsRequired()
                        .HasColumnType("varchar(200)");

                    b.Property<long>("RestaurantId")
                        .HasColumnType("bigint");

                    b.Property<int>("Sort")
                        .HasColumnType("int");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime");

                    b.HasKey("Id");

                    b.HasIndex("RestaurantId");

                    b.HasIndex("TenantId");

                    b.ToTable("RestaurantPhotos", (string)null);
                });

            modelBuilder.Entity("Product.Api.Model.ShoppingCart", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<int>("Amount")
                        .HasColumnType("int");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<long>("ProductId")
                        .HasColumnType("bigint");

                    b.Property<long>("ProductSkuId")
                        .HasColumnType("bigint");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<long>("UserId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.HasIndex("UserId");

                    b.ToTable("ShoppingCart", (string)null);
                });

            modelBuilder.Entity("Product.Api.Model.Store", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<string>("Address")
                        .IsRequired()
                        .HasColumnType("varchar(50)");

                    b.Property<int>("CityCode")
                        .HasColumnType("int");

                    b.Property<string>("CityName")
                        .HasColumnType("varchar(50)");

                    b.Property<int>("CoordinateType")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasDefaultValue(1);

                    b.Property<int>("CountryCode")
                        .HasColumnType("int");

                    b.Property<string>("CountryName")
                        .HasColumnType("varchar(50)");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<int>("DistrictCode")
                        .HasColumnType("int");

                    b.Property<string>("DistrictName")
                        .HasColumnType("varchar(50)");

                    b.Property<string>("HouseNumber")
                        .HasColumnType("varchar(10)");

                    b.Property<Point>("Location")
                        .IsRequired()
                        .HasColumnType("point");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("varchar(50)");

                    b.Property<sbyte>("OperatingModel")
                        .HasColumnType("tinyint");

                    b.Property<int>("ProvinceCode")
                        .HasColumnType("int");

                    b.Property<string>("ProvinceName")
                        .HasColumnType("varchar(50)");

                    b.Property<string>("ServiceTime")
                        .IsRequired()
                        .HasColumnType("varchar(100)");

                    b.Property<string>("Telephone")
                        .IsRequired()
                        .HasColumnType("varchar(50)");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime");

                    b.HasKey("Id");

                    b.HasIndex("Location");

                    b.HasIndex("TenantId");

                    b.ToTable("Store", (string)null);
                });

            modelBuilder.Entity("Product.Api.Model.StorePhotos", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<sbyte>("Enabled")
                        .HasColumnType("tinyint");

                    b.Property<string>("Path")
                        .IsRequired()
                        .HasColumnType("varchar(200)");

                    b.Property<int>("Sort")
                        .HasColumnType("int");

                    b.Property<long>("StoreId")
                        .HasColumnType("bigint");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("StorePhotos", (string)null);
                });

            modelBuilder.Entity("Product.Api.Model.SupplySetting", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<bool>("IsSupply")
                        .HasColumnType("tinyint(1)");

                    b.Property<long>("ProductId")
                        .HasColumnType("bigint");

                    b.Property<sbyte>("ProductType")
                        .HasColumnType("tinyint");

                    b.Property<long>("SupplierId")
                        .HasColumnType("bigint");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("SupplySetting", (string)null);
                });

            modelBuilder.Entity("Product.Api.Model.TicketProduct", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<bool>("AutoRefundAfterExpiration")
                        .HasColumnType("tinyint(1)");

                    b.Property<decimal>("AutoRefundRate")
                        .HasColumnType("decimal(8,4)");

                    b.Property<string>("Content")
                        .HasColumnType("mediumtext");

                    b.Property<string>("CostCurrencyCode")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("varchar(20)")
                        .HasDefaultValue("CNY");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<long?>("DevelopUserId")
                        .HasColumnType("bigint");

                    b.Property<string>("EnTitle")
                        .HasColumnType("varchar(100)");

                    b.Property<bool>("Enabled")
                        .HasColumnType("tinyint(1)");

                    b.Property<string>("Instructions")
                        .HasColumnType("varchar(500)");

                    b.Property<bool>("IsSupportRefund")
                        .HasColumnType("tinyint(1)");

                    b.Property<string>("KindReminder")
                        .HasColumnType("varchar(500)");

                    b.Property<bool>("NeedConfirmReservation")
                        .HasColumnType("tinyint(1)");

                    b.Property<bool>("NeedReservation")
                        .HasColumnType("tinyint(1)");

                    b.Property<bool>("NeedWriteOff")
                        .HasColumnType("tinyint(1)");

                    b.Property<long?>("OperatorUserId")
                        .HasColumnType("bigint");

                    b.Property<int?>("RefundBeforeTravelDateDay")
                        .HasColumnType("int");

                    b.Property<decimal>("RefundRate")
                        .HasColumnType("decimal(8,4)");

                    b.Property<TimeSpan?>("RefundTravelDateTime")
                        .HasColumnType("time");

                    b.Property<int>("ReservationDaysInAdvance")
                        .HasColumnType("int");

                    b.Property<string>("SaleCurrencyCode")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("varchar(20)")
                        .HasDefaultValue("CNY");

                    b.Property<string>("SellPointDescribe")
                        .HasColumnType("varchar(100)");

                    b.Property<DateTime?>("SellingDateBegin")
                        .HasColumnType("datetime");

                    b.Property<DateTime?>("SellingDateEnd")
                        .HasColumnType("datetime");

                    b.Property<int>("Sort")
                        .HasColumnType("int");

                    b.Property<long>("SupplierId")
                        .HasColumnType("bigint");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<sbyte>("TicketBusinessType")
                        .HasColumnType("tinyint");

                    b.Property<sbyte>("TicketSaleType")
                        .HasColumnType("tinyint");

                    b.Property<string>("Title")
                        .HasColumnType("varchar(100)");

                    b.Property<bool>("TouristIDRequired")
                        .HasColumnType("tinyint(1)");

                    b.Property<sbyte>("TouristInfoType")
                        .HasColumnType("tinyint");

                    b.Property<bool>("TouristNameRequired")
                        .HasColumnType("tinyint(1)");

                    b.Property<bool>("TouristPhoneRequired")
                        .HasColumnType("tinyint(1)");

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.HasIndex("Sort", "UpdateTime");

                    b.ToTable("TicketProduct", (string)null);
                });
#pragma warning restore 612, 618
        }
    }
}
