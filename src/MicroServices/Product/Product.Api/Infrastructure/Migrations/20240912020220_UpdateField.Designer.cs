// <auto-generated />
using System;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using NetTopologySuite.Geometries;
using Product.Api.Infrastructure;

#nullable disable

namespace Product.Api.Infrastructure.Migrations
{
    [DbContext(typeof(CustomDbContext))]
    [Migration("20240912020220_UpdateField")]
    partial class UpdateField
    {
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "6.0.5")
                .HasAnnotation("Relational:MaxIdentifierLength", 64);

            modelBuilder.Entity("Product.Api.Model.AgencyChannelPriceSettings", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<sbyte>("BasePriceType")
                        .HasColumnType("tinyint");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<sbyte>("HotelSaleType")
                        .HasColumnType("tinyint");

                    b.Property<bool>("IsApplyGroupRoom")
                        .HasColumnType("tinyint(1)");

                    b.Property<long>("PriceGroupId")
                        .HasColumnType("bigint");

                    b.Property<sbyte>("PriceSettingType")
                        .HasColumnType("tinyint");

                    b.Property<decimal>("PriceSettingValue")
                        .HasColumnType("decimal(18,2)");

                    b.Property<long>("ProductId")
                        .HasColumnType("bigint");

                    b.Property<sbyte>("ProductType")
                        .HasColumnType("tinyint");

                    b.Property<decimal?>("RedundantMinPrice")
                        .HasColumnType("decimal(18,2)");

                    b.Property<long>("SkuId")
                        .HasColumnType("bigint");

                    b.Property<int>("SkuSubClass")
                        .HasColumnType("int");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("AgencyChannelPriceSettings", (string)null);
                });

            modelBuilder.Entity("Product.Api.Model.CarHailingPoint", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<int>("CityCode")
                        .HasColumnType("int");

                    b.Property<string>("CityName")
                        .IsRequired()
                        .HasColumnType("varchar(50)");

                    b.Property<int>("CountryCode")
                        .HasColumnType("int");

                    b.Property<string>("CountryName")
                        .IsRequired()
                        .HasColumnType("varchar(50)");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<string>("PointName")
                        .IsRequired()
                        .HasColumnType("varchar(50)");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("CarHailingPoint", (string)null);
                });

            modelBuilder.Entity("Product.Api.Model.CarHailingProduct", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<sbyte>("CarHailingType")
                        .HasColumnType("tinyint");

                    b.Property<string>("Content")
                        .HasColumnType("mediumtext");

                    b.Property<string>("CostCurrencyCode")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("varchar(20)")
                        .HasDefaultValue("CNY");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<long>("DeparturePointId")
                        .HasColumnType("bigint");

                    b.Property<long>("DestinationPointId")
                        .HasColumnType("bigint");

                    b.Property<long?>("DevelopUserId")
                        .HasColumnType("bigint");

                    b.Property<bool>("Enabled")
                        .HasColumnType("tinyint(1)");

                    b.Property<string>("Instructions")
                        .HasColumnType("varchar(500)");

                    b.Property<bool>("IsSupportRefund")
                        .HasColumnType("tinyint(1)");

                    b.Property<string>("KindReminder")
                        .HasColumnType("varchar(500)");

                    b.Property<sbyte>("LanguageType")
                        .HasColumnType("tinyint");

                    b.Property<long?>("OperatorUserId")
                        .HasColumnType("bigint");

                    b.Property<int?>("RefundBeforeTravelDateDay")
                        .HasColumnType("int");

                    b.Property<decimal?>("RefundRate")
                        .HasColumnType("decimal(8,4)");

                    b.Property<TimeSpan?>("RefundTravelDateTime")
                        .HasColumnType("time");

                    b.Property<int>("ReservationDaysInAdvance")
                        .HasColumnType("int");

                    b.Property<string>("SaleCurrencyCode")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("varchar(20)")
                        .HasDefaultValue("CNY");

                    b.Property<DateTime?>("SellingDateBegin")
                        .HasColumnType("datetime");

                    b.Property<DateTime?>("SellingDateEnd")
                        .HasColumnType("datetime");

                    b.Property<long>("SupplierId")
                        .HasColumnType("bigint");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<string>("Title")
                        .IsRequired()
                        .HasColumnType("varchar(50)");

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("CarHailingProduct", (string)null);
                });

            modelBuilder.Entity("Product.Api.Model.CarHailingProductSku", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<long>("CarHailingProductId")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<bool>("Enabled")
                        .HasColumnType("tinyint(1)");

                    b.Property<string>("Images")
                        .IsRequired()
                        .HasColumnType("varchar(1000)");

                    b.Property<string>("Name")
                        .HasColumnType("varchar(50)");

                    b.Property<int>("Passengers")
                        .HasColumnType("int");

                    b.Property<int>("Seats")
                        .HasColumnType("int");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("CarHailingProductSku", (string)null);
                });

            modelBuilder.Entity("Product.Api.Model.CarHailingProductSkuCalendarPrice", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<long>("CarHailingProductId")
                        .HasColumnType("bigint");

                    b.Property<long>("CarHailingProductSkuId")
                        .HasColumnType("bigint");

                    b.Property<decimal>("CostPrice")
                        .HasColumnType("decimal(18,2)");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<DateTime>("Date")
                        .HasColumnType("datetime");

                    b.Property<decimal>("SalePrice")
                        .HasColumnType("decimal(18,2)");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("CarHailingProductSkuCalendarPrice", (string)null);
                });

            modelBuilder.Entity("Product.Api.Model.CarProduct", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<long?>("AirportId")
                        .HasColumnType("bigint");

                    b.Property<string>("AirportName")
                        .HasColumnType("varchar(255)");

                    b.Property<sbyte>("B2bSellingStatusType")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("tinyint")
                        .HasDefaultValue((sbyte)0);

                    b.Property<string>("CancelRule")
                        .HasColumnType("varchar(1000)");

                    b.Property<sbyte>("CancelType")
                        .HasColumnType("tinyint");

                    b.Property<sbyte>("CarProductType")
                        .HasColumnType("tinyint");

                    b.Property<int>("CityCode")
                        .HasColumnType("int");

                    b.Property<string>("CityName")
                        .IsRequired()
                        .HasColumnType("varchar(128)");

                    b.Property<sbyte?>("CoordinateType")
                        .HasColumnType("tinyint");

                    b.Property<string>("CostCurrencyCode")
                        .IsRequired()
                        .HasColumnType("varchar(32)");

                    b.Property<int>("CountryCode")
                        .HasColumnType("int");

                    b.Property<string>("CountryName")
                        .IsRequired()
                        .HasColumnType("varchar(64)");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<long?>("DevelopUserId")
                        .HasColumnType("bigint");

                    b.Property<bool>("Enabled")
                        .HasColumnType("tinyint(1)");

                    b.Property<string>("FeeExclude")
                        .HasColumnType("text");

                    b.Property<string>("FeeInclude")
                        .HasColumnType("text");

                    b.Property<string>("GooglePalceId")
                        .HasColumnType("varchar(100)");

                    b.Property<string>("Instructions")
                        .HasColumnType("text");

                    b.Property<Point>("Location")
                        .HasColumnType("point");

                    b.Property<string>("MobileDesc")
                        .HasColumnType("text");

                    b.Property<string>("PCDesc")
                        .HasColumnType("text");

                    b.Property<int>("ReservationDaysInAdvance")
                        .HasColumnType("int");

                    b.Property<string>("SaleCurrencyCode")
                        .IsRequired()
                        .HasColumnType("varchar(32)");

                    b.Property<DateTime?>("SellingDateBegin")
                        .HasColumnType("datetime");

                    b.Property<DateTime?>("SellingDateEnd")
                        .HasColumnType("datetime");

                    b.Property<long>("SupplierId")
                        .HasColumnType("bigint");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<string>("Title")
                        .IsRequired()
                        .HasColumnType("varchar(50)");

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("CarProduct", (string)null);
                });

            modelBuilder.Entity("Product.Api.Model.CarProductSku", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<sbyte?>("AirportTransferType")
                        .HasColumnType("tinyint");

                    b.Property<bool>("B2bSellingStatus")
                        .HasColumnType("tinyint(1)");

                    b.Property<long>("CarProductId")
                        .HasColumnType("bigint");

                    b.Property<long>("CarTypeGradeId")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<string>("Description")
                        .HasColumnType("varchar(125)");

                    b.Property<bool>("Enabled")
                        .HasColumnType("tinyint(1)");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("tinyint(1)");

                    b.Property<int>("MaxBaggages")
                        .HasColumnType("int");

                    b.Property<int>("MaxPassengers")
                        .HasColumnType("int");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("varchar(50)");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime(6)");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("CarProductSku", (string)null);
                });

            modelBuilder.Entity("Product.Api.Model.CarProductSkuCalendarPrice", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<long>("CarProductId")
                        .HasColumnType("bigint");

                    b.Property<long>("CarProductSkuId")
                        .HasColumnType("bigint");

                    b.Property<decimal>("CostPrice")
                        .HasColumnType("decimal(18,2)");

                    b.Property<DateTime>("Date")
                        .HasColumnType("datetime");

                    b.Property<sbyte>("PriceAdjustmentType")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("tinyint")
                        .HasDefaultValue((sbyte)0);

                    b.Property<decimal?>("PriceAdjustmentValue")
                        .HasColumnType("decimal(18,2)");

                    b.Property<sbyte>("PriceBasisType")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("tinyint")
                        .HasDefaultValue((sbyte)0);

                    b.Property<decimal>("SalePrice")
                        .HasColumnType("decimal(18,2)");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("UpdateTime")
                        .HasColumnType("datetime");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("CarProductSkuCalendarPrice", (string)null);
                });

            modelBuilder.Entity("Product.Api.Model.CarProductSkuServiceItem", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<long>("CarProductSkuId")
                        .HasColumnType("bigint");

                    b.Property<long>("CarServiceItemId")
                        .HasColumnType("bigint");

                    b.Property<sbyte>("ServiceOption")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("tinyint")
                        .HasDefaultValue((sbyte)0);

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("CarProductSkuServiceItem", (string)null);
                });

            modelBuilder.Entity("Product.Api.Model.CarProductSkuServiceLanguage", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<long>("CarProductId")
                        .HasColumnType("bigint");

                    b.Property<long>("CarProductSkuId")
                        .HasColumnType("bigint");

                    b.Property<sbyte>("Language")
                        .HasColumnType("tinyint");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("CarProductSkuServiceLanguage", (string)null);
                });

            modelBuilder.Entity("Product.Api.Model.CarServiceItem", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<bool>("B2bSellingStatus")
                        .HasColumnType("tinyint(1)");

                    b.Property<long>("CarProductId")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<string>("Description")
                        .HasColumnType("varchar(500)");

                    b.Property<bool>("Enabled")
                        .HasColumnType("tinyint(1)");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("tinyint(1)");

                    b.Property<bool>("IsNeedCharge")
                        .HasColumnType("tinyint(1)");

                    b.Property<string>("ItemName")
                        .IsRequired()
                        .HasColumnType("varchar(50)");

                    b.Property<int>("Maximum")
                        .HasColumnType("int");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("CarServiceItem", (string)null);
                });

            modelBuilder.Entity("Product.Api.Model.CarServiceItemCalendarPrice", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<long>("CarProductId")
                        .HasColumnType("bigint");

                    b.Property<long>("CarServiceItemId")
                        .HasColumnType("bigint");

                    b.Property<decimal>("CostPrice")
                        .HasColumnType("decimal(18,2)");

                    b.Property<DateTime>("Date")
                        .HasColumnType("datetime");

                    b.Property<bool>("Enabled")
                        .HasColumnType("tinyint(1)");

                    b.Property<sbyte>("PriceAdjustmentType")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("tinyint")
                        .HasDefaultValue((sbyte)0);

                    b.Property<decimal?>("PriceAdjustmentValue")
                        .HasColumnType("decimal(18,2)");

                    b.Property<sbyte>("PriceBasisType")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("tinyint")
                        .HasDefaultValue((sbyte)0);

                    b.Property<decimal>("SalePrice")
                        .HasColumnType("decimal(18,2)");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("UpdateTime")
                        .HasColumnType("datetime");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("CarServiceItemCalendarPrice", (string)null);
                });

            modelBuilder.Entity("Product.Api.Model.CarTypeGrade", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<string>("GradeName")
                        .IsRequired()
                        .HasColumnType("varchar(50)");

                    b.Property<int>("Seating")
                        .HasColumnType("int");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("CarTypeGrade", (string)null);
                });

            modelBuilder.Entity("Product.Api.Model.Disclaimer", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<string>("Content")
                        .HasColumnType("mediumtext");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<bool>("Enabled")
                        .HasColumnType("tinyint(1)")
                        .HasDefaultValue(true);

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<string>("Title")
                        .IsRequired()
                        .HasColumnType("varchar(50)");

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("Disclaimer", (string)null);
                });

            modelBuilder.Entity("Product.Api.Model.DisclaimerUsedDetails", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<long>("DisclaimerId")
                        .HasColumnType("bigint");

                    b.Property<long>("ProductId")
                        .HasColumnType("bigint");

                    b.Property<sbyte>("ProductType")
                        .HasColumnType("tinyint");

                    b.HasKey("Id");

                    b.ToTable("DisclaimerUsedDetails", (string)null);
                });

            modelBuilder.Entity("Product.Api.Model.Fields", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<string>("DefaultValue")
                        .HasColumnType("varchar(100)");

                    b.Property<string>("FieldCode")
                        .IsRequired()
                        .HasColumnType("varchar(50)");

                    b.Property<string>("FieldEnName")
                        .HasColumnType("varchar(100)");

                    b.Property<string>("FieldName")
                        .IsRequired()
                        .HasColumnType("varchar(100)");

                    b.Property<sbyte>("FieldType")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("tinyint")
                        .HasDefaultValue((sbyte)0);

                    b.Property<sbyte>("FieldsGroupType")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("tinyint")
                        .HasDefaultValue((sbyte)0);

                    b.Property<string>("Group")
                        .HasColumnType("varchar(64)");

                    b.Property<string>("GroupName")
                        .HasColumnType("varchar(20)");

                    b.Property<sbyte?>("GroupSort")
                        .HasColumnType("tinyint");

                    b.Property<bool?>("IsMultiple")
                        .HasColumnType("tinyint(1)");

                    b.Property<DateTime?>("MaxDate")
                        .HasColumnType("datetime(6)");

                    b.Property<decimal?>("Maxlength")
                        .HasColumnType("decimal(18,6)");

                    b.Property<DateTime?>("MinDate")
                        .HasColumnType("datetime(6)");

                    b.Property<decimal?>("MinLength")
                        .HasColumnType("decimal(18,6)");

                    b.Property<string>("Pattern")
                        .HasColumnType("varchar(500)");

                    b.Property<string>("Placeholder")
                        .HasColumnType("varchar(100)");

                    b.Property<sbyte?>("Precision")
                        .HasColumnType("tinyint");

                    b.Property<string>("Remark")
                        .HasColumnType("varchar(100)");

                    b.Property<string>("Unit")
                        .HasColumnType("varchar(20)");

                    b.Property<string>("ValueRange")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("FieldCode")
                        .IsUnique();

                    b.ToTable("Fields", (string)null);

                    b.HasData(
                        new
                        {
                            Id = 1281498705313329020L,
                            FieldCode = "Name",
                            FieldEnName = "Name",
                            FieldName = "姓名",
                            FieldType = (sbyte)0,
                            FieldsGroupType = (sbyte)0,
                            Maxlength = 30m,
                            Placeholder = "请输入"
                        },
                        new
                        {
                            Id = 1281498705313329021L,
                            FieldCode = "FirstName",
                            FieldEnName = "First Name",
                            FieldName = "英文名",
                            FieldType = (sbyte)0,
                            FieldsGroupType = (sbyte)0,
                            Maxlength = 30m,
                            Placeholder = "请输入"
                        },
                        new
                        {
                            Id = 1281498705313329022L,
                            FieldCode = "LastName",
                            FieldEnName = "Last Name",
                            FieldName = "英文姓",
                            FieldType = (sbyte)0,
                            FieldsGroupType = (sbyte)0,
                            Maxlength = 30m,
                            Placeholder = "请输入"
                        },
                        new
                        {
                            Id = 1281498705313329023L,
                            DefaultValue = "1",
                            FieldCode = "Gender",
                            FieldName = "性别",
                            FieldType = (sbyte)1,
                            FieldsGroupType = (sbyte)0,
                            Placeholder = "请输入",
                            ValueRange = "[{\"code\":\"0\",\"name\":\"女\"},{\"code\":\"1\",\"name\":\"男\"}]"
                        },
                        new
                        {
                            Id = 1281498705313329024L,
                            FieldCode = "LeftEyeVision",
                            FieldName = "左眼视力",
                            FieldType = (sbyte)3,
                            FieldsGroupType = (sbyte)0,
                            Maxlength = 5.3m,
                            MinLength = 0.1m,
                            Placeholder = "请输入",
                            Precision = (sbyte)2,
                            Remark = "兼容1.0和5分视力值"
                        },
                        new
                        {
                            Id = 1281498705313329025L,
                            FieldCode = "RightEyeVision",
                            FieldName = "右眼视力",
                            FieldType = (sbyte)3,
                            FieldsGroupType = (sbyte)0,
                            Maxlength = 5.3m,
                            MinLength = 0.1m,
                            Placeholder = "请输入",
                            Precision = (sbyte)2,
                            Remark = "兼容1.0和5分视力值"
                        },
                        new
                        {
                            Id = 1281498705313329026L,
                            FieldCode = "BirthPlace",
                            FieldName = "出生地",
                            FieldType = (sbyte)0,
                            FieldsGroupType = (sbyte)0,
                            Maxlength = 50m,
                            Placeholder = "请输入"
                        },
                        new
                        {
                            Id = 1281498705313329030L,
                            FieldCode = "Nationality",
                            FieldName = "国籍",
                            FieldType = (sbyte)2,
                            FieldsGroupType = (sbyte)0,
                            Placeholder = "请选择"
                        },
                        new
                        {
                            Id = 1281498705313329031L,
                            FieldCode = "BirthDate",
                            FieldName = "出生日期",
                            FieldType = (sbyte)5,
                            FieldsGroupType = (sbyte)0,
                            MinDate = new DateTime(1899, 12, 31, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            Placeholder = "请输入"
                        },
                        new
                        {
                            Id = 1281498705313329032L,
                            FieldCode = "Height",
                            FieldName = "身高",
                            FieldType = (sbyte)1,
                            FieldsGroupType = (sbyte)0,
                            Maxlength = 300m,
                            MinLength = 0m,
                            Placeholder = "请输入",
                            Unit = "cm"
                        },
                        new
                        {
                            Id = 1281498705313329033L,
                            FieldCode = "Weight",
                            FieldName = "体重",
                            FieldType = (sbyte)1,
                            FieldsGroupType = (sbyte)0,
                            Maxlength = 9999m,
                            MinLength = 0m,
                            Placeholder = "请输入",
                            Unit = "kg"
                        },
                        new
                        {
                            Id = 1281498705313329034L,
                            FieldCode = "ShoeSize",
                            FieldName = "鞋码",
                            FieldType = (sbyte)3,
                            FieldsGroupType = (sbyte)0,
                            Maxlength = 50m,
                            MinLength = 0m,
                            Placeholder = "请输入",
                            Precision = (sbyte)1,
                            Unit = "size"
                        },
                        new
                        {
                            Id = 1281498705313329035L,
                            FieldCode = "Ethnicity",
                            FieldName = "民族",
                            FieldType = (sbyte)0,
                            FieldsGroupType = (sbyte)0,
                            Maxlength = 30m,
                            Placeholder = "请输入"
                        },
                        new
                        {
                            Id = 1281498705313329036L,
                            DefaultValue = "1",
                            FieldCode = "CardType",
                            FieldName = "证件类型",
                            FieldType = (sbyte)1,
                            FieldsGroupType = (sbyte)1,
                            Group = "CardGroup",
                            GroupName = "证件号",
                            GroupSort = (sbyte)0,
                            ValueRange = "[{\"code\":\"0\",\"name\":\"身份证\"},{\"code\":\"1\",\"name\":\"护照\"},{\"code\":\"2\",\"name\":\"港澳通行证\"},{\"code\":\"3\",\"name\":\"台湾通行证\"},{\"code\":\"4\",\"name\":\"驾驶证\"},{\"code\":\"5\",\"name\":\"台胞证\"},{\"code\":\"6\",\"name\":\"回乡证\"},{\"code\":\"7\",\"name\":\"军官证\"},{\"code\":\"8\",\"name\":\"外国人永久居留证\"},{\"code\":\"9\",\"name\":\"学生证\"},{\"code\":\"10\",\"name\":\"警官证\"}]"
                        },
                        new
                        {
                            Id = 1281498705313329037L,
                            FieldCode = "CardNo",
                            FieldName = "证件号",
                            FieldType = (sbyte)0,
                            FieldsGroupType = (sbyte)1,
                            Group = "CardGroup",
                            GroupName = "证件号",
                            GroupSort = (sbyte)1,
                            Maxlength = 30m,
                            Placeholder = "请输入证件号"
                        },
                        new
                        {
                            Id = 1281498705313329038L,
                            FieldCode = "CardEndDate",
                            FieldName = "证件有效期",
                            FieldType = (sbyte)5,
                            FieldsGroupType = (sbyte)1,
                            Placeholder = "请选择"
                        },
                        new
                        {
                            Id = 1281498705313329039L,
                            FieldCode = "CardStartDate",
                            FieldName = "证件签发日期",
                            FieldType = (sbyte)5,
                            FieldsGroupType = (sbyte)1,
                            Placeholder = "请选择"
                        },
                        new
                        {
                            Id = 1281498705313329040L,
                            FieldCode = "CardCountry",
                            FieldName = "证件签发国",
                            FieldType = (sbyte)2,
                            FieldsGroupType = (sbyte)1,
                            Placeholder = "请选择"
                        },
                        new
                        {
                            Id = 1281498705313329041L,
                            FieldCode = "CardPlace",
                            FieldName = "证件签发地",
                            FieldType = (sbyte)0,
                            FieldsGroupType = (sbyte)1,
                            Maxlength = 30m,
                            Placeholder = "请输入"
                        },
                        new
                        {
                            Id = 1281498705313372342L,
                            FieldCode = "ContactName",
                            FieldName = "联系人",
                            FieldType = (sbyte)0,
                            FieldsGroupType = (sbyte)2,
                            Maxlength = 30m,
                            Placeholder = "请输入"
                        },
                        new
                        {
                            Id = 1281498705313372343L,
                            DefaultValue = "[+86]",
                            FieldCode = "ContactDialingCode",
                            FieldName = "联系手机区号",
                            FieldType = (sbyte)0,
                            FieldsGroupType = (sbyte)2,
                            Group = "ContactPhoneGroup",
                            GroupName = "联系手机号",
                            GroupSort = (sbyte)0,
                            Placeholder = "请选择",
                            ValueRange = "[{\"code\":\"[+86]\",\"name\":\"中国大陆 +86\"},{\"code\":\"[+852]\",\"name\":\"中国香港 +852\"},{\"code\":\"[+853]\",\"name\":\"中国澳门 +853\"},{\"code\":\"[+886]\",\"name\":\"中国台湾 +886\"},{\"code\":\"[+82]\",\"name\":\"韩国 +82\"},{\"code\":\"[+81]\",\"name\":\"日本 +81\"},{\"code\":\"[+1]\",\"name\":\"美国/加拿大 +1\"},{\"code\":\"[+44]\",\"name\":\"英国 +44\"},{\"code\":\"[+33]\",\"name\":\"法国 +33\"},{\"code\":\"[+49]\",\"name\":\"德国 +49\"},{\"code\":\"[+7]\",\"name\":\"俄罗斯 +7\"},{\"code\":\"[+65]\",\"name\":\"新加坡 +65\"},{\"code\":\"[+60]\",\"name\":\"马来西亚 +60\"},{\"code\":\"[+66]\",\"name\":\"泰国 +66\"},{\"code\":\"[+84]\",\"name\":\"越南 +84\"},{\"code\":\"[+63]\",\"name\":\"菲律宾 +63\"},{\"code\":\"[+62]\",\"name\":\"印度尼西亚 +62\"},{\"code\":\"[+91]\",\"name\":\"印度 +91\"},{\"code\":\"[+95]\",\"name\":\"缅甸 +95\"},{\"code\":\"[+856]\",\"name\":\"老挝 +856\"},{\"code\":\"[+855]\",\"name\":\"柬埔寨 +855\"},{\"code\":\"[+673]\",\"name\":\"文莱 +673\"},{\"code\":\"[+960]\",\"name\":\"马尔代夫 +960\"},{\"code\":\"[+1808]\",\"name\":\"夏威夷 +1808\"},{\"code\":\"[+61]\",\"name\":\"澳大利亚 +61\"},{\"code\":\"[+64]\",\"name\":\"新西兰 +64\"},{\"code\":\"[+30]\",\"name\":\"希腊 +30\"},{\"code\":\"[+39]\",\"name\":\"意大利 +39\"},{\"code\":\"[+977]\",\"name\":\"尼泊尔 +977\"},{\"code\":\"[+850]\",\"name\":\"朝鲜 +850\"},{\"code\":\"[+880]\",\"name\":\"孟加拉国 +880\"},{\"code\":\"[+90]\",\"name\":\"土耳其 +90\"},{\"code\":\"[+966]\",\"name\":\"沙特阿拉伯 +966\"},{\"code\":\"[+30]\",\"name\":\"希腊 +30\"},{\"code\":\"[+31]\",\"name\":\"荷兰 +31\"},{\"code\":\"[+32]\",\"name\":\"比利时 +32\"},{\"code\":\"[+34]\",\"name\":\"西班牙 +34\"},{\"code\":\"[+351]\",\"name\":\"葡萄牙 +351\"},{\"code\":\"[+352]\",\"name\":\"卢森堡 +352\"},{\"code\":\"[+353]\",\"name\":\"爱尔兰 +353\"},{\"code\":\"[+354]\",\"name\":\"冰岛 +354\"},{\"code\":\"[+358]\",\"name\":\"芬兰 +358\"},{\"code\":\"[+36]\",\"name\":\"匈牙利 +36\"},{\"code\":\"[+396]\",\"name\":\"梵蒂冈 +396\"},{\"code\":\"[+41]\",\"name\":\"瑞士 +41\"},{\"code\":\"[+43]\",\"name\":\"奥地利 +43\"},{\"code\":\"[+45]\",\"name\":\"丹麦 +45\"},{\"code\":\"[+46]\",\"name\":\"瑞典 +46\"},{\"code\":\"[+47]\",\"name\":\"挪威 +47\"}]"
                        },
                        new
                        {
                            Id = 1281498705313372344L,
                            FieldCode = "ContactPhone",
                            FieldName = "联系手机号",
                            FieldType = (sbyte)0,
                            FieldsGroupType = (sbyte)2,
                            Group = "ContactPhoneGroup",
                            GroupName = "联系手机号",
                            GroupSort = (sbyte)1,
                            Maxlength = 16m,
                            Pattern = "^(13[0-9]|15[012356789]|166|17[3678]|18[0-9]|14[57])[0-9]{8}$",
                            Placeholder = "请输入"
                        },
                        new
                        {
                            Id = 1281498705313372345L,
                            DefaultValue = "[+86]",
                            FieldCode = "LocalDialingCode",
                            FieldName = "当地手机区号",
                            FieldType = (sbyte)0,
                            FieldsGroupType = (sbyte)2,
                            Group = "LocalPhoneGroup",
                            GroupName = "当地手机号",
                            GroupSort = (sbyte)0,
                            Placeholder = "请选择",
                            ValueRange = "[{\"code\":\"[+86]\",\"name\":\"中国大陆 +86\"},{\"code\":\"[+852]\",\"name\":\"中国香港 +852\"},{\"code\":\"[+853]\",\"name\":\"中国澳门 +853\"},{\"code\":\"[+886]\",\"name\":\"中国台湾 +886\"},{\"code\":\"[+82]\",\"name\":\"韩国 +82\"},{\"code\":\"[+81]\",\"name\":\"日本 +81\"},{\"code\":\"[+1]\",\"name\":\"美国/加拿大 +1\"},{\"code\":\"[+44]\",\"name\":\"英国 +44\"},{\"code\":\"[+33]\",\"name\":\"法国 +33\"},{\"code\":\"[+49]\",\"name\":\"德国 +49\"},{\"code\":\"[+7]\",\"name\":\"俄罗斯 +7\"},{\"code\":\"[+65]\",\"name\":\"新加坡 +65\"},{\"code\":\"[+60]\",\"name\":\"马来西亚 +60\"},{\"code\":\"[+66]\",\"name\":\"泰国 +66\"},{\"code\":\"[+84]\",\"name\":\"越南 +84\"},{\"code\":\"[+63]\",\"name\":\"菲律宾 +63\"},{\"code\":\"[+62]\",\"name\":\"印度尼西亚 +62\"},{\"code\":\"[+91]\",\"name\":\"印度 +91\"},{\"code\":\"[+95]\",\"name\":\"缅甸 +95\"},{\"code\":\"[+856]\",\"name\":\"老挝 +856\"},{\"code\":\"[+855]\",\"name\":\"柬埔寨 +855\"},{\"code\":\"[+673]\",\"name\":\"文莱 +673\"},{\"code\":\"[+960]\",\"name\":\"马尔代夫 +960\"},{\"code\":\"[+1808]\",\"name\":\"夏威夷 +1808\"},{\"code\":\"[+61]\",\"name\":\"澳大利亚 +61\"},{\"code\":\"[+64]\",\"name\":\"新西兰 +64\"},{\"code\":\"[+30]\",\"name\":\"希腊 +30\"},{\"code\":\"[+39]\",\"name\":\"意大利 +39\"},{\"code\":\"[+977]\",\"name\":\"尼泊尔 +977\"},{\"code\":\"[+850]\",\"name\":\"朝鲜 +850\"},{\"code\":\"[+880]\",\"name\":\"孟加拉国 +880\"},{\"code\":\"[+90]\",\"name\":\"土耳其 +90\"},{\"code\":\"[+966]\",\"name\":\"沙特阿拉伯 +966\"},{\"code\":\"[+30]\",\"name\":\"希腊 +30\"},{\"code\":\"[+31]\",\"name\":\"荷兰 +31\"},{\"code\":\"[+32]\",\"name\":\"比利时 +32\"},{\"code\":\"[+34]\",\"name\":\"西班牙 +34\"},{\"code\":\"[+351]\",\"name\":\"葡萄牙 +351\"},{\"code\":\"[+352]\",\"name\":\"卢森堡 +352\"},{\"code\":\"[+353]\",\"name\":\"爱尔兰 +353\"},{\"code\":\"[+354]\",\"name\":\"冰岛 +354\"},{\"code\":\"[+358]\",\"name\":\"芬兰 +358\"},{\"code\":\"[+36]\",\"name\":\"匈牙利 +36\"},{\"code\":\"[+396]\",\"name\":\"梵蒂冈 +396\"},{\"code\":\"[+41]\",\"name\":\"瑞士 +41\"},{\"code\":\"[+43]\",\"name\":\"奥地利 +43\"},{\"code\":\"[+45]\",\"name\":\"丹麦 +45\"},{\"code\":\"[+46]\",\"name\":\"瑞典 +46\"},{\"code\":\"[+47]\",\"name\":\"挪威 +47\"}]"
                        },
                        new
                        {
                            Id = 1281498705313372346L,
                            FieldCode = "LocalPhone",
                            FieldName = "当地手机号",
                            FieldType = (sbyte)0,
                            FieldsGroupType = (sbyte)2,
                            Group = "LocalPhoneGroup",
                            GroupName = "当地手机号",
                            GroupSort = (sbyte)1,
                            Maxlength = 16m,
                            Placeholder = "请输入"
                        },
                        new
                        {
                            Id = 1281498705313372347L,
                            FieldCode = "ContactEmail",
                            FieldName = "邮箱",
                            FieldType = (sbyte)0,
                            FieldsGroupType = (sbyte)2,
                            Maxlength = 50m,
                            Pattern = "\\w+([-+.]\\w+)*@\\w+([-.]\\w+)*\\.\\w+([-.]\\w+)*",
                            Placeholder = "请输入"
                        },
                        new
                        {
                            Id = 1281498705313372348L,
                            DefaultValue = "WeChat",
                            FieldCode = "ContactSocialMediaType",
                            FieldName = "社交媒体类型",
                            FieldType = (sbyte)0,
                            FieldsGroupType = (sbyte)2,
                            Group = "ContactSocialMediaGroup",
                            GroupName = "社交媒体",
                            GroupSort = (sbyte)0,
                            Placeholder = "请选择",
                            ValueRange = "[{\"code\":\"WeChat\",\"name\":\"微信\"},{\"code\":\"WhatsApp\",\"name\":\"WhatsApp\"},{\"code\":\"Line\",\"name\":\"Line\"},{\"code\":\"Telegram\",\"name\":\"Telegram\"},{\"code\":\"Skype\",\"name\":\"Skype\"},{\"code\":\"Facebook\",\"name\":\"Facebook\"},{\"code\":\"WangWang\",\"name\":\"旺旺\"},{\"code\":\"KakaoTalk\",\"name\":\"KakaoTalk\"}]"
                        },
                        new
                        {
                            Id = 1281498705313372349L,
                            FieldCode = "ContactSocialMedia",
                            FieldName = "社交媒体",
                            FieldType = (sbyte)0,
                            FieldsGroupType = (sbyte)2,
                            Group = "ContactSocialMediaGroup",
                            GroupName = "社交媒体",
                            GroupSort = (sbyte)1,
                            Maxlength = 30m,
                            Placeholder = "请输入"
                        },
                        new
                        {
                            Id = 1281498705313372350L,
                            FieldCode = "TourGuideName",
                            FieldName = "导游姓名",
                            FieldType = (sbyte)0,
                            FieldsGroupType = (sbyte)2,
                            Maxlength = 30m,
                            Placeholder = "请输入"
                        },
                        new
                        {
                            Id = 1281498705313372351L,
                            DefaultValue = "[+86]",
                            FieldCode = "TourGuideDialingCode",
                            FieldName = "导游电话区号",
                            FieldType = (sbyte)0,
                            FieldsGroupType = (sbyte)2,
                            Group = "TourGuidePhoneGroup",
                            GroupName = "导游电话",
                            GroupSort = (sbyte)0,
                            Placeholder = "请选择",
                            ValueRange = "[{\"code\":\"[+86]\",\"name\":\"中国大陆 +86\"},{\"code\":\"[+852]\",\"name\":\"中国香港 +852\"},{\"code\":\"[+853]\",\"name\":\"中国澳门 +853\"},{\"code\":\"[+886]\",\"name\":\"中国台湾 +886\"},{\"code\":\"[+82]\",\"name\":\"韩国 +82\"},{\"code\":\"[+81]\",\"name\":\"日本 +81\"},{\"code\":\"[+1]\",\"name\":\"美国/加拿大 +1\"},{\"code\":\"[+44]\",\"name\":\"英国 +44\"},{\"code\":\"[+33]\",\"name\":\"法国 +33\"},{\"code\":\"[+49]\",\"name\":\"德国 +49\"},{\"code\":\"[+7]\",\"name\":\"俄罗斯 +7\"},{\"code\":\"[+65]\",\"name\":\"新加坡 +65\"},{\"code\":\"[+60]\",\"name\":\"马来西亚 +60\"},{\"code\":\"[+66]\",\"name\":\"泰国 +66\"},{\"code\":\"[+84]\",\"name\":\"越南 +84\"},{\"code\":\"[+63]\",\"name\":\"菲律宾 +63\"},{\"code\":\"[+62]\",\"name\":\"印度尼西亚 +62\"},{\"code\":\"[+91]\",\"name\":\"印度 +91\"},{\"code\":\"[+95]\",\"name\":\"缅甸 +95\"},{\"code\":\"[+856]\",\"name\":\"老挝 +856\"},{\"code\":\"[+855]\",\"name\":\"柬埔寨 +855\"},{\"code\":\"[+673]\",\"name\":\"文莱 +673\"},{\"code\":\"[+960]\",\"name\":\"马尔代夫 +960\"},{\"code\":\"[+1808]\",\"name\":\"夏威夷 +1808\"},{\"code\":\"[+61]\",\"name\":\"澳大利亚 +61\"},{\"code\":\"[+64]\",\"name\":\"新西兰 +64\"},{\"code\":\"[+30]\",\"name\":\"希腊 +30\"},{\"code\":\"[+39]\",\"name\":\"意大利 +39\"},{\"code\":\"[+977]\",\"name\":\"尼泊尔 +977\"},{\"code\":\"[+850]\",\"name\":\"朝鲜 +850\"},{\"code\":\"[+880]\",\"name\":\"孟加拉国 +880\"},{\"code\":\"[+90]\",\"name\":\"土耳其 +90\"},{\"code\":\"[+966]\",\"name\":\"沙特阿拉伯 +966\"},{\"code\":\"[+30]\",\"name\":\"希腊 +30\"},{\"code\":\"[+31]\",\"name\":\"荷兰 +31\"},{\"code\":\"[+32]\",\"name\":\"比利时 +32\"},{\"code\":\"[+34]\",\"name\":\"西班牙 +34\"},{\"code\":\"[+351]\",\"name\":\"葡萄牙 +351\"},{\"code\":\"[+352]\",\"name\":\"卢森堡 +352\"},{\"code\":\"[+353]\",\"name\":\"爱尔兰 +353\"},{\"code\":\"[+354]\",\"name\":\"冰岛 +354\"},{\"code\":\"[+358]\",\"name\":\"芬兰 +358\"},{\"code\":\"[+36]\",\"name\":\"匈牙利 +36\"},{\"code\":\"[+396]\",\"name\":\"梵蒂冈 +396\"},{\"code\":\"[+41]\",\"name\":\"瑞士 +41\"},{\"code\":\"[+43]\",\"name\":\"奥地利 +43\"},{\"code\":\"[+45]\",\"name\":\"丹麦 +45\"},{\"code\":\"[+46]\",\"name\":\"瑞典 +46\"},{\"code\":\"[+47]\",\"name\":\"挪威 +47\"}]"
                        },
                        new
                        {
                            Id = 1281498705313372352L,
                            FieldCode = "TourGuidePhone",
                            FieldName = "导游电话",
                            FieldType = (sbyte)0,
                            FieldsGroupType = (sbyte)2,
                            Group = "TourGuidePhoneGroup",
                            GroupName = "导游电话",
                            GroupSort = (sbyte)1,
                            Maxlength = 16m,
                            Pattern = "^(13[0-9]|15[012356789]|166|17[3678]|18[0-9]|14[57])[0-9]{8}$",
                            Placeholder = "请输入"
                        },
                        new
                        {
                            Id = 1281498705313372353L,
                            FieldCode = "TourGuideEmail",
                            FieldName = "导游邮箱",
                            FieldType = (sbyte)0,
                            FieldsGroupType = (sbyte)2,
                            Maxlength = 50m,
                            Pattern = "\\w+([-+.]\\w+)*@\\w+([-.]\\w+)*\\.\\w+([-.]\\w+)*",
                            Placeholder = "请输入"
                        },
                        new
                        {
                            Id = 1281498705313372354L,
                            DefaultValue = "WeChat",
                            FieldCode = "TourGuideSocialMediaType",
                            FieldName = "导游社交媒体类型",
                            FieldType = (sbyte)0,
                            FieldsGroupType = (sbyte)2,
                            Group = "TourGuideSocialMediaGroup",
                            GroupName = "导游社交媒体",
                            GroupSort = (sbyte)0,
                            Placeholder = "请选择",
                            ValueRange = "[{\"code\":\"WeChat\",\"name\":\"微信\"},{\"code\":\"WhatsApp\",\"name\":\"WhatsApp\"},{\"code\":\"Line\",\"name\":\"Line\"},{\"code\":\"Telegram\",\"name\":\"Telegram\"},{\"code\":\"Skype\",\"name\":\"Skype\"},{\"code\":\"Facebook\",\"name\":\"Facebook\"},{\"code\":\"WangWang\",\"name\":\"旺旺\"},{\"code\":\"KakaoTalk\",\"name\":\"KakaoTalk\"}]"
                        },
                        new
                        {
                            Id = 1281498705313372355L,
                            FieldCode = "TourGuideSocialMedia",
                            FieldName = "导游社交媒体",
                            FieldType = (sbyte)0,
                            FieldsGroupType = (sbyte)2,
                            Group = "TourGuideSocialMediaGroup",
                            GroupName = "导游社交媒体",
                            GroupSort = (sbyte)1,
                            Maxlength = 30m,
                            Placeholder = "请输入"
                        },
                        new
                        {
                            Id = 1281498705313372356L,
                            FieldCode = "DriverName",
                            FieldName = "司机姓名",
                            FieldType = (sbyte)0,
                            FieldsGroupType = (sbyte)2,
                            Maxlength = 30m,
                            Placeholder = "请输入"
                        },
                        new
                        {
                            Id = 1281498705313372357L,
                            DefaultValue = "[+86]",
                            FieldCode = "DriverDialingCode",
                            FieldName = "司机电话区号",
                            FieldType = (sbyte)0,
                            FieldsGroupType = (sbyte)2,
                            Group = "DriverPhoneGroup",
                            GroupName = "司机电话",
                            GroupSort = (sbyte)0,
                            Placeholder = "请选择",
                            ValueRange = "[{\"code\":\"[+86]\",\"name\":\"中国大陆 +86\"},{\"code\":\"[+852]\",\"name\":\"中国香港 +852\"},{\"code\":\"[+853]\",\"name\":\"中国澳门 +853\"},{\"code\":\"[+886]\",\"name\":\"中国台湾 +886\"},{\"code\":\"[+82]\",\"name\":\"韩国 +82\"},{\"code\":\"[+81]\",\"name\":\"日本 +81\"},{\"code\":\"[+1]\",\"name\":\"美国/加拿大 +1\"},{\"code\":\"[+44]\",\"name\":\"英国 +44\"},{\"code\":\"[+33]\",\"name\":\"法国 +33\"},{\"code\":\"[+49]\",\"name\":\"德国 +49\"},{\"code\":\"[+7]\",\"name\":\"俄罗斯 +7\"},{\"code\":\"[+65]\",\"name\":\"新加坡 +65\"},{\"code\":\"[+60]\",\"name\":\"马来西亚 +60\"},{\"code\":\"[+66]\",\"name\":\"泰国 +66\"},{\"code\":\"[+84]\",\"name\":\"越南 +84\"},{\"code\":\"[+63]\",\"name\":\"菲律宾 +63\"},{\"code\":\"[+62]\",\"name\":\"印度尼西亚 +62\"},{\"code\":\"[+91]\",\"name\":\"印度 +91\"},{\"code\":\"[+95]\",\"name\":\"缅甸 +95\"},{\"code\":\"[+856]\",\"name\":\"老挝 +856\"},{\"code\":\"[+855]\",\"name\":\"柬埔寨 +855\"},{\"code\":\"[+673]\",\"name\":\"文莱 +673\"},{\"code\":\"[+960]\",\"name\":\"马尔代夫 +960\"},{\"code\":\"[+1808]\",\"name\":\"夏威夷 +1808\"},{\"code\":\"[+61]\",\"name\":\"澳大利亚 +61\"},{\"code\":\"[+64]\",\"name\":\"新西兰 +64\"},{\"code\":\"[+30]\",\"name\":\"希腊 +30\"},{\"code\":\"[+39]\",\"name\":\"意大利 +39\"},{\"code\":\"[+977]\",\"name\":\"尼泊尔 +977\"},{\"code\":\"[+850]\",\"name\":\"朝鲜 +850\"},{\"code\":\"[+880]\",\"name\":\"孟加拉国 +880\"},{\"code\":\"[+90]\",\"name\":\"土耳其 +90\"},{\"code\":\"[+966]\",\"name\":\"沙特阿拉伯 +966\"},{\"code\":\"[+30]\",\"name\":\"希腊 +30\"},{\"code\":\"[+31]\",\"name\":\"荷兰 +31\"},{\"code\":\"[+32]\",\"name\":\"比利时 +32\"},{\"code\":\"[+34]\",\"name\":\"西班牙 +34\"},{\"code\":\"[+351]\",\"name\":\"葡萄牙 +351\"},{\"code\":\"[+352]\",\"name\":\"卢森堡 +352\"},{\"code\":\"[+353]\",\"name\":\"爱尔兰 +353\"},{\"code\":\"[+354]\",\"name\":\"冰岛 +354\"},{\"code\":\"[+358]\",\"name\":\"芬兰 +358\"},{\"code\":\"[+36]\",\"name\":\"匈牙利 +36\"},{\"code\":\"[+396]\",\"name\":\"梵蒂冈 +396\"},{\"code\":\"[+41]\",\"name\":\"瑞士 +41\"},{\"code\":\"[+43]\",\"name\":\"奥地利 +43\"},{\"code\":\"[+45]\",\"name\":\"丹麦 +45\"},{\"code\":\"[+46]\",\"name\":\"瑞典 +46\"},{\"code\":\"[+47]\",\"name\":\"挪威 +47\"}]"
                        },
                        new
                        {
                            Id = 1281498705313372358L,
                            FieldCode = "DriverPhone",
                            FieldName = "司机电话",
                            FieldType = (sbyte)0,
                            FieldsGroupType = (sbyte)2,
                            Group = "DriverPhoneGroup",
                            GroupName = "司机电话",
                            GroupSort = (sbyte)1,
                            Maxlength = 16m,
                            Pattern = "^(13[0-9]|15[012356789]|166|17[3678]|18[0-9]|14[57])[0-9]{8}$",
                            Placeholder = "请输入"
                        },
                        new
                        {
                            Id = 1281498705313372359L,
                            FieldCode = "DriverEmail",
                            FieldName = "司机邮箱",
                            FieldType = (sbyte)0,
                            FieldsGroupType = (sbyte)2,
                            Maxlength = 50m,
                            Pattern = "\\w+([-+.]\\w+)*@\\w+([-.]\\w+)*\\.\\w+([-.]\\w+)*",
                            Placeholder = "请输入"
                        },
                        new
                        {
                            Id = 1281498705313372360L,
                            DefaultValue = "WeChat",
                            FieldCode = "DriverSocialMediaType",
                            FieldName = "司机社交媒体类型",
                            FieldType = (sbyte)0,
                            FieldsGroupType = (sbyte)2,
                            Group = "DriverSocialMediaGroup",
                            GroupName = "司机社交媒体",
                            GroupSort = (sbyte)0,
                            Placeholder = "请选择",
                            ValueRange = "[{\"code\":\"WeChat\",\"name\":\"微信\"},{\"code\":\"WhatsApp\",\"name\":\"WhatsApp\"},{\"code\":\"Line\",\"name\":\"Line\"},{\"code\":\"Telegram\",\"name\":\"Telegram\"},{\"code\":\"Skype\",\"name\":\"Skype\"},{\"code\":\"Facebook\",\"name\":\"Facebook\"},{\"code\":\"WangWang\",\"name\":\"旺旺\"},{\"code\":\"KakaoTalk\",\"name\":\"KakaoTalk\"}]"
                        },
                        new
                        {
                            Id = 1281498705313372361L,
                            FieldCode = "DriverSocialMedia",
                            FieldName = "司机社交媒体",
                            FieldType = (sbyte)0,
                            FieldsGroupType = (sbyte)2,
                            Group = "DriverSocialMediaGroup",
                            GroupName = "司机社交媒体",
                            GroupSort = (sbyte)1,
                            Maxlength = 30m,
                            Placeholder = "请输入"
                        },
                        new
                        {
                            Id = 1281498705313329062L,
                            DefaultValue = "1",
                            FieldCode = "Passengers",
                            FieldName = "乘客数",
                            FieldType = (sbyte)1,
                            FieldsGroupType = (sbyte)3,
                            MinLength = 1m,
                            Placeholder = "请输入"
                        },
                        new
                        {
                            Id = 1281498705313329063L,
                            DefaultValue = "0",
                            FieldCode = "Baggages",
                            FieldName = "行李数",
                            FieldType = (sbyte)1,
                            FieldsGroupType = (sbyte)3,
                            MinLength = 0m,
                            Placeholder = "请输入"
                        },
                        new
                        {
                            Id = 1281498705313329064L,
                            FieldCode = "DepartureCountryCode",
                            FieldName = "出发国家编码",
                            FieldType = (sbyte)2,
                            FieldsGroupType = (sbyte)3,
                            Group = "DepartureGroup",
                            GroupName = "出发地地址",
                            GroupSort = (sbyte)0
                        },
                        new
                        {
                            Id = 1281498705313329065L,
                            FieldCode = "DepartureCountryName",
                            FieldName = "出发国家名称",
                            FieldType = (sbyte)0,
                            FieldsGroupType = (sbyte)3,
                            Group = "DepartureGroup",
                            GroupName = "出发地地址",
                            GroupSort = (sbyte)1
                        },
                        new
                        {
                            Id = 1281498705313329066L,
                            FieldCode = "DepartureCityCode",
                            FieldName = "出发城市编码",
                            FieldType = (sbyte)2,
                            FieldsGroupType = (sbyte)3,
                            Group = "DepartureGroup",
                            GroupName = "出发地地址",
                            GroupSort = (sbyte)1,
                            Maxlength = 30m
                        },
                        new
                        {
                            Id = 1281498705313329067L,
                            FieldCode = "DepartureCityName",
                            FieldName = "出发城市名称",
                            FieldType = (sbyte)0,
                            FieldsGroupType = (sbyte)3,
                            Group = "DepartureGroup",
                            GroupName = "出发地地址",
                            GroupSort = (sbyte)1,
                            Maxlength = 30m
                        },
                        new
                        {
                            Id = 1281498705313329068L,
                            FieldCode = "DepartureAirportId",
                            FieldName = "出发机场id",
                            FieldType = (sbyte)2,
                            FieldsGroupType = (sbyte)3,
                            Group = "DepartureGroup",
                            GroupName = "出发地地址",
                            GroupSort = (sbyte)1,
                            Maxlength = 30m
                        },
                        new
                        {
                            Id = 1281498705313329069L,
                            FieldCode = "DepartureAddress",
                            FieldName = "出发详细地址",
                            FieldType = (sbyte)0,
                            FieldsGroupType = (sbyte)3,
                            Group = "DepartureGroup",
                            GroupName = "出发地地址",
                            GroupSort = (sbyte)1,
                            Maxlength = 200m
                        },
                        new
                        {
                            Id = 1281498705313329070L,
                            DefaultValue = "[+86]",
                            FieldCode = "DepartureHotelDialingCode",
                            FieldName = "出发酒店电话区号",
                            FieldType = (sbyte)0,
                            FieldsGroupType = (sbyte)3,
                            Group = "DepartureHotelPhoneGroup",
                            GroupName = "出发酒店电话",
                            GroupSort = (sbyte)0,
                            Placeholder = "请选择",
                            ValueRange = "[{\"code\":\"[+86]\",\"name\":\"中国大陆 +86\"},{\"code\":\"[+852]\",\"name\":\"中国香港 +852\"},{\"code\":\"[+853]\",\"name\":\"中国澳门 +853\"},{\"code\":\"[+886]\",\"name\":\"中国台湾 +886\"},{\"code\":\"[+82]\",\"name\":\"韩国 +82\"},{\"code\":\"[+81]\",\"name\":\"日本 +81\"},{\"code\":\"[+1]\",\"name\":\"美国/加拿大 +1\"},{\"code\":\"[+44]\",\"name\":\"英国 +44\"},{\"code\":\"[+33]\",\"name\":\"法国 +33\"},{\"code\":\"[+49]\",\"name\":\"德国 +49\"},{\"code\":\"[+7]\",\"name\":\"俄罗斯 +7\"},{\"code\":\"[+65]\",\"name\":\"新加坡 +65\"},{\"code\":\"[+60]\",\"name\":\"马来西亚 +60\"},{\"code\":\"[+66]\",\"name\":\"泰国 +66\"},{\"code\":\"[+84]\",\"name\":\"越南 +84\"},{\"code\":\"[+63]\",\"name\":\"菲律宾 +63\"},{\"code\":\"[+62]\",\"name\":\"印度尼西亚 +62\"},{\"code\":\"[+91]\",\"name\":\"印度 +91\"},{\"code\":\"[+95]\",\"name\":\"缅甸 +95\"},{\"code\":\"[+856]\",\"name\":\"老挝 +856\"},{\"code\":\"[+855]\",\"name\":\"柬埔寨 +855\"},{\"code\":\"[+673]\",\"name\":\"文莱 +673\"},{\"code\":\"[+960]\",\"name\":\"马尔代夫 +960\"},{\"code\":\"[+1808]\",\"name\":\"夏威夷 +1808\"},{\"code\":\"[+61]\",\"name\":\"澳大利亚 +61\"},{\"code\":\"[+64]\",\"name\":\"新西兰 +64\"},{\"code\":\"[+30]\",\"name\":\"希腊 +30\"},{\"code\":\"[+39]\",\"name\":\"意大利 +39\"},{\"code\":\"[+977]\",\"name\":\"尼泊尔 +977\"},{\"code\":\"[+850]\",\"name\":\"朝鲜 +850\"},{\"code\":\"[+880]\",\"name\":\"孟加拉国 +880\"},{\"code\":\"[+90]\",\"name\":\"土耳其 +90\"},{\"code\":\"[+966]\",\"name\":\"沙特阿拉伯 +966\"},{\"code\":\"[+30]\",\"name\":\"希腊 +30\"},{\"code\":\"[+31]\",\"name\":\"荷兰 +31\"},{\"code\":\"[+32]\",\"name\":\"比利时 +32\"},{\"code\":\"[+34]\",\"name\":\"西班牙 +34\"},{\"code\":\"[+351]\",\"name\":\"葡萄牙 +351\"},{\"code\":\"[+352]\",\"name\":\"卢森堡 +352\"},{\"code\":\"[+353]\",\"name\":\"爱尔兰 +353\"},{\"code\":\"[+354]\",\"name\":\"冰岛 +354\"},{\"code\":\"[+358]\",\"name\":\"芬兰 +358\"},{\"code\":\"[+36]\",\"name\":\"匈牙利 +36\"},{\"code\":\"[+396]\",\"name\":\"梵蒂冈 +396\"},{\"code\":\"[+41]\",\"name\":\"瑞士 +41\"},{\"code\":\"[+43]\",\"name\":\"奥地利 +43\"},{\"code\":\"[+45]\",\"name\":\"丹麦 +45\"},{\"code\":\"[+46]\",\"name\":\"瑞典 +46\"},{\"code\":\"[+47]\",\"name\":\"挪威 +47\"}]"
                        },
                        new
                        {
                            Id = 1281498705313329071L,
                            FieldCode = "DepartureHotelPhone",
                            FieldName = "出发酒店电话",
                            FieldType = (sbyte)0,
                            FieldsGroupType = (sbyte)3,
                            Group = "DepartureHotelPhoneGroup",
                            GroupName = "出发酒店电话",
                            GroupSort = (sbyte)1,
                            Maxlength = 16m,
                            Pattern = "^(13[0-9]|15[012356789]|166|17[3678]|18[0-9]|14[57])[0-9]{8}$",
                            Placeholder = "请输入"
                        },
                        new
                        {
                            Id = 1281498705313329082L,
                            FieldCode = "DepartureHotelRoomNO",
                            FieldName = "出发酒店房号",
                            FieldType = (sbyte)0,
                            FieldsGroupType = (sbyte)3,
                            Maxlength = 30m,
                            Placeholder = "请输入"
                        },
                        new
                        {
                            Id = 1281498705313388072L,
                            FieldCode = "DestinationCountryCode",
                            FieldName = "目的国家编码",
                            FieldType = (sbyte)2,
                            FieldsGroupType = (sbyte)3,
                            Group = "DestinationGroup",
                            GroupName = "目的地地址",
                            GroupSort = (sbyte)0
                        },
                        new
                        {
                            Id = 1281498705313388073L,
                            FieldCode = "DestinationCountryName",
                            FieldName = "目的国家名称",
                            FieldType = (sbyte)0,
                            FieldsGroupType = (sbyte)3,
                            Group = "DestinationGroup",
                            GroupName = "目的地地址",
                            GroupSort = (sbyte)1
                        },
                        new
                        {
                            Id = 1281498705313388074L,
                            FieldCode = "DestinationCityCode",
                            FieldName = "目的城市编码",
                            FieldType = (sbyte)2,
                            FieldsGroupType = (sbyte)3,
                            Group = "DestinationGroup",
                            GroupName = "目的地地址",
                            GroupSort = (sbyte)1,
                            Maxlength = 30m
                        },
                        new
                        {
                            Id = 1281498705313388075L,
                            FieldCode = "DestinationCityName",
                            FieldName = "目的城市名称",
                            FieldType = (sbyte)0,
                            FieldsGroupType = (sbyte)3,
                            Group = "DestinationGroup",
                            GroupName = "目的地地址",
                            GroupSort = (sbyte)1,
                            Maxlength = 30m
                        },
                        new
                        {
                            Id = 1281498705313388076L,
                            FieldCode = "DestinationAirportId",
                            FieldName = "目的机场id",
                            FieldType = (sbyte)2,
                            FieldsGroupType = (sbyte)3,
                            Group = "DestinationGroup",
                            GroupName = "目的地地址",
                            GroupSort = (sbyte)1,
                            Maxlength = 30m
                        },
                        new
                        {
                            Id = 1281498705313388077L,
                            FieldCode = "DestinationAddress",
                            FieldName = "目的详细地址",
                            FieldType = (sbyte)0,
                            FieldsGroupType = (sbyte)3,
                            Group = "DestinationGroup",
                            GroupName = "目的地地址",
                            GroupSort = (sbyte)1,
                            Maxlength = 200m
                        },
                        new
                        {
                            Id = 1281498705313388078L,
                            DefaultValue = "[+86]",
                            FieldCode = "DestinationHotelDialingCode",
                            FieldName = "目的酒店电话区号",
                            FieldType = (sbyte)0,
                            FieldsGroupType = (sbyte)3,
                            Group = "DestinationHotelPhoneGroup",
                            GroupName = "目的酒店电话",
                            GroupSort = (sbyte)0,
                            Placeholder = "请选择",
                            ValueRange = "[{\"code\":\"[+86]\",\"name\":\"中国大陆 +86\"},{\"code\":\"[+852]\",\"name\":\"中国香港 +852\"},{\"code\":\"[+853]\",\"name\":\"中国澳门 +853\"},{\"code\":\"[+886]\",\"name\":\"中国台湾 +886\"},{\"code\":\"[+82]\",\"name\":\"韩国 +82\"},{\"code\":\"[+81]\",\"name\":\"日本 +81\"},{\"code\":\"[+1]\",\"name\":\"美国/加拿大 +1\"},{\"code\":\"[+44]\",\"name\":\"英国 +44\"},{\"code\":\"[+33]\",\"name\":\"法国 +33\"},{\"code\":\"[+49]\",\"name\":\"德国 +49\"},{\"code\":\"[+7]\",\"name\":\"俄罗斯 +7\"},{\"code\":\"[+65]\",\"name\":\"新加坡 +65\"},{\"code\":\"[+60]\",\"name\":\"马来西亚 +60\"},{\"code\":\"[+66]\",\"name\":\"泰国 +66\"},{\"code\":\"[+84]\",\"name\":\"越南 +84\"},{\"code\":\"[+63]\",\"name\":\"菲律宾 +63\"},{\"code\":\"[+62]\",\"name\":\"印度尼西亚 +62\"},{\"code\":\"[+91]\",\"name\":\"印度 +91\"},{\"code\":\"[+95]\",\"name\":\"缅甸 +95\"},{\"code\":\"[+856]\",\"name\":\"老挝 +856\"},{\"code\":\"[+855]\",\"name\":\"柬埔寨 +855\"},{\"code\":\"[+673]\",\"name\":\"文莱 +673\"},{\"code\":\"[+960]\",\"name\":\"马尔代夫 +960\"},{\"code\":\"[+1808]\",\"name\":\"夏威夷 +1808\"},{\"code\":\"[+61]\",\"name\":\"澳大利亚 +61\"},{\"code\":\"[+64]\",\"name\":\"新西兰 +64\"},{\"code\":\"[+30]\",\"name\":\"希腊 +30\"},{\"code\":\"[+39]\",\"name\":\"意大利 +39\"},{\"code\":\"[+977]\",\"name\":\"尼泊尔 +977\"},{\"code\":\"[+850]\",\"name\":\"朝鲜 +850\"},{\"code\":\"[+880]\",\"name\":\"孟加拉国 +880\"},{\"code\":\"[+90]\",\"name\":\"土耳其 +90\"},{\"code\":\"[+966]\",\"name\":\"沙特阿拉伯 +966\"},{\"code\":\"[+30]\",\"name\":\"希腊 +30\"},{\"code\":\"[+31]\",\"name\":\"荷兰 +31\"},{\"code\":\"[+32]\",\"name\":\"比利时 +32\"},{\"code\":\"[+34]\",\"name\":\"西班牙 +34\"},{\"code\":\"[+351]\",\"name\":\"葡萄牙 +351\"},{\"code\":\"[+352]\",\"name\":\"卢森堡 +352\"},{\"code\":\"[+353]\",\"name\":\"爱尔兰 +353\"},{\"code\":\"[+354]\",\"name\":\"冰岛 +354\"},{\"code\":\"[+358]\",\"name\":\"芬兰 +358\"},{\"code\":\"[+36]\",\"name\":\"匈牙利 +36\"},{\"code\":\"[+396]\",\"name\":\"梵蒂冈 +396\"},{\"code\":\"[+41]\",\"name\":\"瑞士 +41\"},{\"code\":\"[+43]\",\"name\":\"奥地利 +43\"},{\"code\":\"[+45]\",\"name\":\"丹麦 +45\"},{\"code\":\"[+46]\",\"name\":\"瑞典 +46\"},{\"code\":\"[+47]\",\"name\":\"挪威 +47\"}]"
                        },
                        new
                        {
                            Id = 1281498705313388079L,
                            FieldCode = "DestinationHotelPhone",
                            FieldName = "目的酒店电话",
                            FieldType = (sbyte)0,
                            FieldsGroupType = (sbyte)3,
                            Group = "DestinationHotelPhoneGroup",
                            GroupName = "目的酒店电话",
                            GroupSort = (sbyte)1,
                            Maxlength = 16m,
                            Pattern = "^(13[0-9]|15[012356789]|166|17[3678]|18[0-9]|14[57])[0-9]{8}$",
                            Placeholder = "请输入"
                        },
                        new
                        {
                            Id = 1281498705313388080L,
                            FieldCode = "DestinationHotelRoomNO",
                            FieldName = "目的酒店房号",
                            FieldType = (sbyte)0,
                            FieldsGroupType = (sbyte)3,
                            Maxlength = 30m,
                            Placeholder = "请输入"
                        },
                        new
                        {
                            Id = 1281498705313388081L,
                            FieldCode = "FlightNumber",
                            FieldName = "航班号",
                            FieldType = (sbyte)0,
                            FieldsGroupType = (sbyte)3,
                            Maxlength = 10m,
                            Placeholder = "请输入"
                        },
                        new
                        {
                            Id = 1281498705313388082L,
                            FieldCode = "DepartureFlightNumber",
                            FieldName = "去程航班号",
                            FieldType = (sbyte)0,
                            FieldsGroupType = (sbyte)3,
                            Maxlength = 10m,
                            Placeholder = "请输入"
                        },
                        new
                        {
                            Id = 1281498705313388083L,
                            FieldCode = "ReturnFlightNumber",
                            FieldName = "返程航班号",
                            FieldType = (sbyte)0,
                            FieldsGroupType = (sbyte)3,
                            Maxlength = 10m,
                            Placeholder = "请输入"
                        },
                        new
                        {
                            Id = 1281498705313388084L,
                            FieldCode = "FlightSchedule",
                            FieldName = "班次",
                            FieldType = (sbyte)0,
                            FieldsGroupType = (sbyte)3,
                            Maxlength = 10m,
                            Placeholder = "请输入"
                        },
                        new
                        {
                            Id = 1281498705313388085L,
                            FieldCode = "FlightDepartureTime",
                            FieldName = "航班起飞当地时间",
                            FieldType = (sbyte)6,
                            FieldsGroupType = (sbyte)3,
                            MinDate = new DateTime(1899, 12, 31, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            Placeholder = "请输入"
                        },
                        new
                        {
                            Id = 1281498705313388086L,
                            FieldCode = "FlightLandingTime",
                            FieldName = "航班降落当地时间",
                            FieldType = (sbyte)6,
                            FieldsGroupType = (sbyte)3,
                            MinDate = new DateTime(1899, 12, 31, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            Placeholder = "请输入"
                        },
                        new
                        {
                            Id = 1281498705313388087L,
                            FieldCode = "AirportTerminal",
                            FieldName = "航站楼",
                            FieldType = (sbyte)0,
                            FieldsGroupType = (sbyte)3,
                            Maxlength = 10m,
                            Placeholder = "请输入"
                        },
                        new
                        {
                            Id = 1281498705313388088L,
                            FieldCode = "IsLandingVisa",
                            FieldName = "是否落地签",
                            FieldType = (sbyte)7,
                            FieldsGroupType = (sbyte)3
                        },
                        new
                        {
                            Id = 1281498705313388089L,
                            FieldCode = "CarBrand",
                            FieldName = "车辆品牌",
                            FieldType = (sbyte)0,
                            FieldsGroupType = (sbyte)3,
                            Maxlength = 30m,
                            Placeholder = "请输入"
                        },
                        new
                        {
                            Id = 1281498705313388090L,
                            FieldCode = "CarModelType",
                            FieldName = "车型",
                            FieldType = (sbyte)0,
                            FieldsGroupType = (sbyte)3,
                            Maxlength = 30m,
                            Placeholder = "请输入"
                        },
                        new
                        {
                            Id = 1281498705313388091L,
                            FieldCode = "LicensePlateNumber",
                            FieldName = "车牌号",
                            FieldType = (sbyte)0,
                            FieldsGroupType = (sbyte)3,
                            Maxlength = 30m,
                            Placeholder = "请输入"
                        },
                        new
                        {
                            Id = 1281498705313388092L,
                            FieldCode = "DrivingLicenceNumber",
                            FieldName = "驾照号",
                            FieldType = (sbyte)0,
                            FieldsGroupType = (sbyte)3,
                            Maxlength = 30m,
                            Placeholder = "请输入"
                        },
                        new
                        {
                            Id = 1281498705313388093L,
                            FieldCode = "AuthorizedDrivingModel",
                            FieldName = "准驾车型",
                            FieldType = (sbyte)0,
                            FieldsGroupType = (sbyte)3,
                            Maxlength = 30m,
                            Placeholder = "请输入"
                        },
                        new
                        {
                            Id = 1281498705313388094L,
                            DefaultValue = "1",
                            FieldCode = "CharterCarDays",
                            FieldName = "包车天数",
                            FieldType = (sbyte)1,
                            FieldsGroupType = (sbyte)3,
                            Placeholder = "请输入"
                        },
                        new
                        {
                            Id = 1281498705313388095L,
                            FieldCode = "TipMessage",
                            FieldName = "提示信息",
                            FieldType = (sbyte)8,
                            FieldsGroupType = (sbyte)4,
                            Maxlength = 300m
                        });
                });

            modelBuilder.Entity("Product.Api.Model.Group", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<string>("Name")
                        .HasColumnType("varchar(50)");

                    b.Property<string>("Remark")
                        .HasColumnType("varchar(100)");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("Group", (string)null);
                });

            modelBuilder.Entity("Product.Api.Model.GroupItems", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<long>("GroupId")
                        .HasColumnType("bigint");

                    b.Property<long>("ProductId")
                        .HasColumnType("bigint");

                    b.Property<sbyte>("ProductType")
                        .HasColumnType("tinyint");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("GroupId");

                    b.HasIndex("ProductId");

                    b.HasIndex("TenantId");

                    b.ToTable("GroupItems", (string)null);
                });

            modelBuilder.Entity("Product.Api.Model.InformationTemplate", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<bool>("IsDelete")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("tinyint(1)")
                        .HasDefaultValue(false);

                    b.Property<string>("TemplateName")
                        .IsRequired()
                        .HasColumnType("varchar(50)");

                    b.Property<sbyte>("TemplateType")
                        .HasColumnType("tinyint");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("InformationTemplate", (string)null);
                });

            modelBuilder.Entity("Product.Api.Model.InformationTemplateFields", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<string>("DefaultValue")
                        .HasColumnType("varchar(500)");

                    b.Property<string>("FieldCode")
                        .IsRequired()
                        .HasColumnType("varchar(50)");

                    b.Property<bool?>("IsMultiple")
                        .HasColumnType("tinyint(1)");

                    b.Property<bool>("IsRequired")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("tinyint(1)")
                        .HasDefaultValue(false);

                    b.Property<sbyte>("Sort")
                        .HasColumnType("tinyint");

                    b.Property<long>("TemplateId")
                        .HasColumnType("bigint");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("InformationTemplateFields", (string)null);
                });

            modelBuilder.Entity("Product.Api.Model.LineProduct", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<bool>("AdultsAllowed")
                        .HasColumnType("tinyint(1)");

                    b.Property<string>("AdultsStandard")
                        .HasColumnType("varchar(50)");

                    b.Property<bool>("BabyAllowed")
                        .HasColumnType("tinyint(1)");

                    b.Property<string>("BabyStandard")
                        .HasColumnType("varchar(50)");

                    b.Property<bool>("ChildrenAllowed")
                        .HasColumnType("tinyint(1)");

                    b.Property<string>("ChildrenStandard")
                        .HasColumnType("varchar(50)");

                    b.Property<string>("Content")
                        .HasColumnType("mediumtext");

                    b.Property<string>("CostCurrencyCode")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("varchar(20)")
                        .HasDefaultValue("CNY");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<int>("Days")
                        .HasColumnType("int");

                    b.Property<int>("DepartureCityId")
                        .HasColumnType("int");

                    b.Property<string>("DepartureCityName")
                        .HasColumnType("varchar(50)");

                    b.Property<int>("DepartureCountryId")
                        .HasColumnType("int");

                    b.Property<string>("DepartureCountryName")
                        .HasColumnType("varchar(50)");

                    b.Property<int>("DestinationCityId")
                        .HasColumnType("int");

                    b.Property<string>("DestinationCityName")
                        .HasColumnType("varchar(50)");

                    b.Property<sbyte?>("DestinationCoordinateType")
                        .HasColumnType("tinyint");

                    b.Property<int>("DestinationCountryId")
                        .HasColumnType("int");

                    b.Property<string>("DestinationCountryName")
                        .HasColumnType("varchar(50)");

                    b.Property<string>("DestinationGooglePalceId")
                        .HasColumnType("varchar(100)");

                    b.Property<Point>("DestinationLocation")
                        .HasColumnType("point");

                    b.Property<long?>("DevelopUserId")
                        .HasColumnType("bigint");

                    b.Property<bool>("ElderlyAllowed")
                        .HasColumnType("tinyint(1)");

                    b.Property<string>("ElderlyStandard")
                        .HasColumnType("varchar(50)");

                    b.Property<bool>("Enabled")
                        .HasColumnType("tinyint(1)");

                    b.Property<string>("Instructions")
                        .HasColumnType("varchar(500)");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("tinyint(1)");

                    b.Property<bool>("IsManualConfirm")
                        .HasColumnType("tinyint(1)");

                    b.Property<bool>("IsSupportRefund")
                        .HasColumnType("tinyint(1)");

                    b.Property<string>("KindReminder")
                        .HasColumnType("varchar(500)");

                    b.Property<int>("Nights")
                        .HasColumnType("int");

                    b.Property<long?>("OperatorUserId")
                        .HasColumnType("bigint");

                    b.Property<int?>("RefundBeforeTravelDateDay")
                        .HasColumnType("int");

                    b.Property<decimal?>("RefundRate")
                        .HasColumnType("decimal(8,4)");

                    b.Property<TimeSpan?>("RefundTravelDateTime")
                        .HasColumnType("time");

                    b.Property<int>("ReservationDaysInAdvance")
                        .HasColumnType("int");

                    b.Property<TimeSpan>("ReservationTimeInAdvance")
                        .HasColumnType("time");

                    b.Property<string>("SaleCurrencyCode")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("varchar(20)")
                        .HasDefaultValue("CNY");

                    b.Property<string>("SellPointDescribe")
                        .HasColumnType("varchar(500)");

                    b.Property<DateTime?>("SellingDateBegin")
                        .HasColumnType("datetime");

                    b.Property<DateTime?>("SellingDateEnd")
                        .HasColumnType("datetime");

                    b.Property<int>("Sort")
                        .HasColumnType("int");

                    b.Property<long>("SupplierId")
                        .HasColumnType("bigint");

                    b.Property<string>("SupplierName")
                        .HasColumnType("varchar(128)");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<string>("Title")
                        .IsRequired()
                        .HasColumnType("varchar(50)");

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.HasIndex("Sort", "UpdateTime");

                    b.ToTable("LineProduct", (string)null);
                });

            modelBuilder.Entity("Product.Api.Model.LineProductRallyPoint", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<string>("Address")
                        .IsRequired()
                        .HasColumnType("varchar(100)");

                    b.Property<int>("CoordinateType")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasDefaultValue(1);

                    b.Property<long>("LineProductId")
                        .HasColumnType("bigint");

                    b.Property<Point>("Location")
                        .HasColumnType("point");

                    b.Property<TimeSpan>("Time")
                        .HasColumnType("time");

                    b.HasKey("Id");

                    b.ToTable("LineProductRallyPoint", (string)null);
                });

            modelBuilder.Entity("Product.Api.Model.LineProductSku", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<bool>("B2bSellingStatus")
                        .HasColumnType("tinyint(1)");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<bool>("Enabled")
                        .HasColumnType("tinyint(1)");

                    b.Property<string>("FeeIncludes")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<bool>("IncludedAccommodation")
                        .HasColumnType("tinyint(1)");

                    b.Property<long>("LineProductId")
                        .HasColumnType("bigint");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("varchar(50)");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("LineProductSku", (string)null);
                });

            modelBuilder.Entity("Product.Api.Model.LineProductSkuCalendarPrice", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<decimal?>("CostPrice")
                        .HasColumnType("decimal(18,2)");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<DateTime>("Date")
                        .HasColumnType("datetime");

                    b.Property<long>("LineProductId")
                        .HasColumnType("bigint");

                    b.Property<long>("LineProductSkuId")
                        .HasColumnType("bigint");

                    b.Property<decimal?>("Price")
                        .HasColumnType("decimal(18,2)");

                    b.Property<sbyte>("PriceAdjustmentType")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("tinyint")
                        .HasDefaultValue((sbyte)0);

                    b.Property<decimal?>("PriceAdjustmentValue")
                        .HasColumnType("decimal(18,2)");

                    b.Property<sbyte>("PriceBasisType")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("tinyint")
                        .HasDefaultValue((sbyte)0);

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<sbyte>("Type")
                        .HasColumnType("tinyint");

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("LineProductSkuCalendarPrice", (string)null);
                });

            modelBuilder.Entity("Product.Api.Model.LineProductSkuItinerary", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<sbyte>("ActivityType")
                        .HasColumnType("tinyint");

                    b.Property<string>("Content")
                        .HasColumnType("varchar(500)");

                    b.Property<int>("Day")
                        .HasColumnType("int");

                    b.Property<string>("ImgPath")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<long>("LineProductId")
                        .HasColumnType("bigint");

                    b.Property<long>("LineProductSkuId")
                        .HasColumnType("bigint");

                    b.Property<long>("ResourceId")
                        .HasColumnType("bigint");

                    b.Property<string>("Subject")
                        .IsRequired()
                        .HasColumnType("varchar(100)");

                    b.Property<string>("Subtitle")
                        .HasColumnType("varchar(100)");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<TimeSpan>("Time")
                        .HasColumnType("time");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("LineProductSkuItinerary", (string)null);
                });

            modelBuilder.Entity("Product.Api.Model.MailProduct", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<string>("Content")
                        .HasColumnType("mediumtext");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<bool>("Enabled")
                        .HasColumnType("tinyint(1)");

                    b.Property<bool>("IsSupportRefund")
                        .HasColumnType("tinyint(1)");

                    b.Property<long>("PostageTemplateId")
                        .HasColumnType("bigint");

                    b.Property<string>("SellPointDescribe")
                        .HasColumnType("varchar(100)");

                    b.Property<DateTime?>("SellingDateBegin")
                        .HasColumnType("datetime");

                    b.Property<DateTime?>("SellingDateEnd")
                        .HasColumnType("datetime");

                    b.Property<int>("Sort")
                        .HasColumnType("int");

                    b.Property<long>("SupplierId")
                        .HasColumnType("bigint");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<string>("Title")
                        .HasColumnType("varchar(100)");

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.HasIndex("Sort", "UpdateTime");

                    b.ToTable("MailProduct", (string)null);
                });

            modelBuilder.Entity("Product.Api.Model.PostageTemplate", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<int>("DeliveryCityCode")
                        .HasColumnType("int");

                    b.Property<string>("DeliveryCityName")
                        .HasColumnType("varchar(50)");

                    b.Property<int>("DeliveryProvinceCode")
                        .HasColumnType("int");

                    b.Property<string>("DeliveryProvinceName")
                        .HasColumnType("varchar(50)");

                    b.Property<bool>("FirstChoice")
                        .HasColumnType("tinyint(1)");

                    b.Property<bool>("IsFreeShipping")
                        .HasColumnType("tinyint(1)");

                    b.Property<string>("Name")
                        .HasColumnType("varchar(50)");

                    b.Property<int>("ProcessingTime")
                        .HasColumnType("int");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("PostageTemplate", (string)null);
                });

            modelBuilder.Entity("Product.Api.Model.PostageTemplateRegion", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<int>("CityCode")
                        .HasColumnType("int");

                    b.Property<long>("ItemId")
                        .HasColumnType("bigint");

                    b.Property<int>("ProvinceCode")
                        .HasColumnType("int");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<sbyte>("Type")
                        .HasColumnType("tinyint");

                    b.HasKey("Id");

                    b.HasIndex("ItemId");

                    b.HasIndex("TenantId");

                    b.ToTable("PostageTemplateRegion", (string)null);
                });

            modelBuilder.Entity("Product.Api.Model.PostageTemplateSetting", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<decimal>("AdditionalFee")
                        .HasColumnType("decimal(18,2)");

                    b.Property<int>("AdditionalPieces")
                        .HasColumnType("int");

                    b.Property<decimal>("FreeShippingOverAmount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<int>("FreeShippingOverPieces")
                        .HasColumnType("int");

                    b.Property<long>("PostageTemplateId")
                        .HasColumnType("bigint");

                    b.Property<decimal>("StartFee")
                        .HasColumnType("decimal(18,2)");

                    b.Property<int>("StartPieces")
                        .HasColumnType("int");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<sbyte>("Type")
                        .HasColumnType("tinyint");

                    b.HasKey("Id");

                    b.HasIndex("PostageTemplateId");

                    b.HasIndex("TenantId");

                    b.ToTable("PostageTemplateSetting", (string)null);
                });

            modelBuilder.Entity("Product.Api.Model.ProductDefaultInformationTemplate", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<sbyte>("ProductTemplateType")
                        .HasColumnType("tinyint");

                    b.Property<sbyte>("ProductType")
                        .HasColumnType("tinyint");

                    b.Property<long>("TemplateId")
                        .HasColumnType("bigint");

                    b.Property<sbyte>("TemplateType")
                        .HasColumnType("tinyint");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.HasIndex("TemplateType", "ProductType", "ProductTemplateType", "TenantId")
                        .IsUnique();

                    b.ToTable("ProductDefaultInformationTemplate", (string)null);
                });

            modelBuilder.Entity("Product.Api.Model.ProductOperatorUser", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<long?>("OperatorUserId")
                        .HasColumnType("bigint");

                    b.Property<long>("ProductId")
                        .HasColumnType("bigint");

                    b.Property<sbyte>("ProductType")
                        .HasColumnType("tinyint");

                    b.Property<sbyte>("SellingPlatform")
                        .HasColumnType("tinyint");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.HasIndex("ProductId", "ProductType");

                    b.ToTable("ProductOperatorUser", (string)null);
                });

            modelBuilder.Entity("Product.Api.Model.ProductPhotos", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<bool>("Enabled")
                        .HasColumnType("tinyint(1)");

                    b.Property<sbyte>("MediaType")
                        .HasColumnType("tinyint");

                    b.Property<string>("Path")
                        .HasColumnType("varchar(255)");

                    b.Property<long>("ProductId")
                        .HasColumnType("bigint");

                    b.Property<int>("Sort")
                        .HasColumnType("int");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime");

                    b.HasKey("Id");

                    b.HasIndex("ProductId");

                    b.HasIndex("TenantId");

                    b.ToTable("ProductPhotos", (string)null);
                });

            modelBuilder.Entity("Product.Api.Model.ProductRedundantData", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<int>("AvailableInventory")
                        .HasColumnType("int");

                    b.Property<bool>("HasCommission")
                        .HasColumnType("tinyint(1)");

                    b.Property<decimal?>("MaxLinePrice")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("MaxPrice")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("MinLinePrice")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("MinPrice")
                        .HasColumnType("decimal(18,2)");

                    b.Property<long>("ProductId")
                        .HasColumnType("bigint");

                    b.Property<int>("Sales")
                        .HasColumnType("int");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("UpdateTime")
                        .HasColumnType("datetime");

                    b.HasKey("Id");

                    b.HasIndex("ProductId")
                        .IsUnique();

                    b.HasIndex("TenantId");

                    b.ToTable("ProductRedundantData", (string)null);
                });

            modelBuilder.Entity("Product.Api.Model.ProductResource", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<long>("CityCode")
                        .HasColumnType("bigint");

                    b.Property<string>("CityName")
                        .HasColumnType("varchar(50)");

                    b.Property<long>("CountryCode")
                        .HasColumnType("bigint");

                    b.Property<string>("CountryName")
                        .HasColumnType("varchar(50)");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<long>("ProductId")
                        .HasColumnType("bigint");

                    b.Property<long>("ProvinceCode")
                        .HasColumnType("bigint");

                    b.Property<string>("ProvinceName")
                        .HasColumnType("varchar(50)");

                    b.Property<long>("ResourceId")
                        .HasColumnType("bigint");

                    b.Property<sbyte>("ResourceType")
                        .HasColumnType("tinyint");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<sbyte>("TicketBusinessType")
                        .HasColumnType("tinyint");

                    b.HasKey("Id");

                    b.HasIndex("ProductId");

                    b.HasIndex("TenantId");

                    b.ToTable("ProductResource", (string)null);
                });

            modelBuilder.Entity("Product.Api.Model.ProductSku", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<int>("AfterPurchaseDays")
                        .HasColumnType("int");

                    b.Property<bool>("B2bSellingStatus")
                        .HasColumnType("tinyint(1)");

                    b.Property<string>("CostDescription")
                        .HasColumnType("varchar(500)");

                    b.Property<decimal>("CostPrice")
                        .HasColumnType("decimal(18,2)");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<bool>("Enabled")
                        .HasColumnType("tinyint(1)");

                    b.Property<string>("ImagePath")
                        .HasColumnType("varchar(255)");

                    b.Property<decimal>("LinePrice")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("Name")
                        .HasColumnType("varchar(100)");

                    b.Property<int>("NumberOfNights")
                        .HasColumnType("int");

                    b.Property<long>("ProductId")
                        .HasColumnType("bigint");

                    b.Property<decimal>("SellingPrice")
                        .HasColumnType("decimal(18,2)");

                    b.Property<int>("Sort")
                        .HasColumnType("int");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime");

                    b.Property<DateTime?>("ValidityBegin")
                        .HasColumnType("datetime");

                    b.Property<DateTime?>("ValidityEnd")
                        .HasColumnType("datetime");

                    b.Property<sbyte>("ValidityType")
                        .HasColumnType("tinyint");

                    b.HasKey("Id");

                    b.HasIndex("ProductId");

                    b.HasIndex("TenantId");

                    b.ToTable("ProductSku", (string)null);
                });

            modelBuilder.Entity("Product.Api.Model.ProductSkuReservationCalendarPrice", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<decimal>("CostPrice")
                        .HasColumnType("decimal(18,2)");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<DateTime>("Date")
                        .HasColumnType("datetime");

                    b.Property<decimal>("Price")
                        .HasColumnType("decimal(18,2)");

                    b.Property<long>("ProductId")
                        .HasColumnType("bigint");

                    b.Property<long>("ProductSkuId")
                        .HasColumnType("bigint");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("ProductId");

                    b.HasIndex("ProductSkuId");

                    b.HasIndex("TenantId");

                    b.HasIndex("ProductId", "ProductSkuId", "Date")
                        .IsUnique();

                    b.ToTable("ProductSkuReservationCalendarPrice", (string)null);
                });

            modelBuilder.Entity("Product.Api.Model.Restaurant", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<string>("Address")
                        .IsRequired()
                        .HasColumnType("varchar(50)");

                    b.Property<int>("CityCode")
                        .HasColumnType("int");

                    b.Property<string>("CityName")
                        .HasColumnType("varchar(50)");

                    b.Property<int>("CoordinateType")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasDefaultValue(1);

                    b.Property<int>("CountryCode")
                        .HasColumnType("int");

                    b.Property<string>("CountryName")
                        .HasColumnType("varchar(50)");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<int>("DistrictCode")
                        .HasColumnType("int");

                    b.Property<string>("DistrictName")
                        .HasColumnType("varchar(50)");

                    b.Property<bool>("Enabled")
                        .HasColumnType("tinyint(1)");

                    b.Property<string>("HouseNumber")
                        .HasColumnType("varchar(10)");

                    b.Property<Point>("Location")
                        .IsRequired()
                        .HasColumnType("point");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("varchar(50)");

                    b.Property<sbyte>("OperatingModel")
                        .HasColumnType("tinyint");

                    b.Property<int>("ProvinceCode")
                        .HasColumnType("int");

                    b.Property<string>("ProvinceName")
                        .HasColumnType("varchar(50)");

                    b.Property<string>("ServiceTime")
                        .IsRequired()
                        .HasColumnType("varchar(100)");

                    b.Property<string>("Telephone")
                        .IsRequired()
                        .HasColumnType("varchar(50)");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime");

                    b.HasKey("Id");

                    b.HasIndex("Location");

                    b.HasIndex("TenantId");

                    b.ToTable("Restaurant", (string)null);
                });

            modelBuilder.Entity("Product.Api.Model.RestaurantPhotos", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<bool>("Enabled")
                        .HasColumnType("tinyint(1)");

                    b.Property<string>("Path")
                        .IsRequired()
                        .HasColumnType("varchar(200)");

                    b.Property<long>("RestaurantId")
                        .HasColumnType("bigint");

                    b.Property<int>("Sort")
                        .HasColumnType("int");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime");

                    b.HasKey("Id");

                    b.HasIndex("RestaurantId");

                    b.HasIndex("TenantId");

                    b.ToTable("RestaurantPhotos", (string)null);
                });

            modelBuilder.Entity("Product.Api.Model.ShoppingCart", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<int>("Amount")
                        .HasColumnType("int");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<long>("ProductId")
                        .HasColumnType("bigint");

                    b.Property<long>("ProductSkuId")
                        .HasColumnType("bigint");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<long>("UserId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.HasIndex("UserId");

                    b.ToTable("ShoppingCart", (string)null);
                });

            modelBuilder.Entity("Product.Api.Model.Store", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<string>("Address")
                        .IsRequired()
                        .HasColumnType("varchar(50)");

                    b.Property<int>("CityCode")
                        .HasColumnType("int");

                    b.Property<string>("CityName")
                        .HasColumnType("varchar(50)");

                    b.Property<int>("CoordinateType")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasDefaultValue(1);

                    b.Property<int>("CountryCode")
                        .HasColumnType("int");

                    b.Property<string>("CountryName")
                        .HasColumnType("varchar(50)");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<int>("DistrictCode")
                        .HasColumnType("int");

                    b.Property<string>("DistrictName")
                        .HasColumnType("varchar(50)");

                    b.Property<string>("HouseNumber")
                        .HasColumnType("varchar(10)");

                    b.Property<Point>("Location")
                        .IsRequired()
                        .HasColumnType("point");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("varchar(50)");

                    b.Property<sbyte>("OperatingModel")
                        .HasColumnType("tinyint");

                    b.Property<int>("ProvinceCode")
                        .HasColumnType("int");

                    b.Property<string>("ProvinceName")
                        .HasColumnType("varchar(50)");

                    b.Property<string>("ServiceTime")
                        .IsRequired()
                        .HasColumnType("varchar(100)");

                    b.Property<string>("Telephone")
                        .IsRequired()
                        .HasColumnType("varchar(50)");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime");

                    b.HasKey("Id");

                    b.HasIndex("Location");

                    b.HasIndex("TenantId");

                    b.ToTable("Store", (string)null);
                });

            modelBuilder.Entity("Product.Api.Model.StorePhotos", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<sbyte>("Enabled")
                        .HasColumnType("tinyint");

                    b.Property<string>("Path")
                        .IsRequired()
                        .HasColumnType("varchar(200)");

                    b.Property<int>("Sort")
                        .HasColumnType("int");

                    b.Property<long>("StoreId")
                        .HasColumnType("bigint");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("StorePhotos", (string)null);
                });

            modelBuilder.Entity("Product.Api.Model.SupplySetting", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<bool>("IsSupply")
                        .HasColumnType("tinyint(1)");

                    b.Property<long>("ProductId")
                        .HasColumnType("bigint");

                    b.Property<sbyte>("ProductType")
                        .HasColumnType("tinyint");

                    b.Property<long>("SupplierId")
                        .HasColumnType("bigint");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("SupplySetting", (string)null);
                });

            modelBuilder.Entity("Product.Api.Model.TicketProduct", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<bool>("AutoRefundAfterExpiration")
                        .HasColumnType("tinyint(1)");

                    b.Property<decimal>("AutoRefundRate")
                        .HasColumnType("decimal(8,4)");

                    b.Property<string>("Content")
                        .HasColumnType("mediumtext");

                    b.Property<string>("CostCurrencyCode")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("varchar(20)")
                        .HasDefaultValue("CNY");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<long?>("DevelopUserId")
                        .HasColumnType("bigint");

                    b.Property<bool>("Enabled")
                        .HasColumnType("tinyint(1)");

                    b.Property<string>("Instructions")
                        .HasColumnType("varchar(500)");

                    b.Property<bool>("IsSupportRefund")
                        .HasColumnType("tinyint(1)");

                    b.Property<string>("KindReminder")
                        .HasColumnType("varchar(500)");

                    b.Property<bool>("NeedConfirmReservation")
                        .HasColumnType("tinyint(1)");

                    b.Property<bool>("NeedReservation")
                        .HasColumnType("tinyint(1)");

                    b.Property<bool>("NeedWriteOff")
                        .HasColumnType("tinyint(1)");

                    b.Property<long?>("OperatorUserId")
                        .HasColumnType("bigint");

                    b.Property<int?>("RefundBeforeTravelDateDay")
                        .HasColumnType("int");

                    b.Property<decimal>("RefundRate")
                        .HasColumnType("decimal(8,4)");

                    b.Property<TimeSpan?>("RefundTravelDateTime")
                        .HasColumnType("time");

                    b.Property<int>("ReservationDaysInAdvance")
                        .HasColumnType("int");

                    b.Property<string>("SaleCurrencyCode")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("varchar(20)")
                        .HasDefaultValue("CNY");

                    b.Property<string>("SellPointDescribe")
                        .HasColumnType("varchar(100)");

                    b.Property<DateTime?>("SellingDateBegin")
                        .HasColumnType("datetime");

                    b.Property<DateTime?>("SellingDateEnd")
                        .HasColumnType("datetime");

                    b.Property<int>("Sort")
                        .HasColumnType("int");

                    b.Property<long>("SupplierId")
                        .HasColumnType("bigint");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<sbyte>("TicketBusinessType")
                        .HasColumnType("tinyint");

                    b.Property<sbyte>("TicketSaleType")
                        .HasColumnType("tinyint");

                    b.Property<string>("Title")
                        .HasColumnType("varchar(100)");

                    b.Property<bool>("TouristIDRequired")
                        .HasColumnType("tinyint(1)");

                    b.Property<sbyte>("TouristInfoType")
                        .HasColumnType("tinyint");

                    b.Property<bool>("TouristNameRequired")
                        .HasColumnType("tinyint(1)");

                    b.Property<bool>("TouristPhoneRequired")
                        .HasColumnType("tinyint(1)");

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.HasIndex("Sort", "UpdateTime");

                    b.ToTable("TicketProduct", (string)null);
                });
#pragma warning restore 612, 618
        }
    }
}
