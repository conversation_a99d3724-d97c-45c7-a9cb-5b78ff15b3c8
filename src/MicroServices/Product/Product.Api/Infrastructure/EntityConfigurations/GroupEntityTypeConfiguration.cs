using EfCoreExtensions.EntityConfigurationsBase;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Product.Api.Infrastructure.EntityConfigurations
{
    public class GroupEntityTypeConfiguration : TenantBaseConfiguration<Model.Group>, IEntityTypeConfiguration<Model.Group>
    {
        public void Configure(EntityTypeBuilder<Model.Group> builder)
        {
            base.ConfigureBase(builder);

            builder.Property(s => s.Name)
                .HasColumnType("varchar(50)");
            
            builder.Property(s => s.Remark)
                .HasColumnType("varchar(100)");
            
            builder.Property(s => s.CreateTime)
                .HasColumnType("datetime");
            
            builder.Property(s => s.UpdateTime)
                .HasColumnType("datetime");
        }
    }
}
