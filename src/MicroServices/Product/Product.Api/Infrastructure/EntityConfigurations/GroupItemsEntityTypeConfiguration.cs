using EfCoreExtensions.EntityConfigurationsBase;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Product.Api.Infrastructure.EntityConfigurations
{
    public class GroupItemsEntityTypeConfiguration : TenantBaseConfiguration<Model.GroupItems>, IEntityTypeConfiguration<Model.GroupItems>
    {
        public void Configure(EntityTypeBuilder<Model.GroupItems> builder)
        {
            base.ConfigureBase(builder);

            builder.Property(s => s.GroupId)
                .HasColumnType("bigint");
            
            builder.Property(s => s.ProductId)
                .HasColumnType("bigint");
            
            builder.Property(s => s.ProductType)
                .HasColumnType("tinyint");
            
            builder.Property(s => s.CreateTime)
                .HasColumnType("datetime");

            //索引
            builder.HasIndex(s => s.GroupId);
            builder.HasIndex(s => s.ProductId);
        }
    }
}
