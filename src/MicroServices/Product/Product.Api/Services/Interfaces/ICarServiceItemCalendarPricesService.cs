using Contracts.Common.Product.DTOs.CarServiceItemCalendarPrices;

namespace Product.Api.Services.Interfaces;

public interface ICarServiceItemCalendarPricesService
{
    Task Update(UpdateCarServiceItemCalendarPriceInput input);

    Task BatchUpdate(BatchUpdateCarServiceItemCalendarPriceInput input);

    Task<List<GetCarServiceItemCalendarPriceOutput>> Get(GetCarServiceItemCalendarPriceInput input);
}
