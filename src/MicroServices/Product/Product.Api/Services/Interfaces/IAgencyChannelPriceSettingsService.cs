using Contracts.Common.Product.DTOs.AgencyChannelPriceSettings;
using Contracts.Common.Product.Messages;

namespace Product.Api.Services.Interfaces;

public interface IAgencyChannelPriceSettingsService
{
    Task Set(SetChannelPriceDto dto);
    Task Remove(RemoveChannelPriceDto dto);
    Task<IEnumerable<AgencyChannelPriceSettingOutput>> Get(GetAgencyChannelPriceSettingInput input);
    Task<IEnumerable<AgencyChannelPriceSettingOutput>> Query(QueryChannelPriceInput input);
    Task<IEnumerable<GroupByProductOutput>> GroupingQuery(QueryChannelPriceInput input);

    /// <summary>
    /// 订阅-更新分销商价格渠道配置最低价
    /// </summary>
    /// <param name="receive"></param>
    /// <returns></returns>
    Task UpdateMinPrice(UpdatePriceChannelMinPriceMessage receive);

    /// <summary>
    /// 汇智酒店渠道价配置
    /// </summary>
    Task SetHuiZhiHotel(SetHuiZhiHotelChannelPriceInput input, long tenantId);

    /// <summary>
    /// 汇智酒店渠道价查询
    /// </summary>
    Task<GetHuiZhiHotelChannelPriceOutput> GetHuiZhiHotel(long priceGroupId);

    /// <summary>
    /// cap-更新无效产品的配置
    /// </summary>
    /// <param name="message"></param>
    /// <returns></returns>
    Task UpdateInvalidProductSettings(UpdateInvalidProductPriceSettingsMessage message);
}