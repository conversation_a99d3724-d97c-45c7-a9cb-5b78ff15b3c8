
using Contracts.Common.Product.DTOs.CarServiceItemCalendarPrices;
using FluentValidation;

namespace Product.Api.Services.Validator.CarServiceItemCalendarPrice;

public class GetCarServiceItemCalendarPriceInputValidator : AbstractValidator<GetCarServiceItemCalendarPriceInput>
{
    public GetCarServiceItemCalendarPriceInputValidator()
    {
        When(x => x.CarProductId is null, () =>
        {
            RuleFor(x => x.CarServiceItemId).NotEmpty().WithMessage("产品id或项目id不能为空");
        });
        RuleFor(x => x.StartDate)
            .NotEmpty()
            .GreaterThanOrEqualTo(DateTime.Today.AddDays(-1));
        RuleFor(x => x.EndDate)
            .NotEmpty()
            .GreaterThanOrEqualTo(x => x.StartDate)
            .LessThanOrEqualTo(i => i.StartDate.Date.AddMonths(6))
            .WithMessage("日历范围不能超6个月");//<=6个月;
    }
}
