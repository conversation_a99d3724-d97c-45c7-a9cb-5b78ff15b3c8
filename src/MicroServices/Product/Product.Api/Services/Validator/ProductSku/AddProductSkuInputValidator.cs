using Contracts.Common.Product.DTOs.ProductSku;
using FluentValidation;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Product.Api.Services.Validator.ProductSku
{
    public class AddProductSkuInputValidator : AbstractValidator<AddProductSkusInput>
    {
        public AddProductSkuInputValidator()
        {
            RuleFor(x => x.ProductId).NotEqual(0);
            RuleFor(x => x.ProductSkus).NotEmpty(); 
        }
    }
}
