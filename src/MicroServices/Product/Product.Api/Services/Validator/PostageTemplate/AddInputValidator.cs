using Contracts.Common.Product.DTOs.PostageTemplate;
using FluentValidation;

namespace Product.Api.Services.Validator.PostageTemplate;

public class AddInputValidator : AbstractValidator<AddPostageTemplateInput>
{
    public AddInputValidator()
    {
        RuleFor(a => a.Name).NotEmpty().MaximumLength(15);
        RuleFor(a => a.DeliveryProvinceCode).GreaterThan(0);
        RuleFor(a => a.DeliveryProvinceName).NotEmpty();
        RuleFor(a => a.DeliveryCityCode).GreaterThan(0);
        RuleFor(a => a.DeliveryCityName).NotEmpty();
        RuleFor(a => a.ProcessingTime).GreaterThan(0);
        When(a => !a.IsFreeShipping, () =>
        {
            RuleFor(x => x.DefaultFee).NotEmpty();
        });
        When(a => a.IsFreeShipping, () =>
        {
            RuleFor(x => x.DefaultFee).Empty();
        });
    }
}