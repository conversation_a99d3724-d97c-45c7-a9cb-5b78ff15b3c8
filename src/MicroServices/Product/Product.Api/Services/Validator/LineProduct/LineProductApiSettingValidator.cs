using Contracts.Common.Product.DTOs.LineProductOpenChannelSetting;
using Contracts.Common.Product.DTOs.LineProductOpenSupplierSetting;
using Contracts.Common.Scenic.Enums;
using FluentValidation;

namespace Product.Api.Services.Validator.LineProduct;

public static class LineProductApiSettingValidator
{
    public class ValidateSupplierSetting : AbstractValidator<LineProductOpenSupplierSettingInfo>
    {
        public ValidateSupplierSetting()
        {
            RuleFor(x => x.PriceInventorySyncType).IsInEnum();
            RuleFor(x => x.SyncInterval).GreaterThanOrEqualTo(1);
            RuleFor(x => x.SyncDateRange).GreaterThanOrEqualTo(1);
        }
    }
    
    public class ValidateChannelSetting : AbstractValidator<List<LineProductOpenChannelSettingInfo>>
    {
        public ValidateChannelSetting()
        {
        
            //检查渠道类型是否唯一
            RuleFor(x => x)
                .Must(infos => infos
                    .GroupBy(g => g.PriceInventorySyncChannelType)
                    .All(g => g.Count() == 1))
                .WithMessage("渠道类型不唯一");

            //渠道配置校验
            RuleForEach(x => x)
                .ChildRules(c =>
                {
                    //如果是飞猪渠道并且开启同步,必须填写商品编码
                    c.RuleFor(y => y.ChannelProductId)
                        .NotEmpty()
                        .When(y => y.PriceInventorySyncChannelType == PriceInventorySyncChannelType.Fliggy &&
                                   y.PriceInventorySyncType != PriceInventorySyncType.NoNeedSync);
                });
        }
    }
}