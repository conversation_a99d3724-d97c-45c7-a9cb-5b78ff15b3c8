using Contracts.Common.Product.DTOs.Group;
using FluentValidation;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Product.Api.Services.Validator.Group
{
    public class SetGroupInputValidator : AbstractValidator<SetGroupInput>
    {
        public SetGroupInputValidator()
        {
            RuleFor(x => x.Id).NotEqual(0);
            RuleFor(x => x.Name).NotEmpty();
        }
    }
}
