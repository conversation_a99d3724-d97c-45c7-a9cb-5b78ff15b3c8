using Contracts.Common.Product.DTOs.LineProductSkuItinerary;
using FluentValidation;
using System.Data;

namespace Product.Api.Services.Validator.LineProductSkuItinerary;

public class UpdateLineProductSkuItineraryInputValidator : AbstractValidator<UpdateLineProductSkuItineraryInput>
{
    public UpdateLineProductSkuItineraryInputValidator()
    {
        RuleFor(x => x.LineProductId).GreaterThan(0).WithMessage("线路产品id不能为空");
        RuleFor(x => x.LineProductSkuId).GreaterThan(0).WithMessage("产品套餐id不能为空");
        RuleForEach(i => i.Itineraries).ChildRules(c =>
        {
            c.RuleFor(x => x.Day).NotEmpty().WithMessage("行程天数不能为空");
            c.RuleFor(x => x.Subject).NotEmpty().WithMessage("行程主题不能为空")
                .Length(0, 100).WithMessage("行程主题长度为100个字");
            c.RuleForEach(x => x.DayItinerary).ChildRules(d =>
            {
                d.RuleFor(x => x.Time).NotEmpty().WithMessage("行程时间不能为空");
                d.RuleFor(x => x.Subtitle).NotEmpty().WithMessage("行程类型主题不能为空")
                    .Length(0, 100).WithMessage("行程类型主题长度为100个字");
                d.RuleFor(x => x.ImgPath).Must(x => x.Count <= 6).WithMessage("图片最多6张");
                d.RuleFor(x => x.Content).Length(0, 500).WithMessage("补充说明长度为500个字");
            });
        });
    }
}