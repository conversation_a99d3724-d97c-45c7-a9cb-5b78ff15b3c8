using Contracts.Common.Product.DTOs.MailProduct;
using FluentValidation;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Product.Api.Services.Validator.MailProduct
{
    public class SetMailEnabledInputValidator : AbstractValidator<SetMailEnabledInput>
    {
        public SetMailEnabledInputValidator() 
        {
            RuleFor(x => x.ProductIds).NotNull();
        }
    }
}
