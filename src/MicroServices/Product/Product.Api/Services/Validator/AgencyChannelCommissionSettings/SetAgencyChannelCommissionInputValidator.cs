using Contracts.Common.Product.DTOs.AgencyChannelCommissionSettings;
using Contracts.Common.Product.Enums;
using FluentValidation;

namespace Product.Api.Services.Validator.AgencyChannelCommissionSettings;

public class SetAgencyChannelCommissionInputValidator : AbstractValidator<SetAgencyChannelCommissionInput>
{
    public SetAgencyChannelCommissionInputValidator()
    {
        RuleFor(x => x.PriceGroupId).NotEmpty();
        RuleForEach(x => x.CommissionSettings).ChildRules(c =>
        {
            c.RuleFor(x => x.ProductType).NotNull().IsInEnum();
            c.RuleFor(x => x.CommissionSettingType).NotNull().IsInEnum();
            c.RuleFor(x => x.BaseCommissionType).NotNull().IsInEnum();
            c.RuleFor(x => x.CommissionSettingValue).NotNull();
            c.RuleFor(x => x.BaseValue).GreaterThan(0)
                     .When(x => x.BaseCommissionType == ChannelBaseCommissionType.FixedRatio);
        });
    }

}
