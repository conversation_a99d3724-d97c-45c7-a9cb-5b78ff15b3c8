using Contracts.Common.Product.DTOs.CarProductSkuCalendarPrice;
using FluentValidation;

namespace Product.Api.Services.Validator.CarProduct;

public class GetCarProductSkuCalendarPriceInputValidator : AbstractValidator<GetCarProductSkuCalendarPriceInput>
{
    public GetCarProductSkuCalendarPriceInputValidator()
    {
        When(x => x.CarProductId is null, () =>
        {
            RuleFor(x => x.CarProductSkuId).NotEmpty().WithMessage("产品id或套餐id不能为空");
        });
        RuleFor(x => x.StartDate)
            .NotEmpty()
            .GreaterThanOrEqualTo(DateTime.Today.AddDays(-1));
        RuleFor(x => x.EndDate)
            .NotEmpty()
            .GreaterThanOrEqualTo(x => x.StartDate)
            .LessThanOrEqualTo(i => i.StartDate.Date.AddMonths(6))
            .WithMessage("日历范围不能超6个月");//<=6个月;
    }
}
