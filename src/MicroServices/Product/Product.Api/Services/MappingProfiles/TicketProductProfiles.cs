using AutoMapper;
using Contracts.Common.Product.DTOs.SupplySetting;
using Contracts.Common.Product.DTOs.TicketProduct;

namespace Product.Api.Services.MappingProfiles;

public class TicketProductProfiles : Profile
{
    public TicketProductProfiles()
    {
        CreateMap<UpdateTicketInput, TicketProduct>();
        CreateMap<SearchTicketInfo, SearchSupplyTicketProductOutput>()
            .ForMember(x => x.Id, x => x.MapFrom(m => m.ProductId));
        CreateMap<TicketProduct, TicketInfo>();
        CreateMap<TicketProduct, TicketDetailInfo>();
    }
}
