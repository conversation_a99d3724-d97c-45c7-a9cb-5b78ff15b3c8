namespace Product.Api.Services.OpenPlatform.Contracts.Supplier;

public class SupplierProductRequest
{
    
}


#region 查询产品详情

/// <summary>
/// 查询产品详情
/// </summary>
public class SupplierProductDetailRequest
{
    /// <summary>
    /// 开放平台供应商配置-productId
    /// <remarks>saas ==> ActivityId</remarks>
    /// </summary>
    public string OutProductId { get; set; }
    
    /// <summary>
    /// 开放平台供应商配置-optionId
    /// <remarks>saas => PackageId </remarks>
    /// </summary>
    public string OutProductOptionId { get; set; }
    
    /// <summary>
    ///开放平台供应商配置-skuId
    /// <remarks>saas => SkuId </remarks>
    /// </summary>
    public string OutSkuId { get; set; }
    
    /// <summary>
    /// 供应商类型
    /// <value>OpenSupplierType</value>
    /// </summary>
    public string SupplierType { get; set; }
}

#endregion

#region 查询产品内容信息

public class SupplierProductContentRequest
{
    /// <summary>
    /// 开放平台供应商配置-productId
    /// <remarks>saas ==> ActivityId</remarks>
    /// </summary>
    public string OutProductId { get; set; }
    
    /// <summary>
    /// 供应商类型
    /// <value>OpenSupplierType</value>
    /// </summary>
    public string SupplierType { get; set; }

    /// <summary>
    /// 语言
    /// <value>zh_CN,en_US</value>
    /// </summary>
    public string Language { get; set; }
}

#endregion

#region 查询产品日历价库

/// <summary>
/// 查询产品日历价库
/// </summary>
public class SupplierSkuScheduleRequest
{
    /// <summary>
    /// 供应商类型
    /// </summary>
    public string SupplierType { get; set; }

    /// <summary>
    /// 开放平台供应商配置-productId
    /// <remarks>saas ==> ActivityId</remarks>
    /// </summary>
    public string OutProductId { get; set; }
    
    /// <summary>
    /// 开放平台供应商配置-optionId
    /// <remarks>saas => PackageId </remarks>
    /// </summary>
    public string OutProductOptionId { get; set; }
    
    /// <summary>
    ///开放平台供应商配置-skuId
    /// <remarks>saas => SkuId </remarks>
    /// </summary>
    public string OutSkuId { get; set; }

    /// <summary>
    /// 开始时间
    /// <value>citopen 查库存每次只支持90天的范围</value>
    /// </summary>
    public DateTime DateFrom { get; set; } = DateTime.Today;

    /// <summary>
    /// 截止时间
    /// <value>citopen 查库存每次只支持180天的范围</value>
    /// </summary>
    public DateTime DateTo { get; set; } = DateTime.Today.AddDays(179);

    /// <summary>
    /// 时段
    /// </summary>
    public string Timeslot { get; set; }

    /// <summary>
    /// 是否异步返回结果
    /// <remarks>如果是 true，响应里面 stocks 是空的</remarks>
    /// </summary>
    public bool IsAsync { get; set; }
}

#endregion
