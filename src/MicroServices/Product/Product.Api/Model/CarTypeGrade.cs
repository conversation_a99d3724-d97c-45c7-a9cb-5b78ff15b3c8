using EfCoreExtensions.EntityBase;

namespace Product.Api.Model;

/// <summary>
/// 用车 - 车型等级
/// </summary>
public class CarTypeGrade : TenantBase
{
    /// <summary>
    /// 车型等级名称
    /// </summary>
    public string GradeName { get; set; }

    /// <summary>
    /// 座位数
    /// </summary>
    public int Seating { get; set; }

    public DateTime CreateTime { get; set; } = DateTime.Now;

    public DateTime? UpdateTime { get; set; } = DateTime.Now;
}
