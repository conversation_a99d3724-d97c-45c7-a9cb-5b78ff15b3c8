using AutoMapper;
using Contracts.Common.Order.DTOs;
using Contracts.Common.Order.Enums;
using Contracts.Common.Order.Messages;
using DotNetCore.CAP;
using Microsoft.EntityFrameworkCore;
using Order.Api.Infrastructure;
using Order.Api.Model;
using Order.Api.Services;
using Order.Api.Services.Interfaces;
using Order.Api.Services.MappingProfiles;
using System.Linq;
using System.Threading.Tasks;
using UnitTest.Base;
using Xunit;

namespace Order.UnitTest
{
    public class RefundOrderServiceTests : TestBase<CustomDbContext>
    {
        private static RefundOrderService GetRefundOrderService(CustomDbContext dbContext,
          ICapPublisher capPublisher,
          IMapper mapper,
          IMessageNotifyService messageNotifyService = null)
        {
            return new RefundOrderService(dbContext, capPublisher, mapper, messageNotifyService);
        }

        [Fact(DisplayName = "申请订单退款_成功")]
        public async Task RefundOrderApply_Success()
        {
            //arrange
            var tenantId = 1;
            var dbContext = GetNewDbContext();
            BaseOrder baseOrder = new()
            {
                ProductName = "",
                ContactsName="",
                ContactsPhoneNumber="",
                Status = BaseOrderStatus.UnFinished,
                PaymentAmount = 1
            };
            await dbContext.AddAsync(baseOrder);
            await dbContext.SetTenantId(tenantId).SaveChangesAsync();
            var capPublisher = GetCapPublisher();
            MapperConfiguration mapperConfiguration = new(s =>
            {
                s.AddMaps(typeof(BaseOrderNotifyProfile).Assembly);
            });
            //act
            var service = GetRefundOrderService(dbContext,
                capPublisher,
                mapperConfiguration.CreateMapper(),
                new MessageNotifyService(capPublisher));
            var command = new RefundOrderApplyMessage
            {
                TenantId = tenantId,
                BaseOrderId = baseOrder.Id,
                RefundOrderType = RefundOrderType.Ticket,
                PostageAmount = 0,
                ProofImgs = "",
                Quantity = 1,
                Reason = "申请取消",
                HasReviewed = false,
                SubOrdeId = 1,
                UserType = UserType.Customer,
                TotalAmount = 1
            };
            await service.RefundOrderApply(command);
            await dbContext.SaveChangesAsync();
            //assert
            Assert.True(dbContext.RefundOrders.IgnoreQueryFilters().Any());
        }

        [Fact(DisplayName = "订单退款审核通过_成功")]
        public async Task RefundOrderReviewAgree_Success()
        {
            //arrange
            var tenantId = 1;
            var dbContext = GetNewDbContext(tenantId);
            TicketOrder ticketOrder = new()
            {
                BaseOrderId = 1
            };
            var refundOrder = new RefundOrder
            {
                OrderType = RefundOrderType.Ticket,
                BaseOrderId = ticketOrder.BaseOrderId,
                SubOrdeId = ticketOrder.Id,
                Status = RefundOrderStatus.UnderReview,
                Quantity = 1,
                Reason = "申请退款",
                PostageAmount = 0,
                TotalAmount = 1,
                UserType = UserType.Customer
            };
            await dbContext.AddAsync(ticketOrder);
            await dbContext.AddAsync(refundOrder);
            await dbContext.SaveChangesAsync();

            MapperConfiguration mapperConfiguration = new(s => s.AddMaps(typeof(RefundOrderService).Assembly));

            //act
            var service = GetRefundOrderService(dbContext,
                GetCapPublisher(),
                mapperConfiguration.CreateMapper());
            var input = new ReviewRefundInput
            {
                IncludePostageAmount = true,
                RefundOrderId = refundOrder.Id,
                RejectedReason = "",
                ReviewType = ReviewRefundType.Agreed
            };
            await service.Review(input, new Common.Jwt.CurrentUser { userid = 1 });
            await dbContext.SaveChangesAsync();
            //assert
            Assert.True(dbContext.RefundOrders.Any(r => r.Id == refundOrder.Id && r.Status == RefundOrderStatus.Refunding));
        }

        [Fact(DisplayName = "订单退款审核拒绝_成功")]
        public async Task RefundOrderReviewReject_Success()
        {
            //arrange
            var tenantId = 1;
            var dbContext = GetNewDbContext(tenantId);
            var baseOrder = new BaseOrder
            {
                OrderType = OrderType.Ticket,
                ContactsName = "test",
                ContactsPhoneNumber = "123456"
            };
            var ticketOrder = new TicketOrder()
            {
                BaseOrderId = baseOrder.Id
            };
            var refundOrder = new RefundOrder
            {
                OrderType = RefundOrderType.Ticket,
                BaseOrderId = ticketOrder.BaseOrderId,
                SubOrdeId = ticketOrder.Id,
                Status = RefundOrderStatus.UnderReview,
                Quantity = 1,
                Reason = "申请退款",
                PostageAmount = 0,
                TotalAmount = 1,
                UserType = UserType.Customer
            };
            await dbContext.AddAsync(baseOrder);
            await dbContext.AddAsync(ticketOrder);
            await dbContext.AddAsync(refundOrder);
            await dbContext.SetTenantId(tenantId).SaveChangesAsync();
            MapperConfiguration mapperConfiguration = new(x =>
            {
                x.AddMaps(typeof(RefundOrderService).Assembly);
            });
            var mapper = mapperConfiguration.CreateMapper();

            //act
            var service = GetRefundOrderService(dbContext,
                GetCapPublisher(),
                mapperConfiguration.CreateMapper(),
                messageNotifyService: new MessageNotifyService(GetCapPublisher()));
            var input = new ReviewRefundInput
            {
                IncludePostageAmount = true,
                RefundOrderId = refundOrder.Id,
                RejectedReason = "拒绝退款",
                ReviewType = ReviewRefundType.Rejected
            };
            await service.Review(input, new Common.Jwt.CurrentUser { userid = 1 });
            await dbContext.SaveChangesAsync();
            //assert
            var result = dbContext.RefundOrders
                .Any(r => r.Id == refundOrder.Id
                && r.Status == RefundOrderStatus.Rejected && r.RejectedReason == input.RejectedReason);
            Assert.True(result);
        }
    }
}
