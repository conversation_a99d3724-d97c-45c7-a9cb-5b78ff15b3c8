using Common.Jwt;
using Contracts.Common.Order.DTOs;
using Contracts.Common.Order.Enums;

namespace Order.Api.Extensions;

public static class CurrentUserExt
{
    public static OperationUserDto GetOperationUser(this CurrentUser currentUser, UserType userType)
    {
        var operationUser = new OperationUserDto()
        {
            UserType = userType,
            UserId = currentUser.userid,
            Name = currentUser.nickname,
        };

        switch (userType)
        {
            case UserType.Agency:
                operationUser.AgencyId = currentUser.GetAgencyId();
                break;
            case UserType.Supplier:
                operationUser.SupplierId = currentUser.GetSupplierId();
                break;
        }
        return operationUser;
    }
}
