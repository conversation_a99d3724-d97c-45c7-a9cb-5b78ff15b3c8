namespace Order.Api.ConfigModel;

/// <summary>
/// 开放平台订单配置
/// </summary>
public class OpenPlatformOrderConfig
{
    /// <summary>
    /// 渠道方地址
    /// </summary>
    public string ChannelBaseUrl { get; set; }

    /// <summary>
    /// 供货方地址
    /// </summary>
    public string SupplierBaseUrl { get; set; }

    /// <summary>
    /// 用车 - 供货方地址
    /// </summary>
    public string SupplierCarBaseUrl { get; set; }

    /// <summary>
    /// 渠道appkey
    /// </summary>
    public string ChannelAppKey { get; set; }

    /// <summary>
    /// 渠道appSecret
    /// </summary>
    public string ChannelAppSecret { get; set; }

    /// <summary>
    /// 供货方appkey
    /// </summary>
    public string SupplierAppKey { get; set; }

    /// <summary>
    /// 供货方appSecret
    /// </summary>
    public string SupplierAppSecret { get; set; }
    
    /// <summary>
    /// 供货方客路订单创建 - 待移除
    /// </summary>
    public string SupplierOrderCreateUrl { get; set; } = "/klook/Order";

    /// <summary>
    /// 供货方客路订单支付 - 待移除
    /// </summary>
    public string SupplierOrderPayUrl { get; set; } = "/klook/PayBalance";

    /// <summary>
    /// 供货方客路订单详情 - 待移除
    /// </summary>
    public string SupplierOrderDetailUrl { get; set; } = "/klook/OrderDetail";

    /// <summary>
    /// 渠道 - 时效凭证PDF路径
    /// </summary>
    public string ChannelTimelinessVoucherPdfPath { get; set; }
    
    /// <summary>
    /// 渠道 - 时效凭证图片路径
    /// </summary>
    public string ChannelTimelinessVoucherImagePath { get; set; }

    /// <summary>
    /// 抖音渠道 - 时效凭证PDF路径
    /// </summary>
    public string TikTokChannelTimelinessVoucherPdfPath { get; set; }
    
    /// <summary>
    /// 渠道 - 时效凭证图片路径
    /// </summary>
    public string TikTokChannelTimelinessVoucherImagePath { get; set; }
    
    #region 供货方通用订单接口
    
    /// <summary>
    /// 订单创建
    /// </summary>
    public string SupplierCommonOrderCreateUrl { get; set; } = "/saas/orderCreate";

    /// <summary>
    /// 订单详情
    /// </summary>
    public string SupplierCommonOrderDetailUrl { get; set; } = "/saas/orderDetail";

    /// <summary>
    /// 订单支付
    /// </summary>
    public string SupplierCommonOrderPayUrl { get; set; } = "/saas/orderPay";

    /// <summary>
    /// 订单取消
    /// </summary>
    public string SupplierCommonOrderCancelUrl { get; set; } = "/saas/orderCancel";
    
    #endregion

    #region 供货方用车业务接口

    /// <summary>
    /// 用车 - 机场/地址模糊搜索
    /// </summary>
    public string SupplierCarAddressSearchUrl { get; set; } = "/saas/fuzzySearch";
    
    /// <summary>
    /// 用车询价
    /// </summary>
    public string SupplierCarOrderQuoteUrl { get; set; } = "/saas/search";
    
    /// <summary>
    /// 用车预订
    /// </summary>
    public string SupplierCarOrderReservationsUrl { get; set; } = "/saas/reservations";
    
    /// <summary>
    /// 用车订单取消
    /// </summary>
    public string SupplierCarOrderCancelUrl { get; set; } = "/saas/cancel";
    
    /// <summary>
    /// 用车订单详情
    /// </summary>
    public string SupplierCarOrderDetailUrl { get; set; } = "/saas/orderDetail";

    #endregion
    
    #region 供货方通用产品接口

    /// <summary>
    /// 供货方-产品详情
    /// </summary>
    public string SupplierCommonProductDetailUrl { get; set; } = "/saas/productDetail";

    /// <summary>
    /// 供货方-有效库存
    /// </summary>
    public string SupplierCommonSkuScheduleUrl { get; set; } = "/saas/productStock";

    #endregion

    #region 渠道端通用订单接口

    /// <summary>
    /// 渠道订单发货接口
    /// </summary>
    public string ChannelOrderDeliverUrl { get; set; } = "/saas/orderDeliver";
    
    /// <summary>
    /// 渠道订单确认接口
    /// </summary>
    public string ChannelOrderConfirmUrl { get; set; } = "/saas/orderConfirm";
    
    /// <summary>
    /// 订单发货接口（全目录）
    /// </summary>
    public string ChannelOrderDeliverAllUrl { get; set; } = "/saas/orderDeliverAll";
    
    /// <summary>
    /// 订单详情接口
    /// </summary>
    public string ChannelOrderDetailUrl { get; set; } = "/saas/orderDetail";
    
    /// <summary>
    /// 订单修改接口
    /// </summary>
    public string ChannelOrderModifyUrl { get; set; } = "/saas/orderModify";
    
    /// <summary>
    /// 订单退款审核接口
    /// </summary>
    public string ChannelOrderRefundAudit { get; set; } = "/saas/orderRefundAudit";

    #endregion
    
    #region 渠道端价库同步接口

    public string ChannelCommonPriceStockUploadUrl { get; set; } = "/saas/priceStockUpload";

    #endregion
}