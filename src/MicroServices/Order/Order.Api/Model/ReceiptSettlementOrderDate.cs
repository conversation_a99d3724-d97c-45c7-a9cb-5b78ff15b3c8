using Contracts.Common.Order.Enums;
using EfCoreExtensions.EntityBase;

namespace Order.Api.Model;

/// <summary>
/// 结算业务订单日期类型
/// </summary>
public class ReceiptSettlementOrderDate : KeyBase
{
    public long SettlementOrderId { get; set; }

    /// <summary>
    /// 结算业务类型
    /// </summary>
    public ReceiptSettlementBusinessType BusinessType { get; set; }

    /// <summary>
    /// 订单日期类型
    /// </summary>
    public ReceiptDateTye OrderDateType { get; set; }
}