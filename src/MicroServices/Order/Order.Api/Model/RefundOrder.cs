using Contracts.Common.Order.Enums;
using Contracts.Common.Payment.Enums;
using EfCoreExtensions.EntityBase;

namespace Order.Api.Model
{
    public class RefundOrder : TenantBase
    {
        /// <summary>
        /// 主单号
        /// </summary>
        public long BaseOrderId { get; set; }

        /// <summary>
        /// 子单号
        /// </summary>
        public long SubOrdeId { get; set; }

        /// <summary>
        /// 订单类型
        /// </summary>
        public RefundOrderType OrderType { get; set; }

        /// <summary>
        /// 退款(产品)数量
        /// </summary>
        public int Quantity { get; set; }

        /// <summary>
        /// 总退款金额(含运费)
        /// </summary>
        public decimal TotalAmount { get; set; }

        /// <summary>
        /// 退款币种
        /// </summary>
        public string PaymentCurrencyCode { get; set; } = Currency.CNY.ToString();

        /// <summary>
        /// 运费金额
        /// </summary>
        public decimal PostageAmount { get; set; }

        /// <summary>
        /// 谁发起退款
        /// </summary>
        public Contracts.Common.Order.Enums.UserType UserType { get; set; }

        /// <summary>
        /// 退款方用户id
        /// </summary>
        public long? UserId { get; set; }

        /// <summary>
        /// 退款方
        /// </summary>
        public string? UserName { get; set; }

        /// <summary>
        /// 状态
        /// </summary>
        public RefundOrderStatus Status { get; set; }

        /// <summary>
        /// 审核时间
        /// </summary>
        public DateTime? ReviewTime { get; set; }

        /// <summary>
        /// 凭据图片url,半角逗号隔开
        /// </summary>
        public string? ProofImgs { get; set; }

        /// <summary>
        /// 发起退款原因
        /// </summary>
        public string? Reason { get; set; }

        /// <summary>
        /// 审核拒绝原因
        /// </summary>
        public string? RejectedReason { get; set; }

        /// <summary>
        /// 退款失败原因
        /// </summary>
        public string? FailedReason { get; set; }

        /// <summary>
        /// 外部单号
        /// </summary>
        public string? ExtRefundNo { get; set; }

        public DateTime CreateTime { get; set; } = DateTime.Now;

        public DateTime? UpdateTime { get; set; } = DateTime.Now;

        /// <summary>
        /// 供应商id
        /// </summary>
        public long SupplierId { get; set; }

        /// <summary>
        /// 供应商应退采购价
        /// </summary>
        public decimal Cost { get; set; }

        /// <summary>
        /// 采购币种
        /// </summary>
        public string CostCurrencyCode { get; set; } = Currency.CNY.ToString();
    }
}
