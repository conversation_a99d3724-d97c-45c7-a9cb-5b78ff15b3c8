using Contracts.Common.Order.Enums;
using EfCoreExtensions.EntityBase;

namespace Order.Api.Model;

/// <summary>
/// 结算业务订单日期类型
/// </summary>
public class ReceiptSettlementOrderRecord : TenantBase
{
    public long SettlementOrderId { get; set; }

    /// <summary>
    /// 本次应收金额 同结算单应收币种
    /// </summary>
    public decimal Amount { get; set; }

    /// <summary>
    /// 实收金额
    /// </summary>
    public decimal? ReceivedAmount { get; set; }

    /// <summary>
    /// 实收总额币种
    /// </summary>
    public string? ReceivedAmountCurrencyCode { get; set; }

    #region 收款账户信息

    /// <summary>
    /// 收款方式
    /// </summary>
    public ReceiptSettlementTransferType SettlementTransferType { get; set; }

    /// <summary>
    /// 开户名称
    /// </summary>
    public string? AccountName { get; set; }

    /// <summary>
    /// 银行编号
    /// </summary>
    public string? BankCode { get; set; }

    /// <summary>
    /// 银行名称
    /// </summary>
    public string? BankName { get; set; }

    /// <summary>
    /// 分行名称
    /// </summary>
    public string? BranchName { get; set; }

    /// <summary>
    /// 银行账号
    /// </summary>
    public string? BankAccount { get; set; }

    /// <summary>
    /// 转账凭证图片
    /// </summary>
    public string? ProofImg { get; set; }

    /// <summary>
    /// 备注
    /// </summary>
    public string? Remark { get; set; }

    #endregion

    #region 收款操作人信息

    /// <summary>
    /// 收款操作人人Id
    /// </summary>
    public long? PayeeId { get; set; }

    /// <summary>
    /// 收款操作人
    /// </summary>
    public string? PayeeName { get; set; }

    /// <summary>
    /// 收款时间
    /// </summary>
    public DateTime PayeeTime { get; set; }

    #endregion

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreatTime { get; set; } = DateTime.Now;
}