using Contracts.Common.Payment.Enums;
using Contracts.Common.User.Enums;
using EfCoreExtensions.EntityBase;

namespace Order.Api.Model;

public class GroupBookingAggregateOrderDetail : TenantBase
{

    public long GroupBookingOrderItemId { get; set; }

    /// <summary>
    /// 申请单id
    /// </summary>
    public long ApplicationFormId { get; set; }

    public long GroupBookingOrderId { get; set; }

    public long GroupBookingPreOrderItemId { get; set; }

    public long BaseOrderId { get; set; }

    public long HotelOrderId { get; set; }

    public int? CountryCode { get; set; }

    public string? CountryName { get; set; }

    public int? ProvinceCode { get; set; }

    public string? ProvinceName { get; set; }

    public int? CityCode { get; set; }

    public string? CityName { get; set; }

    /// <summary>
    /// 酒店Id
    /// </summary>
    public long HotelId { get; set; }

    public string? HotelZHName { get; set; }

    public DateTime CheckInDate { get; set; }

    public DateTime CheckOutDate { get; set; }

    /// <summary>
    /// 订单总额 = DiscountAmount + Payment.Amount
    /// </summary>
    public decimal TotalAmount { get; set; }

    /// <summary>
    /// 优惠金额
    /// </summary>
    public decimal DiscountAmount { get; set; }

    /// <summary>
    /// 支付金额
    /// </summary>
    public decimal PaymentAmount { get; set; }

    /// <summary>
    /// 采购总折扣金额
    /// </summary>
    public decimal CostDiscountAmount { get; set; }

    /// <summary>
    /// 订单支付币种
    /// </summary>
    public string PaymentCurrencyCode { get; set; }

    /// <summary>
    /// 支付类型
    /// </summary>
    public PayType PaymentType { get; set; }

    /// <summary>
    /// 是否已付首款
    /// </summary>
    public bool HasDownPayment { get; set; }

    /// <summary>
    /// 是否已付尾款
    /// </summary>
    public bool HasFinalPayment { get; set; }

    /// <summary>
    /// 申请时间
    /// </summary>
    public DateTime ApplicationTime { get; set; }

    /// <summary>
    /// 首笔款付款时间
    /// </summary>
    public DateTime? InitialPaymentTime { get; set; }

    /// <summary>
    /// 尾款付款时间
    /// </summary>
    public DateTime? FinalPaymentTime { get; set; }

    /// <summary>
    /// 用户来源类型
    /// </summary>
    public UserPlatform UserPlatform { get; set; }

    /// <summary>
    /// 申请人类型
    /// </summary>
    public Contracts.Common.Order.Enums.UserType ApplicantUserType { get; set; }
}
