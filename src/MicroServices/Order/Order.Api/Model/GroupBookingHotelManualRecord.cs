using Contracts.Common.Order.Enums;
using EfCoreExtensions.EntityBase;

namespace Order.Api.Model;

/// <summary>
/// 团房申请单酒店手工记录
/// </summary>
public class GroupBookingHotelManualRecord : TenantBase
{
    /// <summary>
    /// 申请单id
    /// </summary>
    public long ApplicationFormId { get; set; }

    /// <summary>
    /// 需求目的地id
    /// </summary>
    public long ApplicationDemandId { get; set; }

    /// <summary>
    /// 资源酒店id
    /// </summary>
    public long ResourceHotelId { get; set; }

    /// <summary>
    /// 酒店报价信息
    /// </summary>
    public string Content { get; set; }

    /// <summary>
    /// 操作用户类型
    /// </summary>
    public UserType UserType { get; set; }

    /// <summary>
    /// 用户Id
    /// </summary>
    public long UserId { get; set; }

    /// <summary>
    /// 用户名称
    /// </summary>
    public string UserName { get; set; }

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime  CreateTime { get; set; } = DateTime.Now;
}
