using Contracts.Common.Product.Enums;
using EfCoreExtensions.EntityBase;

namespace Order.Api.Model;

/// <summary>
/// 线路套餐票种项数据
/// </summary>
public class TravelLineOrderSkuTypeItem : TenantBase
{
    /// <summary>
    /// 主单Id
    /// </summary>
    public long BaseOrderId { get; set; }

    /// <summary>
    /// 线路订单Id
    /// </summary>
    public long TravelLineOrderId { get; set; }

    /// <summary>
    /// 线路套餐票种项id
    /// </summary>
    public long SkuTypeItemId { get; set; }

    /// <summary>
    /// 线路套餐票种项名称
    /// </summary>
    public string SkuTypeItemName { get; set; }

    /// <summary>
    /// 票种年龄段
    /// </summary>
    public LineSkuPriceType SkuPriceType { get; set; }
    
    /// <summary>
    /// 活动id
    /// <remarks>供应端 - productid</remarks>
    /// </summary>
    public string? ActivityId { get; set; }

    /// <summary>
    /// 套餐包id
    /// <remarks>供应端 - optionId</remarks>
    /// </summary>
    public string? PackageId { get; set; }

    /// <summary>
    /// SkuId
    /// <remarks>供应端 - SkuId</remarks>
    /// </summary>
    public string? SkuId { get; set; }

    
    public DateTime CreateTime { get; set; } = DateTime.Now;
}