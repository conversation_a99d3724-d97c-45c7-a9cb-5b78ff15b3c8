using Contracts.Common.Order.Enums;
using EfCoreExtensions.EntityBase;
using System.ComponentModel.DataAnnotations.Schema;

namespace Order.Api.Model;


public class KingdeeAPPayable : TenantBase
{
    /// <summary>
    /// 推送id
    /// </summary>
    public long KingdeePushId { get; set; }

    /// <summary>
    /// 应收单类型 1-订单 2-退款单 3-抵充单
    /// </summary>
    public KingdeeARReceivableType ReceivableType { get; set; }

    /// <summary>
    /// 日期
    /// </summary>
    public DateTime Date { get; set; }

    /// <summary>
    /// 供应商id
    /// </summary>
    public long SupplierId { get; set; }

    /// <summary>
    /// 状态
    /// </summary>
    public KingdeeFormStatus Status { get; set; }

    public DateTime? UpdateTime { get; set; } = DateTime.Now;

    /// <summary>
    /// 应收单信息
    /// </summary>
    public APPayable APPayable { get; set; }
}

[NotMapped]
public class APPayable
{
    public int? FID { get; set; }
    /// <summary>
    /// 单据类型
    /// 标准应付单：YFD01_SYS，费用应付单：YFD02_SYS，资产应付单：YFD03_SYS，转销应付单：YFD04_SYS，应付抵冲单：YFD05_SYS
    /// </summary>
    public string FBillTypeID { get; set; }
    /// <summary>
    /// 单据编号
    /// </summary>
    public string FBillNo { get; set; }

    /// <summary>
    /// 业务日期
    /// </summary>
    public DateTime FDATE { get; set; }

    /// <summary>
    /// 本位币
    /// </summary>
    public string FMAINBOOKSTDCURRID { get; set; }
    /// <summary>
    /// 汇率类型
    /// 固定汇率:HLTX01_SYS,即期汇率:HLTX02_SYS,即时汇率:HLTX04_SYS,预算汇率:HLTX03_SYS
    /// </summary>
    public string FEXCHANGETYPE { get; set; }
    /// <summary>
    /// 汇率
    /// </summary>
    public decimal FExchangeRate { get; set; }

    /// <summary>
    /// 物料编码
    /// </summary>
    public string FMATERIALID { get; set; } = "CH4441";

    /// <summary>
    /// 计价单位
    /// </summary>
    public string FPRICEUNITID { get; set; } = "Pcs";

    /// <summary>
    /// 产品线
    /// </summary>
    public string F_qwe_Assistant { get; set; } = string.Empty;

    /// <summary>
    /// 抵冲类型
    /// </summary>
    public string F_qwe_Assistant1 { get; set; } = string.Empty;

    /// <summary>
    /// 计价数量
    /// </summary>
    public int FPriceQty { get; set; }
    /// <summary>
    /// 含税单价
    /// </summary>
    public decimal FTaxPrice { get; set; }
    /// <summary>
    /// 税率
    /// </summary>
    public decimal FEntryTaxRate { get; set; }

    /// <summary>
    /// 结算组织
    /// </summary>
    public string FSETTLEORGID { get; set; }
    /// <summary>
    /// 付款组织
    /// </summary>
    public string FPAYORGID { get; set; }
    /// <summary>
    /// 采购组织
    /// </summary>
    public string FPURCHASERGROUPID { get; set; }
    /// <summary>
    /// 采购部门
    /// </summary>
    public string FPURCHASEDEPTID { get; set; }
    /// <summary>
    /// 供应商
    /// </summary>
    public string FSUPPLIERID { get; set; } = string.Empty;
    /// <summary>
    /// 币别
    /// </summary>
    public string FCURRENCYID { get; set; }
}