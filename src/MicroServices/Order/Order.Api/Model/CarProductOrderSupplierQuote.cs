using EfCoreExtensions.EntityBase;

namespace Order.Api.Model;

/// <summary>
/// 用车订单询价数据
/// </summary>
public class CarProductOrderSupplierQuote : TenantBase
{
    /// <summary>
    /// 主订单id
    /// </summary>
    public long BaseOrderId { get; set; }

    /// <summary>
    /// 搜索id
    /// <remarks>预定时必传</remarks>
    /// </summary>
    public string SearchId { get; set; }

    public string ResultId { get; set; }

    /// <summary>
    /// 询价结果 对应的询价结果 SupplierCarQuoteResultItem 对象 json
    /// </summary>
    public string? Results { get; set; }
}
