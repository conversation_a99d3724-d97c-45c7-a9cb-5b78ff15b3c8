using Contracts.Common.Order.Enums;
using EfCoreExtensions.EntityBase;

namespace Order.Api.Model;

/// <summary>
/// 订单与保险产品
/// </summary>
public class InsureOrderRelation : TenantBase
{
    /// <summary>
    /// 订单id
    /// </summary>
    public long BaseOrderId { get; set; }
    /// <summary>
    /// 保险产品id
    /// </summary>
    public long InsureProductId { get; set; }

    /// <summary>
    /// 是否自动购买
    /// </summary>
    public bool IsAuto { get; set; }

    /// <summary>
    /// 保险状态
    /// </summary>
    public InsureOrderStatus Status { get; set; }
}
