using Contracts.Common.Order.Enums;
using EfCoreExtensions.EntityBase;
using System.ComponentModel.DataAnnotations.Schema;

namespace Order.Api.Model;

/// <summary>
/// 应收单
/// </summary>
public class KingdeeARReceivable : TenantBase
{
    /// <summary>
    /// 推送id
    /// </summary>
    public long KingdeePushId { get; set; }

    /// <summary>
    /// 应收单类型 1-订单 2-退款单 3-抵充单
    /// </summary>
    public KingdeeARReceivableType ReceivableType { get; set; }

    /// <summary>
    /// 日期
    /// </summary>
    public DateTime Date { get; set; }

    /// <summary>
    /// 分销商id
    /// </summary>
    public long AgencyId { get; set; }

    /// <summary>
    /// 状态
    /// </summary>
    public KingdeeFormStatus Status { get; set; }

    public DateTime? UpdateTime { get; set; } = DateTime.Now;

    /// <summary>
    /// 金蝶应收单信息
    /// </summary>
    public ARReceivable ARReceivable { get; set; }
}

[NotMapped]
public class ARReceivable
{
    public string? FID { get; set; }

    /// <summary>
    /// 单据类型
    /// 标准应收单：YSD01_SYS，费用应收单：YSD02_SYS，资产应收单：YSD03_SYS，转销应收单：YSD04_SYS，应收抵冲单：YSD05_SYS
    /// </summary>
    public string FBillTypeID { get; set; }

    /// <summary>
    /// 单据编号
    /// </summary>
    public string FBillNo { get; set; }

    /// <summary>
    /// 业务日期
    /// </summary>
    public DateTime FDATE { get; set; }

    /// <summary>
    /// 本位币
    /// </summary>
    public string FMAINBOOKSTDCURRID { get; set; }

    /// <summary>
    /// 汇率类型
    /// 固定汇率:HLTX01_SYS,即期汇率:HLTX02_SYS,即时汇率:HLTX04_SYS,预算汇率:HLTX03_SYS
    /// </summary>
    public string FEXCHANGETYPE { get; set; }

    /// <summary>
    /// 汇率
    /// </summary>
    public decimal? FExchangeRate { get; set; }

    /// <summary>
    /// 物料编码
    /// 物料默认值：CH4441
    /// </summary>
    public string FMATERIALID { get; set; } = "CH4441";
    /// <summary>
    /// 计价单位
    /// 单位默认值：Pcs
    /// </summary>
    public string FPRICEUNITID { get; set; } = "Pcs";
    /// <summary>
    /// 计价数量
    /// 金额为正数传1，为负数传-1
    /// </summary>
    public int? FPriceQty { get; set; }

    /// <summary>
    /// 产品线 酒店的推直采，门票、线路推玩乐
    /// </summary>
    public string F_qwe_Assistant { get; set; } = string.Empty;

    /// <summary>
    /// 抵冲类型 应收：入账抵冲   应付：出账抵冲
    /// </summary>
    public string F_qwe_Assistant1 { get; set; } = string.Empty;

    /// <summary>
    /// 含税单价
    /// </summary>
    public decimal? FTaxPrice { get; set; }
    /// <summary>
    /// 税率(%)
    /// </summary>
    public decimal? FEntryTaxRate { get; set; }

    /// <summary>
    /// 结算组织
    /// </summary>
    public string FSETTLEORGID { get; set; }

    /// <summary>
    /// 收款组织
    /// </summary>
    public string FPAYORGID { get; set; }

    /// <summary>
    /// 销售组织
    /// </summary>
    public string FSALEORGID { get; set; }

    /// <summary>
    /// 销售部门
    /// </summary>
    public string FSALEDEPTID { get; set; }

    /// <summary>
    /// 客户
    /// </summary>
    public string FCUSTOMERID { get; set; }

    /// <summary>
    /// 币别
    /// </summary>
    public string FCURRENCYID { get; set; }

    /// <summary>
    /// 备注
    /// </summary>
    public string? FAR_Remark { get; set; }
}