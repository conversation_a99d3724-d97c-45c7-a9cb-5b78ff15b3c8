using Common.GlobalException;
using Common.Swagger.Header;
using Contracts.Common.Order.DTOs.TravelLineOrder.OTA;
using Contracts.Common.Order.DTOs.TravelLineOrder;
using Contracts.Common.Payment.DTOs;
using Contracts.Common.Payment.Enums;
using Microsoft.AspNetCore.Mvc;
using Order.Api.Services.Interfaces;

namespace Order.Api.Controllers;

/// <summary>
/// OTA线路订单
/// </summary>
[Route("[controller]/[action]")]
[ApiController]
public class TravelLineOTAOrderController : ControllerBase
{
    private readonly ITravelLineOrderService _travelLineOrderService;
    
    public TravelLineOTAOrderController(
        ITravelLineOrderService travelLineOrderService)
    {
        _travelLineOrderService = travelLineOrderService;
    }

    /// <summary>
    /// 创建OTA订单
    /// </summary>
    /// <returns></returns>
    [HttpPost]
    [ProducesResponseType(typeof(CreateTravelLineOTAOrderOutput),200)]
    public async Task<IActionResult> Create(CreateDto input)
    {
        var createOutput = await _travelLineOrderService.Create(input);
        return Ok(new CreateTravelLineOTAOrderOutput
        {
            BaseOrderId = createOutput.BaseOrderId
        });
    }
}