using Common.Jwt;
using Common.Swagger;
using Contracts.Common.Order.DTOs;
using Contracts.Common.Order.DTOs.Insure;
using Contracts.Common.Order.DTOs.RefundOrder;
using Contracts.Common.Order.Enums;
using Contracts.Common.Order.Messages;
using DotNetCore.CAP;
using EfCoreExtensions.Abstract;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Order.Api.Services;
using Order.Api.Services.Interfaces;

namespace Order.Api.Controllers
{
    /// <summary>
    /// 投保人信息
    /// </summary>
    [Route("[controller]/[action]")]
    [ApiController]
    public class InsurePolicyHolderController : ControllerBase
    {
        private readonly IInsurePolicyHolderService _insurePolicyHolderService;

        public InsurePolicyHolderController(IInsurePolicyHolderService insurePolicyHolderService)
        {
            _insurePolicyHolderService = insurePolicyHolderService;
        }

        /// <summary>
        /// 获取投保人信息
        /// </summary>
        /// <param name="orderId">订单Id</param>
        /// <returns></returns>
        [HttpGet]
        [ProducesResponseType(typeof(InsurePolicyHolderOutput), (int)HttpStatusCode.OK)]
        public async Task<IActionResult> Detail()
        {
            var result = await _insurePolicyHolderService.Detail();
            return Ok(result);
        }

        /// <summary>
        /// 保存投保人
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost]
        [ProducesResponseType((int)HttpStatusCode.OK)]
        public async Task<IActionResult> Save(SaveInsurePolicyHolderInput input)
        {
            await _insurePolicyHolderService.Save(input);
            return Ok();
        }
    }
}
