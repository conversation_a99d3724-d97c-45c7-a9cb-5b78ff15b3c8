using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Order.Api.Infrastructure.Migrations
{
    public partial class AddTravelerExtendInfo : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<string>(
                name: "ExtendInfo",
                table: "TravelLineOrderTravelers",
                type: "mediumtext",
                nullable: true)
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.AddColumn<string>(
                name: "ExtendInfo",
                table: "ScenicTicketOrderTravelers",
                type: "mediumtext",
                nullable: true)
                .Annotation("MySql:CharSet", "utf8mb4");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "ExtendInfo",
                table: "TravelLineOrderTravelers");

            migrationBuilder.DropColumn(
                name: "ExtendInfo",
                table: "ScenicTicketOrderTravelers");
        }
    }
}
