using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Order.Api.Infrastructure.Migrations
{
    public partial class AddOffsetOrderBatchNumber : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<long>(
                name: "<PERSON>ch<PERSON><PERSON><PERSON>",
                table: "OffsetOrder",
                type: "bigint",
                nullable: true);

            migrationBuilder.CreateIndex(
                name: "IX_OffsetOrder_BaseOrderId",
                table: "OffsetOrder",
                column: "BaseOrderId");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropIndex(
                name: "IX_OffsetOrder_BaseOrderId",
                table: "OffsetOrder");

            migrationBuilder.DropColumn(
                name: "BatchNumber",
                table: "OffsetOrder");
        }
    }
}
