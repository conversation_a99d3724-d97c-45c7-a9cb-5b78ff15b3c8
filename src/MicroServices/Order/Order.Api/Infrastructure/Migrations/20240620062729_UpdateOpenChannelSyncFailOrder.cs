using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Order.Api.Infrastructure.Migrations
{
    public partial class UpdateOpenChannelSyncFailOrder : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<string>(
                name: "CurrencyCode",
                table: "OpenChannelSyncFailOrder",
                type: "varchar(50)",
                nullable: false,
                defaultValue: "")
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.AddColumn<DateTime>(
                name: "FinishTime",
                table: "OpenChannelSyncFailOrder",
                type: "datetime",
                nullable: true);

            migrationBuilder.AddColumn<long>(
                name: "RelatedCombinationProductId",
                table: "OpenChannelSyncFailOrder",
                type: "bigint",
                nullable: true);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "CurrencyCode",
                table: "OpenChannelSyncFailOrder");

            migrationBuilder.DropColumn(
                name: "FinishTime",
                table: "OpenChannelSyncFailOrder");

            migrationBuilder.DropColumn(
                name: "RelatedCombinationProductId",
                table: "OpenChannelSyncFailOrder");
        }
    }
}
