using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Order.Api.Infrastructure.Migrations
{
    public partial class updateReceiptSettlementOrder : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<long>(
                name: "TenantDepartmentId",
                table: "SettlementOrder",
                type: "bigint",
                nullable: true);

            migrationBuilder.AddColumn<long>(
                name: "TenantDepartmentId",
                table: "ReceiptSettlementOrder",
                type: "bigint",
                nullable: true);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "TenantDepartmentId",
                table: "SettlementOrder");

            migrationBuilder.DropColumn(
                name: "TenantDepartmentId",
                table: "ReceiptSettlementOrder");
        }
    }
}
