using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Order.Api.Infrastructure.Migrations
{
    public partial class OffsetOrderAddProp : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<sbyte>(
                name: "OffsetAmountType",
                table: "OffsetOrder",
                type: "tinyint",
                nullable: false,
                defaultValue: (sbyte)0);

            migrationBuilder.AddColumn<long>(
                name: "WorkOrderId",
                table: "OffsetOrder",
                type: "bigint",
                nullable: true);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "OffsetAmountType",
                table: "OffsetOrder");

            migrationBuilder.DropColumn(
                name: "WorkOrderId",
                table: "OffsetOrder");
        }
    }
}
