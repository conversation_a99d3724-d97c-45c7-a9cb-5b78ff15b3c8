using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Order.Api.Infrastructure.Migrations
{
    public partial class AddScenicTicketRelatedOrderIndexV2 : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateIndex(
                name: "IX_ScenicTicketOrder_TicketsCombinationOrderId",
                table: "ScenicTicketOrder",
                column: "TicketsCombinationOrderId");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropIndex(
                name: "IX_ScenicTicketOrder_TicketsCombinationOrderId",
                table: "ScenicTicketOrder");
        }
    }
}
