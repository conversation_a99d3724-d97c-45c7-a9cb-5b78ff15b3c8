// <auto-generated />
using System;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using NetTopologySuite.Geometries;
using Order.Api.Infrastructure;

#nullable disable

namespace Order.Api.Infrastructure.Migrations
{
    [DbContext(typeof(CustomDbContext))]
    [Migration("20250613021736_GroupBookingPreOrderItemPreBookingCode")]
    partial class GroupBookingPreOrderItemPreBookingCode
    {
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "6.0.5")
                .HasAnnotation("Relational:MaxIdentifierLength", 64);

            modelBuilder.Entity("Order.Api.Model.AfterSaleFinancialHandleOrder", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<DateTime?>("AccountTime")
                        .HasColumnType("datetime");

                    b.Property<long>("AgencyId")
                        .HasColumnType("bigint");

                    b.Property<string>("AgencyName")
                        .IsRequired()
                        .HasColumnType("varchar(256)");

                    b.Property<decimal>("Amount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<sbyte>("BusinessType")
                        .HasColumnType("tinyint");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<string>("CurrencyCode")
                        .IsRequired()
                        .HasColumnType("varchar(32)");

                    b.Property<DateTime?>("FinishTime")
                        .HasColumnType("datetime");

                    b.Property<sbyte>("HandleType")
                        .HasColumnType("tinyint");

                    b.Property<long>("OffsetOrderId")
                        .HasColumnType("bigint");

                    b.Property<string>("Operator")
                        .HasColumnType("varchar(100)");

                    b.Property<long?>("OperatorId")
                        .HasColumnType("bigint");

                    b.Property<string>("Proof")
                        .HasColumnType("varchar(200)");

                    b.Property<string>("Remark")
                        .HasColumnType("varchar(500)");

                    b.Property<sbyte>("Status")
                        .HasColumnType("tinyint");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("AfterSaleFinancialHandleOrder", (string)null);
                });

            modelBuilder.Entity("Order.Api.Model.AggregateChannelOrderNo", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<long>("BaseOrderId")
                        .HasColumnType("bigint");

                    b.Property<string>("ChannelOrderNo")
                        .HasColumnType("varchar(500)");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("BaseOrderId");

                    b.HasIndex("TenantId");

                    b.HasIndex("TenantId", "ChannelOrderNo");

                    b.ToTable("AggregateChannelOrderNo", (string)null);
                });

            modelBuilder.Entity("Order.Api.Model.AggregateOrder", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<sbyte?>("AbnormalOrderSourceType")
                        .HasColumnType("tinyint");

                    b.Property<string>("AbnormalReason")
                        .IsRequired()
                        .HasColumnType("varchar(500)");

                    b.Property<long>("AgencyId")
                        .HasColumnType("bigint");

                    b.Property<string>("AgencyName")
                        .IsRequired()
                        .HasColumnType("varchar(256)");

                    b.Property<sbyte?>("AirportTransferType")
                        .HasColumnType("tinyint");

                    b.Property<long>("BaseOrderId")
                        .HasColumnType("bigint");

                    b.Property<sbyte?>("CarHailingType")
                        .HasColumnType("tinyint");

                    b.Property<sbyte?>("CarProductPurchaseSourceType")
                        .HasColumnType("tinyint");

                    b.Property<sbyte?>("CarProductType")
                        .HasColumnType("tinyint");

                    b.Property<string>("ChannelOrderNo")
                        .IsRequired()
                        .HasColumnType("varchar(512)");

                    b.Property<int>("CityCode")
                        .HasColumnType("int");

                    b.Property<DateTime?>("ClosedDate")
                        .HasColumnType("datetime");

                    b.Property<decimal?>("CommissionFee")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("CommissionRate")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("ConfirmCode")
                        .IsRequired()
                        .HasColumnType("varchar(128)");

                    b.Property<sbyte?>("ConfirmStatus")
                        .HasColumnType("tinyint");

                    b.Property<DateTime?>("ConfirmTime")
                        .HasColumnType("datetime");

                    b.Property<string>("ContactsEmail")
                        .IsRequired()
                        .HasColumnType("varchar(50)");

                    b.Property<string>("ContactsName")
                        .IsRequired()
                        .HasColumnType("varchar(50)");

                    b.Property<string>("ContactsPhoneNumber")
                        .IsRequired()
                        .HasColumnType("varchar(50)");

                    b.Property<string>("CostCurrencyCode")
                        .IsRequired()
                        .HasColumnType("varchar(32)");

                    b.Property<decimal>("CostDiscountAmount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal>("CostDiscountRate")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal>("CostExchangeRate")
                        .HasColumnType("decimal(18,6)");

                    b.Property<int>("CountryCode")
                        .HasColumnType("int");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<sbyte?>("CredentialSourceType")
                        .HasColumnType("tinyint");

                    b.Property<DateTime?>("DelayedPayDeadline")
                        .HasColumnType("datetime");

                    b.Property<bool?>("DelayedPayStatus")
                        .HasColumnType("tinyint(1)");

                    b.Property<int?>("DeliveryErrorCode")
                        .HasColumnType("int");

                    b.Property<string>("DeliveryErrorMsg")
                        .HasColumnType("varchar(1000)");

                    b.Property<sbyte?>("DeliveryStatus")
                        .HasColumnType("tinyint");

                    b.Property<string>("DeparturePointName")
                        .IsRequired()
                        .HasColumnType("varchar(50)");

                    b.Property<string>("DestinationPointName")
                        .IsRequired()
                        .HasColumnType("varchar(50)");

                    b.Property<long?>("DevelopUserId")
                        .HasColumnType("bigint");

                    b.Property<decimal>("DiscountAmount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("EnProductName")
                        .IsRequired()
                        .HasColumnType("varchar(200)");

                    b.Property<string>("EnProductSkuName")
                        .IsRequired()
                        .HasColumnType("varchar(100)");

                    b.Property<string>("EnResourceName")
                        .IsRequired()
                        .HasColumnType("varchar(100)");

                    b.Property<decimal>("ExchangeRate")
                        .HasColumnType("decimal(18,6)");

                    b.Property<DateTime?>("FinishDate")
                        .HasColumnType("datetime");

                    b.Property<string>("GroupNo")
                        .IsRequired()
                        .HasColumnType("varchar(50)");

                    b.Property<string>("HotelOrderCancelRule")
                        .IsRequired()
                        .HasColumnType("varchar(500)");

                    b.Property<decimal?>("InsurePurchaseAmount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<sbyte?>("IsGroupRoom")
                        .HasColumnType("tinyint");

                    b.Property<sbyte?>("LineProductPurchaseSourceType")
                        .HasColumnType("tinyint");

                    b.Property<long?>("OperatorAssistantUserId")
                        .HasColumnType("bigint");

                    b.Property<long?>("OperatorUserId")
                        .HasColumnType("bigint");

                    b.Property<sbyte>("OrderCategory")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("tinyint")
                        .HasDefaultValue((sbyte)1);

                    b.Property<bool>("OrderExpired")
                        .HasColumnType("tinyint(1)");

                    b.Property<long>("OrderId")
                        .HasColumnType("bigint");

                    b.Property<string>("OrderMark")
                        .IsRequired()
                        .HasColumnType("varchar(32)");

                    b.Property<sbyte>("OrderStatus")
                        .HasColumnType("tinyint");

                    b.Property<sbyte>("OrderType")
                        .HasColumnType("tinyint");

                    b.Property<int?>("Passengers")
                        .HasColumnType("int");

                    b.Property<decimal>("PayableOffsetAmount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal>("PaymentAmount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("PaymentCurrencyCode")
                        .IsRequired()
                        .HasColumnType("varchar(32)");

                    b.Property<int>("PaymentType")
                        .HasColumnType("int");

                    b.Property<sbyte?>("ProcessingLevelTag")
                        .HasColumnType("tinyint");

                    b.Property<long>("ProductId")
                        .HasColumnType("bigint");

                    b.Property<string>("ProductName")
                        .IsRequired()
                        .HasColumnType("varchar(200)");

                    b.Property<long>("ProductSkuId")
                        .HasColumnType("bigint");

                    b.Property<string>("ProductSkuName")
                        .IsRequired()
                        .HasColumnType("varchar(100)");

                    b.Property<int>("ProvinceCode")
                        .HasColumnType("int");

                    b.Property<int>("Quantity")
                        .HasColumnType("int");

                    b.Property<decimal>("ReceiptOffsetAmount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<sbyte>("RefundStatus")
                        .HasColumnType("tinyint");

                    b.Property<decimal?>("RefundTotalAmount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("Remark")
                        .IsRequired()
                        .HasColumnType("varchar(500)");

                    b.Property<long>("ResourceId")
                        .HasColumnType("bigint");

                    b.Property<string>("ResourceName")
                        .IsRequired()
                        .HasColumnType("varchar(100)");

                    b.Property<long?>("SalespersonId")
                        .HasColumnType("bigint");

                    b.Property<string>("SalespersonName")
                        .IsRequired()
                        .HasColumnType("varchar(50)");

                    b.Property<short>("SellingChannels")
                        .HasColumnType("smallint");

                    b.Property<sbyte>("SellingPlatform")
                        .HasColumnType("tinyint");

                    b.Property<string>("SeriesNumber")
                        .HasColumnType("varchar(100)");

                    b.Property<DateTime?>("SkuValidityBegin")
                        .HasColumnType("datetime");

                    b.Property<DateTime?>("SkuValidityEnd")
                        .HasColumnType("datetime");

                    b.Property<bool?>("StaffTag")
                        .HasColumnType("tinyint(1)");

                    b.Property<sbyte?>("Status")
                        .HasColumnType("tinyint");

                    b.Property<short>("SupplierApiType")
                        .HasColumnType("smallint");

                    b.Property<decimal?>("SupplierCommissionFee")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("SupplierCommissionRate")
                        .HasColumnType("decimal(18,2)");

                    b.Property<long>("SupplierId")
                        .HasColumnType("bigint");

                    b.Property<string>("SupplierOrderId")
                        .IsRequired()
                        .HasColumnType("varchar(500)");

                    b.Property<sbyte?>("Tags")
                        .HasColumnType("tinyint");

                    b.Property<long>("TenantDeaprtmentId")
                        .HasColumnType("bigint");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<sbyte?>("TicketBusinessType")
                        .HasColumnType("tinyint");

                    b.Property<string>("TicketCodes")
                        .IsRequired()
                        .HasColumnType("varchar(2048)");

                    b.Property<sbyte?>("TicketsType")
                        .HasColumnType("tinyint");

                    b.Property<decimal>("TotalAmount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal>("TotalCost")
                        .HasColumnType("decimal(18,2)");

                    b.Property<long?>("TrackingUserId")
                        .HasColumnType("bigint");

                    b.Property<DateTime?>("TravelBeginDate")
                        .HasColumnType("datetime");

                    b.Property<DateTime?>("TravelDate")
                        .HasColumnType("datetime");

                    b.Property<DateTime?>("TravelEndDate")
                        .HasColumnType("datetime");

                    b.Property<sbyte?>("TravelStatus")
                        .HasColumnType("tinyint");

                    b.Property<string>("Travelers")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("TravelersCountJson")
                        .HasColumnType("text");

                    b.Property<long>("UserId")
                        .HasColumnType("bigint");

                    b.Property<string>("UserNickName")
                        .IsRequired()
                        .HasColumnType("varchar(50)");

                    b.Property<string>("UserPhone")
                        .HasColumnType("varchar(50)");

                    b.Property<sbyte?>("VccPaymentStatus")
                        .HasColumnType("tinyint");

                    b.HasKey("Id");

                    b.HasIndex("BaseOrderId");

                    b.HasIndex("CreateTime");

                    b.HasIndex("SupplierOrderId");

                    b.HasIndex("TenantId");

                    b.HasIndex("TrackingUserId");

                    b.ToTable("AggregateOrder", (string)null);
                });

            modelBuilder.Entity("Order.Api.Model.AggregateSupplierOrderId", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<long>("BaseOrderId")
                        .HasColumnType("bigint");

                    b.Property<string>("SupplierOrderId")
                        .HasColumnType("varchar(500)");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("BaseOrderId");

                    b.HasIndex("TenantId");

                    b.ToTable("AggregateSupplierOrderId", (string)null);
                });

            modelBuilder.Entity("Order.Api.Model.BaseOrder", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<long>("AgencyId")
                        .HasColumnType("bigint");

                    b.Property<string>("AgencyName")
                        .HasColumnType("varchar(256)");

                    b.Property<string>("ChannelOrderNo")
                        .HasColumnType("varchar(512)");

                    b.Property<decimal?>("CommissionFee")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("CommissionRate")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("ContactsEmail")
                        .HasColumnType("varchar(50)");

                    b.Property<string>("ContactsName")
                        .IsRequired()
                        .HasColumnType("varchar(50)");

                    b.Property<string>("ContactsPhoneNumber")
                        .IsRequired()
                        .HasColumnType("varchar(50)");

                    b.Property<decimal>("CostDiscountAmount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<DateTime?>("DelayedPayDeadline")
                        .HasColumnType("datetime");

                    b.Property<bool?>("DelayedPayStatus")
                        .HasColumnType("tinyint(1)");

                    b.Property<long?>("DevelopUserId")
                        .HasColumnType("bigint");

                    b.Property<decimal>("DiscountAmount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("EnProductName")
                        .HasColumnType("varchar(200)");

                    b.Property<string>("EnProductSkuName")
                        .HasColumnType("varchar(100)");

                    b.Property<string>("EnResourceName")
                        .HasColumnType("varchar(50)");

                    b.Property<string>("GroupNo")
                        .HasColumnType("varchar(32)");

                    b.Property<string>("Message")
                        .HasColumnType("varchar(500)");

                    b.Property<long?>("OperatorAssistantUserId")
                        .HasColumnType("bigint");

                    b.Property<long?>("OperatorUserId")
                        .HasColumnType("bigint");

                    b.Property<sbyte>("OrderCategory")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("tinyint")
                        .HasDefaultValue((sbyte)1);

                    b.Property<sbyte>("OrderType")
                        .HasColumnType("tinyint");

                    b.Property<DateTime?>("PayTime")
                        .HasColumnType("datetime");

                    b.Property<decimal>("PaymentAmount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("PaymentChannel")
                        .HasColumnType("varchar(50)");

                    b.Property<string>("PaymentCurrencyCode")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("varchar(20)")
                        .HasDefaultValue("CNY");

                    b.Property<string>("PaymentExternalNo")
                        .HasColumnType("varchar(50)");

                    b.Property<string>("PaymentMode")
                        .HasColumnType("varchar(50)");

                    b.Property<int>("PaymentType")
                        .HasColumnType("int");

                    b.Property<string>("ProductName")
                        .HasColumnType("varchar(200)");

                    b.Property<string>("ProductSkuName")
                        .HasColumnType("varchar(100)");

                    b.Property<string>("ResourceName")
                        .HasColumnType("varchar(50)");

                    b.Property<long?>("SalespersonId")
                        .HasColumnType("bigint");

                    b.Property<string>("SalespersonName")
                        .HasColumnType("varchar(50)");

                    b.Property<short>("SellingChannels")
                        .HasColumnType("smallint");

                    b.Property<sbyte>("SellingPlatform")
                        .HasColumnType("tinyint");

                    b.Property<string>("SeriesNumber")
                        .HasColumnType("varchar(100)");

                    b.Property<sbyte>("Status")
                        .IsConcurrencyToken()
                        .HasColumnType("tinyint");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<decimal>("TotalAmount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<long?>("TrackingUserId")
                        .HasColumnType("bigint");

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime");

                    b.Property<long>("UserId")
                        .HasColumnType("bigint");

                    b.Property<string>("UserNickName")
                        .HasColumnType("varchar(50)");

                    b.Property<sbyte>("VccPaymentStatus")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("tinyint")
                        .HasDefaultValue((sbyte)1);

                    b.Property<long>("VipLevelId")
                        .HasColumnType("bigint");

                    b.Property<string>("VipLevelName")
                        .HasColumnType("varchar(20)");

                    b.HasKey("Id");

                    b.HasIndex("ChannelOrderNo");

                    b.HasIndex("TenantId");

                    b.HasIndex("UserId");

                    b.HasIndex("TenantId", "SeriesNumber")
                        .IsUnique();

                    b.HasIndex("TenantId", "UpdateTime");

                    b.ToTable("BaseOrder", (string)null);
                });

            modelBuilder.Entity("Order.Api.Model.BaseOrderDiscount", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<long>("BaseOrderId")
                        .HasColumnType("bigint");

                    b.Property<decimal>("DiscountAmount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<long>("DiscountId")
                        .HasColumnType("bigint");

                    b.Property<sbyte>("DiscountType")
                        .HasColumnType("tinyint");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<string>("Title")
                        .HasColumnType("varchar(100)");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("BaseOrderDiscount", (string)null);
                });

            modelBuilder.Entity("Order.Api.Model.BaseOrderRemark", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<long>("BaseOrderId")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<long>("CreatorId")
                        .HasColumnType("bigint");

                    b.Property<string>("CreatorName")
                        .HasColumnType("varchar(50)");

                    b.Property<string>("Imgs")
                        .HasColumnType("varchar(500)");

                    b.Property<string>("Remark")
                        .IsRequired()
                        .HasColumnType("varchar(500)");

                    b.HasKey("Id");

                    b.HasIndex("BaseOrderId");

                    b.ToTable("BaseOrderRemark", (string)null);
                });

            modelBuilder.Entity("Order.Api.Model.BaseOrderSeries", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<DateOnly>("Date")
                        .HasColumnType("date");

                    b.Property<sbyte>("OrderType")
                        .HasColumnType("tinyint");

                    b.Property<string>("Prefix")
                        .IsRequired()
                        .HasColumnType("varchar(50)");

                    b.Property<string>("SeriesNumber")
                        .IsRequired()
                        .HasColumnType("varchar(100)");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.HasIndex("TenantId", "OrderType")
                        .IsUnique();

                    b.HasIndex("TenantId", "Prefix")
                        .IsUnique();

                    b.ToTable("BaseOrderSeries", (string)null);
                });

            modelBuilder.Entity("Order.Api.Model.CarHailingOrder", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<long>("BaseOrderId")
                        .HasColumnType("bigint");

                    b.Property<long>("CarHailingProductId")
                        .HasColumnType("bigint");

                    b.Property<long>("CarHailingProductSkuId")
                        .HasColumnType("bigint");

                    b.Property<sbyte>("CarHailingType")
                        .HasColumnType("tinyint");

                    b.Property<long?>("ClaimantId")
                        .HasColumnType("bigint");

                    b.Property<string>("ClaimantName")
                        .HasColumnType("varchar(50)");

                    b.Property<string>("Image")
                        .HasColumnType("varchar(200)");

                    b.Property<string>("Instructions")
                        .HasColumnType("varchar(500)");

                    b.Property<bool>("IsSupportRefund")
                        .HasColumnType("tinyint(1)");

                    b.Property<string>("KindReminder")
                        .HasColumnType("varchar(500)");

                    b.Property<sbyte>("LanguageType")
                        .HasColumnType("tinyint");

                    b.Property<int>("Passengers")
                        .HasColumnType("int");

                    b.Property<string>("ProductTitle")
                        .HasColumnType("varchar(100)");

                    b.Property<int>("Quantity")
                        .HasColumnType("int");

                    b.Property<int?>("RefundBeforeTravelDateDay")
                        .HasColumnType("int");

                    b.Property<decimal?>("RefundRate")
                        .HasColumnType("decimal(18,2)");

                    b.Property<TimeSpan?>("RefundTravelDateTime")
                        .HasColumnType("time");

                    b.Property<int>("ReservationDaysInAdvance")
                        .HasColumnType("int");

                    b.Property<int>("Seats")
                        .HasColumnType("int");

                    b.Property<string>("SkuName")
                        .HasColumnType("varchar(50)");

                    b.Property<sbyte>("Status")
                        .IsConcurrencyToken()
                        .HasColumnType("tinyint");

                    b.Property<long>("SupplierId")
                        .HasColumnType("bigint");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("CarHailingOrder", (string)null);
                });

            modelBuilder.Entity("Order.Api.Model.CarHailingOrderPoint", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<long>("BaseOrderId")
                        .HasColumnType("bigint");

                    b.Property<long>("CarHailingOrderId")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("Date")
                        .HasColumnType("datetime");

                    b.Property<int>("DepartureCityCode")
                        .HasColumnType("int");

                    b.Property<string>("DepartureCityName")
                        .HasColumnType("varchar(50)");

                    b.Property<string>("DeparturePointName")
                        .HasColumnType("varchar(50)");

                    b.Property<int>("DestinationCityCode")
                        .HasColumnType("int");

                    b.Property<string>("DestinationCityName")
                        .HasColumnType("varchar(50)");

                    b.Property<string>("DestinationPointName")
                        .HasColumnType("varchar(50)");

                    b.Property<string>("FlightNumber")
                        .HasColumnType("varchar(20)");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<TimeSpan?>("Time")
                        .HasColumnType("time");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("CarHailingOrderPoint", (string)null);
                });

            modelBuilder.Entity("Order.Api.Model.CarProductChannelOrder", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<long>("BaseOrderId")
                        .HasColumnType("bigint");

                    b.Property<string>("ChannelOrderNo")
                        .IsRequired()
                        .HasColumnType("varchar(512)");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<sbyte>("Status")
                        .HasColumnType("tinyint");

                    b.Property<long>("SubOrderId")
                        .HasColumnType("bigint");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime");

                    b.HasKey("Id");

                    b.HasIndex("BaseOrderId");

                    b.HasIndex("TenantId");

                    b.ToTable("CarProductChannelOrder", (string)null);
                });

            modelBuilder.Entity("Order.Api.Model.CarProductChannelOrderRecord", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<long>("BaseOrderId")
                        .HasColumnType("bigint");

                    b.Property<string>("ChannelOrderNo")
                        .IsRequired()
                        .HasColumnType("varchar(512)");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<int?>("ErrorCode")
                        .HasColumnType("int");

                    b.Property<string>("ErrorMsg")
                        .HasColumnType("varchar(1000)");

                    b.Property<bool>("IsSuccess")
                        .HasColumnType("tinyint(1)");

                    b.Property<sbyte>("RecordType")
                        .HasColumnType("tinyint");

                    b.Property<long>("SubOrderId")
                        .HasColumnType("bigint");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("BaseOrderId");

                    b.HasIndex("TenantId");

                    b.ToTable("CarProductChannelOrderRecord", (string)null);
                });

            modelBuilder.Entity("Order.Api.Model.CarProductOrder", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<long?>("AirportId")
                        .HasColumnType("bigint");

                    b.Property<string>("AirportTerminal")
                        .HasColumnType("varchar(50)");

                    b.Property<sbyte?>("AirportTransferType")
                        .HasColumnType("tinyint");

                    b.Property<string>("AlternatePhoneNumber")
                        .HasColumnType("varchar(50)");

                    b.Property<bool?>("AutoConfirm")
                        .HasColumnType("tinyint(1)");

                    b.Property<int>("Baggages")
                        .HasColumnType("int");

                    b.Property<long>("BaseOrderId")
                        .HasColumnType("bigint");

                    b.Property<int?>("BookingDuration")
                        .HasColumnType("int");

                    b.Property<string>("CancelRule")
                        .HasColumnType("varchar(1000)");

                    b.Property<sbyte>("CancelType")
                        .HasColumnType("tinyint");

                    b.Property<long>("CarProductId")
                        .HasColumnType("bigint");

                    b.Property<long>("CarProductSkuId")
                        .HasColumnType("bigint");

                    b.Property<sbyte>("CarProductType")
                        .HasColumnType("tinyint");

                    b.Property<long>("CarTypeGradeId")
                        .HasColumnType("bigint");

                    b.Property<string>("ConfirmCode")
                        .HasColumnType("varchar(128)");

                    b.Property<DateTime?>("ConfirmTime")
                        .HasColumnType("datetime");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<string>("DepartureAddress")
                        .HasColumnType("varchar(256)");

                    b.Property<int>("DepartureCityCode")
                        .HasColumnType("int");

                    b.Property<string>("DepartureCityName")
                        .IsRequired()
                        .HasColumnType("varchar(50)");

                    b.Property<string>("DestinationAddress")
                        .HasColumnType("varchar(256)");

                    b.Property<int>("DestinationCityCode")
                        .HasColumnType("int");

                    b.Property<string>("DestinationCityName")
                        .IsRequired()
                        .HasColumnType("varchar(50)");

                    b.Property<string>("FeeExclude")
                        .HasColumnType("mediumtext");

                    b.Property<string>("FeeInclude")
                        .HasColumnType("mediumtext");

                    b.Property<DateTime?>("FinishTime")
                        .HasColumnType("datetime");

                    b.Property<string>("FlightNumber")
                        .HasColumnType("varchar(50)");

                    b.Property<DateTime?>("FlightTime")
                        .HasColumnType("datetime");

                    b.Property<string>("GradeName")
                        .IsRequired()
                        .HasColumnType("varchar(50)");

                    b.Property<string>("Instructions")
                        .HasColumnType("mediumtext");

                    b.Property<bool?>("IsLandingVisa")
                        .HasColumnType("tinyint(1)");

                    b.Property<decimal?>("MinProfit")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("varchar(50)");

                    b.Property<sbyte>("OpenSupplierType")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("tinyint")
                        .HasDefaultValue((sbyte)0);

                    b.Property<int>("Passengers")
                        .HasColumnType("int");

                    b.Property<sbyte>("PurchaseSourceType")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("tinyint")
                        .HasDefaultValue((sbyte)1);

                    b.Property<int>("Quantity")
                        .HasColumnType("int");

                    b.Property<sbyte>("Status")
                        .IsConcurrencyToken()
                        .HasColumnType("tinyint");

                    b.Property<long>("SupplierId")
                        .HasColumnType("bigint");

                    b.Property<string>("SupplierOrderId")
                        .HasColumnType("varchar(500)");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<string>("Title")
                        .IsRequired()
                        .HasColumnType("varchar(50)");

                    b.Property<DateTime>("TravelDate")
                        .HasColumnType("datetime");

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime");

                    b.Property<string>("WeChatID")
                        .HasColumnType("varchar(50)");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("CarProductOrder", (string)null);
                });

            modelBuilder.Entity("Order.Api.Model.CarProductOrderSupplierQuote", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<long>("BaseOrderId")
                        .HasColumnType("bigint");

                    b.Property<string>("ResultId")
                        .IsRequired()
                        .HasColumnType("varchar(128)");

                    b.Property<string>("Results")
                        .HasColumnType("mediumtext");

                    b.Property<string>("SearchId")
                        .IsRequired()
                        .HasColumnType("varchar(128)");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("SearchId");

                    b.HasIndex("TenantId");

                    b.ToTable("CarProductOrderSupplierQuote", (string)null);
                });

            modelBuilder.Entity("Order.Api.Model.CarProductSupplierOrder", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<decimal>("Amount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<long>("BaseOrderId")
                        .HasColumnType("bigint");

                    b.Property<long>("CarProductOrderId")
                        .HasColumnType("bigint");

                    b.Property<string>("ConfirmCode")
                        .HasColumnType("varchar(128)");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<string>("Currency")
                        .IsRequired()
                        .HasColumnType("varchar(20)");

                    b.Property<sbyte>("OrderStatus")
                        .HasColumnType("tinyint");

                    b.Property<string>("ResultId")
                        .HasColumnType("varchar(100)");

                    b.Property<string>("SearchId")
                        .HasColumnType("varchar(100)");

                    b.Property<long>("SupplierId")
                        .HasColumnType("bigint");

                    b.Property<string>("SupplierOrderId")
                        .HasColumnType("varchar(100)");

                    b.Property<sbyte>("SupplierType")
                        .HasColumnType("tinyint");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime");

                    b.HasKey("Id");

                    b.HasIndex("BaseOrderId");

                    b.HasIndex("TenantId");

                    b.ToTable("CarProductSupplierOrder", (string)null);
                });

            modelBuilder.Entity("Order.Api.Model.CarProductSupplierOrderRecord", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<long>("BaseOrderId")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<string>("NotifyId")
                        .HasColumnType("varchar(100)");

                    b.Property<sbyte>("RecordType")
                        .HasColumnType("tinyint");

                    b.Property<string>("ResultCode")
                        .HasColumnType("varchar(50)");

                    b.Property<string>("ResultMessage")
                        .HasColumnType("varchar(500)");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime");

                    b.HasKey("Id");

                    b.HasIndex("BaseOrderId");

                    b.HasIndex("TenantId");

                    b.ToTable("CarProductSupplierOrderRecord", (string)null);
                });

            modelBuilder.Entity("Order.Api.Model.CarServiceItemOrder", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<long>("BaseOrderId")
                        .HasColumnType("bigint");

                    b.Property<long>("CarProductOrderId")
                        .HasColumnType("bigint");

                    b.Property<long>("CarServiceItemId")
                        .HasColumnType("bigint");

                    b.Property<string>("Description")
                        .HasColumnType("varchar(500)");

                    b.Property<bool>("IsNeedCharge")
                        .HasColumnType("tinyint(1)");

                    b.Property<string>("ItemName")
                        .IsRequired()
                        .HasColumnType("varchar(50)");

                    b.Property<string>("PlatformMapKey")
                        .HasColumnType("varchar(100)");

                    b.Property<int>("Quantity")
                        .HasColumnType("int");

                    b.Property<long>("SupplierId")
                        .HasColumnType("bigint");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("CarServiceItemOrder", (string)null);
                });

            modelBuilder.Entity("Order.Api.Model.CompensationOrderBindItem", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<long>("BaseOrderId")
                        .HasColumnType("bigint");

                    b.Property<long>("CompensationBaseOrderId")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("BaseOrderId");

                    b.HasIndex("CompensationBaseOrderId")
                        .IsUnique();

                    b.HasIndex("TenantId");

                    b.ToTable("CompensationOrderBindItem", (string)null);
                });

            modelBuilder.Entity("Order.Api.Model.GDSHotelOrder", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<bool?>("AdditionalFeesInclusive")
                        .HasColumnType("tinyint(1)");

                    b.Property<long>("BaseOrderId")
                        .HasColumnType("bigint");

                    b.Property<string>("BedTypeJson")
                        .HasColumnType("text");

                    b.Property<string>("BookingKey")
                        .IsRequired()
                        .HasColumnType("varchar(128)");

                    b.Property<string>("ExclusivePrivileges")
                        .HasColumnType("text");

                    b.Property<string>("GuaranteeJson")
                        .HasColumnType("text");

                    b.Property<bool?>("IncidentalsInclusive")
                        .HasColumnType("tinyint(1)");

                    b.Property<bool?>("LocalFeesInclusive")
                        .HasColumnType("tinyint(1)");

                    b.Property<decimal>("OrgAmount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("OrgCurrencyCode")
                        .IsRequired()
                        .HasColumnType("varchar(20)");

                    b.Property<string>("RateKey")
                        .IsRequired()
                        .HasColumnType("varchar(1500)");

                    b.Property<string>("RoomDescribe")
                        .HasColumnType("varchar(1000)");

                    b.Property<string>("SabreHotelCode")
                        .IsRequired()
                        .HasColumnType("varchar(50)");

                    b.Property<bool?>("TaxInclusive")
                        .HasColumnType("tinyint(1)");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("BaseOrderId");

                    b.HasIndex("TenantId");

                    b.ToTable("GDSHotelOrder", (string)null);
                });

            modelBuilder.Entity("Order.Api.Model.GroupBookingAggregate", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<bool>("AcceptRecommendedHotels")
                        .HasColumnType("tinyint(1)");

                    b.Property<int?>("AdultNum")
                        .HasColumnType("int");

                    b.Property<long>("AgencyId")
                        .HasColumnType("bigint");

                    b.Property<string>("AgencyName")
                        .IsRequired()
                        .HasColumnType("varchar(256)");

                    b.Property<string>("Applicant")
                        .HasColumnType("varchar(256)");

                    b.Property<long?>("ApplicantId")
                        .HasColumnType("bigint");

                    b.Property<sbyte>("ApplicantUserType")
                        .HasColumnType("tinyint");

                    b.Property<long>("ApplicationDemandId")
                        .HasColumnType("bigint");

                    b.Property<long>("ApplicationFormId")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("ApplicationTime")
                        .HasColumnType("datetime");

                    b.Property<string>("Assignor")
                        .HasColumnType("varchar(50)");

                    b.Property<long?>("AssignorId")
                        .HasColumnType("bigint");

                    b.Property<sbyte?>("CancelStatus")
                        .HasColumnType("tinyint");

                    b.Property<DateTime?>("CancellationTime")
                        .HasColumnType("datetime(6)");

                    b.Property<DateTime?>("CheckInDate")
                        .HasColumnType("datetime");

                    b.Property<DateTime?>("CheckOutDate")
                        .HasColumnType("datetime");

                    b.Property<int?>("ChildNum")
                        .HasColumnType("int");

                    b.Property<int?>("CityCode")
                        .HasColumnType("int");

                    b.Property<string>("CityName")
                        .HasColumnType("varchar(50)");

                    b.Property<int?>("CountryCode")
                        .HasColumnType("int");

                    b.Property<string>("CountryName")
                        .HasColumnType("varchar(50)");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<string>("CurrencyCode")
                        .HasColumnType("varchar(32)");

                    b.Property<decimal?>("FinalPaymentAmount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("FinalPaymentCurrencyCode")
                        .HasColumnType("varchar(50)");

                    b.Property<sbyte?>("FinalPaymentPayType")
                        .HasColumnType("tinyint");

                    b.Property<decimal?>("FinalPaymentRatio")
                        .HasColumnType("decimal(18,2)");

                    b.Property<DateTime?>("FinalPaymentTime")
                        .HasColumnType("datetime(6)");

                    b.Property<DateTime?>("FinishTime")
                        .HasColumnType("datetime(6)");

                    b.Property<bool>("HasDownPayment")
                        .HasColumnType("tinyint(1)");

                    b.Property<bool>("HasFinalPayment")
                        .HasColumnType("tinyint(1)");

                    b.Property<string>("HotelName")
                        .HasColumnType("varchar(256)");

                    b.Property<DateTime?>("HotelRecoveryTime")
                        .HasColumnType("datetime(6)");

                    b.Property<decimal?>("InitialPaymentAmount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("InitialPaymentCurrencyCode")
                        .HasColumnType("varchar(50)");

                    b.Property<sbyte?>("InitialPaymentPayType")
                        .HasColumnType("tinyint");

                    b.Property<decimal?>("InitialPaymentRatio")
                        .HasColumnType("decimal(18,2)");

                    b.Property<DateTime?>("InitialPaymentTime")
                        .HasColumnType("datetime(6)");

                    b.Property<decimal?>("MeetingBudget")
                        .HasColumnType("decimal(18,2)");

                    b.Property<int?>("MeetingsNum")
                        .HasColumnType("int");

                    b.Property<bool>("NeedHotelRoom")
                        .HasColumnType("tinyint(1)");

                    b.Property<bool>("NeedMeetingRoom")
                        .HasColumnType("tinyint(1)");

                    b.Property<long?>("OperatorUserId")
                        .HasColumnType("bigint");

                    b.Property<DateTime?>("PreOrderedTime")
                        .HasColumnType("datetime(6)");

                    b.Property<long?>("PromotionTraceId")
                        .HasColumnType("bigint");

                    b.Property<int?>("ProvinceCode")
                        .HasColumnType("int");

                    b.Property<string>("ProvinceName")
                        .HasColumnType("varchar(50)");

                    b.Property<DateTime?>("QuotationConfirmedTime")
                        .HasColumnType("datetime(6)");

                    b.Property<DateTime?>("QuotedTime")
                        .HasColumnType("datetime(6)");

                    b.Property<long?>("ResourceHotelId")
                        .HasColumnType("bigint");

                    b.Property<long?>("SalespersonId")
                        .HasColumnType("bigint");

                    b.Property<string>("SalespersonName")
                        .HasColumnType("varchar(256)");

                    b.Property<sbyte>("Status")
                        .HasColumnType("tinyint");

                    b.Property<sbyte?>("TeamNatureType")
                        .HasColumnType("tinyint");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<decimal?>("ToHotelMeetingBudget")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("ToHotelUnitPrice")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("TotalAmount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<DateTime?>("UnableQuotedTime")
                        .HasColumnType("datetime(6)");

                    b.Property<decimal?>("UnitPrice")
                        .HasColumnType("decimal(18,2)");

                    b.Property<DateTime>("UpdateTime")
                        .HasColumnType("datetime");

                    b.Property<long>("UserId")
                        .HasColumnType("bigint");

                    b.Property<string>("UserName")
                        .IsRequired()
                        .HasColumnType("varchar(50)");

                    b.Property<sbyte>("UserPlatform")
                        .HasColumnType("tinyint");

                    b.Property<sbyte>("UserType")
                        .HasColumnType("tinyint");

                    b.Property<DateTime?>("WaitForAuditPreOrderTime")
                        .HasColumnType("datetime(6)");

                    b.Property<DateTime?>("WaitForInquiryTime")
                        .HasColumnType("datetime(6)");

                    b.HasKey("Id");

                    b.HasIndex("ApplicationFormId");

                    b.HasIndex("ApplicationTime");

                    b.HasIndex("InitialPaymentTime");

                    b.HasIndex("TenantId");

                    b.ToTable("GroupBookingAggregate", (string)null);
                });

            modelBuilder.Entity("Order.Api.Model.GroupBookingAggregateOrderDetail", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<sbyte>("ApplicantUserType")
                        .HasColumnType("tinyint");

                    b.Property<long>("ApplicationFormId")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("ApplicationTime")
                        .HasColumnType("datetime");

                    b.Property<long>("BaseOrderId")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("CheckInDate")
                        .HasColumnType("datetime");

                    b.Property<DateTime>("CheckOutDate")
                        .HasColumnType("datetime");

                    b.Property<int?>("CityCode")
                        .HasColumnType("int");

                    b.Property<string>("CityName")
                        .HasColumnType("varchar(50)");

                    b.Property<decimal>("CostDiscountAmount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<int?>("CountryCode")
                        .HasColumnType("int");

                    b.Property<string>("CountryName")
                        .HasColumnType("varchar(50)");

                    b.Property<decimal>("DiscountAmount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<DateTime?>("FinalPaymentTime")
                        .HasColumnType("datetime");

                    b.Property<long>("GroupBookingOrderId")
                        .HasColumnType("bigint");

                    b.Property<long>("GroupBookingOrderItemId")
                        .HasColumnType("bigint");

                    b.Property<long>("GroupBookingPreOrderItemId")
                        .HasColumnType("bigint");

                    b.Property<bool>("HasDownPayment")
                        .HasColumnType("tinyint(1)");

                    b.Property<bool>("HasFinalPayment")
                        .HasColumnType("tinyint(1)");

                    b.Property<long>("HotelId")
                        .HasColumnType("bigint");

                    b.Property<long>("HotelOrderId")
                        .HasColumnType("bigint");

                    b.Property<string>("HotelZHName")
                        .HasColumnType("varchar(100)");

                    b.Property<DateTime?>("InitialPaymentTime")
                        .HasColumnType("datetime");

                    b.Property<decimal>("PaymentAmount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("PaymentCurrencyCode")
                        .IsRequired()
                        .HasColumnType("varchar(20)");

                    b.Property<int>("PaymentType")
                        .HasColumnType("int");

                    b.Property<int?>("ProvinceCode")
                        .HasColumnType("int");

                    b.Property<string>("ProvinceName")
                        .HasColumnType("varchar(50)");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<decimal>("TotalAmount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<sbyte>("UserPlatform")
                        .HasColumnType("tinyint");

                    b.HasKey("Id");

                    b.HasIndex("ApplicationFormId");

                    b.HasIndex("ApplicationTime");

                    b.HasIndex("GroupBookingOrderId");

                    b.HasIndex("InitialPaymentTime");

                    b.HasIndex("TenantId");

                    b.ToTable("GroupBookingAggregateOrderDetail", (string)null);
                });

            modelBuilder.Entity("Order.Api.Model.GroupBookingApplicationDemand", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<int?>("AdultNum")
                        .HasColumnType("int");

                    b.Property<long>("ApplicationFormId")
                        .HasColumnType("bigint");

                    b.Property<string>("BedTypeJson")
                        .HasColumnType("varchar(500)");

                    b.Property<DateTime?>("CheckInDate")
                        .HasColumnType("datetime");

                    b.Property<DateTime?>("CheckOutDate")
                        .HasColumnType("datetime");

                    b.Property<string>("ChildDesc")
                        .HasColumnType("varchar(128)");

                    b.Property<int?>("ChildNum")
                        .HasColumnType("int");

                    b.Property<int?>("CityCode")
                        .HasColumnType("int");

                    b.Property<string>("CityName")
                        .HasColumnType("varchar(50)");

                    b.Property<int?>("CountryCode")
                        .HasColumnType("int");

                    b.Property<string>("CountryName")
                        .HasColumnType("varchar(50)");

                    b.Property<string>("CurrencyCode")
                        .HasColumnType("varchar(32)");

                    b.Property<string>("ENOtherRequirements")
                        .HasColumnType("varchar(500)");

                    b.Property<string>("HotelName")
                        .HasColumnType("varchar(256)");

                    b.Property<decimal?>("MeetingBudget")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("MeetingRequirement")
                        .HasColumnType("varchar(500)");

                    b.Property<string>("MeetingTimeJson")
                        .HasColumnType("varchar(500)");

                    b.Property<int?>("MeetingsNum")
                        .HasColumnType("int");

                    b.Property<bool>("NeedHotelRoom")
                        .HasColumnType("tinyint(1)");

                    b.Property<bool>("NeedMeetingRoom")
                        .HasColumnType("tinyint(1)");

                    b.Property<bool?>("NeedRecommendHotel")
                        .HasColumnType("tinyint(1)");

                    b.Property<string>("OtherRequirement")
                        .HasColumnType("varchar(500)");

                    b.Property<int?>("ProvinceCode")
                        .HasColumnType("int");

                    b.Property<string>("ProvinceName")
                        .HasColumnType("varchar(50)");

                    b.Property<long?>("ResourceHotelId")
                        .HasColumnType("bigint");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<decimal?>("ToHotelMeetingBudget")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("ToHotelUnitPrice")
                        .HasColumnType("decimal(18,2)");

                    b.Property<sbyte>("TravelType")
                        .HasColumnType("tinyint");

                    b.Property<decimal?>("UnitPrice")
                        .HasColumnType("decimal(18,2)");

                    b.HasKey("Id");

                    b.HasIndex("ApplicationFormId");

                    b.HasIndex("TenantId");

                    b.ToTable("GroupBookingApplicationDemand", (string)null);
                });

            modelBuilder.Entity("Order.Api.Model.GroupBookingApplicationDemandHotel", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<long>("ApplicationDemandId")
                        .HasColumnType("bigint");

                    b.Property<string>("Assignor")
                        .HasColumnType("varchar(50)");

                    b.Property<long?>("AssignorId")
                        .HasColumnType("bigint");

                    b.Property<bool>("Enabled")
                        .HasColumnType("tinyint(1)");

                    b.Property<string>("HotelENName")
                        .IsRequired()
                        .HasColumnType("varchar(256)");

                    b.Property<string>("HotelName")
                        .HasColumnType("varchar(256)");

                    b.Property<bool>("IsRecommend")
                        .HasColumnType("tinyint(1)");

                    b.Property<long>("ResourceHotelId")
                        .HasColumnType("bigint");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("ApplicationDemandId");

                    b.HasIndex("TenantId");

                    b.ToTable("GroupBookingApplicationDemandHotel", (string)null);
                });

            modelBuilder.Entity("Order.Api.Model.GroupBookingApplicationDemandItem", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<long>("ApplicationDemandId")
                        .HasColumnType("bigint");

                    b.Property<string>("Breakfast")
                        .HasColumnType("varchar(100)");

                    b.Property<sbyte?>("CateringType")
                        .HasColumnType("tinyint");

                    b.Property<DateTime>("Date")
                        .HasColumnType("datetime");

                    b.Property<string>("Description")
                        .HasColumnType("varchar(256)");

                    b.Property<string>("ENDescription")
                        .HasColumnType("varchar(256)");

                    b.Property<TimeSpan?>("FormTime")
                        .HasColumnType("time");

                    b.Property<sbyte>("ItemType")
                        .HasColumnType("tinyint");

                    b.Property<int>("Quantity")
                        .HasColumnType("int");

                    b.Property<sbyte>("SubItemType")
                        .HasColumnType("tinyint");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<TimeSpan?>("ToTime")
                        .HasColumnType("time");

                    b.HasKey("Id");

                    b.HasIndex("ApplicationDemandId");

                    b.HasIndex("TenantId");

                    b.ToTable("GroupBookingApplicationDemandItem", (string)null);
                });

            modelBuilder.Entity("Order.Api.Model.GroupBookingApplicationForm", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<bool>("AcceptRecommendedHotels")
                        .HasColumnType("tinyint(1)");

                    b.Property<long>("AgencyId")
                        .HasColumnType("bigint");

                    b.Property<string>("AgencyName")
                        .IsRequired()
                        .HasColumnType("varchar(256)");

                    b.Property<string>("Applicant")
                        .HasColumnType("varchar(256)");

                    b.Property<long?>("ApplicantId")
                        .HasColumnType("bigint");

                    b.Property<sbyte>("ApplicantUserType")
                        .HasColumnType("tinyint");

                    b.Property<DateTime>("ApplicationTime")
                        .HasColumnType("datetime");

                    b.Property<string>("Assignor")
                        .HasColumnType("varchar(50)");

                    b.Property<long?>("AssignorId")
                        .HasColumnType("bigint");

                    b.Property<string>("CompanyName")
                        .HasColumnType("varchar(200)");

                    b.Property<string>("ContactEmail")
                        .HasColumnType("varchar(50)");

                    b.Property<string>("ContactName")
                        .HasColumnType("varchar(50)");

                    b.Property<string>("ContactPhone")
                        .IsRequired()
                        .HasColumnType("varchar(64)");

                    b.Property<string>("ContractContent")
                        .HasColumnType("mediumtext");

                    b.Property<bool?>("ContractSigned")
                        .HasColumnType("tinyint(1)");

                    b.Property<string>("CountryDialCode")
                        .HasColumnType("varchar(10)");

                    b.Property<string>("ENOtherRequirements")
                        .HasColumnType("varchar(500)");

                    b.Property<string>("EnTeamNatureTitle")
                        .HasColumnType("varchar(100)");

                    b.Property<DateTime?>("FinishTime")
                        .HasColumnType("datetime");

                    b.Property<long?>("OperatorUserId")
                        .HasColumnType("bigint");

                    b.Property<string>("OtherRequirements")
                        .HasColumnType("varchar(500)");

                    b.Property<long?>("PromotionTraceId")
                        .HasColumnType("bigint");

                    b.Property<long?>("SalespersonId")
                        .HasColumnType("bigint");

                    b.Property<string>("SalespersonName")
                        .HasColumnType("varchar(256)");

                    b.Property<sbyte>("Status")
                        .IsConcurrencyToken()
                        .HasColumnType("tinyint");

                    b.Property<string>("TeamNatureTitle")
                        .HasColumnType("varchar(50)");

                    b.Property<sbyte?>("TeamNatureType")
                        .HasColumnType("tinyint");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime");

                    b.Property<long>("UserId")
                        .HasColumnType("bigint");

                    b.Property<string>("UserName")
                        .IsRequired()
                        .HasColumnType("varchar(50)");

                    b.Property<sbyte>("UserPlatform")
                        .HasColumnType("tinyint");

                    b.Property<sbyte>("UserType")
                        .HasColumnType("tinyint");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("GroupBookingApplicationForm", (string)null);
                });

            modelBuilder.Entity("Order.Api.Model.GroupBookingApplicationOrder", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<int>("AdultNum")
                        .HasColumnType("int");

                    b.Property<long>("AgencyId")
                        .HasColumnType("bigint");

                    b.Property<string>("AgencyName")
                        .IsRequired()
                        .HasColumnType("varchar(256)");

                    b.Property<string>("Applicant")
                        .HasColumnType("varchar(100)");

                    b.Property<long?>("ApplicantId")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("ApplicationTime")
                        .HasColumnType("datetime");

                    b.Property<string>("BaseOrderId")
                        .HasColumnType("varchar(200)");

                    b.Property<DateTime>("CheckInDate")
                        .HasColumnType("datetime");

                    b.Property<DateTime>("CheckOutDate")
                        .HasColumnType("datetime");

                    b.Property<int>("ChildNum")
                        .HasColumnType("int");

                    b.Property<int>("CityCode")
                        .HasColumnType("int");

                    b.Property<string>("CityName")
                        .IsRequired()
                        .HasColumnType("varchar(50)");

                    b.Property<string>("ContactPhone")
                        .IsRequired()
                        .HasColumnType("varchar(50)");

                    b.Property<int>("CountryCode")
                        .HasColumnType("int");

                    b.Property<string>("CountryName")
                        .IsRequired()
                        .HasColumnType("varchar(50)");

                    b.Property<string>("CurrencyCode")
                        .IsRequired()
                        .HasColumnType("varchar(20)");

                    b.Property<int>("DoubleBedNum")
                        .HasColumnType("int");

                    b.Property<DateTime>("FinishTime")
                        .HasColumnType("datetime");

                    b.Property<string>("HotelName")
                        .HasColumnType("varchar(200)");

                    b.Property<bool>("IncludeBreakfast")
                        .HasColumnType("tinyint(1)");

                    b.Property<string>("Nationalities")
                        .IsRequired()
                        .HasColumnType("varchar(200)");

                    b.Property<string>("Operator")
                        .HasColumnType("varchar(100)");

                    b.Property<long?>("OperatorId")
                        .HasColumnType("bigint");

                    b.Property<string>("OtherRequirements")
                        .HasColumnType("varchar(500)");

                    b.Property<int?>("PriceStrategyCount")
                        .HasColumnType("int");

                    b.Property<int>("ProvinceCode")
                        .HasColumnType("int");

                    b.Property<string>("ProvinceName")
                        .IsRequired()
                        .HasColumnType("varchar(50)");

                    b.Property<int>("QueenBedNum")
                        .HasColumnType("int");

                    b.Property<string>("Remark")
                        .HasColumnType("varchar(500)");

                    b.Property<sbyte>("Status")
                        .HasColumnType("tinyint");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<decimal>("UnitPrice")
                        .HasColumnType("decimal(18,2)");

                    b.Property<DateTime>("UpdateTime")
                        .HasColumnType("datetime");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("GroupBookingApplicationOrder", (string)null);
                });

            modelBuilder.Entity("Order.Api.Model.GroupBookingApplicationSuggestItem", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<long>("ApplicationOrderId")
                        .HasColumnType("bigint");

                    b.Property<int>("CityCode")
                        .HasColumnType("int");

                    b.Property<string>("CityName")
                        .IsRequired()
                        .HasColumnType("varchar(50)");

                    b.Property<long>("HotelId")
                        .HasColumnType("bigint");

                    b.Property<string>("HotelName")
                        .IsRequired()
                        .HasColumnType("varchar(100)");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("GroupBookingApplicationSuggestItem", (string)null);
                });

            modelBuilder.Entity("Order.Api.Model.GroupBookingAreaSetting", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("varchar(100)");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<sbyte>("Type")
                        .HasColumnType("tinyint");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("GroupBookingAreaSetting", (string)null);
                });

            modelBuilder.Entity("Order.Api.Model.GroupBookingAreaSettingDetail", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<int>("CountryCode")
                        .HasColumnType("int");

                    b.Property<string>("CountryName")
                        .IsRequired()
                        .HasColumnType("varchar(100)");

                    b.Property<long>("GroupBookingAreaSettingId")
                        .HasColumnType("bigint");

                    b.Property<int?>("ProvinceCode")
                        .HasColumnType("int");

                    b.Property<string>("ProvinceName")
                        .HasColumnType("varchar(100)");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("GroupBookingAreaSettingDetail", (string)null);
                });

            modelBuilder.Entity("Order.Api.Model.GroupBookingFinancialHandleOrder", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<DateTime?>("AccountTime")
                        .HasColumnType("datetime");

                    b.Property<long>("AgencyId")
                        .HasColumnType("bigint");

                    b.Property<string>("AgencyName")
                        .IsRequired()
                        .HasColumnType("varchar(256)");

                    b.Property<decimal>("Amount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<long>("ApplicationFormId")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<string>("Creator")
                        .HasColumnType("varchar(100)");

                    b.Property<long?>("CreatorId")
                        .HasColumnType("bigint");

                    b.Property<string>("CurrencyCode")
                        .IsRequired()
                        .HasColumnType("varchar(50)");

                    b.Property<DateTime?>("FinishTime")
                        .HasColumnType("datetime");

                    b.Property<long>("GroupBookingOrderId")
                        .HasColumnType("bigint");

                    b.Property<long>("GroupBookingOrderPaymentId")
                        .HasColumnType("bigint");

                    b.Property<string>("Operator")
                        .HasColumnType("varchar(100)");

                    b.Property<long?>("OperatorId")
                        .HasColumnType("bigint");

                    b.Property<sbyte?>("PayType")
                        .HasColumnType("tinyint");

                    b.Property<string>("Proof")
                        .HasColumnType("varchar(256)");

                    b.Property<string>("RejectReason")
                        .HasColumnType("varchar(256)");

                    b.Property<string>("Remark")
                        .HasColumnType("varchar(500)");

                    b.Property<sbyte>("Status")
                        .HasColumnType("tinyint");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("GroupBookingFinancialHandleOrder", (string)null);
                });

            modelBuilder.Entity("Order.Api.Model.GroupBookingHotelInquiry", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<long>("ApplicationDemandId")
                        .HasColumnType("bigint");

                    b.Property<long>("ApplicationFormId")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<long>("DemandHoteId")
                        .HasColumnType("bigint");

                    b.Property<string>("Email")
                        .HasColumnType("varchar(100)");

                    b.Property<DateTime?>("ExpiredTime")
                        .HasColumnType("datetime");

                    b.Property<string>("FirstName")
                        .HasColumnType("varchar(100)");

                    b.Property<sbyte?>("MailStatus")
                        .HasColumnType("tinyint");

                    b.Property<long>("ResourceHotelId")
                        .HasColumnType("bigint");

                    b.Property<sbyte>("Status")
                        .IsConcurrencyToken()
                        .HasColumnType("tinyint");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<string>("UnableToQuoteReason")
                        .HasColumnType("varchar(256)");

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime");

                    b.HasKey("Id");

                    b.HasIndex("ApplicationFormId");

                    b.HasIndex("TenantId");

                    b.ToTable("GroupBookingHotelInquiry", (string)null);
                });

            modelBuilder.Entity("Order.Api.Model.GroupBookingHotelInquiryRecord", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<long>("ApplicationDemandId")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<string>("CurrencyCode")
                        .HasColumnType("varchar(32)");

                    b.Property<long>("GroupBookingHotelInquiryId")
                        .HasColumnType("bigint");

                    b.Property<decimal?>("MeetingBudget")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("OtherRequirement")
                        .HasColumnType("varchar(1000)");

                    b.Property<DateTime?>("SendTime")
                        .HasColumnType("datetime");

                    b.Property<string>("TeamNatureTitle")
                        .HasColumnType("varchar(128)");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<decimal?>("UnitPrice")
                        .HasColumnType("decimal(18,2)");

                    b.Property<int>("ValidityPeriod")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.HasIndex("GroupBookingHotelInquiryId", "ApplicationDemandId");

                    b.ToTable("GroupBookingHotelInquiryRecord", (string)null);
                });

            modelBuilder.Entity("Order.Api.Model.GroupBookingHotellQuotationAddition", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<string>("AdditionName")
                        .IsRequired()
                        .HasColumnType("varchar(100)");

                    b.Property<decimal>("Cost")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("CostCurrencyCode")
                        .IsRequired()
                        .HasColumnType("varchar(32)");

                    b.Property<long>("HotelQuotationId")
                        .HasColumnType("bigint");

                    b.Property<int>("Quantity")
                        .HasColumnType("int");

                    b.Property<string>("Remark")
                        .IsRequired()
                        .HasColumnType("varchar(500)");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("HotelQuotationId");

                    b.HasIndex("TenantId");

                    b.ToTable("GroupBookingHotellQuotationAddition", (string)null);
                });

            modelBuilder.Entity("Order.Api.Model.GroupBookingHotellQuotationItem", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<decimal>("AverageCostPrice")
                        .HasColumnType("decimal(18,2)");

                    b.Property<sbyte>("BedType")
                        .HasColumnType("tinyint");

                    b.Property<string>("BedTypeName")
                        .IsRequired()
                        .HasColumnType("varchar(256)");

                    b.Property<int>("Breakfast")
                        .HasColumnType("int");

                    b.Property<string>("CostCurrencyCode")
                        .IsRequired()
                        .HasColumnType("varchar(32)");

                    b.Property<long>("HotelQuotationId")
                        .HasColumnType("bigint");

                    b.Property<int>("RoomCount")
                        .HasColumnType("int");

                    b.Property<string>("RoomName")
                        .IsRequired()
                        .HasColumnType("varchar(256)");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("HotelQuotationId");

                    b.HasIndex("TenantId");

                    b.ToTable("GroupBookingHotellQuotationItem", (string)null);
                });

            modelBuilder.Entity("Order.Api.Model.GroupBookingHotelManualRecord", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<long>("ApplicationDemandId")
                        .HasColumnType("bigint");

                    b.Property<long>("ApplicationFormId")
                        .HasColumnType("bigint");

                    b.Property<string>("Content")
                        .IsRequired()
                        .HasColumnType("varchar(500)");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<long>("ResourceHotelId")
                        .HasColumnType("bigint");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<long>("UserId")
                        .HasColumnType("bigint");

                    b.Property<string>("UserName")
                        .IsRequired()
                        .HasColumnType("varchar(50)");

                    b.Property<sbyte>("UserType")
                        .HasColumnType("tinyint");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.HasIndex("ApplicationFormId", "ApplicationDemandId", "ResourceHotelId");

                    b.ToTable("GroupBookingHotelManualRecord", (string)null);
                });

            modelBuilder.Entity("Order.Api.Model.GroupBookingHotelQuotation", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<long>("ApplicationFormId")
                        .HasColumnType("bigint");

                    b.Property<string>("CancellationPolicy")
                        .IsRequired()
                        .HasColumnType("varchar(500)");

                    b.Property<DateTime>("CheckInDate")
                        .HasColumnType("datetime");

                    b.Property<DateTime>("CheckOutDate")
                        .HasColumnType("datetime");

                    b.Property<string>("ChildPolicy")
                        .HasColumnType("varchar(500)");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<long>("HotelInquiryId")
                        .HasColumnType("bigint");

                    b.Property<string>("OfferValidity")
                        .HasColumnType("varchar(500)");

                    b.Property<string>("OtherDescription")
                        .HasColumnType("varchar(500)");

                    b.Property<string>("PaymentPolicy")
                        .HasColumnType("varchar(500)");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("ApplicationFormId");

                    b.HasIndex("TenantId");

                    b.ToTable("GroupBookingHotelQuotation", (string)null);
                });

            modelBuilder.Entity("Order.Api.Model.GroupBookingHotelTemplate", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<bool>("Enabled")
                        .HasColumnType("tinyint(1)");

                    b.Property<bool>("IsAuto")
                        .HasColumnType("tinyint(1)");

                    b.Property<string>("LinkPrompt")
                        .HasColumnType("varchar(256)");

                    b.Property<string>("Prompt")
                        .HasColumnType("varchar(256)");

                    b.Property<string>("ReplyPrompt")
                        .HasColumnType("varchar(256)");

                    b.Property<DateTime>("UpdateTime")
                        .HasColumnType("datetime");

                    b.Property<string>("Updater")
                        .IsRequired()
                        .HasColumnType("varchar(256)");

                    b.Property<long>("UpdaterId")
                        .HasColumnType("bigint");

                    b.Property<int>("ValidityPeriod")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.ToTable("GroupBookingHotelTemplate", (string)null);
                });

            modelBuilder.Entity("Order.Api.Model.GroupBookingHotelTemplateItem", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<string>("Description")
                        .HasColumnType("mediumtext");

                    b.Property<long>("GroupBookingHotelTemplateId")
                        .HasColumnType("bigint");

                    b.Property<sbyte>("TemplateItemType")
                        .HasColumnType("tinyint");

                    b.Property<string>("Title")
                        .HasColumnType("varchar(500)");

                    b.HasKey("Id");

                    b.ToTable("GroupBookingHotelTemplateItem", (string)null);
                });

            modelBuilder.Entity("Order.Api.Model.GroupBookingHotelTemplateRegion", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<int>("CountryCode")
                        .HasColumnType("int");

                    b.Property<string>("CouuntryName")
                        .IsRequired()
                        .HasColumnType("varchar(50)");

                    b.Property<long>("GroupBookingHotelTemplateId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.ToTable("GroupBookingHotelTemplateRegion", (string)null);
                });

            modelBuilder.Entity("Order.Api.Model.GroupBookingInquiryForm", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<long>("ApplicationFormId")
                        .HasColumnType("bigint");

                    b.Property<decimal>("BudgetAmount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<DateTime>("CheckIn")
                        .HasColumnType("datetime");

                    b.Property<DateTime>("CheckOut")
                        .HasColumnType("datetime");

                    b.Property<int>("CityCode")
                        .HasColumnType("int");

                    b.Property<string>("CityName")
                        .IsRequired()
                        .HasColumnType("varchar(50)");

                    b.Property<int>("CountryCode")
                        .HasColumnType("int");

                    b.Property<string>("CountryName")
                        .IsRequired()
                        .HasColumnType("varchar(50)");

                    b.Property<string>("CurrencyCode")
                        .IsRequired()
                        .HasColumnType("varchar(32)");

                    b.Property<int>("DoubleBedNum")
                        .HasColumnType("int");

                    b.Property<bool>("IncludeBreakfast")
                        .HasColumnType("tinyint(1)");

                    b.Property<int>("ProvinceCode")
                        .HasColumnType("int");

                    b.Property<string>("ProvinceName")
                        .IsRequired()
                        .HasColumnType("varchar(50)");

                    b.Property<int>("QueenBedNum")
                        .HasColumnType("int");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("GroupBookingInquiryForm", (string)null);
                });

            modelBuilder.Entity("Order.Api.Model.GroupBookingOperationLog", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<long>("ApplicationFormId")
                        .HasColumnType("bigint");

                    b.Property<string>("CompanyName")
                        .HasColumnType("varchar(256)");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("varchar(256)");

                    b.Property<string>("OperationReasonType")
                        .HasColumnType("varchar(100)");

                    b.Property<sbyte>("OperationType")
                        .HasColumnType("tinyint");

                    b.Property<sbyte>("OriginalStatus")
                        .HasColumnType("tinyint");

                    b.Property<string>("Remark")
                        .HasColumnType("varchar(500)");

                    b.Property<sbyte>("Status")
                        .HasColumnType("tinyint");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<long>("UserId")
                        .HasColumnType("bigint");

                    b.Property<sbyte>("UserType")
                        .HasColumnType("tinyint");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("GroupBookingOperationLog", (string)null);
                });

            modelBuilder.Entity("Order.Api.Model.GroupBookingOrder", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<long>("AgencyId")
                        .HasColumnType("bigint");

                    b.Property<string>("AgencyName")
                        .HasColumnType("varchar(256)");

                    b.Property<string>("ChannelOrderNo")
                        .HasColumnType("varchar(512)");

                    b.Property<DateTime>("CheckInDate")
                        .HasColumnType("datetime");

                    b.Property<DateTime>("CheckOutDate")
                        .HasColumnType("datetime");

                    b.Property<int>("CityCode")
                        .HasColumnType("int");

                    b.Property<string>("CityName")
                        .IsRequired()
                        .HasColumnType("varchar(50)");

                    b.Property<string>("ContactsEmail")
                        .HasColumnType("varchar(50)");

                    b.Property<string>("ContactsName")
                        .IsRequired()
                        .HasColumnType("varchar(50)");

                    b.Property<string>("ContactsPhoneNumber")
                        .IsRequired()
                        .HasColumnType("varchar(50)");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<long>("GroupBookingApplicationFormId")
                        .HasColumnType("bigint");

                    b.Property<long>("GroupBookingPreOrderId")
                        .HasColumnType("bigint");

                    b.Property<string>("GroupNo")
                        .HasColumnType("varchar(32)");

                    b.Property<string>("HotelENName")
                        .IsRequired()
                        .HasColumnType("varchar(100)");

                    b.Property<string>("HotelOrderGuestFilePath")
                        .HasColumnType("varchar(256)");

                    b.Property<sbyte>("HotelOrderStatus")
                        .IsConcurrencyToken()
                        .HasColumnType("tinyint");

                    b.Property<string>("HotelZHName")
                        .IsRequired()
                        .HasColumnType("varchar(100)");

                    b.Property<string>("Message")
                        .HasColumnType("varchar(500)");

                    b.Property<string>("PaymentCurrencyCode")
                        .IsRequired()
                        .HasColumnType("varchar(32)");

                    b.Property<long>("ResourceHotelId")
                        .HasColumnType("bigint");

                    b.Property<long?>("SalespersonId")
                        .HasColumnType("bigint");

                    b.Property<string>("SalespersonName")
                        .HasColumnType("varchar(50)");

                    b.Property<short>("SellingChannels")
                        .HasColumnType("smallint");

                    b.Property<sbyte>("SellingPlatform")
                        .HasColumnType("tinyint");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<decimal>("TotalPayment")
                        .HasColumnType("decimal(18,2)");

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime");

                    b.Property<long>("UserId")
                        .HasColumnType("bigint");

                    b.Property<string>("UserNickName")
                        .HasColumnType("varchar(50)");

                    b.Property<long>("VipLevelId")
                        .HasColumnType("bigint");

                    b.Property<string>("VipLevelName")
                        .HasColumnType("varchar(20)");

                    b.HasKey("Id");

                    b.HasIndex("GroupBookingApplicationFormId");

                    b.HasIndex("TenantId");

                    b.ToTable("GroupBookingOrder", (string)null);
                });

            modelBuilder.Entity("Order.Api.Model.GroupBookingOrderAddition", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<string>("AdditionName")
                        .IsRequired()
                        .HasColumnType("varchar(100)");

                    b.Property<decimal>("Amount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<long>("BaseOrderId")
                        .HasColumnType("bigint");

                    b.Property<decimal>("Cost")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("CostCurrencyCode")
                        .IsRequired()
                        .HasColumnType("varchar(64)");

                    b.Property<string>("CurrencyCode")
                        .IsRequired()
                        .HasColumnType("varchar(64)");

                    b.Property<long>("GroupBookingOrderId")
                        .HasColumnType("bigint");

                    b.Property<long>("GroupBookingOrderItemId")
                        .HasColumnType("bigint");

                    b.Property<int>("Quantity")
                        .HasColumnType("int");

                    b.Property<string>("Remark")
                        .IsRequired()
                        .HasColumnType("varchar(500)");

                    b.Property<long?>("SupplierId")
                        .HasColumnType("bigint");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("GroupBookingOrderAddition", (string)null);
                });

            modelBuilder.Entity("Order.Api.Model.GroupBookingOrderItem", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<long>("BaseOrderId")
                        .HasColumnType("bigint");

                    b.Property<string>("BedType")
                        .HasColumnType("varchar(1000)");

                    b.Property<DateTime>("CheckInDate")
                        .HasColumnType("datetime");

                    b.Property<DateTime>("CheckOutDate")
                        .HasColumnType("datetime");

                    b.Property<int>("CityCode")
                        .HasColumnType("int");

                    b.Property<string>("CityName")
                        .IsRequired()
                        .HasColumnType("varchar(50)");

                    b.Property<long>("GroupBookingOrderId")
                        .HasColumnType("bigint");

                    b.Property<long>("GroupBookingPreOrderItemId")
                        .HasColumnType("bigint");

                    b.Property<string>("HotelENName")
                        .IsRequired()
                        .HasColumnType("varchar(100)");

                    b.Property<long>("HotelId")
                        .HasColumnType("bigint");

                    b.Property<long>("HotelOrderId")
                        .HasColumnType("bigint");

                    b.Property<sbyte>("HotelOrderStatus")
                        .HasColumnType("tinyint");

                    b.Property<string>("HotelRoomEnName")
                        .HasColumnType("varchar(100)");

                    b.Property<long>("HotelRoomId")
                        .HasColumnType("bigint");

                    b.Property<string>("HotelRoomName")
                        .HasColumnType("varchar(100)");

                    b.Property<string>("HotelZHName")
                        .IsRequired()
                        .HasColumnType("varchar(100)");

                    b.Property<int>("MaximumOccupancy")
                        .HasColumnType("int");

                    b.Property<int>("NumberOfBreakfast")
                        .HasColumnType("int");

                    b.Property<string>("PriceStrategyEnName")
                        .HasColumnType("varchar(100)");

                    b.Property<string>("PriceStrategyId")
                        .IsRequired()
                        .HasColumnType("varchar(64)");

                    b.Property<string>("PriceStrategyName")
                        .HasColumnType("varchar(100)");

                    b.Property<long>("ResourceHotelId")
                        .HasColumnType("bigint");

                    b.Property<int>("RoomCount")
                        .HasColumnType("int");

                    b.Property<sbyte>("SupplierApiType")
                        .HasColumnType("tinyint");

                    b.Property<long?>("SupplierId")
                        .HasColumnType("bigint");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("GroupBookingOrderId");

                    b.HasIndex("TenantId");

                    b.ToTable("GroupBookingOrderItem", (string)null);
                });

            modelBuilder.Entity("Order.Api.Model.GroupBookingOrderPayment", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<long>("GroupBookingOrderId")
                        .HasColumnType("bigint");

                    b.Property<sbyte?>("HandleOrderStatus")
                        .HasColumnType("tinyint");

                    b.Property<DateTime>("LatestPaymentTime")
                        .HasColumnType("datetime");

                    b.Property<sbyte>("PayStatus")
                        .HasColumnType("tinyint");

                    b.Property<sbyte?>("PayType")
                        .HasColumnType("tinyint");

                    b.Property<decimal>("PaymentAmount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("PaymentCurrencyCode")
                        .IsRequired()
                        .HasColumnType("varchar(64)");

                    b.Property<string>("PaymentExternalNo")
                        .IsRequired()
                        .HasColumnType("varchar(128)");

                    b.Property<decimal>("PaymentRatio")
                        .HasColumnType("decimal(18,2)");

                    b.Property<sbyte>("PaymentRatioType")
                        .HasColumnType("tinyint");

                    b.Property<DateTime?>("PaymentTime")
                        .HasColumnType("datetime");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("GroupBookingOrderPayment", (string)null);
                });

            modelBuilder.Entity("Order.Api.Model.GroupBookingOrderPaymentAdjust", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<decimal>("AdjustAmount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal>("AfterPaymentAmount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal>("BeforePaymentAmount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<long>("GroupBookingOrderId")
                        .HasColumnType("bigint");

                    b.Property<long>("GroupBookingOrderPaymentId")
                        .HasColumnType("bigint");

                    b.Property<string>("Operator")
                        .HasColumnType("varchar(200)");

                    b.Property<long?>("OperatorId")
                        .HasColumnType("bigint");

                    b.Property<string>("PaymentCurrencyCode")
                        .IsRequired()
                        .HasColumnType("varchar(32)");

                    b.Property<string>("Remark")
                        .HasColumnType("varchar(256)");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("GroupBookingOrderId");

                    b.HasIndex("GroupBookingOrderPaymentId");

                    b.HasIndex("TenantId");

                    b.ToTable("GroupBookingOrderPaymentAdjust", (string)null);
                });

            modelBuilder.Entity("Order.Api.Model.GroupBookingPreOrder", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<long>("ApplicationFormId")
                        .HasColumnType("bigint");

                    b.Property<string>("CancellationPolicy")
                        .IsRequired()
                        .HasColumnType("varchar(500)");

                    b.Property<DateTime>("CheckIn")
                        .HasColumnType("datetime");

                    b.Property<DateTime>("CheckOut")
                        .HasColumnType("datetime");

                    b.Property<string>("ChildPolicy")
                        .HasColumnType("varchar(500)");

                    b.Property<bool>("Enabled")
                        .HasColumnType("tinyint(1)");

                    b.Property<decimal>("FinalPaymentAmount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<DateTime>("FinalPaymentTime")
                        .HasColumnType("datetime");

                    b.Property<bool>("HasDownPayment")
                        .HasColumnType("tinyint(1)");

                    b.Property<bool>("HasFinalPayment")
                        .HasColumnType("tinyint(1)");

                    b.Property<decimal>("InitialPaymentAmount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal>("InitialPaymentRatio")
                        .HasColumnType("decimal(18,2)");

                    b.Property<sbyte>("OrderStatus")
                        .IsConcurrencyToken()
                        .HasColumnType("tinyint");

                    b.Property<long>("QuotationId")
                        .HasColumnType("bigint");

                    b.Property<int?>("RemindAdvanceDays")
                        .HasColumnType("int");

                    b.Property<string>("SpecialRemarks")
                        .HasColumnType("varchar(500)");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<decimal>("TotalAmount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime");

                    b.Property<DateTime>("ValidityPeriod")
                        .HasColumnType("datetime");

                    b.HasKey("Id");

                    b.HasIndex("ApplicationFormId");

                    b.HasIndex("TenantId");

                    b.ToTable("GroupBookingPreOrder", (string)null);
                });

            modelBuilder.Entity("Order.Api.Model.GroupBookingPreOrderAddition", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<string>("AdditionName")
                        .IsRequired()
                        .HasColumnType("varchar(100)");

                    b.Property<decimal>("Amount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal>("Cost")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("CostCurrencyCode")
                        .IsRequired()
                        .HasColumnType("varchar(32)");

                    b.Property<string>("CurrencyCode")
                        .IsRequired()
                        .HasColumnType("varchar(32)");

                    b.Property<long>("PreOrderId")
                        .HasColumnType("bigint");

                    b.Property<long>("PreOrderItemId")
                        .HasColumnType("bigint");

                    b.Property<int>("Quantity")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasDefaultValue(1);

                    b.Property<string>("Remark")
                        .IsRequired()
                        .HasColumnType("varchar(500)");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("GroupBookingPreOrderAddition", (string)null);
                });

            modelBuilder.Entity("Order.Api.Model.GroupBookingPreOrderItem", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<string>("BedTypeJson")
                        .IsRequired()
                        .HasColumnType("varchar(500)");

                    b.Property<DateTime>("CheckIn")
                        .HasColumnType("datetime");

                    b.Property<DateTime>("CheckOut")
                        .HasColumnType("datetime");

                    b.Property<string>("ChildPolicy")
                        .HasColumnType("varchar(500)");

                    b.Property<string>("CurrencyCode")
                        .IsRequired()
                        .HasColumnType("varchar(32)");

                    b.Property<long>("HotelId")
                        .HasColumnType("bigint");

                    b.Property<string>("HotelName")
                        .HasColumnType("varchar(200)");

                    b.Property<long>("HotelRoomId")
                        .HasColumnType("bigint");

                    b.Property<string>("HotelRoomName")
                        .HasColumnType("varchar(200)");

                    b.Property<int>("NightCount")
                        .HasColumnType("int");

                    b.Property<string>("NightlyPriceJson")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<int>("NumberOfBreakfast")
                        .HasColumnType("int");

                    b.Property<string>("PreBookingCode")
                        .HasColumnType("varchar(500)");

                    b.Property<long>("PreOrderId")
                        .HasColumnType("bigint");

                    b.Property<decimal>("Price")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("PriceStrategyId")
                        .IsRequired()
                        .HasColumnType("varchar(100)");

                    b.Property<string>("PriceStrategyName")
                        .HasColumnType("varchar(200)");

                    b.Property<int>("RoomCount")
                        .HasColumnType("int");

                    b.Property<sbyte>("SupplierApiType")
                        .HasColumnType("tinyint");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("GroupBookingPreOrderItem", (string)null);
                });

            modelBuilder.Entity("Order.Api.Model.GroupBookingPreOrderSetting", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<string>("ElectronicSeal")
                        .IsRequired()
                        .HasColumnType("varchar(200)");

                    b.Property<string>("HeaderImage")
                        .IsRequired()
                        .HasColumnType("varchar(200)");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("GroupBookingPreOrderSetting", (string)null);
                });

            modelBuilder.Entity("Order.Api.Model.GroupBookingQuotation", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<long>("ApplicationFormId")
                        .HasColumnType("bigint");

                    b.Property<DateTime?>("CheckIn")
                        .HasColumnType("datetime");

                    b.Property<DateTime?>("CheckOut")
                        .HasColumnType("datetime");

                    b.Property<int>("CityCode")
                        .HasColumnType("int");

                    b.Property<string>("CityName")
                        .IsRequired()
                        .HasColumnType("varchar(50)");

                    b.Property<DateTime?>("ConfirmTime")
                        .HasColumnType("datetime");

                    b.Property<string>("CurrencyCode")
                        .IsRequired()
                        .HasColumnType("varchar(32)");

                    b.Property<string>("CustomerComments")
                        .HasColumnType("varchar(500)");

                    b.Property<decimal>("DoubleBedPrice")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("DoubleBedRoomENName")
                        .HasColumnType("longtext");

                    b.Property<long>("DoubleBedRoomId")
                        .HasColumnType("bigint");

                    b.Property<string>("DoubleBedRoomZHName")
                        .IsRequired()
                        .HasColumnType("varchar(100)");

                    b.Property<string>("HotelENName")
                        .IsRequired()
                        .HasColumnType("varchar(200)");

                    b.Property<string>("HotelZHName")
                        .IsRequired()
                        .HasColumnType("varchar(100)");

                    b.Property<bool>("IncludeBreakfast")
                        .HasColumnType("tinyint(1)");

                    b.Property<long>("InquiryFormId")
                        .HasColumnType("bigint");

                    b.Property<bool>("IsConfirmed")
                        .HasColumnType("tinyint(1)");

                    b.Property<string>("OtherExpensesDesc")
                        .HasColumnType("varchar(500)");

                    b.Property<decimal>("QueenBedPrice")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("QueenBedRoomENName")
                        .HasColumnType("varchar(100)");

                    b.Property<long>("QueenBedRoomId")
                        .HasColumnType("bigint");

                    b.Property<string>("QueenBedRoomZHName")
                        .IsRequired()
                        .HasColumnType("varchar(100)");

                    b.Property<string>("Remark")
                        .IsRequired()
                        .HasColumnType("varchar(500)");

                    b.Property<long>("ResourceHotelId")
                        .HasColumnType("bigint");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("GroupBookingQuotation", (string)null);
                });

            modelBuilder.Entity("Order.Api.Model.GroupBookingQuotationAddition", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<string>("AdditionName")
                        .IsRequired()
                        .HasColumnType("varchar(100)");

                    b.Property<decimal>("Amount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal>("Cost")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("CostCurrencyCode")
                        .IsRequired()
                        .HasColumnType("varchar(32)");

                    b.Property<string>("CurrencyCode")
                        .IsRequired()
                        .HasColumnType("varchar(32)");

                    b.Property<long>("GroupBookingQuotationId")
                        .HasColumnType("bigint");

                    b.Property<long>("GroupBookingQuotationItemId")
                        .HasColumnType("bigint");

                    b.Property<int>("Quantity")
                        .HasColumnType("int");

                    b.Property<string>("Remark")
                        .IsRequired()
                        .HasColumnType("varchar(500)");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("GroupBookingQuotationId");

                    b.HasIndex("TenantId");

                    b.ToTable("GroupBookingQuotationAddition", (string)null);
                });

            modelBuilder.Entity("Order.Api.Model.GroupBookingQuotationItem", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<decimal>("AverageCostPrice")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal>("AveragePrice")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("BedTypeName")
                        .IsRequired()
                        .HasColumnType("varchar(256)");

                    b.Property<string>("Breakfast")
                        .IsRequired()
                        .HasColumnType("varchar(256)");

                    b.Property<string>("CancellationPolicy")
                        .HasColumnType("varchar(500)");

                    b.Property<DateTime>("CheckIn")
                        .HasColumnType("datetime");

                    b.Property<DateTime>("CheckOut")
                        .HasColumnType("datetime");

                    b.Property<string>("ChildPolicy")
                        .HasColumnType("varchar(500)");

                    b.Property<int>("CityCode")
                        .HasColumnType("int");

                    b.Property<string>("CityName")
                        .IsRequired()
                        .HasColumnType("varchar(50)");

                    b.Property<string>("CostCurrencyCode")
                        .IsRequired()
                        .HasColumnType("varchar(32)");

                    b.Property<string>("CurrencyCode")
                        .IsRequired()
                        .HasColumnType("varchar(32)");

                    b.Property<string>("CustomerComments")
                        .HasColumnType("varchar(500)");

                    b.Property<long>("GroupBookingQuotationId")
                        .HasColumnType("bigint");

                    b.Property<string>("HotelName")
                        .IsRequired()
                        .HasColumnType("varchar(256)");

                    b.Property<string>("OfferValidity")
                        .HasColumnType("varchar(500)");

                    b.Property<string>("OtherExpensesDesc")
                        .HasColumnType("varchar(500)");

                    b.Property<string>("PaymentPolicy")
                        .HasColumnType("varchar(500)");

                    b.Property<int>("RoomCount")
                        .HasColumnType("int");

                    b.Property<string>("RoomName")
                        .IsRequired()
                        .HasColumnType("varchar(256)");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("GroupBookingQuotationItem", (string)null);
                });

            modelBuilder.Entity("Order.Api.Model.HotelApiOrder", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<long>("BaseOrderId")
                        .HasColumnType("bigint");

                    b.Property<int?>("ChildNum")
                        .HasColumnType("int");

                    b.Property<string>("ChildrenAges")
                        .HasColumnType("varchar(256)");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<string>("HotelId")
                        .IsRequired()
                        .HasColumnType("varchar(64)");

                    b.Property<long>("HotelOrderId")
                        .HasColumnType("bigint");

                    b.Property<string>("Message")
                        .IsRequired()
                        .HasColumnType("longtext");

                    b.Property<int>("MinNumberOfRooms")
                        .HasColumnType("int");

                    b.Property<string>("PriceStrategyId")
                        .IsRequired()
                        .HasColumnType("varchar(64)");

                    b.Property<long>("ResourceHotelId")
                        .HasColumnType("bigint");

                    b.Property<string>("RoomId")
                        .IsRequired()
                        .HasColumnType("varchar(64)");

                    b.Property<int>("RoomsCount")
                        .HasColumnType("int");

                    b.Property<sbyte>("Status")
                        .HasColumnType("tinyint");

                    b.Property<sbyte>("SupplierApiType")
                        .HasColumnType("tinyint");

                    b.Property<string>("SupplierOrderId")
                        .HasColumnType("varchar(64)");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<long?>("UnionOrderId")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("UpdateTime")
                        .HasColumnType("datetime");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("HotelApiOrder", (string)null);
                });

            modelBuilder.Entity("Order.Api.Model.HotelOrder", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<string>("ArrivalTaxFeesJson")
                        .HasColumnType("text");

                    b.Property<long>("BaseOrderId")
                        .HasColumnType("bigint");

                    b.Property<string>("BedTypeJson")
                        .HasColumnType("varchar(1000)");

                    b.Property<sbyte?>("BoardCodeType")
                        .HasColumnType("tinyint");

                    b.Property<int>("BoardCount")
                        .HasColumnType("int");

                    b.Property<string>("BookingBenefits")
                        .HasColumnType("varchar(1000)");

                    b.Property<DateTime>("CheckInDate")
                        .HasColumnType("datetime");

                    b.Property<DateTime>("CheckOutDate")
                        .HasColumnType("datetime");

                    b.Property<string>("ConfirmCode")
                        .HasColumnType("varchar(128)");

                    b.Property<DateTime?>("ConfirmTime")
                        .HasColumnType("datetime");

                    b.Property<string>("HotelEnName")
                        .HasColumnType("varchar(100)");

                    b.Property<long>("HotelId")
                        .HasColumnType("bigint");

                    b.Property<bool>("HotelIsAutoConfirmRoomStatus")
                        .HasColumnType("tinyint(1)");

                    b.Property<string>("HotelName")
                        .HasColumnType("varchar(100)");

                    b.Property<string>("HotelRoomEnName")
                        .HasColumnType("varchar(200)");

                    b.Property<long>("HotelRoomId")
                        .HasColumnType("bigint");

                    b.Property<string>("HotelRoomImgPath")
                        .HasColumnType("varchar(200)");

                    b.Property<string>("HotelRoomName")
                        .HasColumnType("varchar(200)");

                    b.Property<bool?>("IsDirect")
                        .HasColumnType("tinyint(1)");

                    b.Property<bool>("IsGroupBooking")
                        .HasColumnType("tinyint(1)");

                    b.Property<sbyte?>("IsOverSaleable")
                        .HasColumnType("tinyint");

                    b.Property<sbyte?>("IsOvertimeCompensation")
                        .HasColumnType("tinyint");

                    b.Property<string>("NationalityJson")
                        .HasColumnType("varchar(256)");

                    b.Property<string>("OvertimeDescription")
                        .HasColumnType("varchar(1024)");

                    b.Property<string>("PriceStrategyEnName")
                        .HasColumnType("varchar(50)");

                    b.Property<long>("PriceStrategyId")
                        .HasColumnType("bigint");

                    b.Property<bool>("PriceStrategyIsAutoConfirm")
                        .HasColumnType("tinyint(1)");

                    b.Property<int>("PriceStrategyMaximumOccupancy")
                        .HasColumnType("int");

                    b.Property<string>("PriceStrategyName")
                        .HasColumnType("varchar(50)");

                    b.Property<int>("PriceStrategyNightsCount")
                        .HasColumnType("int");

                    b.Property<int>("PriceStrategyNumberOfBreakfast")
                        .HasColumnType("int");

                    b.Property<int>("PriceStrategyRoomsCount")
                        .HasColumnType("int");

                    b.Property<long>("PriceStrategySupplierId")
                        .HasColumnType("bigint");

                    b.Property<sbyte>("PriceStrategyType")
                        .HasColumnType("tinyint");

                    b.Property<sbyte>("Status")
                        .IsConcurrencyToken()
                        .HasColumnType("tinyint");

                    b.Property<sbyte>("SupplierApiType")
                        .HasColumnType("tinyint");

                    b.Property<string>("SupplierOrderId")
                        .HasColumnType("varchar(500)");

                    b.Property<sbyte?>("Tag")
                        .HasColumnType("tinyint");

                    b.Property<string>("TaxDescription")
                        .HasColumnType("varchar(1000)");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime");

                    b.HasKey("Id");

                    b.HasIndex("BaseOrderId");

                    b.HasIndex("TenantId");

                    b.ToTable("HotelOrder", (string)null);
                });

            modelBuilder.Entity("Order.Api.Model.HotelOrderCalendarPrice", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<string>("CostCurrencyCode")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("varchar(20)")
                        .HasDefaultValue("CNY");

                    b.Property<decimal>("CostExchangeRate")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("decimal(18,6)")
                        .HasDefaultValue(1m);

                    b.Property<decimal>("CostPrice")
                        .HasColumnType("decimal(18,2)");

                    b.Property<DateTime>("Date")
                        .HasColumnType("datetime");

                    b.Property<decimal>("ExchangeRate")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("decimal(18,6)")
                        .HasDefaultValue(1m);

                    b.Property<long>("HotelOrderId")
                        .HasColumnType("bigint");

                    b.Property<decimal>("OrgPrice")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("OrgPriceCurrencyCode")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("varchar(20)")
                        .HasDefaultValue("CNY");

                    b.Property<string>("PaymentCurrencyCode")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("varchar(20)")
                        .HasDefaultValue("CNY");

                    b.Property<decimal>("SalePrice")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("VipPrice")
                        .HasColumnType("decimal(18,2)");

                    b.HasKey("Id");

                    b.HasIndex("HotelOrderId");

                    b.ToTable("HotelOrderCalendarPrice", (string)null);
                });

            modelBuilder.Entity("Order.Api.Model.HotelOrderCancelRule", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<int>("BeforeCheckInDays")
                        .HasColumnType("int");

                    b.Property<TimeSpan?>("BeforeCheckInTime")
                        .HasColumnType("time");

                    b.Property<sbyte>("CancelChargeType")
                        .HasColumnType("tinyint");

                    b.Property<long>("CancelRuleId")
                        .HasColumnType("bigint");

                    b.Property<sbyte>("CancelRulesType")
                        .HasColumnType("tinyint");

                    b.Property<int>("ChargeValue")
                        .HasColumnType("int");

                    b.Property<TimeSpan>("CheckInDateTime")
                        .HasColumnType("time");

                    b.Property<string>("Description")
                        .HasColumnType("varchar(500)");

                    b.Property<long>("HotelOrderId")
                        .HasColumnType("bigint");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("HotelOrderCancelRule", (string)null);
                });

            modelBuilder.Entity("Order.Api.Model.HotelOrderGuest", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<int?>("Age")
                        .HasColumnType("int");

                    b.Property<long>("BaseOrderId")
                        .HasColumnType("bigint");

                    b.Property<string>("FirstName")
                        .HasColumnType("varchar(50)");

                    b.Property<string>("GuestName")
                        .HasColumnType("varchar(50)");

                    b.Property<sbyte>("HotelOrderGuestType")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("tinyint")
                        .HasDefaultValue((sbyte)1);

                    b.Property<long>("HotelOrderId")
                        .HasColumnType("bigint");

                    b.Property<string>("LastName")
                        .HasColumnType("varchar(50)");

                    b.Property<int?>("RoomNumber")
                        .HasColumnType("int");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("GuestName");

                    b.HasIndex("HotelOrderId");

                    b.HasIndex("TenantId");

                    b.ToTable("HotelOrderGuest", (string)null);
                });

            modelBuilder.Entity("Order.Api.Model.HotelOrderPrice", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<long>("BaseOrderId")
                        .HasColumnType("bigint");

                    b.Property<string>("CostCurrencyCode")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("varchar(20)")
                        .HasDefaultValue("CNY");

                    b.Property<decimal>("CostExchangeRate")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("decimal(18,6)")
                        .HasDefaultValue(1m);

                    b.Property<decimal>("CostPrice")
                        .HasColumnType("decimal(18,2)");

                    b.Property<sbyte>("CostPriceType")
                        .HasColumnType("tinyint");

                    b.Property<decimal>("ExchangeRate")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("decimal(18,6)")
                        .HasDefaultValue(1m);

                    b.Property<int>("OrderSubType")
                        .HasColumnType("int");

                    b.Property<decimal>("OrgCostPrice")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal>("OrgPrice")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("OrgPriceCurrencyCode")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("varchar(20)")
                        .HasDefaultValue("CNY");

                    b.Property<string>("PaymentCurrencyCode")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("varchar(20)")
                        .HasDefaultValue("CNY");

                    b.Property<decimal>("Price")
                        .HasColumnType("decimal(18,2)");

                    b.Property<sbyte>("PriceType")
                        .HasColumnType("tinyint");

                    b.Property<int>("Quantity")
                        .HasColumnType("int");

                    b.Property<long>("SubOrderId")
                        .HasColumnType("bigint");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("BaseOrderId");

                    b.HasIndex("TenantId");

                    b.ToTable("HotelOrderPrice", (string)null);
                });

            modelBuilder.Entity("Order.Api.Model.HotelSupplierOrderRecord", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<long>("BaseOrderId")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<string>("IdKey")
                        .IsRequired()
                        .HasColumnType("varchar(1500)");

                    b.Property<sbyte>("RecordType")
                        .HasColumnType("tinyint");

                    b.Property<string>("Result")
                        .HasColumnType("mediumtext");

                    b.Property<string>("ResultCode")
                        .HasColumnType("varchar(50)");

                    b.Property<string>("ResultIdKey")
                        .HasColumnType("varchar(128)");

                    b.Property<string>("ResultMessage")
                        .HasColumnType("text");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime");

                    b.HasKey("Id");

                    b.HasIndex("BaseOrderId");

                    b.HasIndex("TenantId");

                    b.ToTable("HotelSupplierOrderRecord", (string)null);
                });

            modelBuilder.Entity("Order.Api.Model.InsuredCheckError", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<long>("BaseOrderId")
                        .HasColumnType("bigint");

                    b.Property<string>("ErrorMsg")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("InsuredPerson")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("varchar(20)");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("InsuredCheckError", (string)null);
                });

            modelBuilder.Entity("Order.Api.Model.InsureOrderRelation", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<long>("BaseOrderId")
                        .HasColumnType("bigint");

                    b.Property<long>("InsureProductId")
                        .HasColumnType("bigint");

                    b.Property<ulong>("IsAuto")
                        .HasColumnType("bit");

                    b.Property<sbyte>("Status")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("tinyint")
                        .HasDefaultValue((sbyte)0);

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("InsureOrderRelation", (string)null);
                });

            modelBuilder.Entity("Order.Api.Model.InsurePolicyHolder", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("Birthday")
                        .HasColumnType("datetime");

                    b.Property<string>("CNName")
                        .IsRequired()
                        .HasColumnType("varchar(50)");

                    b.Property<string>("ENName")
                        .IsRequired()
                        .HasColumnType("varchar(50)");

                    b.Property<string>("Email")
                        .HasColumnType("varchar(50)");

                    b.Property<sbyte>("Gender")
                        .HasColumnType("tinyint");

                    b.Property<string>("IDCard")
                        .IsRequired()
                        .HasColumnType("varchar(50)");

                    b.Property<string>("Phone")
                        .IsRequired()
                        .HasColumnType("varchar(50)");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<sbyte>("Type")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("tinyint")
                        .HasDefaultValue((sbyte)1);

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("InsurePolicyHolder", (string)null);
                });

            modelBuilder.Entity("Order.Api.Model.InsureProduct", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<DateTime?>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<string>("CurrencyCode")
                        .IsRequired()
                        .HasColumnType("varchar(32)");

                    b.Property<int>("EndAge")
                        .HasColumnType("int");

                    b.Property<int>("EndDay")
                        .HasColumnType("int");

                    b.Property<string>("PlanCode")
                        .IsRequired()
                        .HasColumnType("varchar(20)");

                    b.Property<string>("PlanName")
                        .IsRequired()
                        .HasColumnType("varchar(50)");

                    b.Property<decimal>("Price")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("ProductId")
                        .IsRequired()
                        .HasColumnType("varchar(20)");

                    b.Property<string>("ProductName")
                        .IsRequired()
                        .HasColumnType("varchar(50)");

                    b.Property<string>("ProductUnionId")
                        .IsRequired()
                        .HasColumnType("varchar(50)");

                    b.Property<int>("Sort")
                        .HasColumnType("int");

                    b.Property<int>("StartAge")
                        .HasColumnType("int");

                    b.Property<int>("StartDay")
                        .HasColumnType("int");

                    b.Property<int>("Status")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasDefaultValue(1);

                    b.Property<long>("SupplierId")
                        .HasColumnType("bigint");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<int>("Type")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasDefaultValue(301);

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("InsureProduct", (string)null);
                });

            modelBuilder.Entity("Order.Api.Model.InsureProductRelation", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<long>("InsureProductId")
                        .HasColumnType("bigint");

                    b.Property<ulong>("IsAuto")
                        .HasColumnType("bit");

                    b.Property<long>("ProductId")
                        .HasColumnType("bigint");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("InsureProductRelation", (string)null);
                });

            modelBuilder.Entity("Order.Api.Model.InsureProductSkuRelation", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<long?>("InsureProductId")
                        .HasColumnType("bigint");

                    b.Property<long>("ProductId")
                        .HasColumnType("bigint");

                    b.Property<long>("ProductSkuId")
                        .HasColumnType("bigint");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<sbyte>("Type")
                        .HasColumnType("tinyint");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("InsureProductSkuRelation", (string)null);
                });

            modelBuilder.Entity("Order.Api.Model.InsurePurchaseRecord", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<long>("BaseOrderId")
                        .HasColumnType("bigint");

                    b.Property<int>("Count")
                        .HasColumnType("int");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<string>("CurrencyCode")
                        .IsRequired()
                        .HasColumnType("varchar(32)");

                    b.Property<DateTime>("EndTime")
                        .HasColumnType("datetime");

                    b.Property<string>("ErrorMsg")
                        .HasColumnType("varchar(500)");

                    b.Property<string>("Extend")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<long>("InsureProductId")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("InsureTime")
                        .HasColumnType("datetime");

                    b.Property<string>("PlanCode")
                        .IsRequired()
                        .HasColumnType("varchar(20)");

                    b.Property<string>("PlanFullName")
                        .IsRequired()
                        .HasColumnType("varchar(200)");

                    b.Property<string>("PlanName")
                        .IsRequired()
                        .HasColumnType("varchar(50)");

                    b.Property<string>("PolicyNo")
                        .HasColumnType("varchar(20)");

                    b.Property<decimal>("Price")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("ProductId")
                        .IsRequired()
                        .HasColumnType("varchar(50)");

                    b.Property<string>("ProductName")
                        .IsRequired()
                        .HasColumnType("varchar(50)");

                    b.Property<sbyte>("PurchaseStatus")
                        .HasColumnType("tinyint");

                    b.Property<sbyte>("RequestStatus")
                        .HasColumnType("tinyint");

                    b.Property<DateTime>("StartTime")
                        .HasColumnType("datetime");

                    b.Property<long>("SupplierId")
                        .HasColumnType("bigint");

                    b.Property<string>("SupplierName")
                        .HasColumnType("varchar(50)");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<decimal>("TotalAmount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("TradeNo")
                        .HasColumnType("varchar(50)");

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("InsurePurchaseRecord", (string)null);
                });

            modelBuilder.Entity("Order.Api.Model.InsurePurchaseRecordDetail", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<long>("BaseOrderId")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<string>("CurrencyCode")
                        .IsRequired()
                        .HasColumnType("varchar(32)");

                    b.Property<long>("DataId")
                        .HasColumnType("bigint");

                    b.Property<string>("ErrorMsg")
                        .HasColumnType("text");

                    b.Property<long>("InsurePurchaseRecordId")
                        .HasColumnType("bigint");

                    b.Property<string>("InsuredPerson")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("varchar(100)");

                    b.Property<int>("OrderType")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasDefaultValue(5);

                    b.Property<decimal>("Price")
                        .HasColumnType("decimal(18,2)");

                    b.Property<sbyte>("Status")
                        .HasColumnType("tinyint");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("InsurePurchaseRecordDetail", (string)null);
                });

            modelBuilder.Entity("Order.Api.Model.InsurePurchaseRecordError", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<long>("BaseOrderId")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<string>("ErrorMsg")
                        .IsRequired()
                        .HasColumnType("varchar(500)");

                    b.Property<string>("RequestContent")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<long>("SupplierId")
                        .HasColumnType("bigint");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("InsurePurchaseRecordError", (string)null);
                });

            modelBuilder.Entity("Order.Api.Model.InsureTask", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<long>("BaseOrderId")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<string>("ErrorMsg")
                        .HasColumnType("text");

                    b.Property<sbyte>("Status")
                        .HasColumnType("tinyint");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("UpdateTime")
                        .HasColumnType("datetime");

                    b.HasKey("Id");

                    b.HasIndex("BaseOrderId")
                        .IsUnique();

                    b.HasIndex("TenantId");

                    b.ToTable("InsureTask", (string)null);
                });

            modelBuilder.Entity("Order.Api.Model.InvoiceConfig", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<string>("Address")
                        .HasColumnType("varchar(200)");

                    b.Property<string>("AuthCode")
                        .HasColumnType("varchar(200)");

                    b.Property<string>("BankName")
                        .HasColumnType("varchar(50)");

                    b.Property<string>("BankNumber")
                        .HasColumnType("varchar(50)");

                    b.Property<string>("ContactNumber")
                        .HasColumnType("varchar(20)");

                    b.Property<int?>("ContentPrefix")
                        .HasColumnType("int");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<bool>("Enabled")
                        .HasColumnType("tinyint(1)");

                    b.Property<string>("ExtensionNumber")
                        .HasColumnType("varchar(50)");

                    b.Property<string>("ITIN")
                        .HasColumnType("varchar(20)");

                    b.Property<string>("Invoicer")
                        .HasColumnType("varchar(8)");

                    b.Property<bool>("IsDefault")
                        .HasColumnType("tinyint(1)");

                    b.Property<string>("Name")
                        .HasColumnType("varchar(200)");

                    b.Property<string>("Payee")
                        .HasColumnType("varchar(8)");

                    b.Property<string>("ProofReader")
                        .HasColumnType("varchar(8)");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime");

                    b.Property<string>("WebServiceUrl")
                        .HasColumnType("varchar(200)");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("InvoiceConfig", (string)null);
                });

            modelBuilder.Entity("Order.Api.Model.InvoiceRecord", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<string>("Address")
                        .HasColumnType("varchar(200)");

                    b.Property<string>("BankName")
                        .HasColumnType("varchar(50)");

                    b.Property<string>("BankNumber")
                        .HasColumnType("longtext");

                    b.Property<string>("ContactNumber")
                        .HasColumnType("varchar(20)");

                    b.Property<string>("ContentType")
                        .IsRequired()
                        .HasColumnType("varchar(100)");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<long>("CreatorId")
                        .HasColumnType("bigint");

                    b.Property<string>("CreatorName")
                        .IsRequired()
                        .HasColumnType("varchar(20)");

                    b.Property<string>("EmailAddress")
                        .IsRequired()
                        .HasColumnType("varchar(100)");

                    b.Property<string>("ErrorMessage")
                        .HasColumnType("varchar(500)");

                    b.Property<string>("ITIN")
                        .HasColumnType("varchar(50)");

                    b.Property<string>("InvCode")
                        .HasColumnType("varchar(50)");

                    b.Property<string>("InvNumber")
                        .HasColumnType("varchar(50)");

                    b.Property<decimal>("InvoiceAmount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<long?>("InvoiceConfigId")
                        .HasColumnType("bigint");

                    b.Property<string>("InvoicePdfUrl")
                        .HasColumnType("longtext");

                    b.Property<string>("InvoiceSerialNo")
                        .IsRequired()
                        .HasColumnType("varchar(100)");

                    b.Property<string>("Invoicer")
                        .IsRequired()
                        .HasColumnType("varchar(8)");

                    b.Property<bool>("IsQueryingStatus")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("tinyint(1)")
                        .HasDefaultValue(false);

                    b.Property<long>("OrderId")
                        .HasColumnType("bigint");

                    b.Property<int>("SourceChannel")
                        .HasColumnType("int");

                    b.Property<int>("Status")
                        .HasColumnType("int");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<string>("Title")
                        .IsRequired()
                        .HasColumnType("varchar(200)");

                    b.Property<int>("TitleType")
                        .HasColumnType("int");

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("InvoiceRecord", (string)null);
                });

            modelBuilder.Entity("Order.Api.Model.InvoiceTitle", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<string>("Address")
                        .HasColumnType("varchar(200)");

                    b.Property<string>("BankName")
                        .HasColumnType("varchar(50)");

                    b.Property<string>("BankNumber")
                        .HasColumnType("longtext");

                    b.Property<string>("ContactNumber")
                        .HasColumnType("varchar(20)");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<bool>("CreditReceiptOrderInvoiceIsSupport")
                        .HasColumnType("tinyint(1)");

                    b.Property<string>("ITIN")
                        .HasColumnType("varchar(20)");

                    b.Property<long?>("InvoiceConfigId")
                        .HasColumnType("bigint");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<string>("Title")
                        .IsRequired()
                        .HasColumnType("varchar(200)");

                    b.Property<sbyte>("TitleType")
                        .HasColumnType("tinyint");

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime");

                    b.Property<long>("UserId")
                        .HasColumnType("bigint");

                    b.Property<sbyte>("UserType")
                        .HasColumnType("tinyint");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("InvoiceTitle", (string)null);
                });

            modelBuilder.Entity("Order.Api.Model.KingdeeAPPayable", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("Date")
                        .HasColumnType("datetime");

                    b.Property<long>("KingdeePushId")
                        .HasColumnType("bigint");

                    b.Property<sbyte>("ReceivableType")
                        .HasColumnType("tinyint");

                    b.Property<sbyte>("Status")
                        .HasColumnType("tinyint");

                    b.Property<long>("SupplierId")
                        .HasColumnType("bigint");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("KingdeeAPPayable", (string)null);
                });

            modelBuilder.Entity("Order.Api.Model.KingdeeARReceivable", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<long>("AgencyId")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("Date")
                        .HasColumnType("datetime");

                    b.Property<long>("KingdeePushId")
                        .HasColumnType("bigint");

                    b.Property<sbyte>("ReceivableType")
                        .HasColumnType("tinyint");

                    b.Property<sbyte>("Status")
                        .HasColumnType("tinyint");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("KingdeeARReceivable", (string)null);
                });

            modelBuilder.Entity("Order.Api.Model.KingdeeBDCustomer", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<long>("AgencyId")
                        .HasColumnType("bigint");

                    b.Property<string>("AgencyName")
                        .IsRequired()
                        .HasColumnType("varchar(256)");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<sbyte>("Status")
                        .HasColumnType("tinyint");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("KingdeeBDCustomer", (string)null);
                });

            modelBuilder.Entity("Order.Api.Model.KingdeeBDSupplier", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<sbyte>("Status")
                        .HasColumnType("tinyint");

                    b.Property<long>("SupplierId")
                        .HasColumnType("bigint");

                    b.Property<string>("SupplierName")
                        .IsRequired()
                        .HasColumnType("varchar(256)");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("KingdeeBDSupplier", (string)null);
                });

            modelBuilder.Entity("Order.Api.Model.KingdeePush", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("BeginDate")
                        .HasColumnType("datetime");

                    b.Property<sbyte>("BillType")
                        .HasColumnType("tinyint");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<DateTime>("EndDate")
                        .HasColumnType("datetime");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("KingdeePush", (string)null);
                });

            modelBuilder.Entity("Order.Api.Model.KingdeePushDetail", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<long?>("AgencyId")
                        .HasColumnType("bigint");

                    b.Property<decimal>("Amount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("BaseCurrencyCode")
                        .IsRequired()
                        .HasColumnType("varchar(32)");

                    b.Property<sbyte>("BillType")
                        .HasColumnType("tinyint");

                    b.Property<long>("BusinessOrderId")
                        .HasColumnType("bigint");

                    b.Property<sbyte>("BusinessType")
                        .HasColumnType("tinyint");

                    b.Property<string>("CurrencyCode")
                        .IsRequired()
                        .HasColumnType("varchar(32)");

                    b.Property<DateTime>("Date")
                        .HasColumnType("datetime");

                    b.Property<string>("Errors")
                        .HasColumnType("varchar(1000)");

                    b.Property<decimal?>("ExchangeRate")
                        .HasColumnType("decimal(18,6)");

                    b.Property<string>("FID")
                        .HasColumnType("varchar(32)");

                    b.Property<string>("FNumber")
                        .HasColumnType("varchar(64)");

                    b.Property<long>("KingdeePushId")
                        .HasColumnType("bigint");

                    b.Property<string>("ModelJson")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<sbyte>("Status")
                        .HasColumnType("tinyint");

                    b.Property<string>("SuccessEntity")
                        .HasColumnType("varchar(128)");

                    b.Property<long?>("SupplierId")
                        .HasColumnType("bigint");

                    b.Property<string>("TargetCurrencyCode")
                        .IsRequired()
                        .HasColumnType("varchar(32)");

                    b.Property<decimal?>("TargetExchangeRate")
                        .HasColumnType("decimal(18,6)");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<decimal>("TotalAmount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("KingdeePushDetail", (string)null);
                });

            modelBuilder.Entity("Order.Api.Model.LogisticsCompany", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<string>("Code")
                        .HasColumnType("varchar(50)");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<string>("Name")
                        .HasColumnType("varchar(50)");

                    b.HasKey("Id");

                    b.HasIndex("Code")
                        .IsUnique();

                    b.ToTable("LogisticsCompany", (string)null);

                    b.HasData(
                        new
                        {
                            Id = 926382999087087616L,
                            Code = "yuantong",
                            CreateTime = new DateTime(2021, 12, 29, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            Name = "圆通速递"
                        },
                        new
                        {
                            Id = 926382999087087617L,
                            Code = "yunda",
                            CreateTime = new DateTime(2021, 12, 29, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            Name = "韵达快递"
                        },
                        new
                        {
                            Id = 926382999087087618L,
                            Code = "zhongtong",
                            CreateTime = new DateTime(2021, 12, 29, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            Name = "中通快递"
                        },
                        new
                        {
                            Id = 926382999087087619L,
                            Code = "shentong",
                            CreateTime = new DateTime(2021, 12, 29, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            Name = "申通快递"
                        },
                        new
                        {
                            Id = 926382999087087620L,
                            Code = "youzhengguonei",
                            CreateTime = new DateTime(2021, 12, 29, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            Name = "邮政快递包裹"
                        },
                        new
                        {
                            Id = 926382999087087621L,
                            Code = "huitongkuaidi",
                            CreateTime = new DateTime(2021, 12, 29, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            Name = "百世快递"
                        },
                        new
                        {
                            Id = 926382999087087622L,
                            Code = "shunfeng",
                            CreateTime = new DateTime(2021, 12, 29, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            Name = "顺丰速运"
                        },
                        new
                        {
                            Id = 926382999087087623L,
                            Code = "ems",
                            CreateTime = new DateTime(2021, 12, 29, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            Name = "EMS"
                        },
                        new
                        {
                            Id = 926382999087087624L,
                            Code = "jtexpress",
                            CreateTime = new DateTime(2021, 12, 29, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            Name = "极兔速递"
                        },
                        new
                        {
                            Id = 926382999087087625L,
                            Code = "jd",
                            CreateTime = new DateTime(2021, 12, 29, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            Name = "京东物流"
                        },
                        new
                        {
                            Id = 926382999087087626L,
                            Code = "youzhengbk",
                            CreateTime = new DateTime(2021, 12, 29, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            Name = "邮政标准快递"
                        },
                        new
                        {
                            Id = 926382999087087627L,
                            Code = "fengwang",
                            CreateTime = new DateTime(2021, 12, 29, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            Name = "丰网速运"
                        },
                        new
                        {
                            Id = 926382999087087628L,
                            Code = "debangkuaidi",
                            CreateTime = new DateTime(2021, 12, 29, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            Name = "德邦快递"
                        },
                        new
                        {
                            Id = 926382999087087629L,
                            Code = "debangwuliu",
                            CreateTime = new DateTime(2021, 12, 29, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            Name = "德邦"
                        },
                        new
                        {
                            Id = 926382999087087630L,
                            Code = "zhongtongguoji",
                            CreateTime = new DateTime(2021, 12, 29, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            Name = "中通国际"
                        },
                        new
                        {
                            Id = 926382999087087631L,
                            Code = "baishiwuliu",
                            CreateTime = new DateTime(2021, 12, 29, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            Name = "百世快运"
                        },
                        new
                        {
                            Id = 926382999087087632L,
                            Code = "emsbg",
                            CreateTime = new DateTime(2021, 12, 29, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            Name = "EMS包裹"
                        },
                        new
                        {
                            Id = 926382999087087633L,
                            Code = "zhongtongkuaiyun",
                            CreateTime = new DateTime(2021, 12, 29, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            Name = "中通快运"
                        },
                        new
                        {
                            Id = 926382999087087634L,
                            Code = "yundakuaiyun",
                            CreateTime = new DateTime(2021, 12, 29, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            Name = "韵达快运"
                        },
                        new
                        {
                            Id = 926382999087087635L,
                            Code = "annengwuliu",
                            CreateTime = new DateTime(2021, 12, 29, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            Name = "安能快运"
                        });
                });

            modelBuilder.Entity("Order.Api.Model.MailOrder", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<long>("BaseOrderId")
                        .HasColumnType("bigint");

                    b.Property<long>("ProductId")
                        .HasColumnType("bigint");

                    b.Property<string>("ProductImagePath")
                        .HasColumnType("varchar(255)");

                    b.Property<bool>("ProductIsSupportRefund")
                        .HasColumnType("tinyint(1)");

                    b.Property<string>("ProductName")
                        .HasColumnType("varchar(100)");

                    b.Property<long>("ProductSkuId")
                        .HasColumnType("bigint");

                    b.Property<string>("ProductSkuName")
                        .HasColumnType("varchar(100)");

                    b.Property<long>("ProductSupplierId")
                        .HasColumnType("bigint");

                    b.Property<int>("Quantity")
                        .HasColumnType("int");

                    b.Property<DateTime?>("SignTime")
                        .HasColumnType("datetime");

                    b.Property<sbyte>("Status")
                        .IsConcurrencyToken()
                        .HasColumnType("tinyint");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<string>("TrackingNumber")
                        .HasColumnType("varchar(50)");

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime");

                    b.HasKey("Id");

                    b.HasIndex("BaseOrderId");

                    b.HasIndex("TenantId");

                    b.HasIndex("TrackingNumber");

                    b.ToTable("MailOrder", (string)null);
                });

            modelBuilder.Entity("Order.Api.Model.MailOrderLogistics", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<long>("BaseOrderId")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<string>("LogisticsCompanyCode")
                        .HasColumnType("varchar(50)");

                    b.Property<string>("LogisticsCompanyName")
                        .HasColumnType("varchar(50)");

                    b.Property<string>("Status")
                        .HasColumnType("varchar(20)");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<string>("TrackData")
                        .HasColumnType("text");

                    b.Property<string>("TrackingNumber")
                        .HasColumnType("varchar(50)");

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime");

                    b.HasKey("Id");

                    b.HasIndex("BaseOrderId");

                    b.HasIndex("TenantId");

                    b.HasIndex("TenantId", "TrackingNumber")
                        .IsUnique();

                    b.ToTable("MailOrderLogistics", (string)null);
                });

            modelBuilder.Entity("Order.Api.Model.MailOrderPostage", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<decimal>("Amount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<long>("BaseOrderId")
                        .HasColumnType("bigint");

                    b.Property<long>("MailOrderId")
                        .HasColumnType("bigint");

                    b.Property<long>("PostageTemplateId")
                        .HasColumnType("bigint");

                    b.Property<string>("PostageTemplateName")
                        .HasColumnType("varchar(50)");

                    b.HasKey("Id");

                    b.HasIndex("BaseOrderId");

                    b.ToTable("MailOrderPostage", (string)null);
                });

            modelBuilder.Entity("Order.Api.Model.MailOrderReceiverAddress", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<string>("Address")
                        .HasColumnType("varchar(200)");

                    b.Property<long>("BaseOrderId")
                        .HasColumnType("bigint");

                    b.Property<int>("CityCode")
                        .HasColumnType("int");

                    b.Property<string>("CityName")
                        .HasColumnType("varchar(50)");

                    b.Property<int>("DistrictCode")
                        .HasColumnType("int");

                    b.Property<string>("DistrictName")
                        .HasColumnType("varchar(50)");

                    b.Property<string>("Name")
                        .HasColumnType("varchar(32)");

                    b.Property<string>("PhoneNumber")
                        .HasColumnType("varchar(20)");

                    b.Property<int>("ProvinceCode")
                        .HasColumnType("int");

                    b.Property<string>("ProvinceName")
                        .HasColumnType("varchar(50)");

                    b.Property<long>("ReceiverAddressId")
                        .HasColumnType("bigint");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("BaseOrderId");

                    b.HasIndex("TenantId");

                    b.ToTable("MailOrderReceiverAddress", (string)null);
                });

            modelBuilder.Entity("Order.Api.Model.OffsetOrder", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<long>("AgencyId")
                        .HasColumnType("bigint");

                    b.Property<decimal>("Amount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<long>("BaseOrderId")
                        .HasColumnType("bigint");

                    b.Property<long?>("BatchNumber")
                        .HasColumnType("bigint");

                    b.Property<long>("BusinessOrderId")
                        .HasColumnType("bigint");

                    b.Property<sbyte>("BusinessType")
                        .HasColumnType("tinyint");

                    b.Property<DateTime>("CreatTime")
                        .HasColumnType("datetime");

                    b.Property<long>("CreatorId")
                        .HasColumnType("bigint");

                    b.Property<string>("CreatorName")
                        .HasColumnType("varchar(50)");

                    b.Property<string>("CurrencyCode")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("varchar(20)")
                        .HasDefaultValue("CNY");

                    b.Property<string>("Images")
                        .HasColumnType("varchar(1000)");

                    b.Property<sbyte>("OffsetAmountType")
                        .HasColumnType("tinyint");

                    b.Property<sbyte>("OffsetType")
                        .HasColumnType("tinyint");

                    b.Property<string>("ProcessErrorMsg")
                        .HasColumnType("varchar(1000)");

                    b.Property<sbyte?>("ProcessStatus")
                        .HasColumnType("tinyint");

                    b.Property<long>("ProductId")
                        .HasColumnType("bigint");

                    b.Property<string>("ProductName")
                        .IsRequired()
                        .HasColumnType("varchar(100)");

                    b.Property<long>("ProductSkuId")
                        .HasColumnType("bigint");

                    b.Property<string>("ProductSkuName")
                        .IsRequired()
                        .HasColumnType("varchar(100)");

                    b.Property<string>("Remark")
                        .HasColumnType("varchar(500)");

                    b.Property<sbyte>("SettlementType")
                        .HasColumnType("tinyint");

                    b.Property<sbyte>("Status")
                        .HasColumnType("tinyint");

                    b.Property<long>("SupplierId")
                        .HasColumnType("bigint");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime");

                    b.Property<long>("UpdaterId")
                        .HasColumnType("bigint");

                    b.Property<string>("UpdaterName")
                        .HasColumnType("varchar(50)");

                    b.Property<long?>("WorkOrderId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("BaseOrderId");

                    b.HasIndex("TenantId");

                    b.ToTable("OffsetOrder", (string)null);
                });

            modelBuilder.Entity("Order.Api.Model.OffsetOrderDingtalkApply", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<sbyte>("AuditStatus")
                        .HasColumnType("tinyint");

                    b.Property<long>("BaseOrderId")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("CreatTime")
                        .HasColumnType("datetime");

                    b.Property<long>("CreatorId")
                        .HasColumnType("bigint");

                    b.Property<string>("CreatorName")
                        .HasColumnType("varchar(50)");

                    b.Property<string>("DingtalkBusinessId")
                        .HasColumnType("varchar(64)");

                    b.Property<string>("DingtalkInstanceId")
                        .HasColumnType("varchar(64)");

                    b.Property<DateTime?>("FinishTime")
                        .HasColumnType("datetime");

                    b.Property<long>("OffsetOrderId")
                        .HasColumnType("bigint");

                    b.Property<string>("OperationRecords")
                        .HasColumnType("mediumtext");

                    b.Property<string>("ProcessCode")
                        .IsRequired()
                        .HasColumnType("varchar(128)");

                    b.Property<string>("ResultMsg")
                        .HasColumnType("varchar(500)");

                    b.Property<string>("SchemaContent")
                        .HasColumnType("mediumtext");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<string>("Title")
                        .IsRequired()
                        .HasColumnType("varchar(256)");

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("OffsetOrderDingtalkApply", (string)null);
                });

            modelBuilder.Entity("Order.Api.Model.OpenChannelRefundApplyOrder", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<string>("ChannelOrderNo")
                        .IsRequired()
                        .HasColumnType("varchar(200)");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<sbyte>("OrderType")
                        .HasColumnType("tinyint");

                    b.Property<sbyte>("SellingPlatform")
                        .HasColumnType("tinyint");

                    b.Property<sbyte>("Status")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("tinyint")
                        .HasDefaultValue((sbyte)0);

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("OpenChannelRefundApplyOrder", (string)null);
                });

            modelBuilder.Entity("Order.Api.Model.OpenChannelSyncFailOrder", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<long?>("AgencyId")
                        .HasColumnType("bigint");

                    b.Property<string>("ChannelMasterOrderNo")
                        .HasColumnType("varchar(512)");

                    b.Property<string>("ChannelOrderNo")
                        .IsRequired()
                        .HasColumnType("varchar(512)");

                    b.Property<string>("ContactsEmail")
                        .HasColumnType("varchar(50)");

                    b.Property<string>("ContactsName")
                        .HasColumnType("varchar(50)");

                    b.Property<string>("ContactsPhoneNumber")
                        .HasColumnType("varchar(50)");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<string>("CurrencyCode")
                        .IsRequired()
                        .HasColumnType("varchar(50)");

                    b.Property<string>("DataContentJson")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime?>("FinishTime")
                        .HasColumnType("datetime");

                    b.Property<bool>("IsCombination")
                        .HasColumnType("tinyint(1)");

                    b.Property<sbyte>("OrderType")
                        .HasColumnType("tinyint");

                    b.Property<decimal>("PaymentAmount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<long?>("ProductId")
                        .HasColumnType("bigint");

                    b.Property<string>("ProductName")
                        .HasColumnType("varchar(100)");

                    b.Property<long?>("RelatedCombinationProductId")
                        .HasColumnType("bigint");

                    b.Property<short>("SellingChannels")
                        .HasColumnType("smallint");

                    b.Property<sbyte>("SellingPlatform")
                        .HasColumnType("tinyint");

                    b.Property<long?>("SkuId")
                        .HasColumnType("bigint");

                    b.Property<string>("SkuName")
                        .HasColumnType("varchar(100)");

                    b.Property<long?>("SupplierId")
                        .HasColumnType("bigint");

                    b.Property<sbyte>("SyncStatus")
                        .IsConcurrencyToken()
                        .HasColumnType("tinyint");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<TimeSpan?>("TimeSlot")
                        .HasColumnType("time(0)");

                    b.Property<long?>("TimeSlotId")
                        .HasColumnType("bigint");

                    b.Property<long?>("TrackingUserId")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("TravelDate")
                        .HasColumnType("datetime");

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime");

                    b.HasKey("Id");

                    b.HasIndex("ChannelOrderNo");

                    b.HasIndex("TenantId");

                    b.ToTable("OpenChannelSyncFailOrder", (string)null);
                });

            modelBuilder.Entity("Order.Api.Model.OpenChannelSyncFailOrderRecord", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<string>("ChannelOrderNo")
                        .IsRequired()
                        .HasColumnType("varchar(512)");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<long>("OpenChannelSyncFailOrderId")
                        .HasColumnType("bigint");

                    b.Property<string>("Operator")
                        .HasColumnType("varchar(100)");

                    b.Property<long?>("OperatorId")
                        .HasColumnType("bigint");

                    b.Property<string>("Reason")
                        .IsRequired()
                        .HasColumnType("varchar(500)");

                    b.Property<sbyte>("RecordSource")
                        .HasColumnType("tinyint");

                    b.Property<sbyte>("RecordType")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("tinyint")
                        .HasDefaultValue((sbyte)1);

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("ChannelOrderNo");

                    b.HasIndex("OpenChannelSyncFailOrderId");

                    b.HasIndex("TenantId");

                    b.ToTable("OpenChannelSyncFailOrderRecord", (string)null);
                });

            modelBuilder.Entity("Order.Api.Model.OpenSupplierOrderExtraInfo", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<long>("BaseOrderId")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<sbyte>("DataType")
                        .HasColumnType("tinyint");

                    b.Property<sbyte>("OpenSupplierType")
                        .HasColumnType("tinyint");

                    b.Property<string>("OptionKey")
                        .HasColumnType("varchar(100)");

                    b.Property<string>("OptionValue")
                        .IsRequired()
                        .HasColumnType("varchar(500)");

                    b.Property<sbyte>("OrderType")
                        .HasColumnType("tinyint");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.HasIndex("BaseOrderId", "TenantId");

                    b.ToTable("OpenSupplierOrderExtraInfo", (string)null);
                });

            modelBuilder.Entity("Order.Api.Model.OrderCommission", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<sbyte?>("AgencyCommisionStatus")
                        .HasColumnType("tinyint");

                    b.Property<decimal?>("AgencyCommissionFee")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("AgencyCommissionRate")
                        .HasColumnType("decimal(18,2)");

                    b.Property<long>("BaseOrderId")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<sbyte?>("SupplierCommisionStatus")
                        .HasColumnType("tinyint");

                    b.Property<decimal?>("SupplierCommissionFee")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("SupplierCommissionRate")
                        .HasColumnType("decimal(18,2)");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime");

                    b.HasKey("Id");

                    b.HasIndex("BaseOrderId");

                    b.ToTable("OrderCommission", (string)null);
                });

            modelBuilder.Entity("Order.Api.Model.OrderFieldInformation", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<string>("FieldCode")
                        .IsRequired()
                        .HasColumnType("varchar(50)");

                    b.Property<string>("FieldName")
                        .IsRequired()
                        .HasColumnType("varchar(100)");

                    b.Property<sbyte>("FieldType")
                        .HasColumnType("tinyint");

                    b.Property<string>("FieldValue")
                        .HasColumnType("text");

                    b.Property<string>("Group")
                        .HasColumnType("varchar(64)");

                    b.Property<string>("GroupName")
                        .HasColumnType("varchar(20)");

                    b.Property<sbyte?>("GroupSort")
                        .HasColumnType("tinyint");

                    b.Property<bool>("IsRequired")
                        .HasColumnType("tinyint(1)");

                    b.Property<long>("OrderFieldInformationTypeId")
                        .HasColumnType("bigint");

                    b.Property<int?>("ProductTemplateType")
                        .HasColumnType("int");

                    b.Property<sbyte>("Sort")
                        .HasColumnType("tinyint");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("OrderFieldInformationTypeId");

                    b.HasIndex("TenantId");

                    b.ToTable("OrderFieldInformation", (string)null);
                });

            modelBuilder.Entity("Order.Api.Model.OrderFieldInformationType", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<long>("BaseOrderId")
                        .HasColumnType("bigint");

                    b.Property<string>("ExtendInfo")
                        .HasColumnType("mediumtext");

                    b.Property<long>("OrderId")
                        .HasColumnType("bigint");

                    b.Property<sbyte>("OrderType")
                        .HasColumnType("tinyint");

                    b.Property<sbyte>("ProductTemplateType")
                        .HasColumnType("tinyint");

                    b.Property<long?>("TemplateId")
                        .HasColumnType("bigint");

                    b.Property<sbyte>("TemplateType")
                        .HasColumnType("tinyint");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("BaseOrderId");

                    b.HasIndex("TenantId");

                    b.ToTable("OrderFieldInformationType", (string)null);
                });

            modelBuilder.Entity("Order.Api.Model.OrderLogs", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<string>("ExtensionData")
                        .HasColumnType("varchar(500)");

                    b.Property<sbyte>("OperationRole")
                        .HasColumnType("tinyint");

                    b.Property<int>("OperationType")
                        .HasColumnType("int");

                    b.Property<long>("OrderId")
                        .HasColumnType("bigint");

                    b.Property<int>("OrderLogType")
                        .HasColumnType("int");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<long>("UserId")
                        .HasColumnType("bigint");

                    b.Property<string>("UserName")
                        .HasColumnType("varchar(50)");

                    b.HasKey("Id");

                    b.HasIndex("OrderId");

                    b.HasIndex("TenantId");

                    b.ToTable("OrderLogs", (string)null);
                });

            modelBuilder.Entity("Order.Api.Model.OrderOperationTask", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<int>("FailCount")
                        .HasColumnType("int");

                    b.Property<string>("FileUrl")
                        .HasColumnType("varchar(1024)");

                    b.Property<DateTime?>("FinishTime")
                        .HasColumnType("datetime");

                    b.Property<long>("OperationUserId")
                        .HasColumnType("bigint");

                    b.Property<string>("OperationUserName")
                        .HasColumnType("varchar(50)");

                    b.Property<string>("Parameters")
                        .HasColumnType("mediumtext");

                    b.Property<string>("Remak")
                        .HasColumnType("varchar(500)");

                    b.Property<sbyte>("SourceType")
                        .HasColumnType("tinyint");

                    b.Property<int>("SuccessCount")
                        .HasColumnType("int");

                    b.Property<sbyte>("TaskStatus")
                        .IsConcurrencyToken()
                        .HasColumnType("tinyint");

                    b.Property<sbyte>("TaskType")
                        .HasColumnType("tinyint");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<int>("TotalCount")
                        .HasColumnType("int");

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime");

                    b.HasKey("Id");

                    b.HasIndex("TaskType");

                    b.HasIndex("TenantId");

                    b.ToTable("OrderOperationTask", (string)null);
                });

            modelBuilder.Entity("Order.Api.Model.OrderPaymentAdjust", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<decimal>("AdjustAmount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal>("AfterPaymentAmount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<long>("BaseOrderId")
                        .HasColumnType("bigint");

                    b.Property<decimal>("BeforePaymentAmount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<string>("Operator")
                        .HasColumnType("varchar(256)");

                    b.Property<long?>("OperatorId")
                        .HasColumnType("bigint");

                    b.Property<long>("OrderId")
                        .HasColumnType("bigint");

                    b.Property<string>("PaymentCurrencyCode")
                        .IsRequired()
                        .HasColumnType("varchar(32)");

                    b.Property<string>("Remark")
                        .HasColumnType("varchar(256)");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<long?>("WorkOrderId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("BaseOrderId");

                    b.HasIndex("OrderId");

                    b.HasIndex("TenantId");

                    b.ToTable("OrderPaymentAdjust", (string)null);
                });

            modelBuilder.Entity("Order.Api.Model.OrderPaymentCard", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<long>("BaseOrderId")
                        .HasColumnType("bigint");

                    b.Property<string>("CardCode")
                        .IsRequired()
                        .HasColumnType("varchar(20)");

                    b.Property<string>("CardHolderFirstName")
                        .IsRequired()
                        .HasColumnType("varchar(100)");

                    b.Property<string>("CardHolderLastName")
                        .IsRequired()
                        .HasColumnType("varchar(100)");

                    b.Property<string>("CardNumber")
                        .IsRequired()
                        .HasColumnType("varchar(64)");

                    b.Property<int>("ExpiryMonth")
                        .HasColumnType("int");

                    b.Property<int>("ExpiryYear")
                        .HasColumnType("int");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("OrderPaymentCard", (string)null);
                });

            modelBuilder.Entity("Order.Api.Model.OrderPrice", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<long>("BaseOrderId")
                        .HasColumnType("bigint");

                    b.Property<string>("CostCurrencyCode")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("varchar(20)")
                        .HasDefaultValue("CNY");

                    b.Property<decimal>("CostDiscountRate")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal>("CostExchangeRate")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("decimal(18,6)")
                        .HasDefaultValue(1m);

                    b.Property<decimal>("CostPrice")
                        .HasColumnType("decimal(18,2)");

                    b.Property<sbyte>("CostPriceType")
                        .HasColumnType("tinyint");

                    b.Property<decimal>("ExchangeRate")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("decimal(18,6)")
                        .HasDefaultValue(1m);

                    b.Property<long?>("OrderSubItemId")
                        .HasColumnType("bigint");

                    b.Property<int>("OrderSubType")
                        .HasColumnType("int");

                    b.Property<sbyte>("OrderType")
                        .HasColumnType("tinyint");

                    b.Property<decimal>("OrgCostPrice")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal>("OrgPrice")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("OrgPriceCurrencyCode")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("varchar(20)")
                        .HasDefaultValue("CNY");

                    b.Property<string>("PaymentCurrencyCode")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("varchar(20)")
                        .HasDefaultValue("CNY");

                    b.Property<decimal>("Price")
                        .HasColumnType("decimal(18,2)");

                    b.Property<sbyte>("PriceType")
                        .HasColumnType("tinyint");

                    b.Property<int>("Quantity")
                        .HasColumnType("int");

                    b.Property<long>("SubOrderId")
                        .HasColumnType("bigint");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("BaseOrderId");

                    b.HasIndex("SubOrderId");

                    b.HasIndex("TenantId");

                    b.ToTable("OrderPrice", (string)null);
                });

            modelBuilder.Entity("Order.Api.Model.OrderPrintSetting", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<sbyte>("Enable")
                        .HasColumnType("tinyint");

                    b.Property<string>("ImpressionImage")
                        .HasColumnType("varchar(200)");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("UpdateTime")
                        .HasColumnType("datetime");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("OrderPrintSetting", (string)null);
                });

            modelBuilder.Entity("Order.Api.Model.OrderShareInfo", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<long>("BaseOrderId")
                        .HasColumnType("bigint");

                    b.Property<long>("BuyerId")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<sbyte>("OrderType")
                        .HasColumnType("tinyint");

                    b.Property<long>("SharerId")
                        .HasColumnType("bigint");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<long?>("TraceId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("OrderShareInfo", (string)null);
                });

            modelBuilder.Entity("Order.Api.Model.ReceiptOrderSetting", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<int>("ReceiptDateTye")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasDefaultValue(2);

                    b.Property<string>("TenantDepartmentIds")
                        .HasColumnType("varchar(400)");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("ReceiptOrderSetting", (string)null);
                });

            modelBuilder.Entity("Order.Api.Model.ReceiptSettlementOrder", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<string>("AccountName")
                        .HasColumnType("varchar(50)");

                    b.Property<long>("AgencyId")
                        .HasColumnType("bigint");

                    b.Property<string>("BankAccount")
                        .HasColumnType("varchar(50)");

                    b.Property<string>("BankCode")
                        .HasColumnType("varchar(50)");

                    b.Property<string>("BankName")
                        .HasColumnType("varchar(50)");

                    b.Property<DateTime>("BillingCycleBegin")
                        .HasColumnType("datetime");

                    b.Property<DateTime>("BillingCycleEnd")
                        .HasColumnType("datetime");

                    b.Property<string>("BranchName")
                        .HasColumnType("varchar(50)");

                    b.Property<DateTime>("CreatTime")
                        .HasColumnType("datetime");

                    b.Property<long>("CreatorId")
                        .HasColumnType("bigint");

                    b.Property<string>("CreatorName")
                        .HasColumnType("varchar(50)");

                    b.Property<decimal>("OrderAmount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<int>("OrderCount")
                        .HasColumnType("int");

                    b.Property<long?>("PayeeId")
                        .HasColumnType("bigint");

                    b.Property<string>("PayeeName")
                        .HasColumnType("varchar(50)");

                    b.Property<DateTime?>("PayeeTime")
                        .HasColumnType("datetime");

                    b.Property<string>("ProofImg")
                        .HasColumnType("varchar(200)");

                    b.Property<DateTime?>("ReceiptCompleteTime")
                        .HasColumnType("datetime");

                    b.Property<decimal?>("ReceivedAmount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("ReceivedAmountCurrencyCode")
                        .HasColumnType("varchar(20)");

                    b.Property<decimal>("RechargeAmount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("Remark")
                        .HasColumnType("varchar(200)");

                    b.Property<long?>("SalespersonId")
                        .HasColumnType("bigint");

                    b.Property<string>("SalespersonName")
                        .HasColumnType("varchar(128)");

                    b.Property<DateTime?>("SendEmailTime")
                        .HasColumnType("datetime");

                    b.Property<sbyte>("SettlementTransferType")
                        .HasColumnType("tinyint");

                    b.Property<sbyte>("Status")
                        .IsConcurrencyToken()
                        .HasColumnType("tinyint");

                    b.Property<long?>("TenantDepartmentId")
                        .HasColumnType("bigint");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<decimal>("TotalAmount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("TotalAmountCurrencyCode")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("varchar(20)")
                        .HasDefaultValue("CNY");

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("ReceiptSettlementOrder", (string)null);
                });

            modelBuilder.Entity("Order.Api.Model.ReceiptSettlementOrderDate", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<sbyte>("BusinessType")
                        .HasColumnType("tinyint");

                    b.Property<sbyte>("OrderDateType")
                        .HasColumnType("tinyint");

                    b.Property<long>("SettlementOrderId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("SettlementOrderId");

                    b.ToTable("ReceiptSettlementOrderDate", (string)null);
                });

            modelBuilder.Entity("Order.Api.Model.ReceiptSettlementOrderDetail", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<long>("AgencyId")
                        .HasColumnType("bigint");

                    b.Property<long>("BaseOrderId")
                        .HasColumnType("bigint");

                    b.Property<long>("BusinessOrderId")
                        .HasColumnType("bigint");

                    b.Property<sbyte>("BusinessType")
                        .HasColumnType("tinyint");

                    b.Property<int>("Quantity")
                        .HasColumnType("int");

                    b.Property<long>("SettlementOrderId")
                        .HasColumnType("bigint");

                    b.Property<decimal>("TotalAmount")
                        .HasColumnType("decimal(18,2)");

                    b.HasKey("Id");

                    b.HasIndex("BusinessOrderId")
                        .IsUnique();

                    b.HasIndex("SettlementOrderId");

                    b.ToTable("ReceiptSettlementOrderDetail", (string)null);
                });

            modelBuilder.Entity("Order.Api.Model.ReceiptSettlementOrderRecord", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<string>("AccountName")
                        .HasColumnType("varchar(50)");

                    b.Property<string>("BankAccount")
                        .HasColumnType("varchar(50)");

                    b.Property<string>("BankCode")
                        .HasColumnType("varchar(50)");

                    b.Property<string>("BankName")
                        .HasColumnType("varchar(50)");

                    b.Property<string>("BranchName")
                        .HasColumnType("varchar(50)");

                    b.Property<DateTime>("CreatTime")
                        .HasColumnType("datetime");

                    b.Property<long?>("PayeeId")
                        .HasColumnType("bigint");

                    b.Property<string>("PayeeName")
                        .HasColumnType("varchar(50)");

                    b.Property<DateTime>("PayeeTime")
                        .HasColumnType("datetime");

                    b.Property<string>("ProofImg")
                        .HasColumnType("varchar(200)");

                    b.Property<decimal?>("ReceivedAmount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("ReceivedAmountCurrencyCode")
                        .HasColumnType("varchar(20)");

                    b.Property<string>("Remark")
                        .HasColumnType("varchar(200)");

                    b.Property<long>("SettlementOrderId")
                        .HasColumnType("bigint");

                    b.Property<sbyte>("SettlementTransferType")
                        .HasColumnType("tinyint");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("ReceiptSettlementOrderRecord", (string)null);
                });

            modelBuilder.Entity("Order.Api.Model.RefundOrder", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<long>("BaseOrderId")
                        .HasColumnType("bigint");

                    b.Property<decimal>("Cost")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("CostCurrencyCode")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("varchar(20)")
                        .HasDefaultValue("CNY");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<string>("ExtRefundNo")
                        .HasColumnType("varchar(50)");

                    b.Property<string>("FailedReason")
                        .HasColumnType("varchar(200)");

                    b.Property<sbyte>("OrderType")
                        .HasColumnType("tinyint");

                    b.Property<string>("PaymentCurrencyCode")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("varchar(20)")
                        .HasDefaultValue("CNY");

                    b.Property<decimal>("PostageAmount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("ProofImgs")
                        .HasColumnType("varchar(500)");

                    b.Property<int>("Quantity")
                        .HasColumnType("int");

                    b.Property<string>("Reason")
                        .HasColumnType("varchar(200)");

                    b.Property<string>("RejectedReason")
                        .HasColumnType("varchar(200)");

                    b.Property<DateTime?>("ReviewTime")
                        .HasColumnType("datetime");

                    b.Property<sbyte>("Status")
                        .IsConcurrencyToken()
                        .HasColumnType("tinyint");

                    b.Property<long>("SubOrdeId")
                        .HasColumnType("bigint");

                    b.Property<long>("SupplierId")
                        .HasColumnType("bigint");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<decimal>("TotalAmount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime");

                    b.Property<long?>("UserId")
                        .HasColumnType("bigint");

                    b.Property<string>("UserName")
                        .HasColumnType("varchar(256)");

                    b.Property<sbyte>("UserType")
                        .HasColumnType("tinyint");

                    b.HasKey("Id");

                    b.HasIndex("BaseOrderId");

                    b.HasIndex("SubOrdeId");

                    b.HasIndex("TenantId");

                    b.ToTable("RefundOrder", (string)null);
                });

            modelBuilder.Entity("Order.Api.Model.ReservationOrder", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<long>("BaseOrderId")
                        .HasColumnType("bigint");

                    b.Property<long>("ClaimantId")
                        .HasColumnType("bigint");

                    b.Property<string>("ClaimantName")
                        .HasColumnType("varchar(50)");

                    b.Property<string>("ConfirmNumber")
                        .HasColumnType("varchar(20)");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<bool>("IsSendPDF")
                        .HasColumnType("tinyint(1)");

                    b.Property<string>("Message")
                        .HasColumnType("varchar(200)");

                    b.Property<decimal>("PaymentAmount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("PaymentChannel")
                        .HasColumnType("varchar(50)");

                    b.Property<string>("PaymentCurrencyCode")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("varchar(20)")
                        .HasDefaultValue("CNY");

                    b.Property<string>("PaymentExternalNo")
                        .HasColumnType("varchar(50)");

                    b.Property<string>("PaymentMode")
                        .HasColumnType("varchar(50)");

                    b.Property<sbyte>("PaymentType")
                        .HasColumnType("tinyint");

                    b.Property<int>("Quantity")
                        .HasColumnType("int");

                    b.Property<long>("ResourceId")
                        .HasColumnType("bigint");

                    b.Property<string>("ResourceName")
                        .HasColumnType("varchar(100)");

                    b.Property<sbyte>("ResourceType")
                        .HasColumnType("tinyint");

                    b.Property<sbyte>("Status")
                        .IsConcurrencyToken()
                        .HasColumnType("tinyint");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<long>("TicketOrderId")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("TravelDateBegin")
                        .HasColumnType("datetime");

                    b.Property<DateTime>("TravelDateEnd")
                        .HasColumnType("datetime");

                    b.Property<string>("Traveler")
                        .HasColumnType("varchar(200)");

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime");

                    b.HasKey("Id");

                    b.HasIndex("BaseOrderId");

                    b.HasIndex("TenantId");

                    b.HasIndex("TicketOrderId");

                    b.ToTable("ReservationOrder", (string)null);
                });

            modelBuilder.Entity("Order.Api.Model.ReservationOrderPayment", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<decimal>("Amount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal>("Cost")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("CostCurrencyCode")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("varchar(20)")
                        .HasDefaultValue("CNY");

                    b.Property<DateTime>("Date")
                        .HasColumnType("datetime");

                    b.Property<decimal>("ExchangeRate")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("decimal(18,6)")
                        .HasDefaultValue(1m);

                    b.Property<decimal>("OrgPrice")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("OrgPriceCurrencyCode")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("varchar(20)")
                        .HasDefaultValue("CNY");

                    b.Property<string>("PaymentCurrencyCode")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("varchar(20)")
                        .HasDefaultValue("CNY");

                    b.Property<long>("ReservationOrderId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("ReservationOrderId");

                    b.ToTable("ReservationOrderPayment", (string)null);
                });

            modelBuilder.Entity("Order.Api.Model.ReservationOrderTicketCode", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<long>("Code")
                        .HasColumnType("bigint");

                    b.Property<long>("ReservationOrderId")
                        .HasColumnType("bigint");

                    b.Property<sbyte>("Status")
                        .HasColumnType("tinyint");

                    b.Property<long>("TicketCodeId")
                        .HasColumnType("bigint");

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime");

                    b.HasKey("Id");

                    b.HasIndex("ReservationOrderId");

                    b.ToTable("ReservationOrderTicketCode", (string)null);
                });

            modelBuilder.Entity("Order.Api.Model.ScenicTicketKlookOrder", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<long>("BaseOrderId")
                        .HasColumnType("bigint");

                    b.Property<int>("Count")
                        .HasColumnType("int");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<string>("Email")
                        .HasColumnType("varchar(100)");

                    b.Property<string>("IDCard")
                        .HasColumnType("varchar(100)");

                    b.Property<string>("KlookActiveId")
                        .IsRequired()
                        .HasColumnType("varchar(50)");

                    b.Property<string>("KlookOrderId")
                        .HasColumnType("varchar(100)");

                    b.Property<string>("KlookPackageId")
                        .IsRequired()
                        .HasColumnType("varchar(50)");

                    b.Property<string>("KlookSkuId")
                        .IsRequired()
                        .HasColumnType("varchar(50)");

                    b.Property<decimal?>("KlookSkuPrice")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("KlookSkuPriceCurrencyCode")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("varchar(20)")
                        .HasDefaultValue("CNY");

                    b.Property<sbyte>("OrderStatus")
                        .IsConcurrencyToken()
                        .HasColumnType("tinyint");

                    b.Property<string>("Phone")
                        .HasColumnType("varchar(100)");

                    b.Property<long>("SubOrderId")
                        .HasColumnType("bigint");

                    b.Property<long>("SupplierId")
                        .HasColumnType("bigint");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime");

                    b.HasKey("Id");

                    b.HasIndex("BaseOrderId");

                    b.HasIndex("TenantId");

                    b.ToTable("ScenicTicketKlookOrder", (string)null);
                });

            modelBuilder.Entity("Order.Api.Model.ScenicTicketKlookOrderRecord", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<int?>("ErrorCode")
                        .HasColumnType("int");

                    b.Property<string>("ErrorMsg")
                        .HasColumnType("varchar(1000)");

                    b.Property<bool>("IsSuccess")
                        .HasColumnType("tinyint(1)");

                    b.Property<sbyte>("RecordType")
                        .HasColumnType("tinyint");

                    b.Property<long>("ScenicTicketKlookOrderId")
                        .HasColumnType("bigint");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("ScenicTicketKlookOrderId");

                    b.HasIndex("TenantId");

                    b.ToTable("ScenicTicketKlookOrderRecord", (string)null);
                });

            modelBuilder.Entity("Order.Api.Model.ScenicTicketKlookVoucher", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<string>("FilePath")
                        .IsRequired()
                        .HasColumnType("varchar(500)");

                    b.Property<string>("ImageSourcePath")
                        .HasColumnType("varchar(500)");

                    b.Property<string>("PdfSourcePath")
                        .HasColumnType("varchar(500)");

                    b.Property<long>("ScenicTicketKlookOrderId")
                        .HasColumnType("bigint");

                    b.Property<string>("SourcePath")
                        .HasColumnType("varchar(500)");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<string>("Thumbnail")
                        .IsRequired()
                        .HasColumnType("varchar(500)");

                    b.HasKey("Id");

                    b.HasIndex("ScenicTicketKlookOrderId");

                    b.HasIndex("TenantId");

                    b.ToTable("ScenicTicketKlookVoucher", (string)null);
                });

            modelBuilder.Entity("Order.Api.Model.ScenicTicketOrder", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<bool>("AutoRefundAfterExpiration")
                        .HasColumnType("tinyint(1)");

                    b.Property<int>("AutoRefundRate")
                        .HasColumnType("int");

                    b.Property<long>("BaseOrderId")
                        .HasColumnType("bigint");

                    b.Property<sbyte?>("CredentialSourceType")
                        .HasColumnType("tinyint");

                    b.Property<string>("ExchangeLocation")
                        .HasColumnType("varchar(200)");

                    b.Property<string>("ExchangeNote")
                        .HasColumnType("varchar(500)");

                    b.Property<int>("ExchangeProof")
                        .HasColumnType("int");

                    b.Property<string>("FeeNotNote")
                        .HasColumnType("text");

                    b.Property<string>("FeeNote")
                        .HasColumnType("text");

                    b.Property<bool>("IsChannelTimeliness")
                        .HasColumnType("tinyint(1)");

                    b.Property<bool>("IsSupportRefund")
                        .HasColumnType("tinyint(1)");

                    b.Property<bool>("IsVirtualVoucher")
                        .HasColumnType("tinyint(1)");

                    b.Property<decimal>("LinePrice")
                        .HasColumnType("decimal(18,2)");

                    b.Property<bool>("NeedToExchange")
                        .HasColumnType("tinyint(1)");

                    b.Property<sbyte?>("OpenSupplierType")
                        .HasColumnType("tinyint");

                    b.Property<string>("OpeningTime")
                        .HasColumnType("varchar(100)");

                    b.Property<string>("OtherNote")
                        .HasColumnType("text");

                    b.Property<int>("Quantity")
                        .HasColumnType("int");

                    b.Property<int>("RefundDaysInAdvance")
                        .HasColumnType("int");

                    b.Property<int>("RefundRate")
                        .HasColumnType("int");

                    b.Property<TimeOnly>("RefundTimeInAdvance")
                        .HasColumnType("time");

                    b.Property<string>("ScenicSpotAddress")
                        .HasColumnType("varchar(100)");

                    b.Property<long>("ScenicSpotId")
                        .HasColumnType("bigint");

                    b.Property<string>("ScenicSpotPhotoPath")
                        .HasColumnType("varchar(256)");

                    b.Property<long>("ScenicTicketId")
                        .HasColumnType("bigint");

                    b.Property<sbyte>("ScenicTicketsType")
                        .HasColumnType("tinyint");

                    b.Property<sbyte>("Status")
                        .IsConcurrencyToken()
                        .HasColumnType("tinyint");

                    b.Property<string>("SupplierActivityId")
                        .HasColumnType("varchar(500)");

                    b.Property<long>("SupplierId")
                        .HasColumnType("bigint");

                    b.Property<string>("SupplierOrderId")
                        .HasColumnType("varchar(500)");

                    b.Property<string>("SupplierPackageId")
                        .HasColumnType("varchar(500)");

                    b.Property<string>("SupplierSkuId")
                        .HasColumnType("varchar(500)");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<long?>("TicketsCombinationOrderId")
                        .HasColumnType("bigint");

                    b.Property<TimeSpan?>("TimeSlot")
                        .HasColumnType("time");

                    b.Property<long?>("TimeSlotId")
                        .HasColumnType("bigint");

                    b.Property<int?>("TimelinessChannelTypes")
                        .HasColumnType("int");

                    b.Property<DateTime?>("TravelDate")
                        .HasColumnType("datetime");

                    b.Property<DateTime>("ValidityBegin")
                        .HasColumnType("datetime");

                    b.Property<DateTime>("ValidityEnd")
                        .HasColumnType("datetime");

                    b.HasKey("Id");

                    b.HasIndex("BaseOrderId");

                    b.HasIndex("TenantId");

                    b.HasIndex("TicketsCombinationOrderId");

                    b.HasIndex("TenantId", "BaseOrderId");

                    b.ToTable("ScenicTicketOrder", (string)null);
                });

            modelBuilder.Entity("Order.Api.Model.ScenicTicketOrderTravelers", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<long>("BaseOrderId")
                        .HasColumnType("bigint");

                    b.Property<DateTime?>("Birthday")
                        .HasColumnType("datetime");

                    b.Property<DateTime?>("CardValidDate")
                        .HasColumnType("datetime");

                    b.Property<string>("Email")
                        .HasColumnType("varchar(50)");

                    b.Property<string>("ExtendInfo")
                        .HasColumnType("mediumtext");

                    b.Property<string>("FirstName")
                        .HasColumnType("varchar(50)");

                    b.Property<sbyte>("Gender")
                        .HasColumnType("tinyint");

                    b.Property<decimal?>("Height")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("IDCard")
                        .HasColumnType("varchar(50)");

                    b.Property<sbyte>("IdCardType")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("tinyint")
                        .HasDefaultValue((sbyte)1);

                    b.Property<string>("LastName")
                        .HasColumnType("varchar(50)");

                    b.Property<string>("Name")
                        .HasColumnType("varchar(50)");

                    b.Property<string>("NationalityCode")
                        .HasColumnType("varchar(50)");

                    b.Property<string>("NationalityName")
                        .HasColumnType("varchar(50)");

                    b.Property<string>("PhoneNumber")
                        .HasColumnType("varchar(50)");

                    b.Property<long>("ScenicTicketOrderId")
                        .HasColumnType("bigint");

                    b.Property<decimal?>("ShoeSize")
                        .HasColumnType("decimal(18,2)");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<decimal?>("Weight")
                        .HasColumnType("decimal(18,2)");

                    b.HasKey("Id");

                    b.HasIndex("BaseOrderId");

                    b.HasIndex("ScenicTicketOrderId");

                    b.HasIndex("TenantId");

                    b.ToTable("ScenicTicketOrderTravelers", (string)null);
                });

            modelBuilder.Entity("Order.Api.Model.ScenicTicketPurchaseDeliveryRecord", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<long>("BaseOrderId")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<sbyte>("Status")
                        .HasColumnType("tinyint");

                    b.Property<long>("SubOrderId")
                        .HasColumnType("bigint");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("BaseOrderId");

                    b.HasIndex("TenantId");

                    b.ToTable("ScenicTicketPurchaseDeliveryRecord", (string)null);
                });

            modelBuilder.Entity("Order.Api.Model.ScenicTicketPurchaseInventoryCheckRecord", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<string>("ErrorMessage")
                        .HasColumnType("varchar(100)");

                    b.Property<int>("FailCount")
                        .HasColumnType("int");

                    b.Property<string>("FileUrl")
                        .HasColumnType("varchar(1024)");

                    b.Property<sbyte>("InventoryCheckResult")
                        .HasColumnType("tinyint");

                    b.Property<sbyte>("InventoryCheckType")
                        .HasColumnType("tinyint");

                    b.Property<long>("OperationUserId")
                        .HasColumnType("bigint");

                    b.Property<string>("OperationUserName")
                        .HasColumnType("varchar(50)");

                    b.Property<int>("SuccessCount")
                        .HasColumnType("int");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<int>("TotalCount")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("InventoryCheckResult");

                    b.HasIndex("InventoryCheckType");

                    b.HasIndex("TenantId");

                    b.ToTable("ScenicTicketPurchaseInventoryCheckRecord", (string)null);
                });

            modelBuilder.Entity("Order.Api.Model.ScenicTicketPurchaseOrder", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<string>("BatchName")
                        .IsRequired()
                        .HasColumnType("varchar(100)");

                    b.Property<decimal>("CostDiscountRate")
                        .HasColumnType("decimal(18,2)");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<string>("CurrencyCode")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("varchar(20)")
                        .HasDefaultValue("CNY");

                    b.Property<bool>("InboundInventoryChecked")
                        .HasColumnType("tinyint(1)");

                    b.Property<DateTime?>("InboundInventoryCheckedTime")
                        .HasColumnType("datetime");

                    b.Property<bool>("IsDefault")
                        .HasColumnType("tinyint(1)");

                    b.Property<bool>("Outbound")
                        .HasColumnType("tinyint(1)");

                    b.Property<DateTime?>("OutboundTime")
                        .HasColumnType("datetime");

                    b.Property<string>("Remark")
                        .HasColumnType("varchar(500)");

                    b.Property<long>("ScenicSpotId")
                        .HasColumnType("bigint");

                    b.Property<long>("SupplierId")
                        .HasColumnType("bigint");

                    b.Property<string>("SupplierOrderId")
                        .HasColumnType("varchar(100)");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<long>("TicketId")
                        .HasColumnType("bigint");

                    b.Property<string>("TicketName")
                        .IsRequired()
                        .HasColumnType("varchar(100)");

                    b.Property<sbyte>("TicketsType")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("tinyint")
                        .HasDefaultValue((sbyte)2);

                    b.Property<decimal>("TotalAmount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<int>("TotalQuantity")
                        .HasColumnType("int");

                    b.Property<int>("UnUsedQuantity")
                        .HasColumnType("int");

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime");

                    b.Property<DateTime>("ValidityBegin")
                        .HasColumnType("datetime");

                    b.Property<DateTime>("ValidityEnd")
                        .HasColumnType("datetime");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.HasIndex("TicketId");

                    b.ToTable("ScenicTicketPurchaseOrder", (string)null);
                });

            modelBuilder.Entity("Order.Api.Model.ScenicTicketPurchaseVoucher", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<string>("CompositeImagePath")
                        .HasColumnType("varchar(500)");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<string>("FilePath")
                        .IsRequired()
                        .HasColumnType("varchar(500)");

                    b.Property<string>("OriginalName")
                        .IsRequired()
                        .HasColumnType("varchar(500)");

                    b.Property<long>("PurchaseOrderId")
                        .HasColumnType("bigint");

                    b.Property<sbyte>("Status")
                        .IsConcurrencyToken()
                        .HasColumnType("tinyint");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<string>("Thumbnail")
                        .IsRequired()
                        .HasColumnType("varchar(500)");

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime");

                    b.Property<DateTime?>("UsedTime")
                        .HasColumnType("datetime");

                    b.HasKey("Id");

                    b.HasIndex("PurchaseOrderId");

                    b.HasIndex("TenantId");

                    b.ToTable("ScenicTicketPurchaseVoucher", (string)null);
                });

            modelBuilder.Entity("Order.Api.Model.ScenicTicketPurchaseVoucherUsedDetail", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<long>("BaseOrderId")
                        .HasColumnType("bigint");

                    b.Property<decimal>("CostPrice")
                        .HasColumnType("decimal(18,2)");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<long>("PurchaseOrderId")
                        .HasColumnType("bigint");

                    b.Property<long>("SubOrderId")
                        .HasColumnType("bigint");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<long>("TicketVoucherId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("BaseOrderId");

                    b.HasIndex("TenantId");

                    b.ToTable("ScenicTicketPurchaseVoucherUsedDetail", (string)null);
                });

            modelBuilder.Entity("Order.Api.Model.ScenicTicketSupplierOrder", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<string>("ActiveId")
                        .IsRequired()
                        .HasColumnType("varchar(500)");

                    b.Property<decimal>("Amount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<long>("BaseOrderId")
                        .HasColumnType("bigint");

                    b.Property<string>("Buyer")
                        .HasColumnType("varchar(100)");

                    b.Property<decimal>("CommissionRate")
                        .HasColumnType("decimal(18,2)");

                    b.Property<int>("Count")
                        .HasColumnType("int");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<string>("CurrencyCode")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("varchar(20)")
                        .HasDefaultValue("CNY");

                    b.Property<string>("Email")
                        .HasColumnType("varchar(100)");

                    b.Property<string>("IDCard")
                        .HasColumnType("varchar(100)");

                    b.Property<bool>("IsVirtualVoucher")
                        .HasColumnType("tinyint(1)");

                    b.Property<sbyte>("OrderStatus")
                        .HasColumnType("tinyint");

                    b.Property<string>("PackageId")
                        .IsRequired()
                        .HasColumnType("varchar(500)");

                    b.Property<string>("Phone")
                        .HasColumnType("varchar(100)");

                    b.Property<string>("SkuId")
                        .IsRequired()
                        .HasColumnType("varchar(500)");

                    b.Property<decimal?>("SkuPrice")
                        .HasColumnType("decimal(18,2)");

                    b.Property<long>("SubOrderId")
                        .HasColumnType("bigint");

                    b.Property<long>("SupplierId")
                        .HasColumnType("bigint");

                    b.Property<string>("SupplierOrderId")
                        .HasColumnType("varchar(100)");

                    b.Property<sbyte>("SupplierType")
                        .HasColumnType("tinyint");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<TimeSpan?>("TimeSlot")
                        .HasColumnType("time");

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime");

                    b.HasKey("Id");

                    b.HasIndex("BaseOrderId");

                    b.HasIndex("TenantId");

                    b.ToTable("ScenicTicketSupplierOrder", (string)null);
                });

            modelBuilder.Entity("Order.Api.Model.ScenicTicketSupplierOrderRecord", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<long>("BaseOrderId")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<int?>("ErrorCode")
                        .HasColumnType("int");

                    b.Property<string>("ErrorMsg")
                        .HasColumnType("varchar(1000)");

                    b.Property<bool>("IsSuccess")
                        .HasColumnType("tinyint(1)");

                    b.Property<sbyte>("RecordType")
                        .HasColumnType("tinyint");

                    b.Property<long>("ScenicTicketSupplierOrderId")
                        .HasColumnType("bigint");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("BaseOrderId");

                    b.HasIndex("TenantId");

                    b.ToTable("ScenicTicketSupplierOrderRecord", (string)null);
                });

            modelBuilder.Entity("Order.Api.Model.ScenicTicketSupplierOrderVoucher", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<long>("BaseOrderId")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<string>("FilePath")
                        .IsRequired()
                        .HasColumnType("varchar(500)");

                    b.Property<string>("ImageSourcePath")
                        .HasColumnType("varchar(500)");

                    b.Property<string>("PdfSourcePath")
                        .HasColumnType("varchar(500)");

                    b.Property<long>("ScenicTicketSupplierOrderId")
                        .HasColumnType("bigint");

                    b.Property<string>("SourcePath")
                        .HasColumnType("varchar(500)");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<string>("Thumbnail")
                        .IsRequired()
                        .HasColumnType("varchar(500)");

                    b.Property<sbyte>("VoucherSourceType")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("tinyint")
                        .HasDefaultValue((sbyte)0);

                    b.HasKey("Id");

                    b.HasIndex("BaseOrderId");

                    b.HasIndex("TenantId");

                    b.ToTable("ScenicTicketSupplierOrderVoucher", (string)null);
                });

            modelBuilder.Entity("Order.Api.Model.SettlementOrder", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<string>("AbroadAccountAddress")
                        .HasColumnType("varchar(100)");

                    b.Property<string>("AbroadAccountCurrencyCode")
                        .HasColumnType("varchar(20)");

                    b.Property<string>("AccountName")
                        .HasColumnType("varchar(50)");

                    b.Property<string>("BankAccount")
                        .HasColumnType("varchar(50)");

                    b.Property<int>("BankAccountType")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasDefaultValue(1)
                        .HasComment("银行账户类型 1:国内 2:国外 3:第三方银行账户");

                    b.Property<string>("BankCode")
                        .HasColumnType("varchar(50)");

                    b.Property<string>("BankName")
                        .HasColumnType("varchar(50)");

                    b.Property<DateTime>("BillingCycleBegin")
                        .HasColumnType("datetime");

                    b.Property<DateTime>("BillingCycleEnd")
                        .HasColumnType("datetime");

                    b.Property<string>("BranchName")
                        .HasColumnType("varchar(50)");

                    b.Property<DateTime>("CreatTime")
                        .HasColumnType("datetime");

                    b.Property<long>("CreatorId")
                        .HasColumnType("bigint");

                    b.Property<string>("CreatorName")
                        .HasColumnType("varchar(50)");

                    b.Property<int>("OrderCount")
                        .HasColumnType("int");

                    b.Property<DateTime?>("PayTime")
                        .HasColumnType("datetime");

                    b.Property<long?>("PayerId")
                        .HasColumnType("bigint");

                    b.Property<string>("PayerName")
                        .HasColumnType("varchar(50)");

                    b.Property<decimal?>("PaymentAmount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("PaymentAmountCurrencyCode")
                        .HasColumnType("varchar(20)");

                    b.Property<string>("ProofImg")
                        .HasColumnType("varchar(200)");

                    b.Property<string>("Remark")
                        .HasColumnType("varchar(200)");

                    b.Property<sbyte>("Status")
                        .IsConcurrencyToken()
                        .HasColumnType("tinyint");

                    b.Property<long>("SupplierId")
                        .HasColumnType("bigint");

                    b.Property<string>("SwiftCode")
                        .HasColumnType("varchar(200)");

                    b.Property<long?>("TenantDepartmentId")
                        .HasColumnType("bigint");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<decimal>("TotalAmount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("TotalAmountCurrencyCode")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("varchar(20)")
                        .HasDefaultValue("CNY");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("SettlementOrder", (string)null);
                });

            modelBuilder.Entity("Order.Api.Model.SettlementOrderDate", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<sbyte>("OrderDateType")
                        .HasColumnType("tinyint");

                    b.Property<sbyte>("SettlementBusinessType")
                        .HasColumnType("tinyint");

                    b.Property<long>("SettlementOrderId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("SettlementOrderId");

                    b.ToTable("SettlementOrderDate", (string)null);
                });

            modelBuilder.Entity("Order.Api.Model.SettlementOrderDetail", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<long>("BaseOrderId")
                        .HasColumnType("bigint");

                    b.Property<long>("BusinessOrderId")
                        .HasColumnType("bigint");

                    b.Property<int>("Quantity")
                        .HasColumnType("int");

                    b.Property<sbyte>("SettlementBusinessType")
                        .HasColumnType("tinyint");

                    b.Property<long>("SettlementOrderId")
                        .HasColumnType("bigint");

                    b.Property<long>("SupplierId")
                        .HasColumnType("bigint");

                    b.Property<decimal>("TotalAmount")
                        .HasColumnType("decimal(18,2)");

                    b.HasKey("Id");

                    b.HasIndex("BusinessOrderId")
                        .IsUnique();

                    b.HasIndex("SettlementOrderId");

                    b.ToTable("SettlementOrderDetail", (string)null);
                });

            modelBuilder.Entity("Order.Api.Model.SettlementOrderTransferRecord", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<decimal>("BankFee")
                        .HasColumnType("decimal(18,2)");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<string>("ErrorMsg")
                        .HasColumnType("varchar(500)");

                    b.Property<long?>("PayerId")
                        .HasColumnType("bigint");

                    b.Property<string>("PayerName")
                        .HasColumnType("varchar(50)");

                    b.Property<long>("SettlementOrderId")
                        .HasColumnType("bigint");

                    b.Property<sbyte>("Status")
                        .IsConcurrencyToken()
                        .HasColumnType("tinyint");

                    b.Property<sbyte>("TransferType")
                        .HasColumnType("tinyint");

                    b.HasKey("Id");

                    b.ToTable("SettlementOrderTransferRecord", (string)null);
                });

            modelBuilder.Entity("Order.Api.Model.StoredValueCardOrder", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<long>("BaseOrderId")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<sbyte>("Status")
                        .HasColumnType("tinyint");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("StoredValueCardOrder", (string)null);
                });

            modelBuilder.Entity("Order.Api.Model.StoredValueCardOrderGearGift", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<sbyte>("GiftType")
                        .HasColumnType("tinyint");

                    b.Property<long>("StoredValueCardOrderId")
                        .HasColumnType("bigint");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("StoredValueCardOrderGearGift", (string)null);
                });

            modelBuilder.Entity("Order.Api.Model.StoredValueCardOrderGearGiftItem", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<int>("Count")
                        .HasColumnType("int");

                    b.Property<long>("GiftItemId")
                        .HasColumnType("bigint");

                    b.Property<string>("ItemName")
                        .HasColumnType("varchar(200)");

                    b.Property<long>("StoredValueCardOrderGearGiftId")
                        .HasColumnType("bigint");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("StoredValueCardOrderGearGiftItem", (string)null);
                });

            modelBuilder.Entity("Order.Api.Model.ThirdInsureProduct", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<string>("Extend")
                        .IsRequired()
                        .HasColumnType("mediumtext");

                    b.Property<string>("ProductId")
                        .HasColumnType("varchar(50)");

                    b.Property<string>("ProductName")
                        .HasColumnType("varchar(500)");

                    b.Property<string>("ProductUnionId")
                        .IsRequired()
                        .HasColumnType("varchar(50)");

                    b.Property<int>("Type")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.ToTable("ThirdInsureProduct", (string)null);
                });

            modelBuilder.Entity("Order.Api.Model.TicketCode", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<long>("BaseOrderId")
                        .HasColumnType("bigint");

                    b.Property<long>("Code")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<sbyte>("OrderType")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("tinyint")
                        .HasDefaultValue((sbyte)1);

                    b.Property<sbyte>("Status")
                        .IsConcurrencyToken()
                        .HasColumnType("tinyint");

                    b.Property<long>("SubOrderId")
                        .HasColumnType("bigint");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime");

                    b.HasKey("Id");

                    b.HasIndex("BaseOrderId");

                    b.HasIndex("Code")
                        .IsUnique();

                    b.HasIndex("SubOrderId");

                    b.HasIndex("TenantId");

                    b.ToTable("TicketCode", (string)null);
                });

            modelBuilder.Entity("Order.Api.Model.TicketCodeUsed", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<decimal>("Amount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("AmountCurrencyCode")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("varchar(20)")
                        .HasDefaultValue("CNY");

                    b.Property<long>("BaseOrderId")
                        .HasColumnType("bigint");

                    b.Property<int>("Count")
                        .HasColumnType("int");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<long>("CreatorId")
                        .HasColumnType("bigint");

                    b.Property<string>("CreatorName")
                        .HasColumnType("varchar(20)");

                    b.Property<sbyte>("OrderType")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("tinyint")
                        .HasDefaultValue((sbyte)1);

                    b.Property<sbyte>("ProductBusinessType")
                        .HasColumnType("tinyint");

                    b.Property<long>("ProductSupplierId")
                        .HasColumnType("bigint");

                    b.Property<string>("Remark")
                        .HasColumnType("varchar(256)");

                    b.Property<long>("ReservationOrderId")
                        .HasColumnType("bigint");

                    b.Property<long>("ResourceId")
                        .HasColumnType("bigint");

                    b.Property<string>("ResourceName")
                        .HasColumnType("varchar(100)");

                    b.Property<sbyte>("ResourceType")
                        .HasColumnType("tinyint");

                    b.Property<long>("SubOrderId")
                        .HasColumnType("bigint");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<sbyte>("UsedSource")
                        .HasColumnType("tinyint");

                    b.Property<sbyte>("UserType")
                        .HasColumnType("tinyint");

                    b.HasKey("Id");

                    b.HasIndex("BaseOrderId");

                    b.HasIndex("SubOrderId");

                    b.HasIndex("TenantId");

                    b.ToTable("TicketCodeUsed", (string)null);
                });

            modelBuilder.Entity("Order.Api.Model.TicketCodeUsedDetails", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<long>("Code")
                        .HasColumnType("bigint");

                    b.Property<long>("TicketCodeId")
                        .HasColumnType("bigint");

                    b.Property<long>("TicketCodeUsedId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.ToTable("TicketCodeUsedDetails", (string)null);
                });

            modelBuilder.Entity("Order.Api.Model.TicketOrder", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<long>("BaseOrderId")
                        .HasColumnType("bigint");

                    b.Property<bool>("ProductAutoRefundAfterExpiration")
                        .HasColumnType("tinyint(1)");

                    b.Property<decimal>("ProductAutoRefundRate")
                        .HasColumnType("decimal(8,4)");

                    b.Property<long>("ProductId")
                        .HasColumnType("bigint");

                    b.Property<bool>("ProductIsSupportRefund")
                        .HasColumnType("tinyint(1)");

                    b.Property<bool>("ProductNeedConfirmReservation")
                        .HasColumnType("tinyint(1)");

                    b.Property<bool>("ProductNeedReservation")
                        .HasColumnType("tinyint(1)");

                    b.Property<bool>("ProductNeedWriteOff")
                        .HasColumnType("tinyint(1)");

                    b.Property<decimal>("ProductRefundRate")
                        .HasColumnType("decimal(8,4)");

                    b.Property<int>("ProductReservationDaysInAdvance")
                        .HasColumnType("int");

                    b.Property<long>("ProductSupplierId")
                        .HasColumnType("bigint");

                    b.Property<sbyte>("ProductTicketBusinessType")
                        .HasColumnType("tinyint");

                    b.Property<string>("ProductTitle")
                        .HasColumnType("varchar(100)");

                    b.Property<int>("Quantity")
                        .HasColumnType("int");

                    b.Property<int?>("RefundBeforeTravelDateDay")
                        .HasColumnType("int");

                    b.Property<TimeSpan?>("RefundTravelDateTime")
                        .HasColumnType("time");

                    b.Property<string>("SkuCostDescription")
                        .HasColumnType("varchar(500)");

                    b.Property<long>("SkuId")
                        .HasColumnType("bigint");

                    b.Property<string>("SkuImagePath")
                        .HasColumnType("varchar(255)");

                    b.Property<string>("SkuName")
                        .HasColumnType("varchar(100)");

                    b.Property<int>("SkuNumberOfNights")
                        .HasColumnType("int");

                    b.Property<DateTime>("SkuValidityBegin")
                        .HasColumnType("datetime");

                    b.Property<DateTime>("SkuValidityEnd")
                        .HasColumnType("datetime");

                    b.Property<string>("SupplierOrderId")
                        .HasColumnType("varchar(500)");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<sbyte>("TicketSaleType")
                        .HasColumnType("tinyint");

                    b.HasKey("Id");

                    b.HasIndex("BaseOrderId");

                    b.HasIndex("TenantId");

                    b.ToTable("TicketOrder", (string)null);
                });

            modelBuilder.Entity("Order.Api.Model.TicketOrderModifyRecord", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<long>("BaseOrderId")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<long>("CreatorId")
                        .HasColumnType("bigint");

                    b.Property<string>("CreatorName")
                        .HasColumnType("varchar(20)");

                    b.Property<decimal>("DiscountAmount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal>("PayAmount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<int>("Quantity")
                        .HasColumnType("int");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<long>("TicektOrderId")
                        .HasColumnType("bigint");

                    b.Property<decimal>("TotalAmount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<sbyte>("UserType")
                        .HasColumnType("tinyint");

                    b.HasKey("Id");

                    b.HasIndex("BaseOrderId");

                    b.HasIndex("TenantId");

                    b.ToTable("TicketOrderModifyRecord", (string)null);
                });

            modelBuilder.Entity("Order.Api.Model.TicketOrderTraveler", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<long>("BaseOrderId")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<string>("IDCard")
                        .HasColumnType("varchar(50)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("varchar(50)");

                    b.Property<string>("PhoneNumber")
                        .HasColumnType("varchar(50)");

                    b.Property<long>("ReservationOrderId")
                        .HasColumnType("bigint");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<long>("TicketOrderId")
                        .HasColumnType("bigint");

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.HasIndex("TenantId", "BaseOrderId");

                    b.ToTable("TicketOrderTraveler", (string)null);
                });

            modelBuilder.Entity("Order.Api.Model.TicketsCombinationMergeOrder", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<long>("CombinationOrderId")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<string>("SupplierOrderId")
                        .HasColumnType("varchar(100)");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("CombinationOrderId");

                    b.HasIndex("TenantId");

                    b.ToTable("TicketsCombinationMergeOrder", (string)null);
                });

            modelBuilder.Entity("Order.Api.Model.TicketsCombinationMergeOrderItem", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<long>("BaseOrderId")
                        .HasColumnType("bigint");

                    b.Property<long>("CombinationOrderId")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<long>("MergeOrderId")
                        .HasColumnType("bigint");

                    b.Property<string>("SupplierOrderId")
                        .HasColumnType("varchar(100)");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("BaseOrderId");

                    b.HasIndex("CombinationOrderId");

                    b.HasIndex("TenantId");

                    b.ToTable("TicketsCombinationMergeOrderItem", (string)null);
                });

            modelBuilder.Entity("Order.Api.Model.TicketsCombinationOrder", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<sbyte?>("AbnormalOrderSourceType")
                        .HasColumnType("tinyint");

                    b.Property<string>("AbnormalReason")
                        .IsRequired()
                        .HasColumnType("varchar(500)");

                    b.Property<long?>("AgencyId")
                        .HasColumnType("bigint");

                    b.Property<string>("ChannelOrderNo")
                        .IsRequired()
                        .HasColumnType("longtext");

                    b.Property<DateTime?>("ClosedTime")
                        .HasColumnType("datetime");

                    b.Property<sbyte>("CombinationType")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("tinyint")
                        .HasDefaultValue((sbyte)1);

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<string>("DataContentJson")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<sbyte>("OrderStatus")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("tinyint")
                        .HasDefaultValue((sbyte)1);

                    b.Property<decimal>("PaymentAmount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("PaymentCurrencyCode")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("varchar(20)")
                        .HasDefaultValue("CNY");

                    b.Property<int>("Quantity")
                        .HasColumnType("int");

                    b.Property<short?>("SellingChannels")
                        .HasColumnType("smallint");

                    b.Property<sbyte?>("SellingPlatform")
                        .HasColumnType("tinyint");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<long>("TicketsCombinationId")
                        .HasColumnType("bigint");

                    b.Property<string>("TicketsCombinationName")
                        .IsRequired()
                        .HasColumnType("varchar(64)");

                    b.Property<long>("TicketsCombinationPackageId")
                        .HasColumnType("bigint");

                    b.Property<string>("TicketsCombinationPackageName")
                        .IsRequired()
                        .HasColumnType("varchar(200)");

                    b.Property<DateTime?>("TravelDate")
                        .HasColumnType("datetime");

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("TicketsCombinationOrder", (string)null);
                });

            modelBuilder.Entity("Order.Api.Model.TravelLineOrder", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<string>("AdultsStandard")
                        .HasColumnType("varchar(50)");

                    b.Property<bool?>("AutoConfirm")
                        .HasColumnType("tinyint(1)");

                    b.Property<string>("BabyStandard")
                        .HasColumnType("varchar(50)");

                    b.Property<long>("BaseOrderId")
                        .HasColumnType("bigint");

                    b.Property<string>("ChildrenStandard")
                        .HasColumnType("varchar(50)");

                    b.Property<long?>("ClaimantId")
                        .HasColumnType("bigint");

                    b.Property<string>("ClaimantName")
                        .HasColumnType("varchar(50)");

                    b.Property<int>("CoordinateType")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasDefaultValue(1);

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<int>("Days")
                        .HasColumnType("int");

                    b.Property<string>("ElderlyStandard")
                        .HasColumnType("varchar(50)");

                    b.Property<string>("FeeIncludes")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("FeeNotNote")
                        .HasColumnType("text");

                    b.Property<string>("FeeNote")
                        .HasColumnType("text");

                    b.Property<string>("ImagePath")
                        .HasColumnType("varchar(100)");

                    b.Property<string>("Instructions")
                        .HasColumnType("varchar(500)");

                    b.Property<bool>("IsChannelTimeliness")
                        .HasColumnType("tinyint(1)");

                    b.Property<bool>("IsSupportRefund")
                        .HasColumnType("tinyint(1)");

                    b.Property<bool>("IsVirtualVoucher")
                        .HasColumnType("tinyint(1)");

                    b.Property<string>("KindReminder")
                        .HasColumnType("varchar(500)");

                    b.Property<long>("LineProductId")
                        .HasColumnType("bigint");

                    b.Property<long>("LineProductSkuId")
                        .HasColumnType("bigint");

                    b.Property<int>("Nights")
                        .HasColumnType("int");

                    b.Property<int>("NumberOfRooms")
                        .HasColumnType("int");

                    b.Property<sbyte?>("OpenSupplierType")
                        .HasColumnType("tinyint");

                    b.Property<string>("OtherNote")
                        .HasColumnType("text");

                    b.Property<sbyte>("PurchaseSourceType")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("tinyint")
                        .HasDefaultValue((sbyte)1);

                    b.Property<string>("RallyPointAddress")
                        .HasColumnType("varchar(100)");

                    b.Property<Point>("RallyPointLocation")
                        .HasColumnType("point");

                    b.Property<TimeSpan?>("RallyPointTime")
                        .HasColumnType("time");

                    b.Property<int?>("RefundBeforeTravelDateDay")
                        .HasColumnType("int");

                    b.Property<decimal?>("RefundRate")
                        .HasColumnType("decimal(8,4)");

                    b.Property<TimeSpan?>("RefundTravelDateTime")
                        .HasColumnType("time");

                    b.Property<sbyte>("Status")
                        .IsConcurrencyToken()
                        .HasColumnType("tinyint");

                    b.Property<string>("SupplierActivityId")
                        .HasColumnType("varchar(500)");

                    b.Property<long>("SupplierId")
                        .HasColumnType("bigint");

                    b.Property<string>("SupplierOrderId")
                        .HasColumnType("varchar(500)");

                    b.Property<string>("SupplierPackageId")
                        .HasColumnType("varchar(500)");

                    b.Property<string>("SupplierSkuId")
                        .HasColumnType("varchar(500)");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<long?>("TimeSlotId")
                        .HasColumnType("bigint");

                    b.Property<string>("TimeSlotName")
                        .HasColumnType("varchar(200)");

                    b.Property<int?>("TimelinessChannelTypes")
                        .HasColumnType("int");

                    b.Property<string>("TourGuideCarNumber")
                        .HasColumnType("varchar(20)");

                    b.Property<string>("TourGuideName")
                        .HasColumnType("varchar(20)");

                    b.Property<string>("TourGuidePhoneNumber")
                        .HasColumnType("varchar(20)");

                    b.Property<DateTime>("TravelBeginDate")
                        .HasColumnType("datetime");

                    b.Property<DateTime>("TravelEndDate")
                        .HasColumnType("datetime");

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime");

                    b.HasKey("Id");

                    b.HasIndex("BaseOrderId");

                    b.HasIndex("TenantId");

                    b.ToTable("TravelLineOrder", (string)null);
                });

            modelBuilder.Entity("Order.Api.Model.TravelLineOrderSkuTypeItem", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<string>("ActivityId")
                        .HasColumnType("varchar(100)");

                    b.Property<long>("BaseOrderId")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<string>("PackageId")
                        .HasColumnType("varchar(100)");

                    b.Property<string>("SkuId")
                        .HasColumnType("varchar(100)");

                    b.Property<sbyte>("SkuPriceType")
                        .HasColumnType("tinyint");

                    b.Property<long>("SkuTypeItemId")
                        .HasColumnType("bigint");

                    b.Property<string>("SkuTypeItemName")
                        .IsRequired()
                        .HasColumnType("varchar(500)");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<long>("TravelLineOrderId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("BaseOrderId");

                    b.HasIndex("TenantId");

                    b.ToTable("TravelLineOrderSkuTypeItem", (string)null);
                });

            modelBuilder.Entity("Order.Api.Model.TravelLineOrderTravelers", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<long>("BaseOrderId")
                        .HasColumnType("bigint");

                    b.Property<DateTime?>("Birthday")
                        .HasColumnType("datetime");

                    b.Property<DateTime?>("CardValidDate")
                        .HasColumnType("datetime");

                    b.Property<string>("Email")
                        .HasColumnType("varchar(50)");

                    b.Property<string>("ExtendInfo")
                        .HasColumnType("mediumtext");

                    b.Property<string>("FirstName")
                        .HasColumnType("varchar(50)");

                    b.Property<sbyte>("Gender")
                        .HasColumnType("tinyint");

                    b.Property<decimal?>("Height")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("IDCard")
                        .HasColumnType("varchar(20)");

                    b.Property<sbyte>("IdCardType")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("tinyint")
                        .HasDefaultValue((sbyte)1);

                    b.Property<string>("InternationalDialingCode")
                        .HasColumnType("varchar(20)");

                    b.Property<string>("LastName")
                        .HasColumnType("varchar(50)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("varchar(20)");

                    b.Property<string>("NationalityCode")
                        .HasColumnType("varchar(50)");

                    b.Property<string>("NationalityName")
                        .HasColumnType("varchar(50)");

                    b.Property<string>("Phone")
                        .HasColumnType("varchar(20)");

                    b.Property<decimal?>("ShoeSize")
                        .HasColumnType("decimal(18,2)");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<long>("TravelLineOrderId")
                        .HasColumnType("bigint");

                    b.Property<sbyte>("Type")
                        .HasColumnType("tinyint");

                    b.Property<decimal?>("Weight")
                        .HasColumnType("decimal(18,2)");

                    b.HasKey("Id");

                    b.HasIndex("BaseOrderId");

                    b.HasIndex("TenantId");

                    b.HasIndex("TravelLineOrderId");

                    b.ToTable("TravelLineOrderTravelers", (string)null);
                });

            modelBuilder.Entity("Order.Api.Model.TravelLineOtaOrder", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<long>("BaseOrderId")
                        .HasColumnType("bigint");

                    b.Property<string>("ChannelMasterOrderNo")
                        .HasColumnType("varchar(512)");

                    b.Property<string>("ChannelOrderNo")
                        .HasColumnType("varchar(512)");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<sbyte>("Status")
                        .HasColumnType("tinyint");

                    b.Property<long>("SubOrderId")
                        .HasColumnType("bigint");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("TravelLineOtaOrder", (string)null);
                });

            modelBuilder.Entity("Order.Api.Model.TravelLineOtaOrderRecord", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<long>("BaseOrderId")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<int?>("ErrorCode")
                        .HasColumnType("int");

                    b.Property<string>("ErrorMsg")
                        .HasColumnType("varchar(1000)");

                    b.Property<bool>("IsSuccess")
                        .HasColumnType("tinyint(1)");

                    b.Property<sbyte>("RecordType")
                        .HasColumnType("tinyint");

                    b.Property<long>("SubOrderId")
                        .HasColumnType("bigint");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<long>("TravelLineOtaOrderId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("TravelLineOtaOrderRecord", (string)null);
                });

            modelBuilder.Entity("Order.Api.Model.TravelLineSupplierOrder", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<int>("AdultCount")
                        .HasColumnType("int");

                    b.Property<decimal>("Amount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<int>("BabyCount")
                        .HasColumnType("int");

                    b.Property<long>("BaseOrderId")
                        .HasColumnType("bigint");

                    b.Property<int>("ChildCount")
                        .HasColumnType("int");

                    b.Property<decimal>("CommissionRate")
                        .HasColumnType("decimal(18,2)");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<string>("Currency")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("varchar(20)")
                        .HasDefaultValue("CNY");

                    b.Property<long>("LineProductOrderId")
                        .HasColumnType("bigint");

                    b.Property<sbyte>("OrderStatus")
                        .HasColumnType("tinyint");

                    b.Property<long>("SupplierId")
                        .HasColumnType("bigint");

                    b.Property<string>("SupplierOrderId")
                        .HasColumnType("varchar(100)");

                    b.Property<sbyte>("SupplierType")
                        .HasColumnType("tinyint");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<string>("TimeSlotName")
                        .HasColumnType("varchar(200)");

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime");

                    b.HasKey("Id");

                    b.HasIndex("BaseOrderId");

                    b.HasIndex("TenantId");

                    b.ToTable("TravelLineSupplierOrder", (string)null);
                });

            modelBuilder.Entity("Order.Api.Model.TravelLineSupplierOrderRecord", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<long>("BaseOrderId")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<int?>("ErrorCode")
                        .HasColumnType("int");

                    b.Property<string>("ErrorMsg")
                        .HasColumnType("varchar(1000)");

                    b.Property<bool>("IsSuccess")
                        .HasColumnType("tinyint(1)");

                    b.Property<sbyte>("RecordType")
                        .HasColumnType("tinyint");

                    b.Property<string>("SupplierOrderId")
                        .HasColumnType("varchar(100)");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("BaseOrderId");

                    b.HasIndex("TenantId");

                    b.ToTable("TravelLineSupplierOrderRecord", (string)null);
                });

            modelBuilder.Entity("Order.Api.Model.TravelLineSupplierOrderVoucher", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<long>("BaseOrderId")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<string>("FilePath")
                        .IsRequired()
                        .HasColumnType("varchar(500)");

                    b.Property<string>("ImageSourcePath")
                        .HasColumnType("varchar(500)");

                    b.Property<string>("PdfSourcePath")
                        .HasColumnType("varchar(500)");

                    b.Property<string>("SourcePath")
                        .HasColumnType("varchar(500)");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<string>("Thumbnail")
                        .IsRequired()
                        .HasColumnType("varchar(500)");

                    b.Property<sbyte>("VoucherSourceType")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("tinyint")
                        .HasDefaultValue((sbyte)0);

                    b.HasKey("Id");

                    b.HasIndex("BaseOrderId");

                    b.HasIndex("TenantId");

                    b.ToTable("TravelLineSupplierOrderVoucher", (string)null);
                });

            modelBuilder.Entity("Order.Api.Model.WorkOrder", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<string>("AgencyFullName")
                        .IsRequired()
                        .HasColumnType("varchar(256)");

                    b.Property<long>("AgencyId")
                        .HasColumnType("bigint");

                    b.Property<long>("BaseOrderId")
                        .HasColumnType("bigint");

                    b.Property<string>("ContactEmail")
                        .HasColumnType("varchar(50)");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<sbyte?>("CreateType")
                        .HasColumnType("tinyint");

                    b.Property<long>("CreatorId")
                        .HasColumnType("bigint");

                    b.Property<string>("CreatorName")
                        .IsRequired()
                        .HasColumnType("varchar(20)");

                    b.Property<string>("HopWorkOrderNumber")
                        .HasColumnType("varchar(64)");

                    b.Property<string>("QuoteStaff")
                        .HasColumnType("varchar(50)");

                    b.Property<sbyte>("Status")
                        .IsConcurrencyToken()
                        .HasColumnType("tinyint");

                    b.Property<long>("SubOrderId")
                        .HasColumnType("bigint");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime");

                    b.Property<string>("WorkOrderType")
                        .IsRequired()
                        .HasColumnType("varchar(64)");

                    b.HasKey("Id");

                    b.HasIndex("HopWorkOrderNumber")
                        .IsUnique();

                    b.HasIndex("TenantId");

                    b.ToTable("WorkOrder", (string)null);
                });

            modelBuilder.Entity("Order.Api.Model.WorkOrderReply", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<string>("Content")
                        .IsRequired()
                        .HasColumnType("varchar(1024)");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<string>("HopReplyNumber")
                        .HasColumnType("varchar(64)");

                    b.Property<string>("ImagePath")
                        .HasColumnType("varchar(512)");

                    b.Property<sbyte>("ReplyUserType")
                        .HasColumnType("tinyint");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime");

                    b.Property<long>("UserId")
                        .HasColumnType("bigint");

                    b.Property<string>("UserName")
                        .IsRequired()
                        .HasColumnType("varchar(20)");

                    b.Property<long>("WorkOrderId")
                        .HasColumnType("bigint");

                    b.Property<sbyte>("WorkOrderReplyType")
                        .HasColumnType("tinyint");

                    b.HasKey("Id");

                    b.HasIndex("HopReplyNumber")
                        .IsUnique();

                    b.HasIndex("TenantId");

                    b.ToTable("WorkOrderReply", (string)null);
                });

            modelBuilder.Entity("Order.Api.Model.WorkOrderServiceEvaluation", b =>
                {
                    b.Property<long>("Id")
                        .HasColumnType("bigint");

                    b.Property<string>("Content")
                        .IsRequired()
                        .HasColumnType("varchar(1024)");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime");

                    b.Property<sbyte>("Evaluation")
                        .HasColumnType("tinyint");

                    b.Property<long>("TenantId")
                        .HasColumnType("bigint");

                    b.Property<long>("UserId")
                        .HasColumnType("bigint");

                    b.Property<string>("UserName")
                        .IsRequired()
                        .HasColumnType("longtext");

                    b.Property<long>("WorkOrderId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.ToTable("WorkOrderServiceEvaluation", (string)null);
                });

            modelBuilder.Entity("Order.Api.Model.AfterSaleFinancialHandleOrder", b =>
                {
                    b.OwnsOne("Order.Api.Model.TenantBankAccountInfo", "TenantBankAccount", b1 =>
                        {
                            b1.Property<long>("AfterSaleFinancialHandleOrderId")
                                .HasColumnType("bigint");

                            b1.Property<string>("AccountName")
                                .HasColumnType("varchar(50)");

                            b1.Property<string>("AccountNo")
                                .HasColumnType("varchar(50)");

                            b1.Property<string>("Address")
                                .HasColumnType("varchar(200)");

                            b1.Property<string>("BankCode")
                                .HasColumnType("varchar(32)");

                            b1.Property<string>("BankName")
                                .HasColumnType("varchar(50)");

                            b1.Property<string>("BranchName")
                                .HasColumnType("varchar(50)");

                            b1.Property<string>("CurrencyCode")
                                .HasColumnType("varchar(50)");

                            b1.Property<long?>("Id")
                                .HasColumnType("bigint");

                            b1.Property<string>("OpeningBankCode")
                                .HasColumnType("varchar(30)");

                            b1.Property<string>("SwiftCode")
                                .HasColumnType("varchar(200)");

                            b1.Property<sbyte?>("TenantBankAccountType")
                                .HasColumnType("tinyint");

                            b1.HasKey("AfterSaleFinancialHandleOrderId");

                            b1.ToTable("AfterSaleFinancialHandleOrder");

                            b1.WithOwner()
                                .HasForeignKey("AfterSaleFinancialHandleOrderId");
                        });

                    b.Navigation("TenantBankAccount")
                        .IsRequired();
                });

            modelBuilder.Entity("Order.Api.Model.GroupBookingFinancialHandleOrder", b =>
                {
                    b.OwnsOne("Order.Api.Model.TenantBankAccountInfo", "TenantBankAccount", b1 =>
                        {
                            b1.Property<long>("GroupBookingFinancialHandleOrderId")
                                .HasColumnType("bigint");

                            b1.Property<string>("AccountName")
                                .HasColumnType("varchar(50)");

                            b1.Property<string>("AccountNo")
                                .HasColumnType("varchar(50)");

                            b1.Property<string>("Address")
                                .HasColumnType("varchar(200)");

                            b1.Property<string>("BankCode")
                                .HasColumnType("varchar(32)");

                            b1.Property<string>("BankName")
                                .HasColumnType("varchar(50)");

                            b1.Property<string>("BranchName")
                                .HasColumnType("varchar(50)");

                            b1.Property<string>("CurrencyCode")
                                .HasColumnType("varchar(50)");

                            b1.Property<long?>("Id")
                                .HasColumnType("bigint");

                            b1.Property<string>("OpeningBankCode")
                                .HasColumnType("varchar(30)");

                            b1.Property<string>("SwiftCode")
                                .HasColumnType("varchar(200)");

                            b1.Property<sbyte?>("TenantBankAccountType")
                                .HasColumnType("tinyint");

                            b1.HasKey("GroupBookingFinancialHandleOrderId");

                            b1.ToTable("GroupBookingFinancialHandleOrder");

                            b1.WithOwner()
                                .HasForeignKey("GroupBookingFinancialHandleOrderId");
                        });

                    b.Navigation("TenantBankAccount")
                        .IsRequired();
                });

            modelBuilder.Entity("Order.Api.Model.KingdeeAPPayable", b =>
                {
                    b.OwnsOne("Order.Api.Model.APPayable", "APPayable", b1 =>
                        {
                            b1.Property<long>("KingdeeAPPayableId")
                                .HasColumnType("bigint");

                            b1.Property<string>("FBillNo")
                                .IsRequired()
                                .HasColumnType("varchar(32)")
                                .HasComment("单据编号");

                            b1.Property<string>("FBillTypeID")
                                .IsRequired()
                                .HasColumnType("varchar(32)")
                                .HasComment("单据类型");

                            b1.Property<string>("FCURRENCYID")
                                .IsRequired()
                                .HasColumnType("varchar(32)")
                                .HasComment("币别");

                            b1.Property<DateTime>("FDATE")
                                .HasColumnType("datetime")
                                .HasComment("业务日期");

                            b1.Property<string>("FEXCHANGETYPE")
                                .IsRequired()
                                .HasColumnType("varchar(32)")
                                .HasComment("汇率类型");

                            b1.Property<decimal>("FEntryTaxRate")
                                .HasColumnType("decimal(8,2)")
                                .HasComment("税率(%)");

                            b1.Property<decimal>("FExchangeRate")
                                .HasColumnType("decimal(8,2)")
                                .HasComment("汇率");

                            b1.Property<int?>("FID")
                                .HasColumnType("int");

                            b1.Property<string>("FMAINBOOKSTDCURRID")
                                .IsRequired()
                                .HasColumnType("varchar(32)")
                                .HasComment("本位币");

                            b1.Property<string>("FMATERIALID")
                                .IsRequired()
                                .HasColumnType("varchar(32)")
                                .HasComment("物料编码");

                            b1.Property<string>("FPAYORGID")
                                .IsRequired()
                                .HasColumnType("varchar(32)")
                                .HasComment("付款组织");

                            b1.Property<string>("FPRICEUNITID")
                                .IsRequired()
                                .HasColumnType("varchar(32)")
                                .HasComment("计价单位");

                            b1.Property<string>("FPURCHASEDEPTID")
                                .IsRequired()
                                .HasColumnType("varchar(32)")
                                .HasComment("采购部门");

                            b1.Property<string>("FPURCHASERGROUPID")
                                .IsRequired()
                                .HasColumnType("varchar(32)")
                                .HasComment("采购组织");

                            b1.Property<int>("FPriceQty")
                                .HasColumnType("int")
                                .HasComment("计价数量");

                            b1.Property<string>("FSETTLEORGID")
                                .IsRequired()
                                .HasColumnType("varchar(32)")
                                .HasComment("结算组织");

                            b1.Property<string>("FSUPPLIERID")
                                .IsRequired()
                                .HasColumnType("varchar(32)")
                                .HasComment("供应商");

                            b1.Property<decimal>("FTaxPrice")
                                .HasColumnType("decimal(18,2)")
                                .HasComment("含税单价");

                            b1.Property<string>("F_qwe_Assistant")
                                .IsRequired()
                                .HasColumnType("varchar(32)")
                                .HasComment("产品线");

                            b1.Property<string>("F_qwe_Assistant1")
                                .IsRequired()
                                .HasColumnType("varchar(32)")
                                .HasComment("抵冲类型");

                            b1.HasKey("KingdeeAPPayableId");

                            b1.ToTable("KingdeeAPPayable");

                            b1.WithOwner()
                                .HasForeignKey("KingdeeAPPayableId");
                        });

                    b.Navigation("APPayable")
                        .IsRequired();
                });

            modelBuilder.Entity("Order.Api.Model.KingdeeARReceivable", b =>
                {
                    b.OwnsOne("Order.Api.Model.ARReceivable", "ARReceivable", b1 =>
                        {
                            b1.Property<long>("KingdeeARReceivableId")
                                .HasColumnType("bigint");

                            b1.Property<string>("FAR_Remark")
                                .HasColumnType("varchar(200)")
                                .HasComment("备注");

                            b1.Property<string>("FBillNo")
                                .IsRequired()
                                .HasColumnType("varchar(32)")
                                .HasComment("单据编号");

                            b1.Property<string>("FBillTypeID")
                                .IsRequired()
                                .HasColumnType("varchar(32)")
                                .HasComment("单据类型");

                            b1.Property<string>("FCURRENCYID")
                                .IsRequired()
                                .HasColumnType("varchar(32)")
                                .HasComment("币种");

                            b1.Property<string>("FCUSTOMERID")
                                .IsRequired()
                                .HasColumnType("varchar(32)")
                                .HasComment("客户");

                            b1.Property<DateTime>("FDATE")
                                .HasColumnType("datetime")
                                .HasComment("业务日期");

                            b1.Property<string>("FEXCHANGETYPE")
                                .IsRequired()
                                .HasColumnType("varchar(32)")
                                .HasComment("汇率类型");

                            b1.Property<decimal?>("FEntryTaxRate")
                                .HasColumnType("decimal(8,2)")
                                .HasComment("税率%");

                            b1.Property<decimal?>("FExchangeRate")
                                .HasColumnType("decimal(8,2)")
                                .HasComment("汇率");

                            b1.Property<string>("FID")
                                .HasColumnType("varchar(32)");

                            b1.Property<string>("FMAINBOOKSTDCURRID")
                                .IsRequired()
                                .HasColumnType("varchar(32)")
                                .HasComment("本位币");

                            b1.Property<string>("FMATERIALID")
                                .IsRequired()
                                .HasColumnType("varchar(32)")
                                .HasComment("物料编码");

                            b1.Property<string>("FPAYORGID")
                                .IsRequired()
                                .HasColumnType("varchar(32)")
                                .HasComment("收款组织");

                            b1.Property<string>("FPRICEUNITID")
                                .IsRequired()
                                .HasColumnType("varchar(32)")
                                .HasComment("计价单位");

                            b1.Property<int?>("FPriceQty")
                                .HasColumnType("int")
                                .HasComment("计价数量");

                            b1.Property<string>("FSALEDEPTID")
                                .IsRequired()
                                .HasColumnType("varchar(32)")
                                .HasComment("销售部门");

                            b1.Property<string>("FSALEORGID")
                                .IsRequired()
                                .HasColumnType("varchar(32)")
                                .HasComment("销售组织");

                            b1.Property<string>("FSETTLEORGID")
                                .IsRequired()
                                .HasColumnType("varchar(32)")
                                .HasComment("结算组织");

                            b1.Property<decimal?>("FTaxPrice")
                                .HasColumnType("decimal(18,2)")
                                .HasComment("含税单价");

                            b1.Property<string>("F_qwe_Assistant")
                                .IsRequired()
                                .HasColumnType("varchar(32)")
                                .HasComment("产品线");

                            b1.Property<string>("F_qwe_Assistant1")
                                .IsRequired()
                                .HasColumnType("varchar(32)")
                                .HasComment("抵冲类型");

                            b1.HasKey("KingdeeARReceivableId");

                            b1.ToTable("KingdeeARReceivable");

                            b1.WithOwner()
                                .HasForeignKey("KingdeeARReceivableId");
                        });

                    b.Navigation("ARReceivable")
                        .IsRequired();
                });

            modelBuilder.Entity("Order.Api.Model.KingdeeBDCustomer", b =>
                {
                    b.OwnsOne("Order.Api.Model.BDCustomer", "BDCustomer", b1 =>
                        {
                            b1.Property<long>("KingdeeBDCustomerId")
                                .HasColumnType("bigint");

                            b1.Property<string>("FCUSTID")
                                .HasColumnType("varchar(32)");

                            b1.Property<string>("FCreateOrgId")
                                .IsRequired()
                                .HasColumnType("varchar(32)");

                            b1.Property<string>("FName")
                                .IsRequired()
                                .HasColumnType("varchar(256)");

                            b1.Property<string>("FNumber")
                                .IsRequired()
                                .HasColumnType("varchar(32)");

                            b1.Property<string>("FTRADINGCURRID")
                                .IsRequired()
                                .HasColumnType("varchar(32)");

                            b1.HasKey("KingdeeBDCustomerId");

                            b1.ToTable("KingdeeBDCustomer");

                            b1.WithOwner()
                                .HasForeignKey("KingdeeBDCustomerId");
                        });

                    b.Navigation("BDCustomer")
                        .IsRequired();
                });

            modelBuilder.Entity("Order.Api.Model.KingdeeBDSupplier", b =>
                {
                    b.OwnsOne("Order.Api.Model.BDSupplier", "BDSupplier", b1 =>
                        {
                            b1.Property<long>("KingdeeBDSupplierId")
                                .HasColumnType("bigint");

                            b1.Property<string>("FCreateOrgId")
                                .IsRequired()
                                .HasColumnType("varchar(32)");

                            b1.Property<string>("FGroup")
                                .IsRequired()
                                .HasColumnType("varchar(32)");

                            b1.Property<string>("FName")
                                .IsRequired()
                                .HasColumnType("varchar(256)");

                            b1.Property<string>("FNumber")
                                .IsRequired()
                                .HasColumnType("varchar(32)");

                            b1.Property<string>("FPayCurrencyId")
                                .IsRequired()
                                .HasColumnType("varchar(32)");

                            b1.Property<string>("FShortName")
                                .IsRequired()
                                .HasColumnType("varchar(128)");

                            b1.Property<int?>("FSupplierId")
                                .HasColumnType("int");

                            b1.HasKey("KingdeeBDSupplierId");

                            b1.ToTable("KingdeeBDSupplier");

                            b1.WithOwner()
                                .HasForeignKey("KingdeeBDSupplierId");
                        });

                    b.Navigation("BDSupplier")
                        .IsRequired();
                });

            modelBuilder.Entity("Order.Api.Model.StoredValueCardOrder", b =>
                {
                    b.OwnsOne("Order.Api.Model.StoredValueCardGearInfo", "StoredValueCardGearInfo", b1 =>
                        {
                            b1.Property<long>("StoredValueCardOrderId")
                                .HasColumnType("bigint");

                            b1.Property<decimal>("GiftValue")
                                .HasColumnType("decimal(8,2)");

                            b1.Property<long>("Id")
                                .HasColumnType("bigint");

                            b1.Property<decimal>("Price")
                                .HasColumnType("decimal(8,2)");

                            b1.HasKey("StoredValueCardOrderId");

                            b1.ToTable("StoredValueCardOrder");

                            b1.WithOwner()
                                .HasForeignKey("StoredValueCardOrderId");
                        });

                    b.OwnsOne("Order.Api.Model.StoredValueCardInfo", "StoredValueCardInfo", b1 =>
                        {
                            b1.Property<long>("StoredValueCardOrderId")
                                .HasColumnType("bigint");

                            b1.Property<string>("CardName")
                                .IsRequired()
                                .HasColumnType("varchar(200)");

                            b1.Property<sbyte>("CardType")
                                .HasColumnType("tinyint");

                            b1.Property<string>("Cover")
                                .IsRequired()
                                .HasColumnType("varchar(500)");

                            b1.Property<long>("Id")
                                .HasColumnType("bigint");

                            b1.Property<string>("Instruction")
                                .IsRequired()
                                .HasColumnType("varchar(1000)");

                            b1.Property<int>("ProductBusinessType")
                                .HasColumnType("int");

                            b1.HasKey("StoredValueCardOrderId");

                            b1.ToTable("StoredValueCardOrder");

                            b1.WithOwner()
                                .HasForeignKey("StoredValueCardOrderId");
                        });

                    b.Navigation("StoredValueCardGearInfo");

                    b.Navigation("StoredValueCardInfo");
                });
#pragma warning restore 612, 618
        }
    }
}
