using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Order.Api.Infrastructure.Migrations
{
    public partial class HotelOrderArrivalTaxFeesJson : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<string>(
                name: "Arrival<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>",
                table: "HotelOrder",
                type: "text",
                nullable: true)
                .Annotation("MySql:CharSet", "utf8mb4");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "ArrivalT<PERSON><PERSON><PERSON><PERSON><PERSON>",
                table: "HotelOrder");
        }
    }
}
