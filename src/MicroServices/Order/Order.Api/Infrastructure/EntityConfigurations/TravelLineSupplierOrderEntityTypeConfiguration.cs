using Contracts.Common.Payment.Enums;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using EfCoreExtensions.EntityConfigurationsBase;

namespace Order.Api.Infrastructure.EntityConfigurations
{
    public class TravelLineSupplierOrderEntityTypeConfiguration :
        TenantBaseConfiguration<Model.TravelLineSupplierOrder>, IEntityTypeConfiguration<Model.TravelLineSupplierOrder>
    {
        public void Configure(EntityTypeBuilder<Model.TravelLineSupplierOrder> builder)
        {
            base.ConfigureBase(builder);

            builder.Property(s => s.BaseOrderId)
                .HasColumnType("bigint");

            builder.Property(s => s.LineProductOrderId)
                .HasColumnType("bigint");

            builder.Property(s => s.SupplierOrderId)
                .HasColumnType("varchar(100)");

            builder.Property(s => s.SupplierId)
                .HasColumnType("bigint");

            builder.Property(s => s.AdultCount)
                .HasColumnType("int");

            builder.Property(s => s.ChildCount)
                .HasColumnType("int");

            builder.Property(s => s.BabyCount)
                .HasColumnType("int");

            builder.Property(s => s.Amount)
                .HasColumnType("decimal(18,2)");

            builder.Property(s => s.Currency)
                .HasColumnType("varchar(20)")
                .HasDefaultValue(Currency.CNY.ToString())
                .IsRequired();

            builder.Property(s => s.SupplierType)
                .HasColumnType("tinyint");

            builder.Property(s => s.CommissionRate)
                .HasColumnType("decimal(18,2)");

            builder.Property(s => s.TimeSlotName)
                .HasColumnType("varchar(200)");

            builder.Property(s => s.OrderStatus)
                .HasColumnType("tinyint");

            builder.Property(s => s.CreateTime)
                .HasColumnType("datetime");

            builder.Property(s => s.UpdateTime)
                .HasColumnType("datetime");

            //索引
            builder.HasIndex(s => s.BaseOrderId);
        }
    }
}