using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using EfCoreExtensions.EntityConfigurationsBase;

namespace Order.Api.Infrastructure.EntityConfigurations
{
    public class HotelOrderCancelRuleEntityTypeConfiguration : TenantBaseConfiguration<Model.HotelOrderCancelRule>, IEntityTypeConfiguration<Model.HotelOrderCancelRule>
    {
        public void Configure(EntityTypeBuilder<Model.HotelOrderCancelRule> builder)
        {
            base.ConfigureBase(builder);

            builder.Property(s => s.HotelOrderId)
                .HasColumnType("bigint");

            builder.Property(s => s.CancelRuleId)
                .HasColumnType("bigint");

            builder.Property(s => s.CancelRulesType)
                .HasColumnType("tinyint");

            builder.Property(s => s.BeforeCheckInDays)
                .HasColumnType("int");

            builder.Property(s => s.BeforeCheckInTime)
                .HasColumnType("time");

            builder.Property(s => s.CheckInDateTime)
                .HasColumnType("time");

            builder.Property(s => s.CancelChargeType)
                .HasColumnType("tinyint");

            builder.Property(s => s.ChargeValue)
                .HasColumnType("int");

            builder.Property(s => s.Description)
                .HasColumnType("varchar(500)");
        }
    }
}
