using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using EfCoreExtensions.EntityConfigurationsBase;

namespace Order.Api.Infrastructure.EntityConfigurations
{
    public class GroupBookingFinancialHandleOrderEntityTypeConfiguration : TenantBaseConfiguration<Model.GroupBookingFinancialHandleOrder>, IEntityTypeConfiguration<Model.GroupBookingFinancialHandleOrder>
    {
        public void Configure(EntityTypeBuilder<Model.GroupBookingFinancialHandleOrder> builder)
        {
            base.ConfigureBase(builder);

            builder.Property(s => s.GroupBookingOrderPaymentId)
                .HasColumnType("bigint");

            builder.Property(s => s.GroupBookingOrderId)
                .HasColumnType("bigint");

            builder.Property(s => s.ApplicationFormId)
                .HasColumnType("bigint");

            builder.Property(s => s.PayType)
                .HasColumnType("tinyint");

            builder.Property(s => s.Amount)
                .HasColumnType("decimal(18,2)");

            builder.Property(s => s.CurrencyCode)
                .HasColumnType("varchar(50)");

            builder.Property(s => s.AgencyId)
                .HasColumnType("bigint");

            builder.Property(s => s.AgencyName)
                .HasColumnType("varchar(256)");

            builder.Property(s => s.Status)
                .HasColumnType("tinyint");

            builder.OwnsOne(s => s.TenantBankAccount, x =>
            {
                x.Property(s => s.Id)
                    .HasColumnType("bigint");

                x.Property(s => s.TenantBankAccountType)
                    .HasColumnType("tinyint");

                x.Property(s => s.AccountName)
                    .HasColumnType("varchar(50)");

                x.Property(s => s.AccountNo)
                    .HasColumnType("varchar(50)");

                x.Property(s => s.BankCode)
                    .HasColumnType("varchar(32)");

                x.Property(s => s.BranchName)
                    .HasColumnType("varchar(50)");

                x.Property(s => s.Address)
                    .HasColumnType("varchar(200)");

                x.Property(s => s.SwiftCode)
                    .HasColumnType("varchar(200)");

                x.Property(s => s.BankName)
                    .HasColumnType("varchar(50)");

                x.Property(s => s.CurrencyCode)
                    .HasColumnType("varchar(50)");

                x.Property(s => s.OpeningBankCode)
                    .HasColumnType("varchar(30)");
            });

            builder.Property(s => s.AccountTime)
                .HasColumnType("datetime");

            builder.Property(s => s.Proof)
                .HasColumnType("text");

            builder.Property(s => s.Remark)
                .HasColumnType("varchar(500)");

            builder.Property(s => s.Creator)
                .HasColumnType("varchar(100)");

            builder.Property(s => s.CreatorId)
                .HasColumnType("bigint");

            builder.Property(s => s.CreateTime)
                .HasColumnType("datetime");

            builder.Property(s => s.Operator)
                .HasColumnType("varchar(100)");

            builder.Property(s => s.OperatorId)
                .HasColumnType("bigint");

            builder.Property(s => s.FinishTime)
                .HasColumnType("datetime");

            builder.Property(s => s.RejectReason)
                .HasColumnType("varchar(256)");
        }
    }
}
