using EfCoreExtensions.EntityConfigurationsBase;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Order.Api.Infrastructure.EntityConfigurations;

public class HotelSupplierOrderRecordEntityTypeConfiguration : TenantBaseConfiguration<Model.HotelSupplierOrderRecord>, IEntityTypeConfiguration<Model.HotelSupplierOrderRecord>
{
    public void Configure(EntityTypeBuilder<Model.HotelSupplierOrderRecord> builder)
    {
        base.ConfigureBase(builder);

        builder.Property(s => s.BaseOrderId)
              .HasColumnType("bigint")
              .IsRequired();

        builder.Property(s => s.IdKey)
            .HasColumnType("varchar(1500)")
            .IsRequired();

        builder.Property(s => s.ResultCode)
            .HasColumnType("varchar(50)");

        builder.Property(s => s.ResultMessage)
          .HasColumnType("text");

        builder.Property(s => s.ResultIdKey)
          .HasColumnType("varchar(128)");

        builder.Property(s => s.Result)
          .HasColumnType("mediumtext");

        builder.Property(s => s.RecordType)
            .HasColumnType("tinyint");

        builder.Property(s => s.CreateTime)
            .HasColumnType("datetime");

        builder.Property(s => s.UpdateTime)
            .HasColumnType("datetime");

        //索引
        builder.HasIndex(s => s.BaseOrderId);
    }
}
