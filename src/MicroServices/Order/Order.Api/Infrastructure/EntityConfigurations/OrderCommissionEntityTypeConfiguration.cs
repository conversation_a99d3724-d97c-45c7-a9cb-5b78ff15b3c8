using EfCoreExtensions.EntityConfigurationsBase;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Order.Api.Infrastructure.EntityConfigurations
{
    public class OrderCommissionEntityTypeConfiguration : KeyBaseConfiguration<Model.OrderCommission>, IEntityTypeConfiguration<Model.OrderCommission>
    {
        public void Configure(EntityTypeBuilder<Model.OrderCommission> builder)
        {
            base.ConfigureBase(builder);

            builder.Property(s => s.BaseOrderId)
                .HasColumnType("bigint");

            builder.Property(s => s.AgencyCommisionStatus)
              .HasColumnType("tinyint");

            builder.Property(s => s.AgencyCommissionRate)
             .HasColumnType("decimal(18,2)");

            builder.Property(s => s.AgencyCommissionFee)
             .HasColumnType("decimal(18,2)");

            builder.Property(s => s.SupplierCommisionStatus)
              .HasColumnType("tinyint");

            builder.Property(s => s.SupplierCommissionFee)
             .HasColumnType("decimal(18,2)");

            builder.Property(s => s.SupplierCommissionRate)
             .HasColumnType("decimal(18,2)");

            builder.Property(s => s.CreateTime)
                .HasColumnType("datetime");

            builder.Property(s => s.UpdateTime)
              .HasColumnType("datetime");

            //索引
            builder.HasIndex(s => s.BaseOrderId);
        }
    }
}
