using EfCoreExtensions.EntityConfigurationsBase;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Order.Api.Infrastructure.EntityConfigurations;

public class WorkOrderEntityTypeConfiguration : TenantBaseConfiguration<WorkOrder>,
    IEntityTypeConfiguration<WorkOrder>
{
    public void Configure(EntityTypeBuilder<WorkOrder> builder)
    {
        base.ConfigureBase(builder);

        builder.Property(s => s.BaseOrderId)
            .HasColumnType("bigint");

        builder.Property(s => s.SubOrderId)
            .HasColumnType("bigint");

        builder.Property(s => s.AgencyId)
            .HasColumnType("bigint");

        builder.Property(s => s.AgencyFullName)
           .HasColumnType("varchar(256)");

        builder.Property(s => s.WorkOrderType)
           .HasColumnType("varchar(64)");

        builder.Property(s => s.HopWorkOrderNumber)
           .HasColumnType("varchar(64)");

        builder.Property(s => s.Status)
           .HasColumnType("tinyint")
           .IsConcurrencyToken();

        builder.Property(s => s.ContactEmail)
           .HasColumnType("varchar(50)");

        builder.Property(s => s.CreatorId)
           .HasColumnType("bigint");

        builder.Property(s => s.CreatorName)
           .HasColumnType("varchar(20)");

        builder.Property(s => s.CreateTime)
               .HasColumnType("datetime");

        builder.Property(s => s.UpdateTime)
            .HasColumnType("datetime");

        builder.Property(s => s.CreateType)
            .HasColumnType("tinyint");

        builder.Property(s => s.QuoteStaff)
          .HasColumnType("varchar(50)");

        builder.HasIndex(s => s.HopWorkOrderNumber).IsUnique();
    }
}
