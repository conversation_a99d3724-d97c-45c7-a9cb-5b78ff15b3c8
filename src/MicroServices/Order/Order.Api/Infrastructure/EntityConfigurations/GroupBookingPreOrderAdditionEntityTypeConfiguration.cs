using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using EfCoreExtensions.EntityConfigurationsBase;

namespace Order.Api.Infrastructure.EntityConfigurations
{
    public class GroupBookingPreOrderAdditionEntityTypeConfiguration : TenantBaseConfiguration<Model.GroupBookingPreOrderAddition>, IEntityTypeConfiguration<Model.GroupBookingPreOrderAddition>
    {
        public void Configure(EntityTypeBuilder<Model.GroupBookingPreOrderAddition> builder)
        {
            base.ConfigureBase(builder);

            builder.Property(s => s.PreOrderId)
                .HasColumnType("bigint");

            builder.Property(s => s.PreOrderItemId)
                .HasColumnType("bigint");

            builder.Property(s => s.AdditionName)
                .HasColumnType("varchar(100)");

            builder.Property(s => s.Amount)
                .HasColumnType("decimal(18,2)");

            builder.Property(s => s.CurrencyCode)
                .HasColumnType("varchar(32)");

            builder.Property(s => s.Cost)
                .HasColumnType("decimal(18,2)");

            builder.Property(s => s.CostCurrencyCode)
                .HasColumnType("varchar(32)");

            builder.Property(s => s.Quantity)
                .HasColumnType("int")
                .HasDefaultValue(1);

            builder.Property(s => s.Remark)
                .HasColumnType("varchar(500)");
        }
    }
}
