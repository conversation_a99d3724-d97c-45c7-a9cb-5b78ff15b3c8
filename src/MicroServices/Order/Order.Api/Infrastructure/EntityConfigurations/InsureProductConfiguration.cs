using Contracts.Common.Order.Enums;
using Contracts.Common.Tenant.Enums;
using EfCoreExtensions.EntityConfigurationsBase;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Order.Api.Infrastructure.EntityConfigurations
{
    public class InsureProductConfiguration : TenantBaseConfiguration<Model.InsureProduct>, IEntityTypeConfiguration<Model.InsureProduct>
    {
        public void Configure(EntityTypeBuilder<Model.InsureProduct> builder)
        {
            base.ConfigureBase(builder);

            builder.Property(s => s.Type)
                .HasColumnType("int")
                .HasDefaultValue(SupplierApiType.PingAnHopeInsurance);

            builder.Property(s => s.ProductId)
                .HasColumnType("varchar(20)")
                .IsRequired();

            builder.Property(s => s.ProductName)
                .HasColumnType("varchar(50)");

            builder.Property(s => s.ProductUnionId)
                .HasColumnType("varchar(50)");

            builder.Property(s => s.SupplierId)
                .HasColumnType("bigint")
                .IsRequired();

            builder.Property(s => s.PlanName)
                .HasColumnType("varchar(50)");

            builder.Property(s => s.PlanCode)
                .HasColumnType("varchar(20)");

            builder.Property(s => s.StartDay)
                .HasColumnType("int");

            builder.Property(s => s.EndDay)
                .HasColumnType("int");

            builder.Property(s => s.StartAge)
                .HasColumnType("int");

            builder.Property(s => s.EndAge)
                .HasColumnType("int");

            builder.Property(s => s.Price)
                .HasColumnType("decimal(18,2)");

            builder.Property(s => s.CurrencyCode)
                .HasColumnType("varchar(32)");

            builder.Property(s => s.Status)
                .HasColumnType("int")
                .HasDefaultValue(InsureProductStatus.Vaild);

            builder.Property(s => s.Sort)
                .HasColumnType("int");

            builder.Property(s => s.CreateTime)
                .HasColumnType("datetime");

            builder.Property(s => s.UpdateTime)
                .HasColumnType("datetime");
        }
    }
}
