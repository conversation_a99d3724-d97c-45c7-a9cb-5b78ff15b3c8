using Contracts.Common.Order.Enums;
using EfCoreExtensions.EntityConfigurationsBase;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Newtonsoft.Json;

namespace Order.Api.Infrastructure.EntityConfigurations
{
    public class InsurePurchaseRecordDetailConfiguration : TenantBaseConfiguration<Model.InsurePurchaseRecordDetail>, IEntityTypeConfiguration<Model.InsurePurchaseRecordDetail>
    {
        public void Configure(EntityTypeBuilder<Model.InsurePurchaseRecordDetail> builder)
        {
            base.ConfigureBase(builder);

            builder.Property(s => s.InsurePurchaseRecordId)
                .HasColumnType("bigint");

            builder.Property(s => s.BaseOrderId)
                .HasColumnType("bigint");

            builder.Property(s => s.Name)
                .HasColumnType("varchar(100)");

            builder.Property(s => s.OrderType)
                .HasColumnType("int")
                .HasDefaultValue(OrderType.TravelLineOrder);

            builder.Property(s => s.Price)
                .HasColumnType("decimal(18,2)");

            builder.Property(s => s.CurrencyCode)
                .HasColumnType("varchar(32)");

            builder.Property(s => s.Status)
                .HasColumnType("tinyint");

            builder.Property(s => s.ErrorMsg)
                .HasColumnType("text");

            builder.Property(s => s.InsuredPerson)
                .HasColumnType("text")
                .IsRequired()
                .HasConversion(
                    mod => JsonConvert.SerializeObject(mod),
                    str => JsonConvert.DeserializeObject<InsuredPerson>(str));

            builder.Property(s => s.CreateTime)
                .HasColumnType("datetime");

            builder.Property(s => s.DataId)
                .HasColumnType("bigint");
        }
    }
}
