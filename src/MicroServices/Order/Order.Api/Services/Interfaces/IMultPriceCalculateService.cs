using Contracts.Common.Order.DTOs;
using Contracts.Common.Product.DTOs.AgencyChannelPriceSettings;
using Contracts.Common.Product.Enums;
using Contracts.Common.User.DTOs;

namespace Order.Api.Services.Interfaces
{
    public interface IMultPriceCalculateService
    {
        Task<ProductMultPriceOutput> GetSingle(GetSingleProductMultPriceInput input);
        /// <summary>
        /// 计算价格
        /// </summary>
        /// <param name="inputs"></param>
        /// <returns></returns>
        Task<IList<ProductMultPriceOutput>> GetProductMultPrices(ProductType productType, params GetProductMultPriceInput[] inputs);

        /// <summary>
        /// 获取用户信息、会员等级信息
        /// </summary>
        /// <param name="userId"></param>
        /// <returns></returns>
        Task<UserInfoOutput> GetCustomerUserInfo();

        /// <summary>
        /// 计算日历房价格
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<IEnumerable<HotelDateMultPrice>> GetHotelDateMultPrices(GetHotelMultPriceInput input);

        /// <summary>
        /// 计算景区门票价格折扣
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        Task<GetScenicTicketMultPriceOutput> GetScenicTicketMultPrice(GetScenicTicketMultPriceInput input);

        #region 渠道价格设置

        /// <summary>
        /// 获取渠道价格设置
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        Task<List<AgencyChannelPriceSettingOutput>> QueryAgencyChannelPrices(QueryChannelPriceInput request);

        /// <summary>
        /// 分销商渠道价格设置计算渠道价
        /// </summary>
        /// <param name="setting"></param>
        /// <param name="salePrice"></param>
        /// <param name="costPrice"></param>
        /// <returns></returns>
        decimal CalcChannelPrice(AgencyChannelPriceSettingOutput setting, decimal salePrice, decimal costPrice);

        #endregion
    }
}
