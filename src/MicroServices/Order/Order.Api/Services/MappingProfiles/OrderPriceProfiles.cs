using AutoMapper;
using Contracts.Common.Order.DTOs;

namespace Order.Api.Services.MappingProfiles;

public class OrderPriceProfiles : Profile
{
    public OrderPriceProfiles()
    {
        CreateMap<OrderPrice, OrderMultPriceDto>().ReverseMap();
        CreateMap<OrderPrice, OrderMultPriceOutput>()
            .ForMember(x=> x.CurrentExchangeRate,f=> f.MapFrom(x=> x.ExchangeRate));
        CreateMap<OrderPrice, MultPriceOutput>();
        CreateMap<OrderMultPriceDto,MultPriceOutput>();
    }
}