using Contracts.Common.Order.DTOs.ScenicTicketOrder;
using FluentValidation;

namespace Order.Api.Services.Validator.ScenicTicketOrder;

public class B2BCreateInputValidator : AbstractValidator<B2BCreateInput>
{
    public B2BCreateInputValidator()
    {
        RuleFor(x => x.SellingPlatform).IsInEnum();
        RuleFor(x => x.Quantity).GreaterThan(0);
        RuleFor(x => x.ScenicSpotId).NotNull().GreaterThan(0);
        RuleFor(x => x.ScenicTicketId).NotNull().GreaterThan(0);
        //RuleFor(x => x.ContactsName).NotEmpty().Length(1, 50);
        //RuleFor(x => x.ContactsPhoneNumber).NotEmpty().Length(1, 50);
    }
}
