using Contracts.Common.Order.DTOs.BaseOrder;
using Contracts.Common.Order.Enums;
using FluentValidation;

namespace Order.Api.Services.Validator.BaseOrder;

public class SetOrderOpUserIdInputValidator : AbstractValidator<SetOrderOpUserIdInput>
{
    public SetOrderOpUserIdInputValidator()
    {
        RuleFor(x => x.BaseOrderId)
            .NotEmpty();

        var orderOperationTypes = new List<OrderOperationType>() {
           OrderOperationType.SetDevelopUserId,
           OrderOperationType.SetOperatorUserId,
           OrderOperationType.SetOperatorAssistantUserId,
        };
        RuleFor(s => s.OrderOperationType)
            .IsInEnum()
            .Must(s => orderOperationTypes.Contains(s))
            .WithMessage("OrderOperationType Error");
    }
}
