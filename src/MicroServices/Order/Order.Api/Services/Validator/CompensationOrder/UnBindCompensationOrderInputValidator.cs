using Contracts.Common.Order.DTOs.CompensationOrder;
using FluentValidation;

namespace Order.Api.Services.Validator.CompensationOrder;

public class UnBindCompensationOrderInputValidator : AbstractValidator<UnBindCompensationOrderInput>
{
    public UnBindCompensationOrderInputValidator()
    {
        RuleFor(x => x.BaseOrderId).NotEmpty();
        RuleFor(x => x.OrderType).IsInEnum();
        RuleFor(x => x.UnBindBaseOrderIdArr).NotEmpty();
    }
}