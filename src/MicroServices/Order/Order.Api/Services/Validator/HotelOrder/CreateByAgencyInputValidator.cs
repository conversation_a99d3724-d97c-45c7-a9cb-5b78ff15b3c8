using Contracts.Common.Order.DTOs.HotelOrder;
using FluentValidation;

namespace Order.Api.Services.Validator.HotelOrder
{
    public class CreateByAgencyInputValidator : AbstractValidator<CreateByAgencyInput>
    {
        public CreateByAgencyInputValidator()
        {
            RuleFor(x => x.AgencyId).NotNull()
                .GreaterThan(0);
            RuleFor(x => x.AgencyName).NotNull()
                .Length(1, 20);
            RuleFor(x => x.ChannelOrderNo).NotNull()
                .Length(1, 50);
            RuleFor(x => x.HotelId).NotNull()
                .GreaterThan(0);
            RuleFor(x => x.RoomId).NotNull()
                .GreaterThan(0);
            RuleFor(x => x.PriceStrategyId).NotNull()
                .GreaterThan(0);
            RuleFor(x => x.CheckIn).NotNull()
                .GreaterThanOrEqualTo(DateTime.Today);
            RuleFor(x => x.CheckOut).NotNull()
                .GreaterThan(x => x.CheckIn);
            RuleFor(x => x.RoomCount).NotNull()
                .GreaterThan(0);
            RuleFor(x => x.Amount).NotNull()
                .GreaterThan(0);
            RuleFor(x => x.Adults).NotNull()
                .GreaterThan(0);
            RuleFor(x => x.ContactsName).NotNull()
                .Length(1, 50);
            RuleFor(x => x.ContactsPhoneNumber)
                .Length(1, 50);
        }
    }
}
