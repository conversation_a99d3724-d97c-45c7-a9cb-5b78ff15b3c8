using Contracts.Common.Order.DTOs.GroupBookingAggregate;
using Contracts.Common.Order.Enums;
using FluentValidation;

namespace Order.Api.Services.Validator.GroupBookingAggregate;

public class GetChatDataInputValidator : AbstractValidator<GetChatDataInput>
{
    public GetChatDataInputValidator()
    {
        RuleFor(x => x.StartDate).NotEmpty().WithMessage("开始日期不能为空");
        RuleFor(x => x.EndDate).NotEmpty().WithMessage("结束日期不能为空")
            .Must(x => x < DateTime.Now.Date)
            .WithMessage("结束日期不能大于等于今天")
            .Must((model, endDate) =>
               (endDate - model.StartDate).Days <= 180)
           .WithMessage("结束日期与开始日期间隔不能超过 180 天");
        RuleFor(x => x.IndicatorsType).IsInEnum();
        RuleForEach(x => x.AreaIds).GreaterThan(0).WithMessage("区域ID必须大于0").When(x => x.DimensionType == StatisticalDimensionType.Area);
        RuleForEach(x => x.SourceTypes).ChildRules(x =>
        {
            x.RuleFor(y => y).IsInEnum().WithMessage("来源类型必须是有效的枚举值");
        }).When(x => x.DimensionType == StatisticalDimensionType.Source);
    }
}
