using Contracts.Common.Order.DTOs.TicketOrder;
using FluentValidation;

namespace Order.Api.Services.Validator.TicketOrder
{
    public class MallAddTicketOrderInputValidator : AbstractValidator<MallAddTicketOrderInput>
    {
        public MallAddTicketOrderInputValidator()
        {
            RuleFor(x => x.ContactsName).NotEmpty().Length(1, 50);
            RuleFor(x => x.ContactsPhoneNumber).NotEmpty().Length(1, 50);
            RuleFor(x => x.SellingPlatform).NotNull();
            RuleFor(x => x.Quantity).NotNull().GreaterThan(0);
            RuleFor(x => x.ProductId).NotNull().GreaterThan(0);
            RuleFor(x => x.ProductSkuId).NotNull().GreaterThan(0);
        }
    }
}
