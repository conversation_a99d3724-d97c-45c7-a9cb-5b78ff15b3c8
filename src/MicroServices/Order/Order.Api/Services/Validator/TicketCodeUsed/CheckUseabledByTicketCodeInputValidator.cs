using Contracts.Common.Order.DTOs.TicketCodeUsed;
using FluentValidation;

namespace Order.Api.Services.Validator.TicketCodeUsed
{
    public class CheckUseabledByTicketCodeInputValidator : AbstractValidator<CheckUseabledByTicketCodeInput>
    {
        public CheckUseabledByTicketCodeInputValidator() 
        {
            RuleFor(x => x.TicketCodes).Must(x => x is not null && x.Any());
        }
    }
}
