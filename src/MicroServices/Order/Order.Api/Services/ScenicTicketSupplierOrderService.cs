using AutoMapper;
using Cit.Storage.Aliyun.Oss;
using Cit.Storage.Redis;
using Common.GlobalException;
using Common.ServicesHttpClient;
using Common.Utils;
using Contracts.Common.Order.DTOs.OpenSupplierOrder;
using Contracts.Common.Order.DTOs.OrderFieldInformation;
using Contracts.Common.Order.DTOs.ScenicTicketSupplierOrder;
using Contracts.Common.Order.Enums;
using Contracts.Common.Order.Messages;
using Contracts.Common.Product.DTOs.Fields.Group;
using Contracts.Common.Product.Enums;
using Contracts.Common.Resource.DTOs;
using Contracts.Common.Scenic.Enums;
using Contracts.Common.Scenic.Messages;
using Contracts.Common.Tenant.DTOs.Supplier;
using DotNetCore.CAP;
using EfCoreExtensions.Abstract;
using EfCoreExtensions.Extensions;
using EfCoreExtensions.UOW;
using Hangfire;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using Order.Api.ConfigModel;
using Order.Api.Extensions;
using Order.Api.Services.Interfaces;
using Order.Api.Services.OpenPlatform.Contracts;
using Order.Api.Services.OpenPlatform.Contracts.Supplier;
using Order.Api.Services.OpenPlatform.Interfaces;

namespace Order.Api.Services;

/// <summary>
/// 开放平台-门票供应端下单服务
/// </summary>
public class ScenicTicketSupplierOrderService : IScenicTicketSupplierOrderService
{
    private readonly CustomDbContext _dbContext;
    private readonly IHttpClientFactory _httpClientFactory;
    private readonly ServicesAddress _servicesAddress;
    private readonly OssConfig _ossConfig;
    private readonly IAliyunOssObject _aliyunOssObject;
    private readonly ILogger<ScenicTicketSupplierOrderService> _logger;
    private readonly IOpenSupplierService _openSupplierService;
    private readonly IScenicTicketVoucherService _scenicTicketVoucherService;
    private readonly ICapPublisher _capPublisher;
    private readonly IRedisClient _redisClient;
    private readonly IOpenPlatformBaseService _openPlatformBaseService;
    private readonly IMapper _mapper;
    private readonly IBackgroundJobClient _backgroundJobClient;
    
    private const int _updateAttemptMaxCount = 10;
    private const int _successCode = 200;
    private const int _errorCode = -1;

    public ScenicTicketSupplierOrderService(CustomDbContext dbContext,
        IHttpClientFactory httpClientFactory,
        IOptions<ServicesAddress> servicesAddressOptions,
        IOptions<OssConfig> ossConfigOptions,
        IAliyunOssObject aliyunOssObject,
        ILogger<ScenicTicketSupplierOrderService> logger,
        IOpenSupplierService openSupplierService,
        IScenicTicketVoucherService scenicTicketVoucherService,
        ICapPublisher capPublisher,
        IRedisClient redisClient,
        IOpenPlatformBaseService openPlatformBaseService,
        IMapper mapper,
        IBackgroundJobClient backgroundJobClient)
    {
        _dbContext = dbContext;
        _httpClientFactory = httpClientFactory;
        _servicesAddress = servicesAddressOptions.Value;
        _ossConfig = ossConfigOptions.Value;
        _aliyunOssObject = aliyunOssObject;
        _logger = logger;
        _scenicTicketVoucherService = scenicTicketVoucherService;
        _openSupplierService = openSupplierService;
        _capPublisher = capPublisher;
        _redisClient = redisClient;
        _openPlatformBaseService = openPlatformBaseService;
        _mapper = mapper;
        _backgroundJobClient = backgroundJobClient;
    }
    
    [UnitOfWork]
    public async Task<CreateSupplierOrderOutput> OrderProcess(CreateSupplierApiOrderMessage receive)
    {
        var result = new CreateSupplierOrderOutput();
        var baseOrder = await _dbContext.BaseOrders.IgnoreQueryFilters()
            .FirstOrDefaultAsync(x => x.Id == receive.BaseOrderId);
        var scenicTicketOrder = await _dbContext.ScenicTicketOrders.IgnoreQueryFilters()
            .FirstOrDefaultAsync(x => x.BaseOrderId == receive.BaseOrderId);
        
        //判断sass订单状态是否是待完成
        if (baseOrder.Status != BaseOrderStatus.UnFinished)
        {
            _logger.LogError("[OpenSupplier]OrderProcess:{@Input},{@Message}",receive,"订单状态已完成");
            result.Msg = "订单状态已完成";
            return result;
        }
        
        //判断订单类型
        if (scenicTicketOrder.CredentialSourceType != CredentialSourceType.InterfaceDock)
        {
            _logger.LogError("[OpenSupplier]OrderProcess:{@Input},{@Message}",receive,"非API对接产品订单");
            result.Msg = "非API对接产品订单";
            return result;
        }
        
        //判断是否已经存在供应端订单数据.如果创单失败,允许重新下单
        var isNewSupplierOrder = false;
        var supplierOrder = await _dbContext.ScenicTicketSupplierOrders.IgnoreQueryFilters()
            .FirstOrDefaultAsync(x => x.BaseOrderId == receive.BaseOrderId);
        if (supplierOrder != null)
        {
            if (supplierOrder.OrderStatus != ScenicTicketSupplierOrderStatus.CreateFail)
            {
                _logger.LogError("[OpenSupplier]OrderProcess:{@Input},{@Message}",receive,"供应端订单已存在");
                result.Msg = "供应端订单已存在";
                return result;
            }
        }
        else
        {
            //初始化供应端订单
            supplierOrder = new ScenicTicketSupplierOrder();
            supplierOrder.SetTenantId(baseOrder.TenantId);
            isNewSupplierOrder = true;
        }
        
        //查询API供应商配置信息
        var tenantId = baseOrder.TenantId;
        var header = new  List<KeyValuePair<string,string>>
        {
            new("tenant",tenantId.ToString())
        };
        var supplier = await _httpClientFactory.InternalGetAsync<GetSupplierOutput>(
            requestUri: _servicesAddress.Tenant_GetSupplier(scenicTicketOrder.SupplierId),
            headers: header); 
        
        var activeId = scenicTicketOrder.SupplierActivityId ?? string.Empty;
        var packageId = scenicTicketOrder.SupplierPackageId ?? string.Empty;
        var skuId = scenicTicketOrder.SupplierSkuId;
        
        //获取开放平台供应商类型
        var openSupplierType = ConvertOpenSupplierType(scenicTicketOrder.OpenSupplierType!.Value);
        
        //订单字段模板信息
        var orderFieldTypeInfos = (await GetOrderFieldInfo(baseOrder.Id)).First();
        var contactInfo = await ContactInfoDataSupplement(orderFieldTypeInfos.ContactsFieldInfo);
        var travelInfo = await TravelerInfoDataSupplement(orderFieldTypeInfos.TravelFieldInfo);
        if (scenicTicketOrder.TicketsCombinationOrderId.HasValue)
        {
            travelInfo = new List<SupplierOrderCreateOrderTravelerInfo>();//组合订单暂时不传出行人信息
        }

        //如果出行人字段模板没有邮箱和手机号,默认赋值联系人信息的相关数据
        foreach (var item in travelInfo)
        {
            if (string.IsNullOrEmpty(item.Email))
            {
                item.Email = contactInfo.Email;
            }

            if (string.IsNullOrEmpty(item.Mobile))
            {
                item.Mobile = contactInfo.Mobile;
                item.MobilePrefix = contactInfo.MobilePrefix;
            }

            // 匹配skuId
            item.OutSkuId = skuId;
        }

        #region 附加信息校验

        //附加信息处理
        var itemExtraInfos = new List<SupplierOrderCreateOrderExtraItem>();
        var errorMsg = string.Empty;
        var extraInfoCheck = true; //附加信息校验结果
        var supplierProductCheck = true; //供应商产品信息校验结果
        var supplierProduct = await _openSupplierService.SupplierProductDetail(
            new SupplierProductDetailRequest
            {
                SupplierType = openSupplierType,
                OutProductId = activeId,
                OutProductOptionId = packageId,
                OutSkuId = skuId
            }, tenantId);
        if (supplierProduct?.Data is null || supplierProduct.Data?.SkuList.Any() is false)
        {
            _logger.LogError("[OpenSupplier]OrderProcess:{@Input},{@Message}", receive, "供应商产品信息为空");
            supplierProductCheck = false;
            errorMsg = supplierProduct.Msg;
        }
        else
        {
            // 查询订单的附加信息
            var orderExtraInfos = await _dbContext.OpenSupplierOrderExtraInfos.IgnoreQueryFilters()
                .AsNoTracking()
                .Where(x => x.BaseOrderId == receive.BaseOrderId)
                .ToListAsync();

            if (orderExtraInfos.Any())
            {
                itemExtraInfos = orderExtraInfos.Select(x => new SupplierOrderCreateOrderExtraItem
                    {
                        Key = x.DataType.ToString(), Value = x.OptionKey ?? x.OptionValue
                    })
                    .ToList();
            }
            else
            {
                // 必填`附加信息`判断
                if (supplierProduct.Data.SkuList.First().ExtraInfos.Any(x => x.Required))
                {
                    extraInfoCheck = false;
                    errorMsg = "附加信息不匹配";
                }
            }
        }

        #endregion

        #region supplierOder 初始化数据

        supplierOrder.BaseOrderId = baseOrder.Id;
        supplierOrder.SubOrderId = scenicTicketOrder.Id;
        supplierOrder.SupplierId = scenicTicketOrder.SupplierId;
        supplierOrder.ActiveId = activeId;
        supplierOrder.PackageId = packageId;
        supplierOrder.SkuId = skuId;
        supplierOrder.CurrencyCode = supplier.CurrencyCode;
        supplierOrder.TimeSlot = scenicTicketOrder.TimeSlot;
        supplierOrder.OrderStatus = ScenicTicketSupplierOrderStatus.CreateFail;
        supplierOrder.SupplierType = scenicTicketOrder.OpenSupplierType!.Value;

        #endregion

        var createCheck = supplierProductCheck && extraInfoCheck;
        if (createCheck)
        {
            #region 创建订单流程

            var timeSlot = scenicTicketOrder.TimeSlot.HasValue
                ? scenicTicketOrder.TimeSlot.Value.ToString(@"hh\:mm")
                : "00:00";
            var travelDate = scenicTicketOrder.TravelDate?.Date ?? scenicTicketOrder.ValidityBegin;
            //默认出行日期格式 yyyy-MM-dd
            var createOrderStartTime = travelDate.ToString("yyyy-MM-dd");
            //klook供应商出行日期的格式需要带上时段信息(yyyy-MM-dd HH:mm:ss)
            if (scenicTicketOrder.OpenSupplierType == OpenSupplierType.Klook)
            {
                //存在时段信息则需要加上时段信息
                if (scenicTicketOrder.TimeSlot.HasValue)
                {
                    travelDate = travelDate.Add(scenicTicketOrder.TimeSlot.Value);
                }

                createOrderStartTime = travelDate.ToString("yyyy-MM-dd HH:mm:ss");
            }

            // 查询订单的`供应商备注`
            var supplierRemark = await _dbContext.BaseOrderRemarks.IgnoreQueryFilters()
                .AsNoTracking()
                .Where(x => x.BaseOrderId == receive.BaseOrderId)
                .Where(x => x.RemarkType == BaseOrderRemarkType.SupplierOrderRemark)
                .OrderByDescending(x => x.Id)
                .FirstOrDefaultAsync();

            var createRequest = new SupplierOrderCreateOrderRequest
            {
                OrderId = baseOrder.Id.ToString(),
                BookingNotes = supplierRemark?.Remark ?? string.Empty,
                SupplierType = openSupplierType,
                ContactInfo = contactInfo,
                TravelerInfos = travelInfo,
                Items = new List<SupplierOrderCreateOrderItem>()
                {
                    new SupplierOrderCreateOrderItem
                    {
                        OutProductId = activeId,
                        OutOptionId = packageId,
                        OutSkuId = skuId,
                        Count = scenicTicketOrder.Quantity,
                        StartTime = createOrderStartTime,
                        Timeslot = timeSlot,
                        ExtraInfos = itemExtraInfos
                    }
                }
            };

            var createResponse = await _openSupplierService.CreateOrder(createRequest, tenantId);
            var createIsSuccess = createResponse.Code == _successCode;

            #region 订单价格

            var orderPrice = await _dbContext.OrderPrices.IgnoreQueryFilters()
                .Where(x => x.BaseOrderId == baseOrder.Id)
                .FirstAsync();
            var costPrice = orderPrice.CostPrice;
            var count = scenicTicketOrder.Quantity;

            #endregion

            #region 供应商订单
            
            supplierOrder.SupplierOrderId = createResponse.Data?.OutOrderId;
            supplierOrder.Count = count;
            supplierOrder.SkuPrice = costPrice;
            supplierOrder.Phone = createRequest.ContactInfo.Mobile;
            supplierOrder.OrderStatus = createIsSuccess switch
            {
                true => ScenicTicketSupplierOrderStatus.WaitingForPay, //创单成功待支付
                false => createResponse.IsGatewayTimeOut switch
                {
                    true => ScenicTicketSupplierOrderStatus.Purchasing, //超时等待采购通知
                    false => ScenicTicketSupplierOrderStatus.CreateFail //非超时直接创单失败
                }
            };
            supplierOrder.CommissionRate = createResponse.Data?.CommissionRate ?? 0m;

            #endregion

            #region 门票订单

            scenicTicketOrder.SupplierOrderId = supplierOrder.SupplierOrderId; //采购单号

            #endregion

            //创单记录
            var createRecord = new ScenicTicketSupplierOrderRecord
            {
                BaseOrderId = baseOrder.Id,
                ScenicTicketSupplierOrderId = supplierOrder.Id,
                RecordType = ScenicTicketSupplierOrderRecordType.Create,
                IsSuccess = createIsSuccess,
                ErrorMsg = createResponse.Msg,
                ErrorCode = createResponse.Code
            };
            createRecord.SetTenantId(receive.TenantId);
            await _dbContext.AddAsync(createRecord);

            #endregion

            #region 订单支付流程

            if (createIsSuccess)
            {
                await OrderPayBaseProcess(
                    saasBaseOrderId: baseOrder.Id,
                    openSupplierType: openSupplierType,
                    baseOrders: new List<BaseOrder> { baseOrder },
                    supplierOrders: new List<ScenicTicketSupplierOrder> { supplierOrder },
                    scenicTicketOrders: new List<ScenicTicketOrder> { scenicTicketOrder },
                    orderPrices: new List<OrderPrice> { orderPrice });
            }

            #endregion
            
            result.IsSuccess = createIsSuccess;
            result.Msg = createResponse.Msg;
        }
        else
        {
            //创建失败
            var createFailRecord = new ScenicTicketSupplierOrderRecord
            {
                BaseOrderId = baseOrder.Id,
                ScenicTicketSupplierOrderId = supplierOrder.Id,
                RecordType = ScenicTicketSupplierOrderRecordType.Create,
                IsSuccess = false,
                ErrorMsg = errorMsg
            };
            createFailRecord.SetTenantId(receive.TenantId);
            await _dbContext.AddAsync(createFailRecord);
            
            result.Msg = errorMsg;
        }
        
        #region 触发供应端价库同步

        if (scenicTicketOrder.OpenSupplierType.HasValue)
        {
            await _capPublisher.PublishAsync(CapTopics.Scenic.TriggerOpenChannelSync,
                new TriggerOpenChannelSyncMessage
                {
                    SupplierType = scenicTicketOrder.OpenSupplierType.ToString().ToLowerInvariant(),
                    OutProductId = scenicTicketOrder.SupplierActivityId,
                    OutProductOptionId = scenicTicketOrder.SupplierPackageId,
                    OutSkuId = scenicTicketOrder.SupplierSkuId,
                    TicketId = scenicTicketOrder.ScenicTicketId,
                    TimeSlotId = scenicTicketOrder.TimeSlotId,
                    Time = scenicTicketOrder.TimeSlot,
                    TravelDateTime = scenicTicketOrder.TravelDate?.Date ?? scenicTicketOrder.ValidityBegin.Date
                });
        }

        #endregion

        if (isNewSupplierOrder)
        {
            //新增供应商订单
            await _dbContext.AddAsync(supplierOrder);
        }
        
        return result;
    }

    [UnitOfWork]
    public async Task<CreateSupplierOrderOutput> CombinationOrderProcess(CreateCombinationSupplierApiOrderMessage receive)
    {
        /*
         * ---组合订单合单采购----
         * -.合单只关注API对接的子订单
         * -.需要所有关联子单都是[已支付,待完成]才能执行
         * -.同供应商同套餐的订单合并下单采购
         * -.合并下单的[saas订单号]默认传值关联N个订单号的最小值的id
         * -.无需合单的子单走正常采购流程
         */
        var result = new CreateSupplierOrderOutput();
        var mergeOrderData = await CheckIsCombinationMergeOrder(receive.BaseOrderId);//查询关联的关联订单id
        var saasBaseOrderId = mergeOrderData.SaasBaseOrderId;//用于供应端采购订单号.兼容组合合单处理
        var relatedBaseOrderIds = mergeOrderData.BaseOrderIds;
        
        var scenicTicketOrders = await _dbContext.ScenicTicketOrders.IgnoreQueryFilters()
            .Where(x=> relatedBaseOrderIds.Contains(x.BaseOrderId))
            .ToListAsync();
        var supplierOrders = await _dbContext.ScenicTicketSupplierOrders.IgnoreQueryFilters()
            .Where(x => relatedBaseOrderIds.Contains(x.BaseOrderId))
            .ToListAsync();
        if (supplierOrders.Any())
        {
            //存在采购单->校验采购单状态
            if (supplierOrders.Any(x => x.OrderStatus != ScenicTicketSupplierOrderStatus.CreateFail))
            {
                _logger.LogError("[OpenSupplier]OrderProcess:{@Input},{@Message}",receive,"供应端订单已存在");
                result.Msg = "供应端订单已存在";
                return result;
            }
        }
        
        var baseOrders = await _dbContext.BaseOrders.IgnoreQueryFilters()
            .Where(x => relatedBaseOrderIds.Contains(x.Id))
            .ToListAsync();
        //订单支付状态判断
        if (baseOrders.Any(x => x.Status != BaseOrderStatus.UnFinished))
        {
            _logger.LogWarning("[OpenSupplier]OrderProcess:{@Input},{@Message}",receive,"存在非[待完成]状态子单");
            result.Msg = "存在非[待完成]状态子单";
            return result;
        }
        var orderPrices = await _dbContext.OrderPrices.IgnoreQueryFilters()
            .Where(x => relatedBaseOrderIds.Contains(x.BaseOrderId))
            .ToListAsync();

        #region 供应商信息
        
        var tenantId = receive.TenantId;
        var supplierId = scenicTicketOrders.First().SupplierId;
        receive.OpenSupplierType = scenicTicketOrders.First().OpenSupplierType;
        var header = new List<KeyValuePair<string, string>> {new("tenant", tenantId.ToString())};
        var supplier = await _httpClientFactory.InternalGetAsync<GetSupplierOutput>(
            requestUri: _servicesAddress.Tenant_GetSupplier(supplierId),
            headers: header);
        var openSupplierType = ConvertOpenSupplierType(receive.OpenSupplierType!.Value);

        #endregion

        #region 字段模板信息处理(联系人 & 出行人)

        var orderFieldTypeInfoList = await GetOrderFieldInfo(relatedBaseOrderIds.ToArray());
        var contactInfo = await ContactInfoDataSupplement(orderFieldTypeInfoList.First(x => x.ContactsFieldInfo.Any()).ContactsFieldInfo);//默认取其中一个有值
        var travelFieldInfos = orderFieldTypeInfoList.SelectMany(x => x.TravelFieldInfo).ToList();
        var travelInfos = await TravelerInfoDataSupplement(travelFieldInfos);
        var travelQuantity = scenicTicketOrders.Sum(x => x.Quantity);
        travelInfos = travelInfos.Take(travelQuantity).ToList();
        
        //如果出行人字段模板没有邮箱和手机号,默认赋值联系人信息的相关数据
        foreach (var item in travelInfos)
        {
            if (string.IsNullOrEmpty(item.Email))
            {
                item.Email = contactInfo.Email;
            }

            if (string.IsNullOrEmpty(item.Mobile))
            {
                item.Mobile = contactInfo.Mobile;
                item.MobilePrefix = contactInfo.MobilePrefix;
            }
        }

        #endregion

        #region 附加信息处理

        //附加信息处理
        var itemExtraInfoDic = new Dictionary<long,List<SupplierOrderCreateOrderExtraItem>>();
        var errorMsg = string.Empty;
        var extraInfoCheck = true; //附加信息校验结果
        var supplierProductCheck = true; //供应商产品信息校验结果
        var mergeActivityId = scenicTicketOrders.First().SupplierActivityId;
        var mergePackageId = scenicTicketOrders.First().SupplierPackageId;
        var supplierProduct = await _openSupplierService.SupplierProductDetail(
            new SupplierProductDetailRequest
            {
                SupplierType = openSupplierType,
                OutProductId = mergeActivityId,
                OutProductOptionId = mergePackageId
            }, tenantId);
        if (supplierProduct?.Data is null || supplierProduct.Data?.SkuList.Any() is false)
        {
            _logger.LogError("[OpenSupplier]OrderProcess:{@Input},{@Message}", receive, "供应商产品信息为空");
            supplierProductCheck = false;
            errorMsg = supplierProduct.Msg;
        }
        else
        {
            // 查询订单的附加信息
            var orderExtraInfos = await _dbContext.OpenSupplierOrderExtraInfos.IgnoreQueryFilters()
                .AsNoTracking()
                .Where(x => relatedBaseOrderIds.Contains(x.BaseOrderId))
                .ToListAsync();

            foreach (var relatedItem in scenicTicketOrders)
            {
                var relatedSupplierSku =
                    supplierProduct.Data.SkuList.FirstOrDefault(x => x.OutSkuId == relatedItem.SupplierSkuId);
                var relatedOrderExtraInfos = orderExtraInfos.Where(x => x.BaseOrderId == relatedItem.BaseOrderId)
                    .ToList();
                if (relatedOrderExtraInfos.Any())
                {
                    var itemExtraInfos = relatedOrderExtraInfos.Select(x => new SupplierOrderCreateOrderExtraItem
                        {
                            Key = x.DataType.ToString(), Value = x.OptionKey ?? x.OptionValue
                        })
                        .ToList();
                    itemExtraInfoDic.Add(relatedItem.BaseOrderId, itemExtraInfos);
                }
                else
                {
                    var supplierSkuExtraInfos =
                        relatedSupplierSku?.ExtraInfos ?? new List<SupplierProductDetailExtraInfoItem>();
                    // 必填`附加信息`判断
                    if (supplierSkuExtraInfos.Any(x => x.Required))
                    {
                        extraInfoCheck = false;
                        errorMsg = "附加信息不匹配";
                        itemExtraInfoDic.Add(relatedItem.BaseOrderId, new List<SupplierOrderCreateOrderExtraItem>());
                    }
                }

                var relatedTravelInfos = travelInfos
                    .Where(x => string.IsNullOrEmpty(x.OutSkuId))
                    .GroupBy(x => x.EnName) // 去除重复
                    .Select(x => x.First()) // 获取一条
                    .ToList();

                foreach (var relatedTravelInfoItem in relatedTravelInfos)
                {
                    relatedTravelInfoItem.OutSkuId = relatedItem.SupplierSkuId;
                }
            }
        }

        #endregion

        var createResponse = new ApiBaseResponse<SupplierOrderCreateOrderResponse>();
        var createIsSuccess = false;
        int? createResponseCode = null;
        var createResponseMsg = string.Empty;
        var createCheck = supplierProductCheck && extraInfoCheck;
        if (createCheck)
        {
            #region 合单采购子项处理

            var orderItems = new List<SupplierOrderCreateOrderItem>();
            foreach (var groupOrderItem in scenicTicketOrders.GroupBy(x => new
                     {
                         x.SupplierActivityId, x.SupplierPackageId, x.SupplierSkuId, x.TimeSlot
                     }))
            {
                //供应端合并采购
                var scenicTicketOrder = groupOrderItem.First();

                #region 出行日期 & 时段处理

                var timeSlot = scenicTicketOrder.TimeSlot.HasValue
                    ? scenicTicketOrder.TimeSlot.Value.ToString(@"hh\:mm")
                    : "00:00";
                var travelDate = scenicTicketOrder.TravelDate?.Date ?? scenicTicketOrder.ValidityBegin;
                //默认出行日期格式 yyyy-MM-dd
                var createOrderStartTime = travelDate.ToString("yyyy-MM-dd");
                //klook供应商出行日期的格式需要带上时段信息(yyyy-MM-dd HH:mm:ss)
                if (scenicTicketOrder.OpenSupplierType == OpenSupplierType.Klook)
                {
                    //存在时段信息则需要加上时段信息
                    if (scenicTicketOrder.TimeSlot.HasValue)
                    {
                        travelDate = travelDate.Add(scenicTicketOrder.TimeSlot.Value);
                    }

                    createOrderStartTime = travelDate.ToString("yyyy-MM-dd HH:mm:ss");
                }

                #endregion

                #region 供应端采购子项

                var activeId = groupOrderItem.Key.SupplierActivityId ?? string.Empty;
                var packageId = groupOrderItem.Key.SupplierPackageId ?? string.Empty;
                var skuId = groupOrderItem.Key.SupplierSkuId ?? string.Empty;
                var quantity = groupOrderItem.Sum(x => x.Quantity);
                _ = itemExtraInfoDic.TryGetValue(scenicTicketOrder.BaseOrderId, out var extraInfos);
                orderItems.Add(new SupplierOrderCreateOrderItem
                {
                    OutProductId = activeId,
                    OutOptionId = packageId,
                    OutSkuId = skuId,
                    Count = quantity,
                    StartTime = createOrderStartTime,
                    Timeslot = timeSlot,
                    ExtraInfos = extraInfos
                });

                #endregion
            }
            
            // 查询订单的`供应商备注`
            var supplierRemark = await _dbContext.BaseOrderRemarks.IgnoreQueryFilters()
                .AsNoTracking()
                .Where(x => relatedBaseOrderIds.Contains(x.BaseOrderId))
                .Where(x => x.RemarkType == BaseOrderRemarkType.SupplierOrderRemark)
                .OrderByDescending(x => x.Id)
                .FirstOrDefaultAsync();

            #endregion

            #region 发起供应端采购

            var createRequest = new SupplierOrderCreateOrderRequest
            {
                OrderId = saasBaseOrderId.ToString(),
                BookingNotes = supplierRemark?.Remark ?? string.Empty,
                SupplierType = openSupplierType,
                ContactInfo = contactInfo,
                TravelerInfos = travelInfos,
                Items = orderItems
            };
            createResponse = await _openSupplierService.CreateOrder(createRequest, tenantId);
            createResponseCode = createResponse.Code;
            createResponseMsg = createResponse.Msg;
            createIsSuccess = createResponse.Code == _successCode;

            #endregion
        }
        else
        {
            createResponse.Data = null;
            createResponseMsg = errorMsg;
        }
        
        #region saas订单数据保存&更新

        var addSupplierOrders = new List<ScenicTicketSupplierOrder>();//处理新增的采购单数据
        var allSupplierOrders = new List<ScenicTicketSupplierOrder>();
        foreach (var scenicTicketOrder in scenicTicketOrders)
        {
            var orderPrice = orderPrices.FirstOrDefault(x => x.BaseOrderId == scenicTicketOrder.BaseOrderId);
            
            #region saas 采购单

            var activeId = scenicTicketOrder.SupplierActivityId ?? string.Empty;
            var packageId = scenicTicketOrder.SupplierPackageId ?? string.Empty;
            var skuId = scenicTicketOrder.SupplierSkuId ?? string.Empty;
            var isNewSupplierOrder = false;
            var supplierOrder = supplierOrders.FirstOrDefault(x => x.BaseOrderId == scenicTicketOrder.BaseOrderId);
            if (supplierOrder == null)
            {
                supplierOrder = new ScenicTicketSupplierOrder();
                supplierOrder.SetTenantId(tenantId);

                isNewSupplierOrder = true;
            }
            
            supplierOrder.BaseOrderId = scenicTicketOrder.BaseOrderId;
            supplierOrder.SubOrderId = scenicTicketOrder.Id;
            supplierOrder.SupplierId = scenicTicketOrder.SupplierId;
            supplierOrder.SupplierOrderId = createResponse.Data?.OutOrderId;
            supplierOrder.ActiveId = activeId;
            supplierOrder.PackageId = packageId;
            supplierOrder.SkuId = skuId;
            supplierOrder.CurrencyCode = supplier.CurrencyCode;
            supplierOrder.Count = scenicTicketOrder.Quantity;
            supplierOrder.SkuPrice = orderPrice.CostPrice;
            supplierOrder.TimeSlot = scenicTicketOrder.TimeSlot;
            supplierOrder.Phone = contactInfo.Mobile;
            supplierOrder.OrderStatus = createIsSuccess switch
            {
                true => ScenicTicketSupplierOrderStatus.WaitingForPay,//创单成功待支付
                false => createResponse.IsGatewayTimeOut switch
                {
                    true => ScenicTicketSupplierOrderStatus.Purchasing, //超时等待采购通知
                    false => ScenicTicketSupplierOrderStatus.CreateFail //非超时直接创单失败
                }
            };
            supplierOrder.SupplierType = scenicTicketOrder.OpenSupplierType!.Value;
            supplierOrder.CommissionRate = createResponse.Data?.CommissionRate ?? 0m;

            //新增采购单
            if (isNewSupplierOrder)
                addSupplierOrders.Add(supplierOrder);

            //记录所有采购单.后续操作
            allSupplierOrders.Add(supplierOrder);
            
            #endregion
            
            #region saas 门票订单

            scenicTicketOrder.SupplierOrderId = supplierOrder.SupplierOrderId;//采购单号

            #endregion

            #region saas 采购记录

            var createRecord = new ScenicTicketSupplierOrderRecord
            {
                BaseOrderId = scenicTicketOrder.BaseOrderId,
                ScenicTicketSupplierOrderId = supplierOrder.Id,
                RecordType = ScenicTicketSupplierOrderRecordType.Create,
                IsSuccess = createIsSuccess,
                ErrorMsg = createResponseMsg,
                ErrorCode = createResponseCode
            };
            createRecord.SetTenantId(tenantId);
            await _dbContext.AddAsync(createRecord);

            #endregion

            #region saas 价库同步触发

            await _capPublisher.PublishAsync(CapTopics.Scenic.TriggerOpenChannelSync,
                new TriggerOpenChannelSyncMessage
                {
                    SupplierType = scenicTicketOrder.OpenSupplierType.ToString().ToLowerInvariant(),
                    OutProductId = scenicTicketOrder.SupplierActivityId,
                    OutProductOptionId = scenicTicketOrder.SupplierPackageId,
                    OutSkuId = scenicTicketOrder.SupplierSkuId,
                    TicketId = scenicTicketOrder.ScenicTicketId,
                    TimeSlotId = scenicTicketOrder.TimeSlotId,
                    Time = scenicTicketOrder.TimeSlot,
                    TravelDateTime = scenicTicketOrder.TravelDate?.Date ?? scenicTicketOrder.ValidityBegin.Date
                });

            #endregion
        }
        
        #endregion

        #region 创单成功发起供应端支付

        if (createIsSuccess)
        {
            await OrderPayBaseProcess(
                saasBaseOrderId: saasBaseOrderId,
                openSupplierType: openSupplierType,
                baseOrders : baseOrders,
                scenicTicketOrders: scenicTicketOrders,
                supplierOrders: allSupplierOrders,
                orderPrices: orderPrices);
        }

        #endregion

        if (addSupplierOrders.Any())
            await _dbContext.AddRangeAsync(addSupplierOrders);
        
        result.IsSuccess = createIsSuccess;
        result.Msg = createResponse.Msg;
        return result;
    }

    [UnitOfWork]
    public async Task<SupplierOrderRetryPayOutput> RetryPay(SupplierOrderRetryPayInput input)
    {
        var result = new SupplierOrderRetryPayOutput();
        var mergeOrderData = await CheckIsCombinationMergeOrder(input.BaseOrderId);//查询关联的关联订单id
        var saasBaseOrderId = mergeOrderData.SaasBaseOrderId;//用于供应端采购订单号.兼容组合合单处理
        var relatedBaseOrderIds = mergeOrderData.BaseOrderIds;
        
        var baseOrders = await _dbContext.BaseOrders
            .Where(x => relatedBaseOrderIds.Contains(x.Id))
            .ToListAsync();
        var scenicTicketOrders = await _dbContext.ScenicTicketOrders
            .Where(x => relatedBaseOrderIds.Contains(x.BaseOrderId))
            .ToListAsync();
        var supplierOrders = await _dbContext.ScenicTicketSupplierOrders
            .Where(x => relatedBaseOrderIds.Contains(x.BaseOrderId))
            .ToListAsync();
        var orderPrices= await _dbContext.OrderPrices.IgnoreQueryFilters()
            .Where(x => relatedBaseOrderIds.Contains(x.BaseOrderId))
            .ToListAsync();

        if (baseOrders.Any() is false || scenicTicketOrders.Any() is false || scenicTicketOrders.Any() is false)
            throw new BusinessException(ErrorTypes.Common.NotSupportedOperation);

        //所有关联的订单状态必须一致
        if (supplierOrders.Any(x => x.OrderStatus != ScenicTicketSupplierOrderStatus.WaitingForPay))
            throw new BusinessException(ErrorTypes.Common.NotSupportedOperation);

        //所有关联的订单必须有采购单号
        if (supplierOrders.Any(x=> string.IsNullOrEmpty(x.SupplierOrderId)))
            throw new BusinessException(ErrorTypes.Common.NotSupportedOperation);
        
        //获取开放平台供应商类型
        var supplierOrder = supplierOrders.First();
        var openSupplierType = ConvertOpenSupplierType(supplierOrder.SupplierType);
        var payProcessResult = await OrderPayBaseProcess(
            saasBaseOrderId: saasBaseOrderId,
            openSupplierType: openSupplierType,
            baseOrders: baseOrders,
            supplierOrders: supplierOrders,
            scenicTicketOrders: scenicTicketOrders,
            orderPrices: orderPrices);
        result.IsSuccess = payProcessResult.IdSuccess;
        result.Msg = payProcessResult.Msg;
        return result;
    }
    
    [UnitOfWork]
    public async Task DelayUpdateSupplierOrderInfo(DelayUpdateSupplierOrderInformationMessage receive)
    {
        //Treep不会立刻返回供应商订单号所以需要重新去拉取
        var scenicTicketOrder = await _dbContext.ScenicTicketOrders
            .FirstOrDefaultAsync(x => x.BaseOrderId == receive.BaseOrderId);
        var supplierOrder = await _dbContext.ScenicTicketSupplierOrders
            .FirstOrDefaultAsync(x => x.BaseOrderId == receive.BaseOrderId);
        if (scenicTicketOrder is null || supplierOrder is null)
            return;

        //供应端订单id已有值.跳过
        if (!string.IsNullOrEmpty(scenicTicketOrder.SupplierOrderId) &&
            !string.IsNullOrEmpty(supplierOrder.SupplierOrderId))
        {
            return;
        }
        
        //查询订单详情
        await Task.Delay(TimeSpan.FromSeconds(1));//延迟1s执行,Treep不会立刻返回供应商订单号所以需要隔一段时间去拉取
        var tenantId = scenicTicketOrder.TenantId;
        var orderDetailResponse = await _openSupplierService.GetOrderDetail(new SupplierOrderDetailRequest
        {
            SupplierType = receive.OpenSupplierType,
            OrderId = receive.BaseOrderId.ToString()
        },tenantId);

        var outOrderId = orderDetailResponse.Data?.OutOrderId;
        if (orderDetailResponse.Code == _successCode && !string.IsNullOrEmpty(outOrderId))
        {
            scenicTicketOrder.SupplierOrderId = outOrderId;//采购单号
            supplierOrder.SupplierOrderId = outOrderId;//采购单号
        }
        else
        {
            //超过最大查询次数,不再查询
            var newAttemptCount = receive.UpdateAttemptCount + 1;
            if (newAttemptCount > _updateAttemptMaxCount)
            {
                _logger.LogWarning("[OpenSupplier]UpdateSupplierOrderInfo:{@input},{@message}",receive," Update attempt count exceed max count.");
                return;
            }
            
            //获取不到供应端订单号或者失败继续查询
            await _capPublisher.PublishAsync(CapTopics.Order.UpdateSupplierOrderInformation,new DelayUpdateSupplierOrderInformationMessage
            {
                OpenSupplierType = receive.OpenSupplierType,
                BaseOrderId = supplierOrder.BaseOrderId,
                UpdateAttemptCount = newAttemptCount
            });
        }
    }

    [UnitOfWork]
    public async Task<SupplierOrderDeliveryOutput> OrderDelivery(SupplierOrderDeliveryInput input)
    {
        var result = new SupplierOrderDeliveryOutput {Code = _successCode};
        var mergeOrderData = await CheckIsCombinationMergeOrder(input.BaseOrderId);//查询关联的关联订单id
        var saasBaseOrderId = mergeOrderData.SaasBaseOrderId;//用于供应端采购订单号.兼容组合合单处理
        var relatedBaseOrderIds = mergeOrderData.BaseOrderIds;
        
        var baseOrders = await _dbContext.BaseOrders.IgnoreQueryFilters()
            .Where(x => relatedBaseOrderIds.Contains(x.Id))
            .ToListAsync();
        var scenicTicketOrders = await _dbContext.ScenicTicketOrders.IgnoreQueryFilters()
            .Where(x => relatedBaseOrderIds.Contains(x.BaseOrderId))
            .ToListAsync();
        var supplierOrders = await _dbContext.ScenicTicketSupplierOrders.IgnoreQueryFilters()
            .Where(x => relatedBaseOrderIds.Contains(x.BaseOrderId) && x.SupplierOrderId == input.SupplierOrderId)
            .ToListAsync();
        var supplierOrderRecords = await _dbContext.ScenicTicketSupplierOrderRecords.IgnoreQueryFilters()
            .Where(x => relatedBaseOrderIds.Contains(x.BaseOrderId))
            .ToListAsync();
        
        //判断订单类型
        if (scenicTicketOrders.Any(x=>x.CredentialSourceType != CredentialSourceType.InterfaceDock))
        {
            result.Code = _errorCode;
            result.Msg = "非API对接产品订单";
            return result;
        }
        
        //组合单判断
        var combinationOrderId = scenicTicketOrders.First().TicketsCombinationOrderId;
        if (combinationOrderId.HasValue)
        {
            var combinationOrderRecord = supplierOrderRecords
                .OrderByDescending(x => x.Id)
                .FirstOrDefault();
            if (combinationOrderRecord.RecordType == ScenicTicketSupplierOrderRecordType.InCombination)
            {
                result.Code = _errorCode;
                result.Msg = "组合中订单不支持重复发货";
                return result;
            }
            
            if (combinationOrderRecord.RecordType == ScenicTicketSupplierOrderRecordType.SyncDelivery)
            {
                result.Code = _errorCode;
                result.Msg = "已发货的组合订单不支持重复发货";
                return result;
            }

            if (combinationOrderRecord is { RecordType: ScenicTicketSupplierOrderRecordType.Delivery, IsSuccess: true })
            {
                result.Code = _errorCode;
                result.Msg = "已发货的组合订单不支持重复发货";
                return result;
            }
        }

        var deliveryStatus = new List<ScenicTicketSupplierOrderStatus>
        {
            ScenicTicketSupplierOrderStatus.WaitingForDeliver,
            ScenicTicketSupplierOrderStatus.DeliveryFail,
            ScenicTicketSupplierOrderStatus.Delivered
        };
        if (supplierOrders.Any(x=> !deliveryStatus.Contains(x.OrderStatus)))
        {
            result.Code = _errorCode;
            result.Msg = "订单状态无法发货";
            return result;
        }
        
        var oldVouchers = await _dbContext.ScenicTicketSupplierOrderVouchers
            .IgnoreQueryFilters()
            .Where(x => relatedBaseOrderIds.Contains(x.BaseOrderId))
            .ToListAsync();
        if (oldVouchers.Any())
        {
            _dbContext.RemoveRange(oldVouchers);//因为允许多次发货,所以需要删除之前的凭证数据
        }

        foreach (var supplierOrder in supplierOrders)
        {
            var baseOrder = baseOrders.First(x => x.Id == supplierOrder.BaseOrderId);
            var scenicTicketOrder = scenicTicketOrders.First(x => x.BaseOrderId == supplierOrder.BaseOrderId);
            var supplierRecordList = supplierOrderRecords.Where(x => x.BaseOrderId == supplierOrder.BaseOrderId)
                .ToList();
            
            // 是否第一次发货
            var isRepeatedDelivery = supplierRecordList.Any(x =>
                x.RecordType is ScenicTicketSupplierOrderRecordType.Delivery
                    or ScenicTicketSupplierOrderRecordType.InCombination);
            
            //供货方订单状态-已发货
            supplierOrder.OrderStatus = ScenicTicketSupplierOrderStatus.Delivered;
            supplierOrder.UpdateTime = DateTime.Now;

            //供货方订单发货记录
            var deliveryRecord = new ScenicTicketSupplierOrderRecord();
            if (input.Vouchers.Any())
            {
                //记录供货方凭证数据
                var vouchers = new List<ScenicTicketSupplierOrderVoucher>();
                foreach (var voucher in input.Vouchers.Select(voucherItem => new ScenicTicketSupplierOrderVoucher
                         {
                             ScenicTicketSupplierOrderId = supplierOrder.Id,
                             BaseOrderId = baseOrder.Id,
                             FilePath = voucherItem.FilePath,
                             Thumbnail = voucherItem.ThumbnailPath,
                             SourcePath = voucherItem.SourcePath,
                             ImageSourcePath = voucherItem.ImageSourcePath,
                             PdfSourcePath = voucherItem.PdfSourcePath
                         }))
                {
                    voucher.SetTenantId(baseOrder.TenantId);
                    vouchers.Add(voucher);
                }

                if (vouchers.Any())
                    await _dbContext.AddRangeAsync(vouchers);

                //OTA发货
                var syncVouchers = vouchers.Select(x => new SupplierOrderVoucherItem
                {
                    FilePath = x.FilePath,
                    ThumbnailPath = x.Thumbnail,
                    SourcePath = x.SourcePath,
                    ImageSourcePath = x.ImageSourcePath,
                    PdfSourcePath = x.PdfSourcePath
                }).ToList();
                deliveryRecord = await _scenicTicketVoucherService.BaseDelivery(baseOrder, scenicTicketOrder,
                    supplierOrder.Id, syncVouchers,
                    ignoreEmailSend: false);
            }
            else
            {
                //记录虚拟凭证订单表示
                supplierOrder.IsVirtualVoucher = true;
                scenicTicketOrder.IsVirtualVoucher = true;

                if (!isRepeatedDelivery)
                {
                    // 紧急标签 (只在第一次发货做处理)
                    var aggregateOrder = await _dbContext.AggregateOrders.IgnoreQueryFilters()
                        .FirstOrDefaultAsync(x => x.BaseOrderId == scenicTicketOrder.BaseOrderId);
                    if (aggregateOrder != null)
                        aggregateOrder.ProcessingLevelTag = AggregateOrderProcessingLevelTag.Emergency; 
                }

                //组合订单.已经同步过渠道的组合订单.不再走组合订单的流程.需要查出组合的凭证.重新发送给渠道
                if (scenicTicketOrder.TicketsCombinationOrderId.HasValue)
                {
                    deliveryRecord = new ScenicTicketSupplierOrderRecord
                    {
                        ScenicTicketSupplierOrderId = supplierOrder?.Id ?? 0,
                        BaseOrderId = baseOrder.Id,
                        RecordType = ScenicTicketSupplierOrderRecordType.InCombination,
                        IsSuccess = true
                    };

                    //定时同步OTA
                    _backgroundJobClient.Schedule<HangfireClient.Jobs.IOrderJob>(
                        s => s.ScenicTicketCombinationOrderDelivery(scenicTicketOrder.TicketsCombinationOrderId.Value,
                            baseOrder.TenantId),
                        TimeSpan.FromSeconds(3));
                }
                else
                {
                    //开放平台无凭证或Saas处理图片失败.记录发货失败结果
                    deliveryRecord = new ScenicTicketSupplierOrderRecord
                    {
                        ScenicTicketSupplierOrderId = supplierOrder.Id,
                        BaseOrderId = baseOrder.Id,
                        RecordType = ScenicTicketSupplierOrderRecordType.Delivery,
                        IsSuccess = false,
                        ErrorMsg = input.Msg,
                        ErrorCode = input.ErrorCode
                    };
                }
            }
            deliveryRecord.SetTenantId(baseOrder.TenantId);
            await _dbContext.AddAsync(deliveryRecord);

            if (baseOrder.SellingPlatform == SellingPlatform.TikTok && (baseOrder.OrderCategory & OrderCategory.CombinationOrder) == 0)
            {
                // 抖音发送退款审核消息
                await _capPublisher.PublishAsync(CapTopics.Order.OpenChannelRefundApplyAudit,
                    new ProcessingChannelRefundApplyOrderMessage
                    {
                        ChannelOrderNo = baseOrder.ChannelOrderNo,
                        SellingPlatform = baseOrder.SellingPlatform,
                        OrderType = baseOrder.OrderType,
                        OrderCategory = baseOrder.OrderCategory,
                    });
            }
        }
        
        // 更新价格
        await _capPublisher.PublishAsync(CapTopics.Order.ScenicSupplierOrderPriceUpdate,new ScenicSupplierOrderPriceUpdateMessage
        {
            SaasBaseOrderId = saasBaseOrderId,
            SupplierOrderId = input.SupplierOrderId,
            RelatedBaseOrderIds = relatedBaseOrderIds
        });
        
        return result;
    }

    public async Task<List<GetSupplierVoucherDeliveryInfoOutput>> GetVouchers(params long[] baseOrderIds)
    {
        if(baseOrderIds.Any() is false) return new List<GetSupplierVoucherDeliveryInfoOutput>();
        
        var vouchers = await _dbContext.ScenicTicketSupplierOrderVouchers.IgnoreQueryFilters()
            .Where(x => baseOrderIds.Contains(x.BaseOrderId))
            .Select(x=> new { x.BaseOrderId,x.FilePath,x.Thumbnail,x.CreateTime })
            .ToListAsync();
        
        #region 兼容旧客路

        var sVoucherBaseOrderIds = vouchers.Select(x => x.BaseOrderId).Distinct().ToList();
        var klookBaseOrderIds = baseOrderIds.Except(sVoucherBaseOrderIds).ToList();
        if (klookBaseOrderIds.Any())
        {
            var klookOrders = await _dbContext.ScenicTicketKlookOrders.AsNoTracking()
                .Where(x => klookBaseOrderIds.Contains(x.BaseOrderId))
                .Select(x => new {x.Id, x.BaseOrderId})
                .ToListAsync();
            var klookOrderIds = klookOrders.Select(x => x.Id).ToList();
            
            var klookVouchers = await _dbContext.ScenicTicketKlookVouchers.AsNoTracking()
                .Where(x => klookOrderIds.Contains(x.ScenicTicketKlookOrderId))
                .Select(x => new
                {
                    x.ScenicTicketKlookOrderId,
                    x.FilePath, x.Thumbnail, x.CreateTime
                })
                .ToListAsync();

            foreach (var klookVoucher in klookVouchers)
            {
                var klookOrder = klookOrders.FirstOrDefault(x => x.Id == klookVoucher.ScenicTicketKlookOrderId);
                vouchers.Add(new
                {
                    klookOrder.BaseOrderId, 
                    klookVoucher.FilePath,
                    klookVoucher.Thumbnail, 
                    klookVoucher.CreateTime
                });
            }
        }

        #endregion
        
        if (vouchers.Any() is false)
        {
            return new List<GetSupplierVoucherDeliveryInfoOutput>();
        }

        var result = new List<GetSupplierVoucherDeliveryInfoOutput>();
        foreach (var voucher in vouchers)
        {
            var item = new GetSupplierVoucherDeliveryInfoOutput
            {
                BaseOrderId = voucher.BaseOrderId,
                FilePath = voucher.FilePath,
                Thumbnail = voucher.Thumbnail,
                CreateTime = voucher.CreateTime
            };
            result.Add(item);
        }
        
        return result.ToList();
    }

    public async Task<GetSupplierOrderDetailOutput> GetOrderDetail(GetSupplierOrderDetailInput input)
    {
        if (string.IsNullOrEmpty(input.SupplierOrderId) && !input.BaseOrderId.HasValue)
            return null;
        
        var orderDetail = await _dbContext.ScenicTicketSupplierOrders.AsNoTracking()
            .WhereIF(!string.IsNullOrEmpty(input.SupplierOrderId), x => x.SupplierOrderId == input.SupplierOrderId)
            .WhereIF(input.BaseOrderId is > 0, x => x.BaseOrderId == input.BaseOrderId)
            .Select(x => new GetSupplierOrderDetailOutput
            {
                SupplierOrderId = x.SupplierOrderId,
                BaseOrderId = x.BaseOrderId,
                TenantId = x.TenantId,
                OrderStatus = x.OrderStatus
            })
            .FirstOrDefaultAsync();
        
        //兼容旧客路
        if (orderDetail == null)
        {
            orderDetail = await _dbContext.ScenicTicketKlookOrders.AsNoTracking()
                .WhereIF(!string.IsNullOrEmpty(input.SupplierOrderId), x => x.KlookOrderId == input.SupplierOrderId)
                .WhereIF(input.BaseOrderId is > 0, x => x.BaseOrderId == input.BaseOrderId)
                .Select(x => new GetSupplierOrderDetailOutput
                {
                    SupplierOrderId = x.KlookOrderId, 
                    BaseOrderId = x.BaseOrderId, 
                    TenantId = x.TenantId,
                    IsOldKlook = true,
                    OrderStatus = x.OrderStatus
                })
                .FirstOrDefaultAsync();
        }

        return orderDetail;
    }

    [UnitOfWork]
    public async Task SupplierOrderStatusNotify(SupplierOrderStatusNotifyInput input)
    {
        if (input.OpenSupplierType == OpenSupplierType.System)
            throw new BusinessException(ErrorTypes.Common.NotSupportedOperation);
        
        var mergeOrderData = await CheckIsCombinationMergeOrder(input.BaseOrderId);//查询关联的关联订单id
        var saasBaseOrderId = mergeOrderData.SaasBaseOrderId;//用于供应端采购订单号.兼容组合合单处理
        var relatedBaseOrderIds = mergeOrderData.BaseOrderIds;
        
        //订单状态通知枚举映射
        if (_orderStatusNotifyMap.TryGetValue(input.OpenSupplierOrderStatus,
                out OrderStatusNotifyMapEnum? statusNotifyMapEnum))
        {
            var supplierOrders = await _dbContext.ScenicTicketSupplierOrders
                .Where(x => relatedBaseOrderIds.Contains(x.BaseOrderId))
                .ToListAsync();

            foreach (var supplierOrder in supplierOrders)
            {
                if (supplierOrder is null)
                {
                    throw new BusinessException(ErrorTypes.Order.OrderNotFind);
                }

                //特殊处理-发货失败
                if (input.OpenSupplierOrderStatus == OpenSupplierOrderStatus.DeliverFailed)
                {
                    //判断供应端原始连接是否有值
                    if (!string.IsNullOrEmpty(input.OriginalVoucherUrl))
                    {
                        //供应商原始凭证地址有值表示供应端发货成功
                        statusNotifyMapEnum = statusNotifyMapEnum with
                        {
                            SupplierOrderStatus = ScenicTicketSupplierOrderStatus.Delivered
                        };
                    }
                }

                if (!string.IsNullOrEmpty(input.OutOrderId))
                    supplierOrder.SupplierOrderId = input.OutOrderId;

                supplierOrder.OrderStatus = statusNotifyMapEnum.SupplierOrderStatus;
                supplierOrder.UpdateTime = DateTime.Now;

                var supplierOrderRecord = new ScenicTicketSupplierOrderRecord
                {
                    ScenicTicketSupplierOrderId = supplierOrder.Id,
                    BaseOrderId = input.BaseOrderId,
                    RecordType = statusNotifyMapEnum.RecordType,
                    IsSuccess = statusNotifyMapEnum.RecordTypeResult,
                    ErrorCode = statusNotifyMapEnum.ErrorCode,
                    ErrorMsg = input.ErrorMsg
                };

                await _dbContext.AddAsync(supplierOrderRecord);
            }
        }
    }

    [UnitOfWork]
    public async Task<ResyncSupplierOrderDataOutput> ResyncOrderData(ResyncSupplierOrderDataInput input)
    {
        var result = new ResyncSupplierOrderDataOutput();
        var mergeOrderData = await CheckIsCombinationMergeOrder(input.BaseOrderId);//查询关联的关联订单id
        var saasBaseOrderId = mergeOrderData.SaasBaseOrderId;//用于供应端采购订单号.兼容组合合单处理
        var relatedBaseOrderIds = mergeOrderData.BaseOrderIds;
        
        var baseOrders = await _dbContext.BaseOrders
            .Where(x => relatedBaseOrderIds.Contains(x.Id))
            .ToListAsync();
        var scenicTicketOrders = await _dbContext.ScenicTicketOrders
            .Where(x => relatedBaseOrderIds.Contains(x.BaseOrderId))
            .ToListAsync();
        var supplierOrders = await _dbContext.ScenicTicketSupplierOrders
            .Where(x => relatedBaseOrderIds.Contains(x.BaseOrderId))
            .ToListAsync();
        var orderPrices = await _dbContext.OrderPrices.IgnoreQueryFilters()
            .Where(x => relatedBaseOrderIds.Contains(x.BaseOrderId))
            .ToListAsync();

        if (baseOrders.Any() is false || supplierOrders.Any() is false || scenicTicketOrders.Any() is false)
            throw new BusinessException(ErrorTypes.Common.NotSupportedOperation);

        if (supplierOrders.Any(x=> string.IsNullOrEmpty(x.SupplierOrderId)))
            throw new BusinessException(ErrorTypes.Common.NotSupportedOperation);

        foreach (var supplierOrder in supplierOrders)
        {
            var baseOrder = baseOrders.First(x => x.Id == supplierOrder.BaseOrderId);
            var scenicTicketOrder = scenicTicketOrders.First(x => x.BaseOrderId == supplierOrder.BaseOrderId);
            var orderPrice = orderPrices.First(x => x.BaseOrderId == supplierOrder.BaseOrderId);
            var processResult = await OrderDetailProcess(
                saasBaseOrderId: saasBaseOrderId,
                baseOrder: baseOrder,
                supplierOrder: supplierOrder,
                scenicTicketOrder: scenicTicketOrder,
                openSupplierType: supplierOrder.SupplierType.ToString().ToLowerInvariant(),
                orderPrice: orderPrice);
            
            result.IsSuccess = processResult.IsSuccess;
            result.Msg = processResult.Msg;
            result.NeedPay = processResult.NeedPay;
            result.NeedDelivery = processResult.NeedDelivery;
            result.OrderDetailStatus = processResult.OpenSupplierOrderDetailStatus;
        }
        return result;
    }

    public async Task<ReCreatePreCheckOutput> ReCreatePreCheck(long baseOrderId)
    {
        var result = new ReCreatePreCheckOutput()
        {
            BaseOrderId = baseOrderId
        };
        var mergeOrderItem = await _dbContext.TicketsCombinationMergeOrderItems.IgnoreQueryFilters()
            .AsNoTracking()
            .Where(x => x.BaseOrderId == baseOrderId)
            .FirstOrDefaultAsync();
        result.MergeOrderId = mergeOrderItem?.MergeOrderId;
        result.CombinationOrderId = mergeOrderItem?.CombinationOrderId;
        return result;
    }

    [UnitOfWork]
    public async Task UpdateOrderPrice(ScenicSupplierOrderPriceUpdateMessage message)
    {
        var baseOrders = await _dbContext.BaseOrders
            .IgnoreQueryFilters()
            .Where(x => message.RelatedBaseOrderIds.Contains(x.Id))
            .ToListAsync();
        var supplierOrders = await _dbContext.ScenicTicketSupplierOrders
            .IgnoreQueryFilters()
            .Where(x => message.RelatedBaseOrderIds.Contains(x.BaseOrderId) && x.SupplierOrderId == message.SupplierOrderId)
            .ToListAsync();
        
        await UpdatePriceAfterDelivery(
            saasBaseOrderId: message.SaasBaseOrderId,
            supplierOrderId: message.SupplierOrderId,
            baseOrders: baseOrders,
            supplierOrders: supplierOrders);
    }
    
    #region 私有方法

    /// <summary>
    /// 订单支付处理,并且更新对应订单数据
    /// </summary>
    /// <param name="baseOrder"></param>
    /// <param name="supplierOrder"></param>
    /// <param name="scenicTicketOrder"></param>
    /// <param name="openSupplierType"></param>
    /// <param name="orderPrice"></param>
    /// <returns></returns>
    record OrderPayBaseProcessResultData(bool IdSuccess,string Msg);
    private async Task<OrderPayBaseProcessResultData> OrderPayBaseProcess(
        long saasBaseOrderId,
        string openSupplierType,
        List<BaseOrder> baseOrders,
        List<ScenicTicketSupplierOrder> supplierOrders,
        List<ScenicTicketOrder> scenicTicketOrders,
        List<OrderPrice> orderPrices)
    {
        #region 支付处理

        var outOrderId = supplierOrders.First().SupplierOrderId;
        var tenantId = supplierOrders.First().TenantId;
        var payRequest = new SupplierOrderPayBalanceRequest
        {
            OutOrderId = outOrderId, 
            OrderId = saasBaseOrderId.ToString(), 
            SupplierType = openSupplierType
        };
        var payResponse = await _openSupplierService.PayOrder(payRequest, tenantId);
        var payIsSuccess = payResponse.Code == _successCode;
        var payMsg = payResponse.Msg;

        #endregion

        foreach (var scenicTicketOrder in scenicTicketOrders)
        {
            var baseOrder = baseOrders.First(x => x.Id == scenicTicketOrder.BaseOrderId);
            var supplierOrder = supplierOrders.First(x => x.BaseOrderId == scenicTicketOrder.BaseOrderId);
            var orderPrice = orderPrices.First(x => x.BaseOrderId == scenicTicketOrder.BaseOrderId);

            if (payIsSuccess)
            {
                //更新供应商订单状态
                var transactionStatus = OpenSupplierOrderTransactionStatus.Fail;
                if (!string.IsNullOrEmpty(payResponse.Data.TransactionStatus))
                {
                    transactionStatus =
                        _openPlatformBaseService.GetSaasEnumValue<OpenSupplierOrderTransactionStatus>(payResponse.Data
                            .TransactionStatus);
                }

                supplierOrder.OrderStatus = transactionStatus switch
                {
                    OpenSupplierOrderTransactionStatus.Success => ScenicTicketSupplierOrderStatus.WaitingForDeliver,
                    OpenSupplierOrderTransactionStatus.Processing => ScenicTicketSupplierOrderStatus.PayProcessing,
                    OpenSupplierOrderTransactionStatus.Fail => ScenicTicketSupplierOrderStatus.WaitingForPay,
                    _ => throw new ArgumentOutOfRangeException(nameof(transactionStatus), transactionStatus, null)
                };

                await OrderDetailProcess(
                    saasBaseOrderId: saasBaseOrderId,
                    baseOrder: baseOrder,
                    supplierOrder: supplierOrder,
                    scenicTicketOrder: scenicTicketOrder,
                    openSupplierType: openSupplierType,
                    orderPrice: orderPrice);


                //时效订单.需要发送时效凭证到渠道(不影响正常流程)
                if (scenicTicketOrder is {IsChannelTimeliness: true, TimelinessChannelTypes: not null} &&
                    supplierOrder.OrderStatus == ScenicTicketSupplierOrderStatus.WaitingForDeliver)
                {
                    await _capPublisher.PublishAsync(CapTopics.Order.ChannelTimelinessVoucherSync,
                        new ChannelTimelinessVoucherSyncMessage
                        {
                            TenantId = tenantId,
                            BaseOrderId = baseOrder.Id,
                            SubOrderId = scenicTicketOrder.Id,
                            OrderType = baseOrder.OrderType,
                            ChannelOrderNo = baseOrder.ChannelOrderNo,
                            SellingPlatform = baseOrder.SellingPlatform,
                            AgencyId = baseOrder.AgencyId,
                            TimelinessChannelTypes = scenicTicketOrder.TimelinessChannelTypes,
                            ProductId = scenicTicketOrder.ScenicTicketId,
                            TimelinessTriggerType = OpenChannelTimelinessTriggerType.SupplierCreatedOrder
                        });
                }
            }

            #region 支付记录
            
            var payRecord = new ScenicTicketSupplierOrderRecord
            {
                BaseOrderId = scenicTicketOrder.BaseOrderId,
                ScenicTicketSupplierOrderId = supplierOrder.Id,
                RecordType = ScenicTicketSupplierOrderRecordType.Payment,
                IsSuccess = supplierOrder.OrderStatus != ScenicTicketSupplierOrderStatus.WaitingForPay, //还在待支付状态 =>支付失败,
                ErrorMsg = payResponse.Msg,
                ErrorCode = payResponse.Code
            };
            payRecord.SetTenantId(scenicTicketOrder.TenantId);
            await _dbContext.AddAsync(payRecord);

            #endregion
        }

        return new OrderPayBaseProcessResultData(payIsSuccess,payMsg);
    }

    /// <summary>
    /// 订单详情处理
    /// </summary>
    /// <param name="baseOrder"></param>
    /// <param name="supplierOrder"></param>
    /// <param name="scenicTicketOrder"></param>
    /// <param name="openSupplierType"></param>
    /// <param name="orderPrice"></param>
    /// <returns></returns>
    private async Task<OpenSupplierOrderDetailProcessOutput> OrderDetailProcess(
        long saasBaseOrderId,
        string openSupplierType,
        BaseOrder baseOrder,
        ScenicTicketSupplierOrder supplierOrder,
        ScenicTicketOrder scenicTicketOrder,
        OrderPrice orderPrice)
    {
        var result = new OpenSupplierOrderDetailProcessOutput();
        var tenantId = scenicTicketOrder.TenantId;
        //查询订单详情
        var orderDetailResponse = await _openSupplierService.GetOrderDetail(
            new SupplierOrderDetailRequest
            {
                OutOrderId = supplierOrder.SupplierOrderId,
                SupplierType = openSupplierType,
                OrderId = saasBaseOrderId.ToString()
            }, tenantId);
        var isSuccess = orderDetailResponse.Code == _successCode;
        OpenSupplierOrderDetailStatus? orderStatus = null;
        if (isSuccess)
        {
            var orderDetail = orderDetailResponse.Data;

            //基础信息补充
            supplierOrder.Buyer = orderDetail.Buyer;
            supplierOrder.Email = orderDetail.Email;
            
            //价格计算补充
            await UpdateCostAndCommission(
                orderDetail: orderDetail,
                baseOrder: baseOrder,
                supplierOrder: supplierOrder,
                orderPrice: orderPrice);
            
            //订单状态补充
            orderStatus =
                _openPlatformBaseService.GetSaasEnumValue<OpenSupplierOrderDetailStatus>(orderDetail.OrderStatus);

            switch (orderStatus.Value)
            {
                case OpenSupplierOrderDetailStatus.Ordered
                    when supplierOrder.OrderStatus == ScenicTicketSupplierOrderStatus.Purchasing: //采购中 => 待支付
                case OpenSupplierOrderDetailStatus.Payed
                    when supplierOrder.OrderStatus is ScenicTicketSupplierOrderStatus.WaitingForPay or ScenicTicketSupplierOrderStatus.PayProcessing: //待支付 or 支付中 => 已支付
                case OpenSupplierOrderDetailStatus.Shipped
                    when supplierOrder.OrderStatus is ScenicTicketSupplierOrderStatus.WaitingForPay or ScenicTicketSupplierOrderStatus.PayProcessing: //已发货,但是saas状态还卡在支付的状态
                case OpenSupplierOrderDetailStatus.Shipped
                    when supplierOrder.OrderStatus is ScenicTicketSupplierOrderStatus.DeliveryFail
                        or ScenicTicketSupplierOrderStatus.Delivered
                        or ScenicTicketSupplierOrderStatus.WaitingForDeliver: //发货失败 or 待发货 or  已发货
                case OpenSupplierOrderDetailStatus.Cancelled: //已取消
                    if (_orderDetailStatusMap.TryGetValue(orderStatus.Value, out OrderDetailStatusMapEnum? detailStatusMapEnum))
                    {
                        //发货成功or失败不需要新增操作记录
                        if (supplierOrder.OrderStatus != ScenicTicketSupplierOrderStatus.DeliveryFail && supplierOrder.OrderStatus != ScenicTicketSupplierOrderStatus.Delivered)
                        {
                            supplierOrder.OrderStatus = detailStatusMapEnum.SupplierOrderStatus;
                            supplierOrder.UpdateTime = DateTime.Now;

                            var supplierOrderRecord = new ScenicTicketSupplierOrderRecord
                            {
                                ScenicTicketSupplierOrderId = supplierOrder.Id,
                                BaseOrderId = baseOrder.Id,
                                RecordType = detailStatusMapEnum.RecordType,
                                IsSuccess = detailStatusMapEnum.RecordTypeResult,
                                ErrorCode = detailStatusMapEnum.ErrorCode
                            };
                            supplierOrderRecord.SetTenantId(tenantId);
                            await _dbContext.AddAsync(supplierOrderRecord);
                        }
                        //待支付记录
                        result.NeedPay = detailStatusMapEnum.SupplierOrderStatus == ScenicTicketSupplierOrderStatus.WaitingForPay;
                        
                        //待发货,重新发货
                        result.NeedDelivery = detailStatusMapEnum.SupplierOrderStatus == ScenicTicketSupplierOrderStatus.WaitingForDeliver;
                        
                        #region 重复发货校验

                        if (result.NeedDelivery &&
                            supplierOrder.OrderStatus == ScenicTicketSupplierOrderStatus.Delivered)
                        {
                            // 限制[凭证转换失败]订单
                            var checkSupplierRecord = await _dbContext.ScenicTicketSupplierOrderRecords.AsNoTracking()
                                .Where(x => x.BaseOrderId == baseOrder.Id)
                                .OrderByDescending(x => x.Id)
                                .FirstOrDefaultAsync();
                            if (checkSupplierRecord is { ErrorCode: (int)OrderBusinessErrorCodeType.DeliverFailed })
                            {
                                var checkOrderTicket = orderDetail.Tickets.FirstOrDefault();
                                if (checkOrderTicket?.Vouchers.Any() is false) //订单详情正常不返回凭证.不做发货处理
                                {
                                    result.NeedDelivery = false; // 不做发货处理
                                }
                            }
                        }

                        #endregion
                    }
                    break;
            }

            //更新期票有效期
            if (orderDetail.Tickets.Any() &&
                scenicTicketOrder.ScenicTicketsType == ScenicTicketsType.PromissoryNote)
            {
                //有返回有效期范围才更新saas订单的有效期
                var orderTicketDetail = orderDetail.Tickets.FirstOrDefault();
                if (orderTicketDetail is {TicketValidBegin: not null, TicketValidEnd: not null})
                {
                    scenicTicketOrder.ValidityBegin = orderTicketDetail.TicketValidBegin!.Value;
                    scenicTicketOrder.ValidityEnd = orderTicketDetail.TicketValidEnd!.Value;
                }
            }

            //特殊处理(treep只有在订单详情才返回供应商订单号)
            if (scenicTicketOrder.OpenSupplierType == OpenSupplierType.TreepFlyingEarth)
            {
                scenicTicketOrder.SupplierOrderId = orderDetail.OutOrderId; //采购单号
                supplierOrder.SupplierOrderId = orderDetail.OutOrderId;

                if (string.IsNullOrEmpty(orderDetail.OutOrderId))//没有返回OutOrderId,延迟更新
                {
                    //因为treep不会立刻返回OutOrderId,需要延迟更新
                    await _capPublisher.PublishAsync(CapTopics.Order.UpdateSupplierOrderInformation,
                        new DelayUpdateSupplierOrderInformationMessage
                        {
                            OpenSupplierType = openSupplierType,
                            BaseOrderId = supplierOrder.BaseOrderId,
                            UpdateAttemptCount = 1
                        });
                }
            }
        }

        result.IsSuccess = isSuccess;
        result.Msg = orderDetailResponse.Msg;
        result.OpenSupplierOrderDetailStatus = orderStatus;
        return result;
    }
    
    /// <summary>
    /// 订单状态通知枚举映射
    /// <param name="RecordTypeResult">表示对应操作的结果</param>
    /// </summary>
    record OrderStatusNotifyMapEnum(
        ScenicTicketSupplierOrderStatus SupplierOrderStatus,
        ScenicTicketSupplierOrderRecordType RecordType,
        int? ErrorCode,
        bool RecordTypeResult);

    private static readonly Dictionary<OpenSupplierOrderStatus, OrderStatusNotifyMapEnum> _orderStatusNotifyMap = new()
    {
        // 订单被拒绝
        {
            OpenSupplierOrderStatus.Reject, new OrderStatusNotifyMapEnum(ScenicTicketSupplierOrderStatus.Reject,
                ScenicTicketSupplierOrderRecordType.Reject, (int)OrderBusinessErrorCodeType.Rejected,true)
        },
        // 订单退款
        {
            OpenSupplierOrderStatus.Cancelled, new OrderStatusNotifyMapEnum(ScenicTicketSupplierOrderStatus.Refunded,
                ScenicTicketSupplierOrderRecordType.Refund, (int)OrderBusinessErrorCodeType.Refunded,true)
        },
        // 发货失败
        {
            OpenSupplierOrderStatus.DeliverFailed, new OrderStatusNotifyMapEnum(ScenicTicketSupplierOrderStatus.DeliveryFail,
                ScenicTicketSupplierOrderRecordType.Delivery, (int)OrderBusinessErrorCodeType.DeliverFailed,false)
        },
        // 创建订单成功
        {
            OpenSupplierOrderStatus.Created, new OrderStatusNotifyMapEnum(ScenicTicketSupplierOrderStatus.WaitingForPay,
                ScenicTicketSupplierOrderRecordType.Create, null,true)
        },
        // 订单支付成功
        {
            OpenSupplierOrderStatus.PaySuccess, new OrderStatusNotifyMapEnum(ScenicTicketSupplierOrderStatus.WaitingForDeliver,
                ScenicTicketSupplierOrderRecordType.Payment, null,true)
        },
        // 订单支付失败
        {
            OpenSupplierOrderStatus.PayFailed, new OrderStatusNotifyMapEnum(ScenicTicketSupplierOrderStatus.WaitingForPay,
                ScenicTicketSupplierOrderRecordType.Payment, null,false)
        }
    };

    /// <summary>
    /// 订单详情状态枚举映射
    /// </summary>
    /// <param name="SupplierOrderStatus"></param>
    /// <param name="RecordType"></param>
    /// <param name="ErrorCode"></param>
    /// <param name="RecordTypeResult"></param>
    record OrderDetailStatusMapEnum(ScenicTicketSupplierOrderStatus SupplierOrderStatus,
        ScenicTicketSupplierOrderRecordType RecordType,
        int? ErrorCode,
        bool RecordTypeResult);
    private static readonly Dictionary<OpenSupplierOrderDetailStatus, OrderDetailStatusMapEnum> _orderDetailStatusMap =
        new()
        {
            //创单成功
            {
                OpenSupplierOrderDetailStatus.Ordered, new OrderDetailStatusMapEnum(
                    ScenicTicketSupplierOrderStatus.WaitingForPay, ScenicTicketSupplierOrderRecordType.Create, null,
                    true)
            },
            
            //支付成功
            {
                OpenSupplierOrderDetailStatus.Payed, new OrderDetailStatusMapEnum(
                    ScenicTicketSupplierOrderStatus.WaitingForDeliver, ScenicTicketSupplierOrderRecordType.Payment,
                    null, true)
            },
            
            //已发货,不直接更新为已发货,因为订单详情状态为已发货时,且需要发货的时候会触发订单发货流程
            {
                OpenSupplierOrderDetailStatus.Shipped, new OrderDetailStatusMapEnum(
                    ScenicTicketSupplierOrderStatus.WaitingForDeliver, ScenicTicketSupplierOrderRecordType.Payment, null, true)
            },

            //已取消
            {
                OpenSupplierOrderDetailStatus.Cancelled, new OrderDetailStatusMapEnum(
                    ScenicTicketSupplierOrderStatus.Refunded,
                    ScenicTicketSupplierOrderRecordType.Refund, (int)OrderBusinessErrorCodeType.Refunded, true)
            }
        };

    /// <summary>
    /// 将汉字转换为对应的拼音
    /// </summary>
    private string ConvertToPinyin(string chinese)
    {
        if (string.IsNullOrWhiteSpace(chinese))
            return string.Empty;

        try
        {
            return ChinesePinyinUtil.ConvertToPinyin(chinese);
        }
        catch (Exception e)
        {
            _logger.LogError(e,"[OpenSupplier]汉字转拼音:{@Input}", chinese);
            return chinese;
        }
    }

    /// <summary>
    /// 解析中文姓名并转换为拼音
    /// </summary>
    private (string FirstName, string LastName) ParseChineseName(string chineseName)
    {
        try
        {
            return ChinesePinyinUtil.ParseChineseName(chineseName);
        }
        catch (Exception e)
        {
            _logger.LogError(e,"[OpenSupplier]解析中文姓名:{@Input}", chineseName);
            return (string.Empty, string.Empty);
        }
    }

    /// <summary>
    /// 格式化手机号码 返回区号和手机号码
    /// <remarks>目前只做简单的区号和手机号码处理</remarks>
    /// </summary>
    /// <param name="phoneNumber">[+86]15918795701</param>
    /// <returns></returns>
    private (string AreaCode, string PhoneNumber) FormattingPhoneNumber(string? phoneNumber)
    {
        //默认+86
        var areaCode = "+86";
        if(string.IsNullOrEmpty(phoneNumber))
            return (areaCode,string.Empty);
        
        //如果存在区号则提取区号和手机号码
        //目前只做简单的区号处理
        if (phoneNumber.StartsWith("[") && phoneNumber.Contains(']'))
        {
            var endOfAreaCodeIndex = phoneNumber.IndexOf(']');
            areaCode = phoneNumber.Substring(1, endOfAreaCodeIndex - 1); // 提取区号
            phoneNumber = phoneNumber[(endOfAreaCodeIndex + 1)..]; // 提取手机号码
        }

        return (areaCode,phoneNumber);
    }
    
    /// <summary>
    /// 转换开发平台供应商类型
    /// </summary>
    private static string ConvertOpenSupplierType(OpenSupplierType openSupplierType)
    {
        return openSupplierType.ToString().ToLowerInvariant();
    }
    
    /// <summary>
    /// 查询订单的字段信息
    /// </summary>
    /// <param name="baseOrderIds"></param>
    /// <returns></returns>
    private async Task<List<GetSupplierOrderFieldInfoDto>> GetOrderFieldInfo(params long[] baseOrderIds)
    {
        var result = new List<GetSupplierOrderFieldInfoDto>();
        var orderFieldTypeList = await _dbContext.OrderFieldInformationType
            .IgnoreQueryFilters()
            .AsNoTracking()
            .Where(x => baseOrderIds.Contains(x.BaseOrderId))
            .ToListAsync();
        foreach (var baseOrderId in baseOrderIds)
        {
            var orderFieldTypes = orderFieldTypeList.Where(x => x.BaseOrderId == baseOrderId).ToList();
            var orderFieldTypesOut = _mapper.Map<List<OrderFieldInformationTypeOutput>>(orderFieldTypes);
            var orderFieldTypeIds = orderFieldTypes.Select(x => x.Id).ToList();
            var orderFields = await _dbContext.OrderFieldInformations
                .IgnoreQueryFilters()
                .AsNoTracking()
                .Where(x => orderFieldTypeIds.Contains(x.OrderFieldInformationTypeId))
                .ToListAsync();
            var orderFieldsOut = _mapper.Map<List<OrderFieldInformationOutput>>(orderFields);
            orderFieldTypesOut.ForEach(type =>
            {
                var fields = orderFieldsOut.Where(x => x.OrderFieldInformationTypeId == type.Id).OrderBy(x => x.Sort).ToList();
                type.Fields = fields;
            });

            var travelFieldInfo = orderFieldTypesOut.Where(x => x.TemplateType == TemplateType.Travel)
                .ToList();
            var contactsFieldInfo = orderFieldTypesOut.Where(x => x.TemplateType == TemplateType.Contacts)
                .ToList();
            var orderConfirmFieldInfo = orderFieldTypesOut.Where(x => x.TemplateType == TemplateType.Order)
                .ToList();

            
            result.Add(new GetSupplierOrderFieldInfoDto
            {
                BaseOrderId = baseOrderId,
                TravelFieldInfo = travelFieldInfo,
                ContactsFieldInfo = contactsFieldInfo,
                OrderConfirmFieldInfo = orderConfirmFieldInfo
            });
        }
        return result;
    }

    /// <summary>
    /// 联系人信息补充
    /// </summary>
    record PersonData(string SplicedEnName,string FirstName,string LastName);
    private PersonData PersonDataSupplement(List<OrderFieldInformationOutput> orderFieldInfos,TemplateType templateType)
    {
        //英文名
        var firstNameFieldValue = orderFieldInfos
            .FirstOrDefault(x => x.FieldCode == "FirstName")?.FieldValue;
        var firstName = firstNameFieldValue ?? string.Empty;
        
        //英文姓
        var lastNameFieldValue = orderFieldInfos
            .FirstOrDefault(x => x.FieldCode == "LastName")?.FieldValue;
        var lastName = lastNameFieldValue ?? string.Empty;

        var splicedEnName = string.Empty;
        if (!string.IsNullOrEmpty(firstName) && !string.IsNullOrEmpty(lastName))
        {
            //拼接(姓在前名在后): 英文姓 英文名
            splicedEnName = $"{lastName} {firstName}";
        }
        // 如果英文姓名不全，则从中文姓名转换
        else
        {
            var chinesNameFieldCode = templateType == TemplateType.Contacts ? "ContactName" : "Name";
            var chineseName = orderFieldInfos
                .FirstOrDefault(x => x.FieldCode == chinesNameFieldCode)?.FieldValue;

            if (!string.IsNullOrEmpty(chineseName))
            {
                var nameInfo = ParseChineseName(chineseName);
                if (string.IsNullOrEmpty(firstName))
                    firstName = nameInfo.FirstName;
                if (string.IsNullOrEmpty(lastName))
                    lastName = nameInfo.LastName;
                splicedEnName = $"{lastName} {firstName}".Trim();
            }
        }

        return new PersonData(splicedEnName,firstName,lastName);
    }

    /// <summary>
    /// 订单出行人信息数据补全
    /// </summary>
    /// <returns></returns>
    private async Task<List<SupplierOrderCreateOrderTravelerInfo>> TravelerInfoDataSupplement(
        List<OrderFieldInformationTypeOutput> travelFieldInfo)
    {
        var travelerInfos = new List<SupplierOrderCreateOrderTravelerInfo>();
        var productTemplateTypes = new List<ProductTemplateType?>() {
          ProductTemplateType.EachPerson,
           ProductTemplateType.JustOnePerson,
        };
        //获取出行人字段模板信息
        var travelOrderFieldInfos = travelFieldInfo
            .Where(x => productTemplateTypes.Contains(x.ProductTemplateType))
            .ToList();

        var countryCodes = new List<int>(1); // 国家编码

        foreach (var fieldInfoItem in travelOrderFieldInfos)
        {
            if(fieldInfoItem.Fields.Any() is false)
                continue;
            
            var currentFieldIs = fieldInfoItem.Fields;
            var personSupplementData = PersonDataSupplement(currentFieldIs, TemplateType.Travel); //处理出行人人名称数据
            var formattingContactName = personSupplementData.SplicedEnName;
            
            // 处理出行人邮箱
            var formattingContactEmail = currentFieldIs
                .FirstOrDefault(x => x.FieldCode == "ContactEmail")?.FieldValue;

            // 处理出行人电话
            var formattingPhoneInfoJson = currentFieldIs
                .FirstOrDefault(x => x.FieldCode == "ContactPhone")?.FieldValue;
            var formattingPhoneInfo = new PhoneDto();
            if (!string.IsNullOrEmpty(formattingPhoneInfoJson))
            {
                formattingPhoneInfo = JsonConvert.DeserializeObject<PhoneDto>(formattingPhoneInfoJson);
                //目前只做简单的区号处理
                if (!string.IsNullOrEmpty(formattingPhoneInfo.Type) &&  formattingPhoneInfo.Type.StartsWith("[") && formattingPhoneInfo.Type.Contains(']'))
                {
                    var endOfAreaCodeIndex = formattingPhoneInfo.Type.IndexOf(']');
                    formattingPhoneInfo.Type = formattingPhoneInfo.Type.Substring(1, endOfAreaCodeIndex - 1); // 提取区号
                }
                else
                {
                    formattingPhoneInfo.Type = "+86";
                }
            }

            // 处理出行人证件信息
            var formattingCardInfoJson = currentFieldIs
                .FirstOrDefault(x => x.FieldCode == "Card")?.FieldValue;
            var formattingCardInfo = new CardDto();
            if (!string.IsNullOrEmpty(formattingCardInfoJson))
            {
                formattingCardInfo = JsonConvert.DeserializeObject<CardDto>(formattingCardInfoJson);
            }
            
            // 处理出行人年龄信息
            var formattingBrithDateStringValue = currentFieldIs
                .FirstOrDefault(x => x.FieldCode == "BirthDate")?.FieldValue;
            int? formattingAgeValue = null;
            string formattingBirthDateValue = string.Empty;
            if (DateTime.TryParse(formattingBrithDateStringValue, out var brithDate))
            {
                formattingAgeValue = _openPlatformBaseService.CalculateAge(brithDate);
                
                //格式 yyyy-MM-dd
                formattingBirthDateValue = brithDate.ToString("yyyy-MM-dd");
            }
            
            //处理出行人体重信息
            var formattingWeightStringValue = currentFieldIs
                .FirstOrDefault(x => x.FieldCode == "Weight")?.FieldValue;
            int? formattingWeightValue = null;
            if (int.TryParse(formattingWeightStringValue, out var weight))
            {
                formattingWeightValue = weight;
            }
            
            //处理出行人性别信息
            var formattingGenderStringValue = currentFieldIs
                .FirstOrDefault(x => x.FieldCode == "Gender")?.FieldValue;
            int formattingGenderValue = 0;
            if (int.TryParse(formattingGenderStringValue, out var gender))
            {
                formattingGenderValue = gender;
            }
            
            // 国籍 换 iso代码
            var formattingCountryJson = currentFieldIs
                .FirstOrDefault(x => x.FieldCode == "Nationality")?.FieldValue;
            int? countryCode = null;
            if (!string.IsNullOrEmpty(formattingCountryJson))
            {
                var countryDto = JsonConvert.DeserializeObject<AddressDto>(formattingCountryJson);
                countryCode = countryDto?.Detail?.CountryCode;
                if(countryCode is not null)
                {
                    countryCodes.Add(countryCode!.Value);
                }
            }
            

            travelerInfos.Add(new SupplierOrderCreateOrderTravelerInfo
            {
                EnName = formattingContactName,
                IdCard = formattingCardInfo.Value,
                Email = formattingContactEmail,
                MobilePrefix = formattingPhoneInfo.Type,
                Mobile = formattingPhoneInfo.Value,
                Age = formattingAgeValue,
                Weight = formattingWeightValue,
                Gender = formattingGenderValue,
                BirthDate = formattingBirthDateValue,
                FirstName = personSupplementData.FirstName,
                LastName = personSupplementData.LastName,
                CountryCode = countryCode
            });
        }
        
        // 换取 国家 iso 代码
        if(countryCodes.Any())
        {
            var countryData = await GetCountryIsoCode(countryCodes);
            foreach (var travelerInfo in travelerInfos)
            {
                if(travelerInfo.CountryCode is not null)
                {
                    travelerInfo.Country = countryData.FirstOrDefault(x => x.CountryCode == travelerInfo.CountryCode.Value)?.IsoCode;
                    if(string.IsNullOrWhiteSpace(travelerInfo.Country))
                    {
                        // 不存在iso代码 都是默认传值: null
                        travelerInfo.Country = null;
                    }
                }
            }
        }

        return travelerInfos;
    }
    
    /// <summary>
    /// 联系人信息数据补全
    /// </summary>
    /// <param name="contactsFieldInfo"></param>
    /// <returns></returns>
    private async Task<SupplierOrderCreateOrderContactInfo> ContactInfoDataSupplement(
        List<OrderFieldInformationTypeOutput> contactFieldInfo)
    {
        //获取联系人字段模板信息
        var contactsOrderFieldInfos = contactFieldInfo
            .SelectMany(x => x.Fields)
            .ToList();
        var personSupplementData = PersonDataSupplement(contactsOrderFieldInfos, TemplateType.Contacts);//处理联系人名称数据
        var formattingContactName = personSupplementData.SplicedEnName;
        var formattingContactEmail = contactsOrderFieldInfos
            .FirstOrDefault(x => x.FieldCode == "ContactEmail")?.FieldValue;
        var formattingPhoneInfoJson = contactsOrderFieldInfos
            .FirstOrDefault(x => x.FieldCode == "ContactPhone")?.FieldValue;
        var formattingPhoneInfo = new PhoneDto();
        if (!string.IsNullOrEmpty(formattingPhoneInfoJson))
        {
            formattingPhoneInfo = JsonConvert.DeserializeObject<PhoneDto>(formattingPhoneInfoJson);
            //目前只做简单的区号处理
            if (!string.IsNullOrEmpty(formattingPhoneInfo.Type) && formattingPhoneInfo.Type.StartsWith("[") && formattingPhoneInfo.Type.Contains(']'))
            {
                var endOfAreaCodeIndex = formattingPhoneInfo.Type.IndexOf(']');
                formattingPhoneInfo.Type = formattingPhoneInfo.Type.Substring(1, endOfAreaCodeIndex - 1); // 提取区号
            }
            else
            {
                formattingPhoneInfo.Type = "+86";
            }
        }
        
        // 国籍 换 iso代码
        var formattingCountryJson = contactsOrderFieldInfos
            .FirstOrDefault(x => x.FieldCode == "Nationality")?.FieldValue;
        string? countryIsoCode = null;
        if (!string.IsNullOrEmpty(formattingCountryJson))
        {
            var countryDto = JsonConvert.DeserializeObject<AddressDto>(formattingCountryJson);
            var countryCode = countryDto?.Detail?.CountryCode;
            if(countryCode is not null)
            {
                // 换取 iso代码
                var countryData = await GetCountryIsoCode(new List<int>{countryCode!.Value});
                countryIsoCode = countryData.FirstOrDefault(x => x.CountryCode == countryCode.Value)?.IsoCode;
                if(string.IsNullOrWhiteSpace(countryIsoCode))
                {
                    // 不存在iso代码 都是默认传值: null
                    countryIsoCode = null;
                }
            }
        }
        

        var contactInfo = new SupplierOrderCreateOrderContactInfo
        {
            Email = formattingContactEmail,
            MobilePrefix = formattingPhoneInfo.Type,
            Mobile = formattingPhoneInfo.Value,
            EnName = formattingContactName,
            IdCard = "string", //目前硬编码默认值
            IdCardType = "string", //目前硬编码默认值,
            FirstName = personSupplementData.FirstName,
            LastName = personSupplementData.LastName,
            Country = countryIsoCode,
        };
        
        return contactInfo;
    }

    record CheckIsCombinationMergeOrderData(
        long SaasBaseOrderId,
        List<long> BaseOrderIds);
    /// <summary>
    /// 查询是否组合合单
    /// </summary>
    /// <param name="baseOrderId">1: 正常订单id  2: 组合合单 mergeOrderId</param>
    /// <param name="supplierOrderId"></param>
    /// <returns></returns>
    private async Task<CheckIsCombinationMergeOrderData> CheckIsCombinationMergeOrder(long baseOrderId)
    {
        var saasBaseOrderId = baseOrderId;
        var relatedBaseOrderIds = new List<long>() { baseOrderId };
        var combinationMergeItem = await _dbContext.TicketsCombinationMergeOrderItems.AsNoTracking()
            .Where(x => x.BaseOrderId == baseOrderId)
            .FirstOrDefaultAsync();

        if (combinationMergeItem != null)
        {
            var mergeOrderId = combinationMergeItem.MergeOrderId;
            var relatedMergeItemIds = await _dbContext.TicketsCombinationMergeOrderItems.AsNoTracking()
                .Where(x => x.MergeOrderId == mergeOrderId)
                .Select(x => x.BaseOrderId).ToListAsync();
            
            saasBaseOrderId = mergeOrderId;
            relatedBaseOrderIds = relatedMergeItemIds;
        }

        return new CheckIsCombinationMergeOrderData(saasBaseOrderId,relatedBaseOrderIds);
    }

    /// <summary>
    /// 价格更新更新逻辑
    /// </summary>
    /// <param name="orderDetail"></param>
    /// <param name="baseOrder"></param>
    /// <param name="supplierOrder"></param>
    /// <param name="orderPrice"></param>
    private async Task UpdateCostAndCommission(
        SupplierOrderDetailResponse orderDetail,
        BaseOrder baseOrder,
        ScenicTicketSupplierOrder supplierOrder,
        OrderPrice orderPrice)
    {
        //价格计算补充
        var orderItem = orderDetail.Items.FirstOrDefault(x => x.OutSkuId == supplierOrder.SkuId);
        if (orderItem != null)
        {
            orderPrice.CostPrice = orderItem.Price;
            supplierOrder.SkuPrice = orderItem.Price;
            supplierOrder.CommissionRate = orderItem.CommissionRate;//采购折扣比例
            orderPrice.CostDiscountRate = orderItem.CommissionRate;//采购折扣比例
            baseOrder.CostDiscountAmount = orderItem.CommissionAmount;//采购折扣总额
        }
        supplierOrder.Amount = orderPrice.CostPrice * orderPrice.Quantity;
    }
    
    /// <summary>
    /// 发货后更新价格
    /// </summary>
    /// <param name="saasBaseOrderId"></param>
    /// <param name="supplierOrderId"></param>
    /// <param name="baseOrders"></param>
    /// <param name="supplierOrders"></param>
    private async Task UpdatePriceAfterDelivery(
        long saasBaseOrderId,
        string supplierOrderId,
        List<BaseOrder> baseOrders,
        List<ScenicTicketSupplierOrder> supplierOrders)
    {
        try
        {
            var tenantId = baseOrders.First().TenantId;
            var openSupplierType = supplierOrders.First().SupplierType.ToString().ToLowerInvariant();
            var baseOrderIds = baseOrders.Select(x => x.Id).ToList();
            var orderPrices = await _dbContext.OrderPrices.IgnoreQueryFilters()
                .Where(x => baseOrderIds.Contains(x.BaseOrderId))
                .ToListAsync();
            
            //查询供应商订单详情
            var orderDetailResponse = await _openSupplierService.GetOrderDetail(
                new SupplierOrderDetailRequest
                {
                    OutOrderId = supplierOrderId,
                    SupplierType = openSupplierType,
                    OrderId = saasBaseOrderId.ToString()
                }, tenantId);
            var isSuccess = orderDetailResponse.Code == _successCode;
            if (isSuccess)
            {
                foreach (var baseOrder in baseOrders)
                {
                    var orderDetail = orderDetailResponse.Data;
                    var supplierOrder = supplierOrders.First(x=>x.BaseOrderId == baseOrder.Id);
                    var orderPrice = orderPrices.First(x => x.BaseOrderId == baseOrder.Id);
                    await UpdateCostAndCommission(
                        orderDetail: orderDetail,
                        baseOrder: baseOrder,
                        supplierOrder: supplierOrder,
                        orderPrice: orderPrice);
                }
            }
        }
        catch (Exception e)
        {
            // ignore
        }
    }
    
    /// <summary>
    /// 获取国家iso代码
    /// </summary>
    /// <param name="CountryCode"></param>
    /// <param name="IsoCode"></param>
    record CountryData(int CountryCode, string IsoCode);
    private async Task<List<CountryData>> GetCountryIsoCode(List<int> countryCodes)
    {
        var result = new List<CountryData>();
        countryCodes = countryCodes.Distinct().ToList();
        try
        {
            var request = new CountrySearchInput
            {
                PageIndex = 1,
                PageSize = countryCodes.Count,
                CountryCodes = countryCodes.Select(x => (long)x).ToList()
            };
            var httpContent = new StringContent(JsonConvert.SerializeObject(request),
                Encoding.UTF8, "application/json");
            var countryPage = await _httpClientFactory.InternalPostAsync<PagingModel<CountrySearchOutput>>(
                requestUri: _servicesAddress.Resource_Country_Search(),
                httpContent: httpContent);
            if (countryPage?.Data?.Any() == true)
            {
                result.AddRange(countryPage.Data.Select(item => 
                    new CountryData(item.CountryCode, item.IsoCode)));
            }
        }
        catch (Exception e)
        {
            // 忽略
        }
        return result;
    }
    
    #endregion

}