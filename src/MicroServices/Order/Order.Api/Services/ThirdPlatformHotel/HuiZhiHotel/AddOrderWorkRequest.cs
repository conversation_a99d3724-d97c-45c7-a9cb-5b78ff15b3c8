using Newtonsoft.Json;

namespace Order.Api.Services.ThirdPlatformHotel.HuiZhiHotel;

public class AddOrderWorkRequest
{
    /// <summary>
    /// 汇智国际旅游订单号 HOP订单号
    /// </summary>
    [JsonProperty("orderid")]
    public string OrderId { get; set; }

    /// <summary>
    /// 工单回复邮箱
    /// </summary>
    [JsonProperty("contact_mail")]
    public string ContactMail { get; set; }

    /// <summary>
    /// 工单类型
    /// </summary>
    [JsonProperty("reason")]
    public string Reason { get; set; }

    /// <summary>
    /// 工单备注
    /// </summary>
    [JsonProperty("remarks")]
    public string Remark { get; set; }
}
