using Common.GlobalException;
using Contracts.Common.Order.DTOs;
using Contracts.Common.Order.Enums;
using Contracts.Common.User.Messages;
using Microsoft.EntityFrameworkCore;
using Order.Api.Services.Interfaces;

namespace Order.Api.Services.OrderProcessing;

public class StoredValueCardOrderProcessingService : IOrderProcessingService
{
    private readonly CustomDbContext _dbContext;
    private readonly IOrderShareInfoService _orderShareInfoService;

    public StoredValueCardOrderProcessingService(CustomDbContext dbContext,
        IOrderShareInfoService orderShareInfoService)
    {
        _dbContext = dbContext;
        _orderShareInfoService = orderShareInfoService;
    }

    public OrderType OrderType => OrderType.StoredValueCard;

    public RefundOrderType RefundOrderType => RefundOrderType.StoredValueCardOrder;

    public async Task PaySuccessProcessing(BaseOrder baseOrder)
    {
        var storedValueCardOrder = await _dbContext.StoredValueCardOrders
                .IgnoreQueryFilters()
                .Where(s => s.BaseOrderId == baseOrder.Id && s.TenantId == baseOrder.TenantId)
                .FirstOrDefaultAsync();
        storedValueCardOrder.Status = StoredValueCardOrderStatus.UnFinished;
        storedValueCardOrder.UpdateTime = DateTime.Now;
        //达人奖金预发放
        var bonusPreReleaseSubOrder = new SubOrder
        {
            ProductName = storedValueCardOrder.StoredValueCardInfo.CardName,
            ProductSkuName = "",
            Price = storedValueCardOrder.StoredValueCardGearInfo.Price,
            Quantity = 1,
            SkuId = storedValueCardOrder.StoredValueCardGearInfo.Id,
            SubOrderId = storedValueCardOrder.Id
        };
        await _orderShareInfoService.OrderDarenPreRelease(baseOrder.Id, new SubOrder[] { bonusPreReleaseSubOrder });
    }

    public async Task<RefundSuccessProcessOutput> RefundSuccessProcessing(RefundOrder refundOrder)
    {
        var storedValueCardOrder = await _dbContext.StoredValueCardOrders
                 .IgnoreQueryFilters()
                 .Where(s => s.BaseOrderId == refundOrder.BaseOrderId)
                 .FirstOrDefaultAsync();

        if (storedValueCardOrder.Status != StoredValueCardOrderStatus.Refunding)
            throw new BusinessException($"退款结果处理，订单状态不符，当前OrderStatus：{storedValueCardOrder.Status}");

        storedValueCardOrder.Status = StoredValueCardOrderStatus.Refunded;
        storedValueCardOrder.UpdateTime = DateTime.Now;

        var baseOrder = await _dbContext.BaseOrders
             .IgnoreQueryFilters()
             .FirstAsync(s => s.Id == refundOrder.BaseOrderId);
        baseOrder.Status = BaseOrderStatus.Closed;
        baseOrder.UpdateTime = DateTime.Now;

        return new RefundSuccessProcessOutput { BaseOrderId = baseOrder.Id, BaseOrderStatus = baseOrder.Status };
    }
}
