using Kingdee.CDP.WebApi.SDK;
using Newtonsoft.Json;

namespace Order.Api.Services.Kingdee;

public interface IKingdeeK3CloudApiClient
{
    /// <summary>
    /// 批量保存
    /// </summary>
    /// <param name="formId"></param>
    /// <param name="jsonData"></param>
    /// <returns></returns>
    KingdeeResponse BatchSave(KingdeeRequestFormId formId, string jsonData);

    /// <summary>
    /// 批量保存
    /// </summary>
    /// <typeparam name="T"></typeparam>
    /// <param name="formId"></param>
    /// <param name="data"></param>
    /// <returns></returns>
    KingdeeResponse BatchSave<T>(KingdeeRequestFormId formId, T data);
    /// <summary>
    /// 提交
    /// </summary>
    /// <typeparam name="T"></typeparam>
    /// <param name="formId"></param>
    /// <param name="data"></param>
    /// <returns></returns>
    KingdeeResponse Submit<T>(KingdeeRequestFormId formId, T data);
    /// <summary>
    /// 审核
    /// </summary>
    /// <typeparam name="T"></typeparam>
    /// <param name="formId"></param>
    /// <param name="data"></param>
    /// <returns></returns>
    KingdeeResponse Audit<T>(KingdeeRequestFormId formId, T data);

    KingdeeResponse UnAudit<T>(KingdeeRequestFormId formId, T data);

    KingdeeResponse Delete<T>(KingdeeRequestFormId formId, T data);

}

public class KingdeeK3CloudApiClient: IKingdeeK3CloudApiClient
{
    private readonly K3CloudApi _k3CloudApi;
    private readonly ILogger<KingdeeK3CloudApiClient> _logger;

    public KingdeeK3CloudApiClient(K3CloudApi k3CloudApi, ILogger<KingdeeK3CloudApiClient> logger)
    {
        _k3CloudApi = k3CloudApi;
        _logger = logger;
    }

    public KingdeeResponse BatchSave(KingdeeRequestFormId formId, string jsonData)
    {
        var resultJson = _k3CloudApi.BatchSave(formId.ToString(), jsonData);
        _logger.LogInformation("[Kingdee]BatchSave Data:{@data},Result:{@result}", jsonData, resultJson);
        var response = JsonConvert.DeserializeObject<KingdeeResponse>(resultJson);
        return response;
    }

    public KingdeeResponse BatchSave<T>(KingdeeRequestFormId formId, T data)
    {
        string jsonData = JsonConvert.SerializeObject(data);
        var resultJson = _k3CloudApi.BatchSave(formId.ToString(), jsonData);
        _logger.LogInformation("[Kingdee]BatchSave Data:{@data},Result:{@result}", jsonData, resultJson);
        var response = JsonConvert.DeserializeObject<KingdeeResponse>(resultJson);
        return response;
    }

    public KingdeeResponse Submit<T>(KingdeeRequestFormId formId, T data)
    {
        string jsonData = JsonConvert.SerializeObject(data);
        var resultJson = _k3CloudApi.Submit(formId.ToString(), jsonData);
        _logger.LogInformation("[Kingdee]Submit Data:{@data},Result:{@result}", jsonData, resultJson);
        var response = JsonConvert.DeserializeObject<KingdeeResponse>(resultJson);
        return response;
    }

    public KingdeeResponse Audit<T>(KingdeeRequestFormId formId, T data)
    {
        string jsonData = JsonConvert.SerializeObject(data);
        var resultJson = _k3CloudApi.Audit(formId.ToString(), jsonData);
        _logger.LogInformation("[Kingdee]Audit Data:{@data},Result:{@result}", jsonData, resultJson);
        var response = JsonConvert.DeserializeObject<KingdeeResponse>(resultJson);
        return response;
    }

    public KingdeeResponse UnAudit<T>(KingdeeRequestFormId formId, T data)
    {
        string jsonData = JsonConvert.SerializeObject(data);
        var resultJson = _k3CloudApi.UnAudit(formId.ToString(), jsonData);
        _logger.LogInformation("[Kingdee]UnAudit Data:{@data},Result:{@result}", jsonData, resultJson);
        var response = JsonConvert.DeserializeObject<KingdeeResponse>(resultJson);
        return response;
    }

    public KingdeeResponse Delete<T>(KingdeeRequestFormId formId, T data)
    {
        string jsonData = JsonConvert.SerializeObject(data);
        var resultJson = _k3CloudApi.Delete(formId.ToString(), jsonData);
        _logger.LogInformation("[Kingdee]Delete Data:{@data},Result:{@result}", jsonData, resultJson);
        var response = JsonConvert.DeserializeObject<KingdeeResponse>(resultJson);
        return response;
    }
}
