using Newtonsoft.Json;
using SKIT.FlurlHttpClient.Wechat.Api;
using SKIT.FlurlHttpClient.Wechat.Api.Models;
using System.Text.Json.Serialization;

namespace WeChat.Api.Services.RequestModel;

//
// 摘要:
//     表示 [POST] /cgi-bin/component/setprivacysetting 接口的请求。
public class CgibinComponentSetPrivacySettingExtRequest : WechatApiRequest, IInferable<CgibinComponentSetPrivacySettingRequest, CgibinComponentSetPrivacySettingResponse>
{
    public static class Types
    {
        public class OwnerSetting
        {
            //
            // 摘要:
            //     获取或设置信息收集方的邮箱地址。
            [JsonProperty("contact_email")]
            [JsonPropertyName("contact_email")]
            public string? ContactEmail { get; set; }

            //
            // 摘要:
            //     获取或设置信息收集方的电话号码。
            [JsonProperty("contact_phone")]
            [JsonPropertyName("contact_phone")]
            public string? ContactPhoneNumber { get; set; }

            //
            // 摘要:
            //     获取或设置信息收集方的 QQ 号。
            [JsonProperty("contact_qq")]
            [JsonPropertyName("contact_qq")]
            public string? ContactQQ { get; set; }

            //
            // 摘要:
            //     获取或设置信息收集方的微信号。
            [JsonProperty("contact_weixin")]
            [JsonPropertyName("contact_weixin")]
            public string? ContactWexin { get; set; }

            //
            // 摘要:
            //     获取或设置用户隐私保护指引文件的 MediaId。
            [JsonProperty("ext_file_media_id")]
            [JsonPropertyName("ext_file_media_id")]
            public string? ExtraFileMediaId { get; set; }

            //
            // 摘要:
            //     获取或设置通知方式。
            [JsonProperty("notice_method")]
            [JsonPropertyName("notice_method")]
            public string? NoticeMethod { get; set; }

            //
            // 摘要:
            //     获取或设置存储期限时间字符串。
            [JsonProperty("store_expire_timestamp")]
            [JsonPropertyName("store_expire_timestamp")]
            public string? StoreExpireTimeString { get; set; }

            /// <summary>
            /// 存储地区；境外主体小程序需要补充「用户隐私保护指引」中「存储地区」的相关信息，否则小程序审核会被驳回。
            /// </summary>
            [JsonProperty("store_region")]
            [JsonPropertyName("store_region")]
            public int? StoreRegion { get; set; }
        }

        public class PrivacySetting
        {
            //
            // 摘要:
            //     获取或设置用户信息类型的英文名称。
            [JsonProperty("privacy_key")]
            [JsonPropertyName("privacy_key")]
            public string PrivacyKey { get; set; } = string.Empty;


            //
            // 摘要:
            //     获取或设置收集该信息的用途。
            [JsonProperty("privacy_text")]
            [JsonPropertyName("privacy_text")]
            public string PrivacyText { get; set; } = string.Empty;

        }
    }

    //
    // 摘要:
    //     获取或设置用户隐私保护指引的版本。
    [JsonProperty("privacy_ver")]
    [JsonPropertyName("privacy_ver")]
    public int? PrivacyVersion { get; set; }

    //
    // 摘要:
    //     获取或设置收集方信息配置。
    [JsonProperty("owner_setting")]
    [JsonPropertyName("owner_setting")]
    public Types.OwnerSetting OwnerSetting { get; set; } = new Types.OwnerSetting();


    //
    // 摘要:
    //     获取或设置要收集的用户信息配置列表。
    [JsonProperty("setting_list")]
    [JsonPropertyName("setting_list")]
    public IList<Types.PrivacySetting>? PrivacySettingList { get; set; }
}