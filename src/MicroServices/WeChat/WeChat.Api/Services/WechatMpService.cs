using Cit.Storage.Redis;
using Common.GlobalException;
using Contracts.Common.WeChat.DTOs;
using Contracts.Common.WeChat.Enums;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using SKIT.FlurlHttpClient.Wechat.Api;
using SKIT.FlurlHttpClient.Wechat.Api.Models;
using System.Web;
using WeChat.Api.ConfigModel;
using WeChat.Api.Services.Interfaces;

namespace WeChat.Api.Services
{
    public class WechatMpService : IWechatMpService
    {
        private const string WECHAT_MP_QRCODE = "wechatmp:qrcode:{0}:{1}";
        private readonly CustomDbContext _dbContext;
        private readonly IAccessTokenService _accessTokenService;
        private readonly IWechatManagedService _wechatManagedService;
        private readonly IRedisClient _redisClient;
        private readonly IOptions<WechatOptions> _options;
        private readonly ILogger<WechatMpService> _logger;

        public WechatMpService(CustomDbContext dbContext,
            IAccessTokenService accessTokenService,
            IWechatManagedService wechatManagedService,
            IRedisClient redisClient,
            IOptions<WechatOptions> options,
            ILogger<WechatMpService> logger)
        {
            _dbContext = dbContext;
            _accessTokenService = accessTokenService;
            _wechatManagedService = wechatManagedService;
            _redisClient = redisClient;
            _options = options;
            _logger = logger;
        }

        public async Task<GetWechatMPInfoOutput> GetWechatMPInfo(long tenantId)
        {
            var query = from config in _dbContext.WechatConfigurations
                        from managed in _dbContext.WechatAuthorizers
                        .Where(x => x.WechatConfigurationId == config.Id).DefaultIfEmpty()
                        where config.TenantId == tenantId
                        select new GetWechatMPInfoOutput
                        {
                            AppId = config.AppId,
                            IsManaged = config.IsAuthorized,
                            AuthorizeInfo = managed != null ? new WechatMpAuthorizeInfo
                            {
                                HeadImg = managed.HeadImg,
                                NickName = managed.NickName,
                                PrincipalName = managed.PrincipalName,
                                QrcodeUrl = managed.QrcodeUrl,
                            } : null
                        };
            var result = await query.IgnoreQueryFilters().FirstOrDefaultAsync();
            return result;
        }

        private async Task<WechatConfiguration> GetWechatMpConfiguration()
        {
            var config = await _dbContext.WechatConfigurations
                .AsNoTracking()
                .Where(c => c.AuthType == AuthType.WechatMp)
                .FirstOrDefaultAsync();
            return config;
        }

        #region 网页授权

        private const string WECHAT_MP_OAUTH2_ACCESSTOKEN = "wechat:mp:oauth2:accesstoken:{0}";
        public async Task<WechatOauth2AccessToken> GetAccessToken(string code)
        {
            var config = await GetWechatMpConfiguration();

            var request = new SnsOAuth2AccessTokenRequest()
            {
                Code = code
            };
            var client = new WechatApiClient(config.AppId, config.Secret);
            var response = await client.ExecuteSnsOAuth2AccessTokenAsync(request);
            if (!response.IsSuccessful())
            {
                switch (response.ErrorCode)
                {
                    case 40029:
                        throw new BusinessException(ErrorTypes.WeChat.CodeInvalid.ToString(), $"{response.ErrorMessage}({response.ErrorCode})");
                        break;
                    case 40163:
                        throw new BusinessException(ErrorTypes.WeChat.CodeBeenUsed.ToString(), $"{response.ErrorMessage}({response.ErrorCode})");
                        break;
                    default:
                        throw new BusinessException($"{response.ErrorMessage}({response.ErrorCode})");
                        break;
                }
            }
            WechatOauth2AccessToken accessToken = new()
            {
                AccessToken = response.AccessToken,
                ExpiresIn = response.ExpiresIn,
                OpenId = response.OpenId,
                RefreshToken = response.RefreshToken,
                Scope = response.Scope,
                CreateTime = DateTime.Now
            };
            var key = string.Format(WECHAT_MP_OAUTH2_ACCESSTOKEN, accessToken.OpenId);
            _ = await _redisClient.StringSetAsync(key, accessToken, TimeSpan.FromDays(30));
            return accessToken;
        }

        public async Task<GetUserInfoOutput> GetUserInfo(string openId)
        {
            var accessToken = await GetAccessTokenByOpenId(openId);
            var config = await GetWechatMpConfiguration();
            var client = new WechatApiClient(config.AppId, config.Secret);
            var request = new SnsUserInfoRequest
            {
                AccessToken = accessToken,
                OpenId = openId
            };
            var response = await client.ExecuteSnsUserInfoAsync(request);
            if (!response.IsSuccessful())
                throw new BusinessException($"微信拉取用户信息错误：{response.ErrorMessage}({response.ErrorCode})");
            return new GetUserInfoOutput()
            {
                City = response.City,
                Country = response.Country,
                HeadImageUrl = response.HeadImageUrl,
                Nickname = response.Nickname,
                OpenId = response.OpenId,
                PrivilegeList = response.PrivilegeList,
                Province = response.Province,
                Sex = response.Sex,
                UnionId = response.UnionId
            };
        }

        private async Task<string> GetAccessTokenByOpenId(string openId)
        {
            var key = string.Format(WECHAT_MP_OAUTH2_ACCESSTOKEN, openId);
            var result = await _redisClient.StringGetAsync<WechatOauth2AccessToken>(key);
            if (result is null)
                return string.Empty;
            //提前10s判断是否过期
            if (result.CreateTime.AddSeconds(result.ExpiresIn).AddSeconds(-10) > DateTime.Now)
            {
                return result.AccessToken;
            }
            //刷新
            var config = await GetWechatMpConfiguration();
            var client = new WechatApiClient(config.AppId, config.Secret);
            var request = new SnsOAuth2RefreshTokenRequest()
            {
                RefreshToken = result.RefreshToken
            };
            var response = await client.ExecuteSnsOAuth2RefreshTokenAsync(request);
            if (response.IsSuccessful())
            {
                WechatOauth2AccessToken accessToken = new()
                {
                    AccessToken = response.AccessToken,
                    ExpiresIn = response.ExpiresIn,
                    OpenId = response.OpenId,
                    RefreshToken = response.RefreshToken,
                    Scope = response.Scope,
                    CreateTime = DateTime.Now
                };
                _ = await _redisClient.StringSetAsync(key, accessToken, TimeSpan.FromSeconds(accessToken.ExpiresIn));
                return accessToken.AccessToken;
            }
            return string.Empty;
        }

        #endregion

        public async Task<string> GetAuthorizeUri(GetAuthorizeUriInput input)
        {
            var config = await GetWechatMpConfiguration();
            var appId = config.AppId;
            var url = string.Empty;
            if (config.IsAuthorized is true)
            {
                var redirectUrl = HttpUtility.UrlEncode(input.RedirectUrl);
                var skipUri = HttpUtility.UrlEncode($"{_options.Value.AuthRedirectUrl}?skipUri={redirectUrl}");
                var componentAppid = _options.Value.Account.AppId;
                url = $"https://open.weixin.qq.com/connect/oauth2/authorize?" +
                   $"appid={appId}&redirect_uri={skipUri}" +
                   $"&response_type=code&scope={input.Scope}" +
                   $"&state={input.State}" +
                   $"&component_appid={componentAppid}#wechat_redirect";
            }
            else
            {
                url = $"https://open.weixin.qq.com/connect/oauth2/authorize?" +
                   $"appid={appId}&redirect_uri={HttpUtility.UrlEncode(input.RedirectUrl)}" +
                   $"&response_type=code" +
                   $"&scope={input.Scope}&state={input.State}#wechat_redirect";
            }
            return url;
        }

        public async Task<CreateMpQrCodeOutput> CreateMpQrCode(CreateMpQrCodeInput input, long tenantId)
        {
            var accesssTokenInfo = await _wechatManagedService.GetAccessTokenInfo(input.AuthType, tenantId);
            var accessToken = accesssTokenInfo.AccessToken;

            var qrCodeKey = string.Format(WECHAT_MP_QRCODE, accesssTokenInfo.AppId, input.ActionInfo.SceneStr);
            var createMpQrCodeOutput = await _redisClient.StringGetAsync<CreateMpQrCodeOutput>(qrCodeKey);

            if (createMpQrCodeOutput is not null)
                return createMpQrCodeOutput;

            var client = new WechatApiClient(accesssTokenInfo.AppId, accesssTokenInfo.Secret);
            var request = new CgibinQrcodeCreateRequest()
            {
                AccessToken = accessToken,
                ExpiresIn = input.ExpireSeconds,
                ActionType = input.ActionName,
                Action = new CgibinQrcodeCreateRequest.Types.Action()
                {
                    Scene = new CgibinQrcodeCreateRequest.Types.Action.Types.Scene()
                    {
                        SceneString = input.ActionInfo.SceneStr
                    }
                }
            };
            var response = await client.ExecuteCgibinQrcodeCreateAsync(request);
            if (!response.IsSuccessful())
                throw new BusinessException($"{response.ErrorMessage}({response.ErrorCode})");

            _logger.LogInformation("WechatMP生成带参数的二维码:{@input},Response:{@response}", input, response);

            createMpQrCodeOutput = new CreateMpQrCodeOutput()
            {
                Ticket = response.Ticket,
                Url = response.Url,
            };

            if (!string.IsNullOrWhiteSpace(response.Url))
            {
                var expiry = TimeSpan.FromSeconds(response.ExpiresIn ?? 2592000);
                await _redisClient.StringSetAsync(qrCodeKey, createMpQrCodeOutput, expiry);
            }
            return createMpQrCodeOutput;
        }
    }
}
