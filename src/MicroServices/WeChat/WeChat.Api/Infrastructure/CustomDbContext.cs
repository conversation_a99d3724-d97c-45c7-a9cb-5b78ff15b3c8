using EfCoreExtensions.Abstract;
using EfCoreExtensions.Extensions;
using Microsoft.EntityFrameworkCore;
using System.Reflection;

namespace WeChat.Api.Infrastructure;

public class CustomDbContext : DbContextBase
{
    public CustomDbContext(DbContextOptions dbContextOptions, ITenantIdentify tenantIdentify) : base(dbContextOptions, tenantIdentify)
    {
    }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        modelBuilder.ApplyConfigurationsFromAssembly(Assembly.GetExecutingAssembly());
        base.OnModelCreating(modelBuilder);
    }

    public DbSet<WechatAuthorizer> WechatAuthorizers { get; set; }
    public DbSet<WechatConfiguration> WechatConfigurations { get; set; }
    public DbSet<WechatMpFollowStatus> WechatMpFollowStatuses { get; set; }
    public DbSet<WechatAppletTemplate> WechatAppletTemplates { get; set; }
    public DbSet<WechatAppletVersionInfo> WechatAppletVersionInfos { get; set; }
    public DbSet<WechatAppletVersionRecord> WechatAppletVersionRecords { get; set; }
    public DbSet<WechatAppletAudit> WechatAppletAudits { get; set; }
    public DbSet<WechatAppletCommit> WechatAppletCommits { get; set; }
    public DbSet<WechatAppletRelease> WechatAppletReleases { get; set; }
    public DbSet<WechatAppletPrivacyAudit> WechatAppletPrivacyAudits { get; set; }
    public DbSet<XiaoHongShuConfiguration> XiaoHongShuConfigurations { get; set; }
}
