using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace WeChat.Api.Infrastructure.Migrations
{
    public partial class addWechatAppletUniqueIndex : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "IsSetPrivacy",
                table: "WechatAuthorizer");

            migrationBuilder.CreateIndex(
                name: "IX_WechatAuthorizer_WechatConfigurationId",
                table: "WechatAuthorizer",
                column: "WechatConfigurationId",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_WechatAppletVersionInfo_WechatConfigurationId",
                table: "WechatAppletVersionInfo",
                column: "WechatConfigurationId",
                unique: true);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropIndex(
                name: "IX_WechatAuthorizer_WechatConfigurationId",
                table: "WechatAuthorizer");

            migrationBuilder.DropIndex(
                name: "IX_WechatAppletVersionInfo_WechatConfigurationId",
                table: "WechatAppletVersionInfo");

            migrationBuilder.AddColumn<ulong>(
                name: "IsSetPrivacy",
                table: "WechatAuthorizer",
                type: "bit",
                nullable: false,
                defaultValue: 0ul,
                comment: "是否设置隐私");
        }
    }
}
