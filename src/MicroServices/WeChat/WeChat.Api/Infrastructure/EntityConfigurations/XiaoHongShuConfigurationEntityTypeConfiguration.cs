using EfCoreExtensions.EntityConfigurationsBase;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace WeChat.Api.Infrastructure.EntityConfigurations;

public class XiaoHongShuConfigurationEntityTypeConfiguration : TenantBaseConfiguration<XiaoHongShuConfiguration>, IEntityTypeConfiguration<XiaoHongShuConfiguration>
{
    public void Configure(EntityTypeBuilder<XiaoHongShuConfiguration> builder)
    {
        base.ConfigureBase(builder);
        builder.Property(x => x.AppId).HasColumnType("varchar(64)");
        builder.Property(x => x.AccessToken).HasColumnType("varchar(64)");
        builder.Property(x => x.CreateTime).HasColumnType("datetime");
        builder.HasIndex(x => x.TenantId).IsUnique();
    }
}
