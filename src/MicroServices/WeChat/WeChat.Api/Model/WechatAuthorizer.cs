using Contracts.Common.WeChat.Enums;
using EfCoreExtensions.EntityBase;

namespace WeChat.Api.Model
{

    /// <summary>
    /// 微信授权信息
    /// </summary>
    public class WechatAuthorizer : TenantBase
    {
        /// <summary>
        /// 授权方配置编号
        /// </summary>
        public long WechatConfigurationId { get; set; }

        /// <summary>
        /// 商户扫码 授权后回调 URI，得到授权码
        /// </summary>
        public string? AuthorizationCode { get; set; }

        /// <summary>
        /// 商户扫码 授权后回调 URI，得到授权码过期时间
        /// </summary>
        public int AuthorizationExpiresIn { get; set; }

        /// <summary>
        /// 得到授权码时间
        /// </summary>
        public DateTime AuthorizationCodeTime { get; set; }

        /// <summary>
        /// 授权方 appid(冗余字段)
        /// </summary>
        public string? AuthorizerAppId { get; set; }

        /// <summary>
        /// 接口调用令牌（在授权的公众号/小程序具备 API 权限时，才有此返回值）
        /// </summary>
        public string? AuthorizerAccessToken { get; set; }

        /// <summary>
        /// authorizer_access_token 的有效期（在授权的公众号/小程序具备API权限时，才有此返回值），单位：秒
        /// </summary>
        public int ExpiresIn { get; set; }

        /// <summary>
        /// 刷新令牌（在授权的公众号具备API权限时，才有此返回值）
        /// </summary>
        public string? AuthorizerRefreshToken { get; set; }

        public DateTime AuthorizerRefreshTokenTime { get; set; }

        /// <summary>
        /// 授权权限列表授权给开发者的(1,2,3)
        /// </summary>
        public string? FuncInfoStr { get; set; }

        /// <summary>
        /// 昵称
        /// </summary>
        public string? NickName { get; set; }

        /// <summary>
        /// 头像
        /// </summary>
        public string? HeadImg { get; set; }

        /// <summary>
        /// 主体名称
        /// </summary>
        public string? PrincipalName { get; set; }

        /// <summary>
        /// 二维码图片的 URL
        /// </summary>
        public string? QrcodeUrl { get; set; }

        /// <summary>
        /// 1 授权成功,2授权更新,3取消授权
        /// </summary>
        public AuthorizedStatus AuthorizedStatus { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreateTime { get; set; } = DateTime.Now;


        public DateTime? UpdateTime { get; set; } = DateTime.Now;

    }
}
