using Common.Swagger;
using Contracts.Common.WeChat.DTOs;
using Contracts.Common.WeChat.DTOs.AppletPrivacyAudit;
using Contracts.Common.WeChat.Messages;
using DotNetCore.CAP;
using EfCoreExtensions.Abstract;
using Microsoft.AspNetCore.Mvc;
using WeChat.Api.Services.Interfaces;

namespace WeChat.Api.Controllers;

[ApiController]
[Route("[controller]/[action]")]
public class WechatAppletPrivacyController : ControllerBase
{
    private readonly IWechatAppletPrivacyAuditService _appletPrivacyAuditService;

    public WechatAppletPrivacyController(
        IWechatAppletPrivacyAuditService appletPrivacyAuditService)
    {
        _appletPrivacyAuditService = appletPrivacyAuditService;
    }

    [HttpPost]
    [ProducesResponseType(typeof(PagingModel<SearchAppletOutput>), (int)HttpStatusCode.OK)]
    public async Task<IActionResult> Search(SearchAppletInput input)
    {
        var result = await _appletPrivacyAuditService.Search(input);
        return Ok(result);
    }

    [HttpPost]
    [ProducesResponseType(typeof(PagingModel<SearchPrivacyRecordOutput>), (int)HttpStatusCode.OK)]
    public async Task<IActionResult> SearchRecords(SearchPrivacyRecordInput input)
    {
        var result = await _appletPrivacyAuditService.SearchRecords(input);
        return Ok(result);
    }

    [HttpPost]
    [ProducesResponseType(typeof(ApplyPrivacySettingOutput), (int)HttpStatusCode.OK)]
    [SwaggerResponseExt(default, ErrorTypes.WeChat.WechatNotConfig)]
    public async Task<IActionResult> ApplyPrivacySetting(ApplyPrivacySettingInput input)
    {
        var result = await _appletPrivacyAuditService.ApplyPrivacySetting(input);
        return Ok(result);
    }

    [HttpPost]
    [ProducesResponseType(typeof(ApplyLocationSettingOutput), (int)HttpStatusCode.OK)]
    [SwaggerResponseExt(default, ErrorTypes.WeChat.WechatNotConfig)]
    public async Task<IActionResult> ApplyLocationSetting(ApplyLocationSettingInput input)
    {
        var result = await _appletPrivacyAuditService.ApplyLocationSetting(input);
        return Ok(result);
    }

    [HttpPost]
    [ProducesResponseType(typeof(WxErrorOutput), (int)HttpStatusCode.OK)]
    [SwaggerResponseExt(default, ErrorTypes.WeChat.WechatNotConfig)]
    public async Task<IActionResult> ApplyServerDomainSetting(ApplyServerDomainSettingInput input)
    {
        var result = await _appletPrivacyAuditService.ApplyServerDomainSetting(input);
        return Ok(result);
    }

    [HttpPost]
    [SwaggerResponseExt(default, ErrorTypes.WeChat.WechatNotConfig)]
    public async Task<IActionResult> BatchSetServerDomain()
    {
        await _appletPrivacyAuditService.BatchSetServerDomain();
        return Ok();
    }

    #region 

    [NonAction]
    [CapSubscribe(CapTopics.Wechat.WechatAppletPreApply)]
    public async Task PreApply(WeChatAppletPreApplyMessage receive)
    {
        await _appletPrivacyAuditService.PreApply(receive);
    }

    #endregion
}