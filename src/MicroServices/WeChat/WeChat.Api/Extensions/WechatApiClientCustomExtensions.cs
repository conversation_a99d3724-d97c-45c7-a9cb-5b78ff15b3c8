using Flurl.Http;
using SKIT.FlurlHttpClient.Wechat.Api;
using SKIT.FlurlHttpClient.Wechat.Api.Models;
using WeChat.Api.Services.RequestModel;

namespace WeChat.Api.Extensions
{
    public static class WechatApiClientCustomExtensions
    {
        public static async Task<PhoneNumberResponse> ExecuteWxaBusinessGetPhoneNumberAsync(this WechatApiClient client, PhoneNumberRequest request, CancellationToken cancellationToken = default)
        {
            if (client is null) throw new ArgumentNullException(nameof(client));
            if (request is null) throw new ArgumentNullException(nameof(request));

            IFlurlRequest flurlReq = client
                .CreateRequest(request, HttpMethod.Post, "wxa", "business", "getuserphonenumber")
                .SetQueryParam("access_token", request.AccessToken);
            return await client.SendRequestWithJsonAsync<PhoneNumberResponse>(flurlReq, request, cancellationToken);
        }

        /// <summary>
        /// 添加类目模板 https://developers.weixin.qq.com/doc/offiaccount/Message_Management/Template_Message_Interface.html#%E8%8E%B7%E5%BE%97%E6%A8%A1%E6%9D%BFID
        /// </summary>
        /// <param name="client"></param>
        /// <param name="request"></param>
        /// <param name="cancellationToken"></param>
        /// <returns></returns>
        /// <exception cref="ArgumentNullException"></exception>
        public static async Task<CgibinTemplateAddTemplateResponse> ExecuteWxaBusinessAddCategoryTemplateAsync(this WechatApiClient client, AddCategoryTemplateRequest request, CancellationToken cancellationToken = default)
        {
            if (client is null) throw new ArgumentNullException(nameof(client));
            if (request is null) throw new ArgumentNullException(nameof(request));

            IFlurlRequest flurlRequest = client.CreateRequest(request, HttpMethod.Post, "cgi-bin", "template", "api_add_template").SetQueryParam("access_token", request.AccessToken);
            return await client.SendRequestWithJsonAsync<CgibinTemplateAddTemplateResponse>(flurlRequest, request, cancellationToken);
        }

        /// <summary>
        /// 删除模板 https://developers.weixin.qq.com/doc/offiaccount/Message_Management/Template_Message_Interface.html#%E5%88%A0%E9%99%A4%E6%A8%A1%E6%9D%BF
        /// </summary>
        /// <param name="client"></param>
        /// <param name="request"></param>
        /// <param name="cancellationToken"></param>
        /// <returns></returns>
        /// <exception cref="ArgumentNullException"></exception>
        public static async Task<WechatApiResponse> ExecuteWxaBusinessDeleteTemplateAsync(this WechatApiClient client, DeleteTemplateRequest request, CancellationToken cancellationToken = default)
        {
            if (client is null) throw new ArgumentNullException(nameof(client));
            if (request is null) throw new ArgumentNullException(nameof(request));

            IFlurlRequest flurlRequest = client.CreateRequest(request, HttpMethod.Post, "cgi-bin", "template", "del_private_template").SetQueryParam("access_token", request.AccessToken);
            return await client.SendRequestWithJsonAsync<CgibinTemplateAddTemplateResponse>(flurlRequest, request, cancellationToken);
        }
    }
}
