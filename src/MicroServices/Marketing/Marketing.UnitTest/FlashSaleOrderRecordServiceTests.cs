using Contracts.Common.Marketing.Enums;
using Contracts.Common.Marketing.Messages;
using Marketing.Api.Infrastructure;
using Marketing.Api.Model;
using Marketing.Api.Services;
using Marketing.Api.Services.Interfaces;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using UnitTest.Base;
using Xunit;

namespace Marketing.UnitTest;
public class FlashSaleOrderRecordServiceTests:TestBase<CustomDbContext>
{
    public static IFlashSaleOrderRecordService GetService(CustomDbContext dbContext)
    {
        return new FlashSaleOrderRecordService(dbContext);
    }

    [Fact(DisplayName = "取消订单记录，恢复库存_成功")]
    public async Task Cancel_Success()
    {
        FlashSale flashSale = new()
        {
            BeginTime = DateTime.Now,
            EndTime = DateTime.Now.AddDays(1),
            Title = "XXX",
            Status = FlashSaleStatus.InProgress
        };
        var availableInventory = 9;
        var flashSaleItem = new FlashSaleItems
        {
            FlashSaleId = flashSale.Id,
            ProductType = FlashSaleItemsType.Ticket_Group,
            AvailableInventory = availableInventory
        };
        var orderId = 1;
        var quantity = 1;
        FlashSaleRecords flashSaleRecord = new()
        {
            OrderId = orderId,
            FlashSaleItemsId = flashSaleItem.Id,
            Quantity = quantity,
            UserId = 1,
        };
        long tenantId = 1;
        var dbContext = GetNewDbContext();
        await dbContext.AddAsync(flashSale);
        await dbContext.AddAsync(flashSaleItem);
        await dbContext.AddAsync(flashSaleRecord);
        await dbContext.SetTenantId(tenantId).SaveChangesAsync();
        var service = GetService(dbContext);
        FlashSaleOrderCancelMessage receive = new()
        {
            TenantId = tenantId,
            OrderId = flashSaleRecord.OrderId
        };
        await service.Cancel(receive);

        Assert.True(flashSaleItem.AvailableInventory == (availableInventory + quantity));
        flashSaleRecord = dbContext.FlashSaleRecords
            .IgnoreQueryFilters()
            .Where(x => x.OrderId == orderId)
            .FirstOrDefault();
        Assert.True(flashSaleRecord is null);
    }
}
