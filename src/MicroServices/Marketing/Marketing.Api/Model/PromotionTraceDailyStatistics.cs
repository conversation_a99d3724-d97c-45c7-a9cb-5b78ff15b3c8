using EfCoreExtensions.EntityBase;

namespace Marketing.Api.Model;

/// <summary>
/// 营销追踪每日统计
/// </summary>
public class PromotionTraceDailyStatistics : TenantBase
{
    /// <summary>
    /// 统计日期
    /// </summary>
    public DateTime StatisticalDate { get; set; }
    
    /// <summary>
    /// 追踪Id
    /// </summary>
    public long PromotionTraceId { get; set; }
    
    /// <summary>
    /// 推广投放位置Id
    /// </summary>
    public long? PromotionPositionId { get; set; }

    /// <summary>
    /// 浏览量(PV)
    /// </summary>
    public int PageViews { get; set; }

    /// <summary>
    /// 访客量(UV)
    /// </summary>
    public int UserSessions { get; set; }

    /// <summary>
    /// 创建订单数
    /// </summary>
    public int CreateOrderCount { get; set; }

    /// <summary>
    /// 取消订单数
    /// </summary>
    public int CancelOrderCount { get; set; }

    /// <summary>
    /// 支付订单数
    /// </summary>
    public int PaymentOrderCount { get; set; }

    /// <summary>
    /// 退款订单数
    /// </summary>
    public int RefundOrderCount { get; set; }

    /// <summary>
    /// 完结订单数
    /// </summary>
    public int CompleteOrderCount { get; set; }

    /// <summary>
    /// 成交金额
    /// </summary>
    public decimal? OrderAmount { get; set; }
    
    /// <summary>
    /// 日志记录时间
    /// </summary>
    public DateTime CreateTime { get; set; } = DateTime.Now;
}