using EfCoreExtensions.EntityBase;

namespace Marketing.Api.Model;

/// <summary>
/// 后台赠送项用户记录
/// </summary>
public class BackstageGiftUserRecord : TenantBase
{
    /// <summary>
    /// 赠送记录id
    /// </summary>
    public long BackstageGiftRecordId { get; set; }
    
    /// <summary>
    /// 被赠送用户的Id
    /// </summary>
    public long CustomerUserId { get; set; }

    /// <summary>
    /// 被赠送用户的昵称
    /// </summary>
    public string? NickName { get; set; }

    /// <summary>
    /// 被赠送用户的手机号码
    /// </summary>
    public string PhoneNumber { get; set; }

    /// <summary>
    /// 被赠送分销商的Id
    /// </summary>
    public long? AgencyId { get; set; }

}