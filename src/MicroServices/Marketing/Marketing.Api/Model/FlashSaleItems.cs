using Contracts.Common.Marketing.Enums;
using EfCoreExtensions.EntityBase;

namespace Marketing.Api.Model;

public class FlashSaleItems : TenantBase
{
    public long FlashSaleId { get; set; }

    /// <summary>
    /// 产品类型
    /// </summary>
    public FlashSaleItemsType ProductType { get; set; }

    /// <summary>
    /// 产品维度
    /// </summary>
    public long ProductId { get; set; }

    public string? ProductName { get; set; }

    /// <summary>
    /// Sku维度
    /// </summary>
    public long SkuId { get; set; }

    public string? SkuName { get; set; }

    /// <summary>
    ///  Sku子类维度 1-成人 2-儿童
    /// </summary>
    public int SkuSubClass { get; set; }

    /// <summary>
    /// 销售价格类型
    /// </summary>
    public FlashSaleItemsPriceType SellingPriceType { get; set; }

    public decimal SellingPriceValue { get; set; }

    /// <summary>
    /// 采购价类型
    /// </summary>
    public FlashSaleItemsPriceType CostPriceType { get; set; }

    public decimal? CostPriceValue { get; set; }

    public int TotalInventory { get; set; }

    public int AvailableInventory { get; set; }

    public DateTime CreateTime { get; set; } = DateTime.Now;
}