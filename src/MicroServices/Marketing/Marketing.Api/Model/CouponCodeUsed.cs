using Contracts.Common.Product.Enums;
using Contracts.Common.Tenant.Enums;
using EfCoreExtensions.EntityBase;

namespace Marketing.Api.Model;

/// <summary>
/// 券码核销
/// </summary>
public class CouponCodeUsed : TenantBase
{
    /// <summary>
    /// 核销用户Id
    /// </summary>
    public long UserId { get; set; }

    /// <summary>
    /// 券Id
    /// </summary>
    public long CouponId { get; set; }
    
    /// <summary>
    /// 用户券Id
    /// </summary>
    public long UserCouponId { get; set; }

    /// <summary>
    /// 核销券码
    /// </summary>
    public long Code { get; set; }
    
    /// <summary>
    /// 资源核销的供应商Id
    /// </summary>
    public long? SupplierId { get; set; }
    
    /// <summary>
    /// 核销资源类型
    /// </summary>
    public ProductResourceType ResourceType { get; set; }

    /// <summary>
    /// 核销资源Id
    /// </summary>
    public long ResourceId { get; set; }

    /// <summary>
    /// 核销资源名称
    /// </summary>
    public string? ResourceName { get; set; }

    /// <summary>
    /// 核销人类型
    /// </summary>
    public UserType UserType { get; set; }

    /// <summary>
    /// 核销人Id
    /// </summary>
    public long CreatorId { get; set; }

    /// <summary>
    /// 核销人
    /// </summary>
    public string? CreatorName { get; set; }

    /// <summary>
    /// 核销备注
    /// </summary>
    public string? Remark { get; set; }
    
    /// <summary>
    /// 核销时间
    /// </summary>
    public DateTime CreateTime { get; set; } = DateTime.Now;
}