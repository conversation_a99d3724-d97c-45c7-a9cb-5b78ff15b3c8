using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Marketing.Api.Infrastructure.Migrations
{
    public partial class AddPromotionRemarkCustomerId : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.RenameColumn(
                name: "PromotionCustomerProfileId",
                table: "PromotionCustomerRemark",
                newName: "CustomerId");

            migrationBuilder.RenameIndex(
                name: "IX_PromotionCustomerRemark_PromotionCustomerProfileId",
                table: "PromotionCustomerRemark",
                newName: "IX_PromotionCustomerRemark_CustomerId");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.RenameColumn(
                name: "CustomerId",
                table: "PromotionCustomerRemark",
                newName: "PromotionCustomerProfileId");

            migrationBuilder.RenameIndex(
                name: "IX_PromotionCustomerRemark_CustomerId",
                table: "PromotionCustomerRemark",
                newName: "IX_PromotionCustomerRemark_PromotionCustomerProfileId");
        }
    }
}
