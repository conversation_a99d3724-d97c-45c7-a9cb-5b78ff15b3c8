using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Marketing.Api.Infrastructure.Migrations
{
    public partial class CouponRangeAddScenic : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<long>(
                name: "ScenicSpotId",
                table: "CouponRangeValue",
                type: "bigint",
                nullable: false,
                defaultValue: 0L);

            migrationBuilder.AddColumn<string>(
                name: "TicketsIds",
                table: "CouponRangeValue",
                type: "varchar(500)",
                nullable: true)
                .Annotation("MySql:CharSet", "utf8mb4");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "ScenicSpotId",
                table: "CouponRangeValue");

            migrationBuilder.DropColumn(
                name: "TicketsIds",
                table: "CouponRangeValue");
        }
    }
}
