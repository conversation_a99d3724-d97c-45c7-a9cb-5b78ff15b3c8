using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using EfCoreExtensions.EntityConfigurationsBase;

namespace Marketing.Api.Infrastructure.EntityConfigurations
{
    public class UserStoredValueCardEntityTypeConfiguration : TenantBaseConfiguration<Model.UserStoredValueCard>, IEntityTypeConfiguration<Model.UserStoredValueCard>
    {
        public void Configure(EntityTypeBuilder<Model.UserStoredValueCard> builder)
        {
            base.ConfigureBase(builder);

            builder.Property(s => s.UserId)
                .HasColumnType("bigint");

            builder.Property(s => s.BaseOrderId)
                .HasColumnType("bigint");

            builder.Property(s => s.StoredValueCardOrderId)
                .HasColumnType("bigint");

            builder.OwnsOne(s => s.StoredValueCardInfo, i =>
            {
                i.Property(s => s.Id).HasColumnType("bigint");
                i.Property(s => s.CardType).HasColumnType("tinyint");
                i.Property(s => s.CardName).HasColumnType("varchar(200)");
                i.Property(s => s.Cover).HasColumnType("varchar(500)");
                i.Property(s => s.Instruction).HasColumnType("varchar(1000)");
                i.Property(s => s.ProductBusinessType).HasColumnType("int");
            });

            builder.Property(s => s.Balance)
                .HasColumnType("decimal(18,2)");

            builder.Property(s => s.TotalValue)
                .HasColumnType("decimal(18,2)");

            builder.Property(s => s.GiftValue)
                .HasColumnType("decimal(18,2)");

            builder.Property(s => s.Status)
                .HasColumnType("tinyint");

            builder.Property(s => s.ActivateTime)
                .HasColumnType("datetime");

            builder.Property(s => s.Timestamp)
                .HasColumnType("timestamp(6)")
                .HasDefaultValueSql("CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6)")
                .IsConcurrencyToken();

            builder.Property(s => s.NickName)
                .HasColumnType("varchar(100)");

            builder.Property(s => s.PhoneNumber)
                .HasColumnType("varchar(100)");
        }
    }
}
