using Contracts.Common.Marketing.Enums;
using EfCoreExtensions.EntityConfigurationsBase;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Marketing.Api.Infrastructure.EntityConfigurations
{
    public class CouponEntityTypeConfiguration : KeyBaseConfiguration<Model.Coupon>, IEntityTypeConfiguration<Model.Coupon>
    {
        public void Configure(EntityTypeBuilder<Model.Coupon> builder)
        {
            base.ConfigureBase(builder);

            builder.Property(s => s.CouponName)
                .HasColumnType("varchar(200)");

            builder.Property(s => s.CouponType)
                .HasColumnType("tinyint");

            builder.Property(s => s.Amount)
                .HasColumnType("decimal(8,2)");

            builder.Property(s => s.LimitMinAmt)
                .HasColumnType("decimal(10,2)");

            builder.Property(s => s.MaxDiscountAmt)
                .HasColumnType("decimal(8,2)");

            builder.Property(s => s.ValidDateType)
                .HasColumnType("tinyint");

            builder.Property(s => s.ValidStartDate)
                .HasColumnType("datetime");

            builder.Property(s => s.ValidEndDate)
                .HasColumnType("datetime");

            builder.Property(s => s.ValidDays)
                .HasColumnType("int");

            builder.Property(s => s.CouponQuantity)
                .HasColumnType("int");

            builder.Property(s => s.ReceivedQuantity)
               .HasColumnType("int");

            builder.Property(s => s.Instruction)
                .HasColumnType("varchar(500)");

            builder.Property(s => s.CreateTime)
                .HasColumnType("datetime");

            builder.Property(s => s.UpdateTime)
                .HasColumnType("datetime");

            builder.Property(s => s.Nights)
                .HasColumnType("int");

            builder.Property(s => s.NumberOfRoom)
                .HasColumnType("int");

            builder.Property(s => s.SupplierId)
                .HasColumnType("bigint");

            builder.Property(s => s.ContentDesc)
                .HasColumnType("varchar(500)");

            builder.Property(s => s.UseChannel)
                .HasDefaultValue(CouponUseChannel.WechatMall)
                .HasColumnType("tinyint");
        }
    }
}
