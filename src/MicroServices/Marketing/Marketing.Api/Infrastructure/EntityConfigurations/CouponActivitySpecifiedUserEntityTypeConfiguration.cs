using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using EfCoreExtensions.EntityConfigurationsBase;

namespace Marketing.Api.Infrastructure.EntityConfigurations
{
    public class CouponActivitySpecifiedUserEntityTypeConfiguration : TenantBaseConfiguration<Model.CouponActivitySpecifiedUser>, IEntityTypeConfiguration<Model.CouponActivitySpecifiedUser>
    {
        public void Configure(EntityTypeBuilder<Model.CouponActivitySpecifiedUser> builder)
        {
            base.ConfigureBase(builder);

            builder.Property(s => s.CouponActivityId)
                .HasColumnType("bigint");

            builder.Property(s => s.AgencyId)
                .HasColumnType("bigint");

            builder.Property(s => s.UserId)
                .HasColumnType("bigint");

            builder.Property(s => s.UpdateTime)
                .HasColumnType("datetime");

            builder.HasIndex(s => s.CouponActivityId);

        }
    }
}
