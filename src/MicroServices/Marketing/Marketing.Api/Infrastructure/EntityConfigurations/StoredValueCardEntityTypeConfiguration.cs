using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using EfCoreExtensions.EntityConfigurationsBase;

namespace Marketing.Api.Infrastructure.EntityConfigurations
{
    public class StoredValueCardEntityTypeConfiguration : TenantBaseConfiguration<Model.StoredValueCard>, IEntityTypeConfiguration<Model.StoredValueCard>
    {
        public void Configure(EntityTypeBuilder<Model.StoredValueCard> builder)
        {
            base.ConfigureBase(builder);

            builder.Property(s => s.CardType)
                .HasColumnType("tinyint");

            builder.Property(s => s.CardName)
                .HasColumnType("varchar(200)");

            builder.Property(s => s.Cover)
                .HasColumnType("varchar(500)");

            builder.Property(s => s.Instruction)
                .HasColumnType("varchar(1000)");

            builder.Property(s => s.ProductBusinessType)
                .HasColumnType("int");

            builder.Property(s => s.Status)
                .HasColumnType("tinyint");

            builder.Property(s => s.CreateTime)
                .HasColumnType("datetime");

            builder.Property(s => s.UpdateTime)
                .HasColumnType("datetime");
        }
    }
}
