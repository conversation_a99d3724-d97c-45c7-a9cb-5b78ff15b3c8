using EfCoreExtensions.EntityConfigurationsBase;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Marketing.Api.Infrastructure.EntityConfigurations;

public class BackstageGiftUserRecordEntityTypeConfiguration : TenantBaseConfiguration<BackstageGiftUserRecord>, IEntityTypeConfiguration<BackstageGiftUserRecord>
{
    public void Configure(EntityTypeBuilder<BackstageGiftUserRecord> builder)
    {
        base.ConfigureBase(builder);

        builder.Property(b => b.BackstageGiftRecordId)
            .HasColumnType("bigint");

        builder.Property(b => b.CustomerUserId)
            .HasColumnType("bigint");

        builder.Property(s => s.PhoneNumber)
            .HasColumnType("varchar(32)")
            .IsRequired();

        builder.Property(s => s.NickName)
            .HasColumnType("varchar(64)");

        builder.Property(s => s.AgencyId)
            .HasColumnType("bigint");

        builder.HasIndex(b => b.BackstageGiftRecordId);
    }
}