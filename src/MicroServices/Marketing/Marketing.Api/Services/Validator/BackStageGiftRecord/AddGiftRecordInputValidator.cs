using Contracts.Common.Marketing.DTOs.BackstageGiftRecord;
using FluentValidation;

namespace Marketing.Api.Services.Validator.BackStageGiftRecord;

public class AddGiftRecordInputValidator : AbstractValidator<AddGiftRecordInput>
{
    public AddGiftRecordInputValidator()
    {
        RuleFor(b => b.Describe).Length(0, 200);
        RuleFor(b => b.GiftType).IsInEnum();
        RuleFor(b=>b.GiftItemInfos).NotNull().NotEmpty();
    }
}