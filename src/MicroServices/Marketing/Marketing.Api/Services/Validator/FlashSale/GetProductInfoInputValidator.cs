using Contracts.Common.Marketing.DTOs.FlashSale;
using FluentValidation;

namespace Marketing.Api.Services.Validator.FlashSale;

public class GetProductInfoInputValidator : AbstractValidator<GetProductInfoInput>
{
    public GetProductInfoInputValidator()
    {
        RuleFor(x => x.ProductType).NotEmpty();
        RuleForEach(x => x.ProductType).ChildRules(c =>
        {
            c.RuleFor(t => t).IsInEnum();
        });
    }
}