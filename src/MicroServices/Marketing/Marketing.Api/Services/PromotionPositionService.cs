using AutoMapper;
using Contracts.Common.Marketing.DTOs.PromotionPosition;
using EfCoreExtensions.Abstract;
using EfCoreExtensions.Extensions;
using Marketing.Api.Services.Interfaces;
using Microsoft.EntityFrameworkCore;

namespace Marketing.Api.Services;

public class PromotionPositionService : IPromotionPositionService
{
    private readonly CustomDbContext _dbContext;
    private readonly IMapper _mapper;

    public PromotionPositionService(CustomDbContext dbContext, IMapper mapper)
    {
        _dbContext = dbContext;
        _mapper = mapper;
    }

    public async Task<PagingModel<SearchPromotionPositionOutput>> Search(SearchPromotionPositionInput input)
    {
        var query = _dbContext.PromotionPositions.AsNoTracking()
                .WhereIF(!string.IsNullOrWhiteSpace(input.Name), x => x.Name.Contains(input.Name!))
                .WhereIF(input.PositionType is not null, x => x.PositionType.Equals(input.PositionType))
                .WhereIF(!string.IsNullOrWhiteSpace(input.Tags), x => x.Tags.Contains(input.Tags!))
                .WhereIF(input.Enabled is not null, x => x.Enabled.Equals(input.Enabled))
                .OrderByDescending(x => x.Enabled);
        var promotionPositions = new PagingModel<PromotionPosition>();
        if (input.Desc)
        {
            promotionPositions = await query
                .ThenByDescending(x => x.CreateTime)
                .PagingAsync(input.PageIndex, input.PageSize, x => x);
        }
        else
        {
            promotionPositions = await query
                .ThenBy(x => x.CreateTime)
                .PagingAsync(input.PageIndex, input.PageSize, x => x);
        }

        if (!promotionPositions.Data.Any()) return new PagingModel<SearchPromotionPositionOutput>();

        return _mapper.Map<PagingModel<SearchPromotionPositionOutput>>(promotionPositions);
    }

    public async Task<long> Add(AddPromotionPositionDto dto)
    {
        var promotionPosition = new PromotionPosition();
        _mapper.Map(dto, promotionPosition);
        if (dto.Longitude is not null && dto.Latitude is not null)
            promotionPosition.SetLocation(dto.Longitude!.Value, dto.Latitude!.Value);
        await _dbContext.AddAsync(promotionPosition);
        var insertResult = await _dbContext.SaveChangesAsync() > 0;
        return insertResult ? promotionPosition.Id : 0;
    }

    public async Task SetEnabled(SetEnabledInput input)
    {
        var promotionPosition = await _dbContext.PromotionPositions.FindAsync(input.Id);
        if (promotionPosition is null) return;

        promotionPosition.Enabled = input.Enabled;
        await _dbContext.SaveChangesAsync();
    }

    public async Task Update(UpdatePromotionPositionInput input)
    {
        var promotionPosition = await _dbContext.PromotionPositions.FindAsync(input.Id);
        if (promotionPosition is null) return;

        _mapper.Map(input, promotionPosition);
        if (input.Longitude is not null && input.Latitude is not null)
            promotionPosition.SetLocation(input.Longitude!.Value, input.Latitude!.Value);
        promotionPosition.UpdateTime = DateTime.Now;
        await _dbContext.SaveChangesAsync();
    }

    public async Task<List<string>> GetSelectionTags()
    {
        var tags = await _dbContext.PromotionPositions.Select(x => x.Tags).Distinct().ToListAsync();
        var tagText = string.Join(",", tags);
        return tagText.Split(",").Distinct().ToList();
    }

    public async Task<List<GetPromotionPositionByIdsOutput>> GetByIds(IEnumerable<long> ids)
    {
        var promotionPositions = await _dbContext.PromotionPositions
            .AsNoTracking().Where(x => ids.Contains(x.Id)).ToListAsync();
        var result = _mapper.Map<List<GetPromotionPositionByIdsOutput>>(promotionPositions);
        return result;
    }
}
