using Contracts.Common.Hotel.DTOs.ApiHotel;
using Hotel.Api.Services.Interfaces;
using Microsoft.AspNetCore.Mvc;

namespace Hotel.Api.Controllers;

[Route("[controller]/[action]")]
[ApiController]
public class ApiHotelSettingController : ControllerBase
{
    private readonly IApiHotelSettingService _apiHotelSetting;

    public ApiHotelSettingController(
        IApiHotelSettingService apiHotelSetting
        )
    {
        _apiHotelSetting = apiHotelSetting;
    }

    /// <summary>
    /// 获取汇智酒店配置
    /// </summary>
    /// <returns></returns>
    [HttpGet]
    [ProducesResponseType(typeof(List<ApiHotelSettingDto>), (int)HttpStatusCode.OK)]
    public async Task<IActionResult> Get()
    {
        var result = await _apiHotelSetting.Get();
        return Ok(result);
    }

    /// <summary>
    /// 保存第三方酒店配置
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    public async Task<IActionResult> Save(SaveApiHotelSettingInput input)
    {
        await _apiHotelSetting.Save(input);
        return Ok();
    }
}
