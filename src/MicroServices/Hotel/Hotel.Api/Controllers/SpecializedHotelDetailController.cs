using Contracts.Common.Hotel.DTOs.SpecializedHotel;
using Contracts.Common.Hotel.DTOs.SpecializedHotelDetail;
using EfCoreExtensions.Abstract;
using Hotel.Api.Services.Interfaces;
using Microsoft.AspNetCore.Mvc;

namespace Hotel.Api.Controllers;

[Route("[controller]/[action]")]
[ApiController]
public class SpecializedHotelDetailController : ControllerBase
{
    private readonly ISpecializedHotelDetailService _specializedHotelDetailService;
    public SpecializedHotelDetailController(ISpecializedHotelDetailService specializedHotelDetailService)
    {
        _specializedHotelDetailService = specializedHotelDetailService;
    }

    [HttpGet]
    [ProducesResponseType(typeof(List<SpecializedHotelDetailOutput>), (int)HttpStatusCode.OK)]
    public async Task<IActionResult> GetBySpecializedHotelId(long specializedHotelId)
    {
        var result = await _specializedHotelDetailService.GetBySpecializedHotelId(specializedHotelId);
        return Ok(result);
    }

    [HttpPost]
    [ProducesResponseType(typeof(List<SpecializedHotelDetailOutput>), (int)HttpStatusCode.OK)]
    public async Task<IActionResult> GetBySpecializedHotelIds(GetBySpecializedHotelIdsInput input)
    {
        var result = await _specializedHotelDetailService.GetBySpecializedHotelId(input.Ids.ToArray());
        return Ok(result);
    }

    [HttpPost]
    [ProducesResponseType(typeof(List<SpecializedHotelItemOutput>), (int)HttpStatusCode.OK)]
    public async Task<IActionResult> GetSpecializedHotelItems(SpecializedHotelItemInput input)
    {
        var result = await _specializedHotelDetailService.GetSpecializedHotelItems(input);
        return Ok(result);
    }

}
