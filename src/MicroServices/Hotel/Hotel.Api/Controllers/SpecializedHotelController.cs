using Common.Swagger;
using Contracts.Common.Hotel.DTOs.SpecializedHotel;
using EfCoreExtensions.Abstract;
using Hotel.Api.Services.Interfaces;
using Microsoft.AspNetCore.Mvc;
using Common.Jwt;

namespace Hotel.Api.Controllers;

[Route("[controller]/[action]")]
[ApiController]
public class SpecializedHotelController : ControllerBase
{
    private readonly ISpecializedHotelService _specializedHotelService;
    private readonly IApiHotelService _apiHotelService;
    public SpecializedHotelController(ISpecializedHotelService specializedHotelService, IApiHotelService apiHotelService)
    {
        _specializedHotelService = specializedHotelService;
        _apiHotelService = apiHotelService;
    }

    [HttpPost]
    [ProducesResponseType((int)HttpStatusCode.OK)]
    [SwaggerResponseExt(default, ErrorTypes.Hotel.SpecializedHotelInUse)]
    public async Task<IActionResult> Update(UpdateSpecializedHotelInput input)
    {
        await _specializedHotelService.Update(input);
        return Ok();
    }

    [HttpPost]
    [ProducesResponseType(typeof(long), (int)HttpStatusCode.OK)]
    public async Task<IActionResult> Add(AddSpecializedHotelInput input)
    {
        var res = await _specializedHotelService.Add(input);
        return Ok(res);
    }

    [HttpPost]
    [ProducesResponseType(typeof(PagingModel<SearchSpecializedHotelOutput>), (int)HttpStatusCode.OK)]
    public async Task<IActionResult> Search(SearchSpecializedHotelInput input)
    {
        var result = await _specializedHotelService.Search(input);
        return Ok(result);
    }


    [HttpPost]
    [ProducesResponseType(typeof(long), (int)HttpStatusCode.OK)]
    [SwaggerResponseExt(default, ErrorTypes.Hotel.SpecializedHotelInUse)]
    public async Task<IActionResult> Delete(List<long> ids)
    {
        await _specializedHotelService.Delete(ids);
        return Ok();
    }


    [HttpPost]
    [ProducesResponseType(typeof(bool), (int)HttpStatusCode.OK)]
    public async Task<IActionResult> ImportHotel(ImportHotelInput input)
    {
        var imports = await _specializedHotelService.AnalysisImport(input);
        var hopIds = imports.Where(x => x.HotelId > 0)
            .Select(x => x.HotelId)
            .Distinct()
            .ToList();
        long? tenantId = HttpContext.GetTenantId();
        var hotels = (await _apiHotelService.SearchV2(new Contracts.Common.Hotel.DTOs.ApiHotel.SearchInput()
        {
            HopIds = hopIds,
            PageSize = hopIds.Count,
            PageIndex = 1
        }, tenantId)).Data;
        imports.ForEach(x =>
        {
            var hotel = hotels.FirstOrDefault(s => s.HopId == x.HotelId);
            if (x.Check && hotel == null)
            {
                x.Check = false;
                x.CheckName = "失败";
                x.Msg = "酒店不存在";
            }
            x.HotelName ??= hotel?.ZHName;
            x.EnHotelName ??= hotel?.ENName;
            x.ApiHotelId = hotel?.Id ?? 0;
        });
        var res = await _specializedHotelService.ImportHotel(input, imports);
        return Ok(res);
    }

    [HttpPost]
    [ProducesResponseType(typeof(PagingModel<SearchImportLogOutput>), (int)HttpStatusCode.OK)]
    public async Task<IActionResult> SearchImportLog(SearchImportLogInput input)
    {
        var res = await _specializedHotelService.SearchImportLog(input);
        return Ok(res);
    }

    [HttpGet]
    [ProducesResponseType(typeof(List<GetSpecializedHotelCitySummaryOutput>), (int)HttpStatusCode.OK)]
    public async Task<IActionResult> GetCitySummary(long id)
    {
        var res = await _specializedHotelService.GetCitySummary(id);
        return Ok(res);
    }

    [HttpGet]
    [ProducesResponseType(typeof(List<GetSpecializedHotelCitySummaryOutput>), (int)HttpStatusCode.OK)]
    public async Task<IActionResult> SearchCitySummary(long id)
    {
        var res = await _specializedHotelService.GetCitySummary(id);
        return Ok(res);
    }
}
