using Contracts.Common.Hotel.DTOs.HotelMapping;
using Hotel.Api.Services.Interfaces;
using MediatR;

namespace Hotel.Api.Notification.Handler;

public class StrategyChangeNotifyHotelMappingHandler : INotificationHandler<StrategyChangeNotification>
{
    private readonly IHotelMappingService _hotelMappingService;

    public StrategyChangeNotifyHotelMappingHandler(IHotelMappingService hotelMappingService)
    {
        _hotelMappingService = hotelMappingService;
    }
    public async Task Handle(StrategyChangeNotification notification, CancellationToken cancellationToken)
    {
        var hotelMappings = await _hotelMappingService.GetHotelMappings(new Contracts.Common.Hotel.DTOs.HotelMapping.HotelMappingInput
        {
            HotelIds = new[] { notification.HotelId },
            SellingPlatform = Contracts.Common.Order.Enums.SellingPlatform.Ctrip
        });
        var hotelMapping = hotelMappings.FirstOrDefault();
        if (hotelMapping is null)
            return;
        await _hotelMappingService.SetPriceStrategyMapping(new Contracts.Common.Hotel.DTOs.HotelMapping.SetPriceStrategyMappingInput
        {
            HotelId = notification.HotelId,
            SellingPlatform = Contracts.Common.Order.Enums.SellingPlatform.Ctrip,
            PriceStrategyMappings = notification.Strategies.Select(p => new StrategyMappingInput
            {
                PriceStrategyId = p.StrategyId
            })
        });
    }
}
