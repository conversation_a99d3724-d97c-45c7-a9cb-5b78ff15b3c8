using Contracts.Common.Resource.Enums;
using Nest;

namespace Hotel.Api.EsDocument;

public class TradingAreaDocument
{
    /// <summary>
    /// id
    /// </summary>
    public long Id { get; set; }
    
    /// <summary>
    /// 城市code
    /// </summary>
    public int CityCode { get; set; }

    /// <summary>
    /// 商圈类型
    /// </summary>
    public TradingAreaType TradingAreaType { get; set; }

    /// <summary>
    /// 商圈名称
    /// </summary>
    public string Name { get; set; }

    /// <summary>
    /// 商圈地址
    /// </summary>
    public string Address { get; set; }
    
    /// <summary>
    /// 地理坐标点
    /// </summary>
    public GeoLocation Location { get; set; }
}