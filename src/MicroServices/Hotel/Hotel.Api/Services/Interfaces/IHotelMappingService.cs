using Contracts.Common.Hotel.DTOs.HotelMapping;
using Contracts.Common.Hotel.Messages;
using EfCoreExtensions.Abstract;

namespace Hotel.Api.Services.Interfaces;

public interface IHotelMappingService
{
    Task<PagingModel<HotelMappingOutput>> Search(SearchHotelMappingsInput input);
    ValueTask<IEnumerable<HotelMappingOutput>> GetHotelMappings(HotelMappingInput input);
    ValueTask<IEnumerable<RoomMappingOutput>> GetRoomMappings(RoomMappingsInput input);
    ValueTask<IEnumerable<RoomMappingInfoOutput>> GetRoomMappingInfos(RoomMappingsInput input);
    ValueTask<IEnumerable<PriceStrategyMappingOutput>> GetPriceStrategyMappings(PriceStrategyMappingInput input);
    ValueTask<IEnumerable<PriceStrategyMappingInfoOutput>> GetPriceStrategyMappingInfos(PriceStrategyMappingInput input);

    Task AddHotelMapping(AddHotelMappingInput input);

    Task SetHotelRoomMapping(SetHotelRoomMappingInput input);

    Task SetPriceStrategyMapping(SetPriceStrategyMappingInput input);
}
