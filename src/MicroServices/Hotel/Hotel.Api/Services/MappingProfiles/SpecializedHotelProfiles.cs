using AutoMapper;
using Contracts.Common.Hotel.DTOs.SpecializedHotel;
using EfCoreExtensions.Abstract;

namespace Hotel.Api.Services.MappingProfiles;

public class SpecializedHotelProfiles : Profile
{

    public SpecializedHotelProfiles()
    {
        CreateMap<SpecializedHotel, SearchSpecializedHotelOutput>();
        CreateMap<PagingModel<SpecializedHotel>, PagingModel<SearchSpecializedHotelOutput>>();

        CreateMap<AddSpecializedHotelInput, SpecializedHotel>();
        CreateMap<UpdateSpecializedHotelInput, SpecializedHotel>();

        CreateMap<SpecializedHotelImportLog, SearchImportLogOutput>();
        CreateMap<PagingModel<SpecializedHotelImportLog>, PagingModel<SearchImportLogOutput>>();
    }

}
