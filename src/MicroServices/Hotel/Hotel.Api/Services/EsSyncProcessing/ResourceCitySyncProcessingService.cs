using CanalSharp.Protocol;
using Common.ServicesHttpClient;
using Contracts.Common.Resource.DTOs.City;
using Contracts.Common.Resource.EsDocuments;
using Hotel.Api.Extensions;
using Hotel.Api.HostedService.CanalMonitorMappedFiles;
using Hotel.Api.Notification;
using Hotel.Api.Services.Interfaces;
using MediatR;
using Microsoft.Extensions.Options;
using Nest;

namespace Hotel.Api.Services.EsSyncProcessing;

/// <summary>
/// 资源库城市数据同步
/// </summary>
public class ResourceCitySyncProcessingService : IEsSyncProcessingService
{
    private readonly ILogger<ResourceCitySyncProcessingService> _logger;
    private readonly IMediator _mediator;
    
    public ResourceCitySyncProcessingService(
        ILogger<ResourceCitySyncProcessingService> logger,
        IMediator mediator)
    {
        _logger = logger;
        _mediator = mediator;
    }
    
    public string SchemaName => "Resource";
    public string TableName => "City";
    public object Document => new CanalMonitorMappedCityFile();
    
    public async Task Sync(List<(object document, long tenantId, EventType eventType)> input)
    {
        try
        {
            var notificationData = new EsSyncNotification
            {
                TableName = TableName,
                Data = input.Select(x=> new EsSyncNotificationData
                {
                    Document = x.document,
                    EventType = x.eventType
                })
                .ToList()
            };
            await _mediator.Publish(notificationData);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex,"ResourceCitySyncProcessing Error,Message:{@Message}", input);
        }
    }
}