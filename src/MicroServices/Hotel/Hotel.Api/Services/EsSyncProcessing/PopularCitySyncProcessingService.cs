using CanalSharp.Protocol;
using Contracts.Common.Resource.Enums;
using Contracts.Common.Resource.EsDocuments;
using Hotel.Api.Services.Interfaces;
using Nest;

namespace Hotel.Api.Services.EsSyncProcessing;

/// <summary>
/// 热门城市数据同步
/// </summary>
public class PopularCitySyncProcessingService : IEsSyncProcessingService
{
    private readonly ILogger<PopularCitySyncProcessingService> _logger;
    private readonly IElasticClient _elasticClient;
    private readonly IEsCreateIndexService _esCreateIndexService;
    private const string _popularCityIndexPrefix = "popularcity-";

    public PopularCitySyncProcessingService(
        ILogger<PopularCitySyncProcessingService> logger,
        IElasticClient elasticClient,
        IEsCreateIndexService esCreateIndexService)
    {
        _logger = logger;
        _elasticClient = elasticClient;
        _esCreateIndexService = esCreateIndexService;
    }

    public string SchemaName => "Resource";
    public string TableName => "PopularCity";
    public object Document => new PopularCityDocument();
    public async Task Sync(List<(object document, long tenantId, EventType eventType)> input)
    {
        try
        {
            //区分不同租户数据
            var tenantIds = input.Select(x => x.tenantId)
                .Distinct();

            foreach (var tenantId in tenantIds)
            {
                var tenantSyncData = input.Where(x => x.tenantId == tenantId)
                    .Select(x => x)
                    .ToList();

                var indexName = $"{_popularCityIndexPrefix}{tenantId}";
                var indexExistsResponse = await _elasticClient.Indices.ExistsAsync(indexName);
                if (indexExistsResponse.Exists is false)
                {
                    var createResponse = await _esCreateIndexService.CreatePopularCityIndex(indexName);
                }
                //执行批量操作
                await PerformBulkCrudOperations(tenantSyncData,indexName);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError("PopularCitySyncProcessing Error,Message:{@Message}", ex.Message);
        }
    }

    /// <summary>
    /// 执行批量操作
    /// </summary>
    /// <param name="syncData">同步数据</param>
    /// <param name="indexName">索引名称</param>
    private async Task PerformBulkCrudOperations(
        List<(object document, long tenantId, EventType eventType)> syncData,
        string indexName)
    {
        var bulkDescriptor = new BulkDescriptor();
        foreach (var syncItem in syncData)
        {
            var syncDocument = syncItem.document as PopularCityDocument;
            switch (syncItem.eventType)
            {
                case EventType.Insert:
                    syncDocument.IsPopular = true;
                    bulkDescriptor.Index<PopularCityDocument>(i=>i
                        .Index(indexName)
                        .Id(syncDocument.CityCode)
                        .Document(syncDocument));
                    break;
                case EventType.Update:
                    syncDocument.IsPopular = true;
                    bulkDescriptor.Update<PopularCityDocument>(i=>i
                        .Index(indexName)
                        .Id(syncDocument.CityCode)
                        .Doc(syncDocument)
                        .Upsert(syncDocument));
                    break;
                case EventType.Delete:
                    bulkDescriptor.Delete<PopularCityDocument>(i => i
                        .Index(indexName)
                        .Id(syncDocument.CityCode));
                    break;
            }
        }
        var response = await _elasticClient.BulkAsync(bulkDescriptor);
    }
}