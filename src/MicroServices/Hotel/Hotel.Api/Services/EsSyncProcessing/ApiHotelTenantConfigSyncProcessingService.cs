using CanalSharp.Protocol;
using Hotel.Api.EsDocument;
using Hotel.Api.HostedService.CanalMonitorMappedFiles;
using Hotel.Api.Services.Interfaces;
using Nest;

namespace Hotel.Api.Services.EsSyncProcessing;

public class ApiHotelTenantConfigSyncProcessingService :IEsSyncProcessingService
{
    private readonly IElasticClient _elasticClient;
    private readonly IEsCreateIndexService _esCreateIndexService;
    private readonly ILogger<ApiHotelTenantConfigSyncProcessingService> _logger;
    
    // 索引名称
    private const string _resourceHotelIndexNameV2 = "resource-hotel-v2";
    
    public ApiHotelTenantConfigSyncProcessingService(
        IElasticClient elasticClient,
        IEsCreateIndexService esCreateIndexService,
        ILogger<ApiHotelTenantConfigSyncProcessingService> logger)
    {
        _elasticClient = elasticClient;
        _esCreateIndexService = esCreateIndexService;
        _logger = logger;
    }

    public string SchemaName => "Hotel";
    public string TableName => "ApiHotelTenantConfig";
    public object Document => new CanalMonitorMappedApiHotelTenantConfigFile();
    public async Task Sync(List<(object document, long tenantId, EventType eventType)> input)
    {
        try
        {
            //存在资源酒店索引才处理.只做更新
            var indexExistsResponse = await _elasticClient.Indices.ExistsAsync(_resourceHotelIndexNameV2);
            if (indexExistsResponse.Exists is false) return;
            
            await PerformBulkCrudOperations(input);
            
        }
        catch (Exception ex)
        {
            _logger.LogError("ApiHotelTenantConfigSyncProcessingService Error,Message:{@Message}", ex.Message);
        }
    }

    /// <summary>
    /// 执行批量操作
    /// <value>查询到对应文档才更新</value>
    /// </summary>
    /// <param name="syncData"></param>
    private async Task PerformBulkCrudOperations(List<(object document, long tenantId, EventType eventType)> syncData)
    {
        var bulkDescriptor = new BulkDescriptor();
        
        foreach (var item in syncData)
        {
            var syncDocument = item.document as CanalMonitorMappedApiHotelTenantConfigFile;
            var updateDto = new TenantResourceHotelConfigNested
            {
                TenantId = syncDocument.TenantId,
                WeightValue = syncDocument.WeightValue,
                OnTop = syncDocument.OnTop ? 1 : 0
            };
            // 优先置顶,然后按权重排序
            updateDto.SortValue = updateDto.OnTop * 100000000 + updateDto.WeightValue;
            switch (item.eventType)
            {
                case EventType.Insert:
                case EventType.Update:
                case EventType.Delete:
                    
                    // 移除数据的时候,只更新数据.初始化
                    if (item.eventType == EventType.Delete)
                    {
                        updateDto.WeightValue = null;
                        updateDto.OnTop = null;
                        updateDto.SortValue = null;
                    }

                    bulkDescriptor.Update<ResourceHotelDocumentV2>(u => u
                        .Id(syncDocument.ResourceHotelId)
                        .Index(_resourceHotelIndexNameV2)
                        .Script(s => s
                            .Source(@" 
                    if (ctx._source.tenantConfig == null) {
                        ctx._source.tenantConfig = [];
                    }
                    def found = false;
                    for (def item : ctx._source.tenantConfig) {
                        if (item.tenantId == params.tenantId) {
                            item.onTop = params.onTop;
                            item.weightValue = params.weightValue;
                            item.sortValue = params.sortValue;
                            found = true;
                            break;
                        }
                    }
                    if (!found) {
                        ctx._source.tenantConfig.add([
                            'tenantId': params.tenantId,
                            'onTop': params.onTop,
                            'weightValue': params.weightValue,
                            'sortValue': params.sortValue
                        ]);
                    }
                ")
                            .Lang("painless")
                            .Params(p => p
                                .Add("tenantId", updateDto.TenantId)
                                .Add("onTop", updateDto.OnTop)
                                .Add("weightValue", updateDto.WeightValue)
                                .Add("sortValue",updateDto.SortValue))
                        )
                        .RetriesOnConflict(3));
                    
                    break;
            }
            
        }
        
        var response = await _elasticClient.BulkAsync(bulkDescriptor);
    }
}