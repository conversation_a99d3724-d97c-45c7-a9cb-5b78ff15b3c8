using Cit.DataAccess.Ctrip.Hotel.Client;
using Cit.DataAccess.Ctrip.Hotel.Models.DynamicInfo;
using Cit.DataAccess.Ctrip.Hotel.Models.StaticInfo;
using Common.GlobalException;
using Common.ServicesHttpClient;
using Contracts.Common.Hotel.DTOs.HotelMapping;
using Contracts.Common.Hotel.Messages;
using Contracts.Common.Inventory.DTOs;
using Contracts.Common.Order.Enums;
using Contracts.Common.Tenant.DTOs.Agency;
using Contracts.Common.Tenant.Enums;
using Hotel.Api.ConfigModel;
using Hotel.Api.Extensions;
using Hotel.Api.Services.Interfaces;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;

namespace Hotel.Api.Services;

public class HotelPushService : IHotelPushService
{
    private readonly CustomDbContext _dbContext;
    private readonly ICtripHotelClientFactory _ctripHotelClientFactory;
    private readonly IHttpClientFactory _httpClientFactory;
    private readonly IOptions<ServicesAddress> _serviceAddress;
    private readonly IOptions<CtripHotelApiConfig> _ctripHotelApiConfig;
    private readonly IHotelMappingService _hotelMappingService;
    private readonly IPriceStrategyCalendarPriceService _priceStrategyCalendarPriceService;
    private readonly IHostEnvironment _hostEnvironment;

    public HotelPushService(CustomDbContext dbContext,
        ICtripHotelClientFactory ctripHotelClientFactory,
        IHttpClientFactory httpClientFactory,
        IOptions<ServicesAddress> serviceAddress,
        IOptions<CtripHotelApiConfig> ctripHotelApiConfig,
        IHotelMappingService hotelMappingService,
        IPriceStrategyCalendarPriceService priceStrategyCalendarPriceService,
        IHostEnvironment hostEnvironment)
    {
        _dbContext = dbContext;
        _ctripHotelClientFactory = ctripHotelClientFactory;
        _httpClientFactory = httpClientFactory;
        _serviceAddress = serviceAddress;
        _ctripHotelApiConfig = ctripHotelApiConfig;
        _hotelMappingService = hotelMappingService;
        _priceStrategyCalendarPriceService = priceStrategyCalendarPriceService;
        _hostEnvironment = hostEnvironment;
    }

    public async Task HotelMappingPush(HotelMappingPushMessage message)
    {
        var hotel = await _dbContext.Hotels
            .Where(x => x.Id == message.HotelId)
            .FirstOrDefaultAsync();
        var client = await GetCtripHotelClient();
        if (client is null)
            return;
        var hotelNotifyRequest = new Cit.DataAccess.Ctrip.Hotel.Models.StaticInfo.HotelNotifyRequest()
        {
            LanguageCode = "zh-CN",
            Active = hotel.Enabled,
            HotelCode = hotel.Id.ToString(),
            HotelBasicInfo = new Cit.DataAccess.Ctrip.Hotel.Models.StaticInfo.Hotelbasicinfo
            {

                HotelNames = new Cit.DataAccess.Ctrip.Hotel.Models.StaticInfo.Hotelname[] {
                     new Cit.DataAccess.Ctrip.Hotel.Models.StaticInfo.Hotelname{
                        LanguageCode="zh-CN",
                        Content=hotel.ZHName
                     }
                  },
                Addresses = new Cit.DataAccess.Ctrip.Hotel.Models.StaticInfo.Address[]{
                    new () {
                        address=hotel.Address,
                        City=hotel.CityName,
                        Province=hotel.ProvinceName,
                        Country=hotel.CountryName,
                        LanguageCode="zh-CN"
                 }
                },
                Currency = "CNY",
                AddressVisible = true,
                Phones = new[]
                {
                    GetPhone(hotel.Telephone)
                }
            }
        };
        if (!string.IsNullOrWhiteSpace(hotel.ENName))
        {
            hotelNotifyRequest.HotelBasicInfo.HotelNames = hotelNotifyRequest.HotelBasicInfo.HotelNames.Append(new Hotelname
            {
                LanguageCode = "en-US",
                Content = hotel.ENName
            });
        }
        if (hotel.Location != null)
        {
            hotelNotifyRequest.HotelBasicInfo.Positions = new Cit.DataAccess.Ctrip.Hotel.Models.StaticInfo.Position[] {
                 new Cit.DataAccess.Ctrip.Hotel.Models.StaticInfo.Position
                 {
                    Source=hotel.CoordinateType switch{
                        Contracts.Common.Resource.Enums.CoordinateType.BD09 => "baidu",
                        Contracts.Common.Resource.Enums.CoordinateType.WGS84 => "google",
                        Contracts.Common.Resource.Enums.CoordinateType.GCJ02 => "gaoDe",
                        _=>"baidu"} ,
                    Latitude=(float)hotel.Location.Y,
                    Longitude=(float)hotel.Location.X
                 }
            };
        }
        var result = await client.HotelNotify(hotelNotifyRequest);
        var mapping = await _dbContext.HotelMappings
            .Where(x => x.SellingPlatform == message.SellingPlatform && x.HotelId == message.HotelId)
            .FirstOrDefaultAsync();
        var isSuccessed = result.IsSucceed();
        if (!isSuccessed)
        {
            mapping.MappingStatus = Contracts.Common.Hotel.Enums.MappingStatus.Failure;
            mapping.ErrorInfos = $"{result.Message}({result.Code})";
            await _dbContext.SaveChangesAsync();
        }
    }

    private static Phone GetPhone(string? phone)
    {
        var _phone = new Phone() { PhoneType = "Phone" };
        var telephones = phone?.Split("-");
        switch (telephones?.Length)
        {
            case 1:
                {
                    long.TryParse(telephones[^1], out long mainCode);
                    _phone.PhoneNumber = new Phonenumber
                    {
                        CountryCode = 86,
                        MainCode = mainCode
                    };
                }
                break;
            case 2:
                {
                    long.TryParse(telephones[^1], out long mainCode);
                    int.TryParse(telephones[^2], out int areaCode);
                    _phone.PhoneNumber = new Phonenumber
                    {
                        CountryCode = 86,
                        AreaCode = areaCode,
                        MainCode = mainCode
                    };
                }
                break;
            case 3:
                {
                    long.TryParse(telephones[^1], out long mainCode);
                    int.TryParse(telephones[^2], out int areaCode);
                    if (!int.TryParse(telephones[^3].Replace("+", ""), out int countryCode))
                        countryCode = 86;
                    _phone.PhoneNumber = new Phonenumber
                    {
                        CountryCode = countryCode,
                        AreaCode = areaCode,
                        MainCode = mainCode
                    };
                }
                break;
        }
        return _phone;
    }

    public async Task HotelRoomMappingPush(HotelRoomMappingPushMessage message)
    {
        var rooms = await _dbContext.HotelRooms
            .AsNoTracking()
            .Where(x => x.HotelId == message.HotelId && message.HotelRoomIds.Contains(x.Id) && x.Viewable)
            .ToListAsync();
        var client = await GetCtripHotelClient();
        if (client is null)
            return;
        RoomNotifyRequest roomNotifyRequest = new()
        {
            LanguageCode = "zh-CN",
            HotelCode = message.HotelId.ToString(),
            RoomDatas = rooms.Select(x => new Roomdata
            {
                active = true,
                roomTypeCode = x.Id.ToString(),
                roomBasicInfo = new Roombasicinfo
                {
                    currency = "CNY",
                    roomNames = new Roomname[] { new Roomname { languageCode = "zh-CN", content = x.ZHName } },
                    roomDescriptions = new Roomdescription[] { new Roomdescription { languageCode = "zh-CN", content = x.ZHName } },
                    smoking = 0,
                    window = x.WindowType switch
                    {
                        Contracts.Common.Hotel.Enums.WindowType.None => 0,
                        Contracts.Common.Hotel.Enums.WindowType.Have => 2,
                        Contracts.Common.Hotel.Enums.WindowType.SomeHave => 1,
                        _ => 0
                    },
                    wifi = new Wifi { available = 2 },
                    cableInternet = new Cableinternet { available = 2 },
                    area = (int)x.AreaMax,
                    floor = $"{x.FloorMin}-{x.FloorMax}",
                    roomQuantity = x.RoomQuantity,
                    occupancy = new Occupancy { maxOccupancy = x.MaximumOccupancy, adult = new Adult { maxAdultOccupancy = x.MaximumOccupancy } },
                }
            })
        };
        var result = await client.RoomNotify(roomNotifyRequest);

        var isSuccess = result.IsSucceed();
        if (!isSuccess)
        {
            var mappings = await _dbContext.HotelRoomMappings
                .Where(x => x.SellingPlatform == message.SellingPlatform
                    && x.HotelId == message.HotelId
                    && message.HotelRoomIds.Contains(x.HotelRoomId))
                .ToListAsync();
            foreach (var mapping in mappings)
            {
                mapping.MappingStatus = Contracts.Common.Hotel.Enums.MappingStatus.Failure;
                mapping.ErrorInfos = $"{result.Message}({result.Code})";
            }
            await _dbContext.SaveChangesAsync();
        }
    }

    public async Task PriceStrategyMappingPush(PriceStrategyMappingPushMessage message)
    {
        var priceStartegies = await _dbContext.PriceStrategies
            .Where(x => x.HotelId == message.HotelId && message.PriceStrategyIds.Contains(x.Id))
            .ToListAsync();
        if (priceStartegies.Any() is false) return;

        var roomIds = priceStartegies.GroupBy(x => x.HotelRoomId).Select(x => x.Key).ToArray();
        var rooms = await _dbContext.HotelRooms
            .Where(x => x.HotelId == message.HotelId && roomIds.Contains(x.Id))
            .Select(r => new { r.Id, r.MaximumOccupancy })
            .ToListAsync();
        var priceStrategyIds = priceStartegies.Select(p => p.Id).ToArray();
        var cancellations = await _dbContext.PriceStrategyCancelRules
            .Where(x => priceStrategyIds.Contains(x.PriceStrategyId))
            .ToListAsync();

        ProductNotifyRequest productNotifyRequest = new ProductNotifyRequest
        {
            LanguageCode = "zh-CN",
            HotelCode = message.HotelId.ToString(),
            RoomTypes = priceStartegies.GroupBy(x => new { x.HotelRoomId, x.SaleCurrencyCode })
            .Select(r => new Roomtype
            {
                Currency = r.Key.SaleCurrencyCode,
                RoomTypeCode = r.Key.HotelRoomId.ToString(),
                RatePlans = r.Select(p =>
                {
                    var cancellation = cancellations.FirstOrDefault(c => c.PriceStrategyId == p.Id);
                    return new Rateplan
                    {
                        RatePlanCode = p.Id.ToString(),
                        Active = p.Enabled,
                        RateCategory = "Prepay",
                        Occupancy = rooms.FirstOrDefault(r => r.Id == p.HotelRoomId)?.MaximumOccupancy ?? 2,
                        LastReserveTime = p.BookingHoursInAdvance,
                        LastCancelTime = cancellation?.CancelRulesType switch
                        {
                            Contracts.Common.Hotel.Enums.CancelRulesType.FreeCancel => 0,
                            Contracts.Common.Hotel.Enums.CancelRulesType.CannotCancel => 23988,
                            Contracts.Common.Hotel.Enums.CancelRulesType.LimitedTimeCancel =>
                            cancellation.BeforeCheckInDays * 24 + cancellation.CheckInDateTime.Hours,
                            _ => 23988
                        },
                        PenaltyBasicStandard = "All",
                        BookingRule = new RateplanBookingrule
                        {

                        },
                        Meal = new Meal { MealType = 1, MealCount = p.NumberOfBreakfast }
                    };
                })
            })
        };

        var client = await GetCtripHotelClient();
        if (client is null)
            return;
        var result = await client.ProductNotify(productNotifyRequest);

        var isSuccess = result.IsSucceed();
        if (!isSuccess)
        {
            var mappings = await _dbContext.HotelPriceStrategyMappings
                .Where(x => x.SellingPlatform == message.SellingPlatform
                    && x.HotelId == message.HotelId
                    && message.PriceStrategyIds.Contains(x.PriceStrategyId))
                .ToListAsync();
            foreach (var mapping in mappings)
            {
                mapping.MappingStatus = Contracts.Common.Hotel.Enums.MappingStatus.Failure;
                mapping.ErrorInfos = $"{result.Message}({result.Code})";
            }
            await _dbContext.SaveChangesAsync();
        }
    }

    public async Task HotelMappingPushQueryHandle(HotelMappingPushQueryInput input)
    {
        var client = await GetCtripHotelClient();
        if(client is null)
            return;
        StaticInfoStatusSearchResponse? result = null;
        try
        {
            result = await client.StaticInfoStatusSearch(new StaticInfoStatusSearchRequest
            {
                LanguageCode = "zh-CN",
                SearchInfos = input.HotelIds.Select(x => new SearchInfo
                {
                    HotelCode = x.ToString(),
                    Type = "Product"
                })
            });
        }
        catch (HttpRequestException ex)
        {
            throw new BusinessException(ErrorTypes.Hotel.ExternalServiceRequestException, ex);
        }

        if (result?.IsSucceed() is not true)
            throw new Exception($"{result.Message}({result.Code})");

        var hotelIds = input.HotelIds;

        var hotelMappings = await _dbContext.HotelMappings
            .Where(x => x.SellingPlatform == input.SellingPlatform && hotelIds.Contains(x.HotelId))
            .ToListAsync();
        var hotelRoomMappings = await _dbContext.HotelRoomMappings
            .Where(x => x.SellingPlatform == input.SellingPlatform && hotelIds.Contains(x.HotelId))
            .ToListAsync();
        var hotelPriceStartegyMappings = await _dbContext.HotelPriceStrategyMappings
            .Where(x => x.SellingPlatform == input.SellingPlatform && hotelIds.Contains(x.HotelId))
            .ToListAsync();

        foreach (var data in result.Datas)
        {
            if (!long.TryParse(data.HotelCode, out long hotelId)) continue;
            var hotelMapping = hotelMappings.FirstOrDefault(x => x.HotelId == hotelId);
            hotelMapping.ErrorInfos = string.Empty;
            hotelMapping.ExtHotelName = data.HotelName;
            hotelMapping.MappingStatus = data.HotelStatusInfo?.MappedMaster is true ?
                Contracts.Common.Hotel.Enums.MappingStatus.Success : Contracts.Common.Hotel.Enums.MappingStatus.Processing;
            if (data.HotelStatusInfo?.HotelId is > 0)
            {
                hotelMapping.ExtHotelId = data.HotelStatusInfo.HotelId.ToString();
            }
            if (data.HotelStatusInfo?.ErrorInfos?.Count > 0)
            {
                hotelMapping.MappingStatus = Contracts.Common.Hotel.Enums.MappingStatus.Failure;
                hotelMapping.ErrorInfos = string.Join(";", data.HotelStatusInfo.ErrorInfos.Select(e => $"{e.Message}({e.Code})"));
            }
            hotelMapping.LastUpdateTime = DateTime.Now;

            if (data.Rooms?.Any() is not true) continue;

            var roomMappings = hotelRoomMappings.Where(x => x.HotelId == hotelId);
            foreach (var room in data.Rooms)
            {
                if (!long.TryParse(room.RoomTypeCode, out long roomId)) continue;
                var roomMapping = roomMappings.FirstOrDefault(x => x.HotelRoomId == roomId);
                if (roomMapping == null) continue;
                roomMapping.ExtRoomName = room.RoomTypeName;
                roomMapping.ErrorInfos = string.Empty;
                if (room.RoomStatus?.BasicRoomId > 0)
                    roomMapping.ExtRoomId = room.RoomStatus.BasicRoomId.ToString();
                roomMapping.MappingStatus = room.RoomStatus?.MappedMaster is true ?
                Contracts.Common.Hotel.Enums.MappingStatus.Success : Contracts.Common.Hotel.Enums.MappingStatus.Processing;
                if (room.RoomStatus?.ErrorInfos?.Any() is true)
                {
                    roomMapping.MappingStatus = Contracts.Common.Hotel.Enums.MappingStatus.Failure;
                    roomMapping.ErrorInfos = string.Join(";", room.RoomStatus.ErrorInfos.Select(e => $"{e.Message}({e.Code})"));
                }
                roomMapping.LastUpdateTime = DateTime.Now;
                if (room.Products?.Any() is not true) continue;
                var priceStartegyMappings = hotelPriceStartegyMappings.Where(x => x.HotelId == hotelId);
                foreach (var product in room.Products)
                {
                    if (!long.TryParse(product.RatePlanCode, out long psId)) continue;
                    var priceStartegyMapping = priceStartegyMappings.FirstOrDefault(x => x.PriceStrategyId == psId);
                    if (priceStartegyMapping == null) continue;
                    priceStartegyMapping.ErrorInfos = string.Empty;
                    priceStartegyMapping.MappingStatus = Contracts.Common.Hotel.Enums.MappingStatus.Processing;
                    if (product?.ProductId is > 0)
                    {
                        priceStartegyMapping.ExtPriceStrategyId = product.ProductId.ToString();
                        priceStartegyMapping.MappingStatus = Contracts.Common.Hotel.Enums.MappingStatus.Success;
                    }
                    if (product?.ErrorInfos?.Any() is true)
                    {
                        priceStartegyMapping.MappingStatus = Contracts.Common.Hotel.Enums.MappingStatus.Failure;
                        priceStartegyMapping.ErrorInfos = string.Join(";", product.ErrorInfos.Select(e => $"{e.Message}({e.Code})"));
                    }
                }
            }
        }
        await _dbContext.SaveChangesAsync();
    }

    public async Task<IEnumerable<ProcessMappingHotelOutput>> GetProcessMappingHotels(ProcessMappingHotelInput input)
    {
        var hotelMappings = await _dbContext.HotelMappings
            .IgnoreQueryFilters()
            .Where(x => x.MappingStatus == Contracts.Common.Hotel.Enums.MappingStatus.Processing
                && x.SellingPlatform == input.SellingPlatform)
            .GroupBy(x => new { x.TenantId, x.HotelId })
            .Select(x => x.Key)
            .ToListAsync();

        var roomMappings = await _dbContext.HotelRoomMappings
            .IgnoreQueryFilters()
            .Where(x => x.MappingStatus == Contracts.Common.Hotel.Enums.MappingStatus.Processing
                && x.SellingPlatform == input.SellingPlatform)
            .GroupBy(x => new { x.TenantId, x.HotelId })
            .Select(x => x.Key)
            .ToListAsync();

        var psMappings = await _dbContext.HotelPriceStrategyMappings
            .IgnoreQueryFilters()
            .Where(x => x.MappingStatus == Contracts.Common.Hotel.Enums.MappingStatus.Processing
                && x.SellingPlatform == input.SellingPlatform)
            .GroupBy(x => new { x.TenantId, x.HotelId })
            .Select(x => x.Key)
            .ToListAsync();

        var data = hotelMappings.Union(roomMappings).Union(psMappings);
        var result = data.GroupBy(x => x.TenantId)
            .Select(x => new ProcessMappingHotelOutput
            {
                TenantId = x.Key,
                HotelIds = x.Select(h => h.HotelId)
            });
        return result;
    }

    /// <summary>
    /// API分销商配置信息 null=未配置API分销商信息
    /// </summary>
    /// <param name="agencyApiType"></param>
    /// <returns></returns>
    private async Task<CtripHotelClient> GetCtripHotelClient(AgencyApiType agencyApiType = AgencyApiType.CtripHotel)
    {
#if DEBUG
        return _ctripHotelClientFactory.Create(new CtripHotelConfig
        {
            Host = "https://gateway.fat.ctripqa.com",
            //Code = "971",
            //Host = "https://receive-vendor-hotel.ctrip.com",
            Code = "1297",
            Account = "zhilianjishuzhuanshu",
            Password = "zhilianzhuanshu11!!"
        });
#endif
        var url = _serviceAddress.Value.Tenant_GetAgencyApiSetting(agencyApiType);
        var apiSetting = await _httpClientFactory.InternalGetAsync<GetAgencyApiSettingOutput>(url);
        if (apiSetting is null) {
            //throw new BusinessException("未配置API分销商信息");
            return null;
        }


        var client = _ctripHotelClientFactory.Create(new CtripHotelConfig
        {
            Host = _ctripHotelApiConfig.Value.Host,
            Code = apiSetting.Code,
            Account = apiSetting.Account,
            Password = apiSetting.Password
        });
        return client;
    }

    public async Task PriceStrategyCalendarPricePush(PriceStrategyCalendarPricePushMessage message)
    {
        var mappings = await _hotelMappingService.GetPriceStrategyMappings(new PriceStrategyMappingInput
        {
            SellingPlatform = message.SellingPlatform,
            HotelIds = new[] { message.HotelId }
        });
        var priceStrategyMappings = mappings.Where(x => message.PriceStrategyIds.Contains(x.PriceStrategyId));

        if (priceStrategyMappings.Any() is false) return;
        var priceStartegyIds = priceStrategyMappings.Select(x => x.PriceStrategyId).ToList();

        var priceStrategies = await _dbContext.PriceStrategies
            .Where(x => x.HotelId == message.HotelId && priceStartegyIds.Contains(x.Id))
            .Select(x => new { x.Id, x.NumberOfBreakfast, x.SaleCurrencyCode })
            .ToListAsync();
        var roomIds = priceStrategyMappings.Select(x => x.HotelRoomId);
        var rooms = await _dbContext.HotelRooms
            .Where(x => x.HotelId == message.HotelId && roomIds.Contains(x.Id))
            .Select(x => new { x.Id, x.MaximumOccupancy })
            .ToListAsync();
        SellingChannels sellingChannels = message.SellingPlatform switch
        {
            SellingPlatform.Ctrip => SellingChannels.Ctrip,
            _ => throw new NotImplementedException()
        };

        var saleChanelsPrices = await _priceStrategyCalendarPriceService.QuerySaleChanelsPrice(new Contracts.Common.Hotel.DTOs.PriceStrategyCalendarPrice.QuerySaleChanelsPriceInput
        {
            BeginDate = message.BeginDate,
            EndDate = message.EndDate,
            HotelId = message.HotelId,
            PriceStartegyIds = priceStartegyIds,
            SellingChannels = new List<SellingChannels> { sellingChannels }
        });

        if (!saleChanelsPrices.Any())
            return;

        RateNotifyRequest rateNotifyRequest = new()
        {
            Data = new RateNotifyData
            {
                HotelCode = message.HotelId.ToString(),
                RoomPrices = priceStrategyMappings.Select(x =>
                {
                    var chanelsPriceOutput = saleChanelsPrices.Where(s => s.PriceStrategyId == x.PriceStrategyId)
                         .FirstOrDefault();
                    var priceStrategy = priceStrategies.FirstOrDefault(s => s.Id == x.PriceStrategyId);
                    var room = rooms.FirstOrDefault(r => r.Id == x.HotelRoomId);
                    return new Roomprice
                    {
                        RoomTypeCode = x.HotelRoomId.ToString(),
                        RatePlanCode = x.PriceStrategyId.ToString(),
                        RateCategory = "Prepay",
                        PriceItems = chanelsPriceOutput.SaleChanelsPrices
                        .Where(p => p.Price.HasValue)
                        .Select(p => new Priceitem
                        {
                            Currency = priceStrategy.SaleCurrencyCode,
                            MealCount = priceStrategy.NumberOfBreakfast,
                            MealType = 4,
                            PeriodStartDate = p.Date.ToString("yyyy-MM-dd"),
                            PeriodEndDate = p.Date.ToString("yyyy-MM-dd"),
                            LengthOfStay = 1,
                            TotalRateAfterTax = p.Price!,
                            TotalRateBeforeTax = p.Price!,
                            RateOccupancy = room?.MaximumOccupancy ?? 2
                        })
                    };
                })
                .Where(r => r.PriceItems.Any())
            }
        };
        if (!rateNotifyRequest.Data.RoomPrices.Any())
            return;

        var client = await GetCtripHotelClient();

        if (client is null)
            return;

        if (_hostEnvironment.IsProduction())
        {
            var result = await client.RateNotify(rateNotifyRequest);

            if (!result.IsSucceed())
            {
                throw new BusinessException($"{result.Message}({result.Code})");
            }
        }
    }

    public async Task PriceStrategyCalendarInventoryPush(PriceStrategyCalendarInventoryPushMessage message)
    {
        var mappings = await _hotelMappingService.GetPriceStrategyMappings(new PriceStrategyMappingInput
        {
            SellingPlatform = message.SellingPlatform,
            HotelIds = new[] { message.HotelId }
        });
        var priceStrategyMappings = mappings.Where(x => message.PriceStrategyIds.Contains(x.PriceStrategyId));

        if (priceStrategyMappings.Any() is false) return;
        var priceStartegyIds = priceStrategyMappings.Select(x => x.PriceStrategyId).ToList();

        var priceStrategies = await _dbContext.PriceStrategies
            .Where(x => x.HotelId == message.HotelId && priceStartegyIds.Contains(x.Id))
            .Select(x => new { x.Id, x.BookingHoursInAdvance })
            .ToListAsync();
        //查询房态 间数
        var calendarInventories = await GetCalendarInventories(new GetCalendarInventoryInput
        {
            StartDate = message.BeginDate,
            EndDate = message.EndDate,
            CalendarProducts = priceStrategyMappings
            .GroupBy(x => x.HotelRoomId)
            .Select(m => new CalendarProduct
            {
                ProductId = m.Key,
                ItemIds = m.Select(s => s.PriceStrategyId).ToList()
            })
        });

        if (calendarInventories?.Any() is not true)
            return;

        var priceStrategyCancelRules = await _dbContext.PriceStrategyCancelRules
            .Where(x => priceStartegyIds.Contains(x.PriceStrategyId))
            .ToListAsync();

        var client = await GetCtripHotelClient();

        if (client is null)
            return;

        AvailNotifyRequest availNotifyRequest = new AvailNotifyRequest
        {
            Data = new AvailNotifyData
            {
                HotelCode = message.HotelId.ToString(),
                RoomInfoItems = priceStrategyMappings.Select(x =>
                {
                    var priceStrategy = priceStrategies.FirstOrDefault(s => s.Id == x.PriceStrategyId);
                    var calendarInventory = calendarInventories.FirstOrDefault(i => i.ItemId == x.PriceStrategyId);
                    List<Cancellationpolicy>? cancellationpolicies = null;
                    var cancelRule = priceStrategyCancelRules.FirstOrDefault(c => c.PriceStrategyId == x.PriceStrategyId);
                    if (cancelRule?.CancelRulesType == Contracts.Common.Hotel.Enums.CancelRulesType.LimitedTimeCancel
                        && cancelRule.CancelChargeType == Contracts.Common.Hotel.Enums.CancelChargeType.Order)
                    {
                        cancellationpolicies ??= new();
                        Cancellationpolicy freeCancellationpolicy = new()
                        {
                            Penalty = 0,
                            PenaltyType = "Rule1",
                            OffsetTimeUnit = "Day",
                            OffsetUnitMultiplier = cancelRule.BeforeCheckInDays
                        };
                        cancellationpolicies.Add(freeCancellationpolicy);

                        Cancellationpolicy cancellationpolicy = new()
                        {
                            Penalty = cancelRule.ChargeValue,
                            PenaltyType = "Rule1",
                            OffsetTimeUnit = "Hour",
                            OffsetUnitMultiplier = (24 - cancelRule.CheckInDateTime.Hours)
                        };
                        cancellationpolicies.Add(cancellationpolicy);
                    }

                    var roominfoitem = new Roominfoitem
                    {
                        RoomTypeCode = x.HotelRoomId.ToString(),
                        RatePlanCode = x.PriceStrategyId.ToString(),
                        RateCategory = "Prepay",
                        InfoItems = calendarInventory.Inventories.Select(c => new Infoitem
                        {
                            PeriodStartDate = message.BeginDate.ToString("yyyy-MM-dd"),
                            PeriodEndDate = message.EndDate.ToString("yyyy-MM-dd"),
                            BookingLimit = c.AvailableQuantity,
                            AllotmentRoomType = 1,
                            SetRoomAllotment = c.AvailableQuantity,
                            MasterStatus = c.Enabled ? "Open" : "Close",
                            LastReserveTime = priceStrategy?.BookingHoursInAdvance,
                            AllotmentLastReserveTime = priceStrategy?.BookingHoursInAdvance,
                            CancellationPolicies = cancellationpolicies
                        })
                    };
                    return roominfoitem;
                })
                .Where(r => r.InfoItems.Any())
            }
        };

        if (!availNotifyRequest.Data.RoomInfoItems.Any())
            return;

        if (_hostEnvironment.IsProduction())
        {
            var result = await client.AvailNotify(availNotifyRequest);
            if (!result.IsSucceed())
            {
                throw new BusinessException($"{result.Message}({result.Code})");
            }
        }
    }

    private async Task<IEnumerable<CalendarInventoryOutput>> GetCalendarInventories(
    GetCalendarInventoryInput request)
    {
        var httpContent = new StringContent(JsonConvert.SerializeObject(request),
            Encoding.UTF8,
            "application/json");
        var result = await _httpClientFactory.InternalPostAsync<IEnumerable<CalendarInventoryOutput>>(
            requestUri: _serviceAddress.Value.Inventory_CalendarGetInventories(),
            httpContent: httpContent);
        return result;
    }
}
