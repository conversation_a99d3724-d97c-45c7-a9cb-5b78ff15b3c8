using Contracts.Common.Hotel.DTOs.HotelCombination;
using FluentValidation;

namespace Hotel.Api.Services.Validator.HotelCombination;

public class UpdateInputValidator : AbstractValidator<UpdateInput>
{
    public UpdateInputValidator()
    {
        RuleFor(x => x).Must(x => x.SkuInfos.GroupBy(s => new { s.PriceStrategyId, s.NumberOfRoom, s.Nights })
            .Any(g => g.Count() <= 1))
            .WithMessage("same pricestrategy,numberofroom and nights");

        RuleFor(x => x.Id).NotEmpty();
        RuleFor(x => x.UpdaterId).NotEmpty();
        RuleFor(x => x.Name).NotEmpty();
        RuleFor(x => x.DestinationInfos).NotEmpty();
        RuleFor(x => x.SkuInfos).NotEmpty();
        RuleForEach(x => x.DestinationInfos).ChildRules(c =>
        {
            c.RuleFor(s => s.CityCode).NotEmpty();
        });
        RuleForEach(x => x.SkuInfos).ChildRules(c =>
        {
            c.RuleFor(s => s.HotelId).NotEmpty();
            c.RuleFor(s => s.HotelZHName).NotEmpty();
            c.RuleFor(s => s.HotelENName).NotEmpty();
            c.RuleFor(s => s.HotelRoomId).NotEmpty();
            c.RuleFor(s => s.HotelRoomZHName).NotEmpty();
            c.RuleFor(s => s.HotelRoomENName).NotEmpty();
            c.RuleFor(s => s.PriceStrategyId).NotEmpty();
            c.RuleFor(s => s.PriceStrategyName).NotEmpty();
            c.RuleFor(s => s.Nights).NotEmpty();
            c.RuleFor(s => s.NumberOfRoom).GreaterThanOrEqualTo(0);
            c.RuleFor(s => s.Tag).IsInEnum();
            c.RuleFor(s => s.MinAdvHours).GreaterThanOrEqualTo(0);
            c.RuleFor(s => s.MinDays).GreaterThanOrEqualTo(0);
            c.RuleFor(s => s.MaxDays).GreaterThanOrEqualTo(0);
            c.RuleFor(s => s.MaxOccupancy).GreaterThanOrEqualTo(0);
        });
    }
}
