using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Hotel.Api.Infrastructure.Migrations
{
    public partial class AddHotelOperationConfig : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "HotelOperationConfig",
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false),
                    OperationConfigType = table.Column<sbyte>(type: "tinyint", nullable: false),
                    HotelId = table.Column<long>(type: "bigint", nullable: false),
                    AllowSelfMaintainHotel = table.Column<bool>(type: "tinyint(1)", nullable: false),
                    AllowSelfMaintainRoom = table.Column<bool>(type: "tinyint(1)", nullable: false),
                    CreateTime = table.Column<DateTime>(type: "datetime", nullable: false),
                    UpdateTime = table.Column<DateTime>(type: "datetime", nullable: false),
                    TenantId = table.Column<long>(type: "bigint", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_HotelOperationConfig", x => x.Id);
                })
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.CreateIndex(
                name: "IX_HotelOperationConfig_TenantId",
                table: "HotelOperationConfig",
                column: "TenantId");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "HotelOperationConfig");
        }
    }
}
