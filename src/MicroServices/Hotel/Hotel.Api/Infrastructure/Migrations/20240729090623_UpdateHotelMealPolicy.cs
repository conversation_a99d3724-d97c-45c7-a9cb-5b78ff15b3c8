using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Hotel.Api.Infrastructure.Migrations
{
    public partial class UpdateHotelMealPolicy : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<decimal>(
                name: "Amount",
                table: "HotelMealPolicy",
                type: "decimal(18,2)",
                nullable: false,
                defaultValue: 0m);

            migrationBuilder.AddColumn<string>(
                name: "Currency",
                table: "HotelMealPolicy",
                type: "varchar(20)",
                nullable: true)
                .Annotation("MySql:CharSet", "utf8mb4");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "Amount",
                table: "HotelMealPolicy");

            migrationBuilder.DropColumn(
                name: "Currency",
                table: "HotelMealPolicy");
        }
    }
}
