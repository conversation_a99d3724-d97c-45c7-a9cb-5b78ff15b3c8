using EfCoreExtensions.EntityConfigurationsBase;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Hotel.Api.Infrastructure.EntityConfigurations;

public class ApiHotelHositoryEntityTypeConfiguration : TenantBaseConfiguration<ApiHotelHository>, IEntityTypeConfiguration<ApiHotelHository>
{
    public void Configure(EntityTypeBuilder<ApiHotelHository> builder)
    {
        base.ConfigureBase(builder);

        builder.Property(s => s.OperatorId)
                 .HasColumnType("bigint");

        builder.Property(s => s.TotalAddQuantity)
                 .HasColumnType("int");

        builder.Property(s => s.AddedQuantity)
                 .HasColumnType("int");

        builder.Property(s => s.Name)
                 .HasColumnType("varchar(50)")
                 .IsRequired(false);

        builder.Property(s => s.Code)
                 .HasColumnType("int")
                 .IsRequired(false);

        builder.Property(s => s.CodeType)
                 .HasColumnType("tinyint")
                 .IsRequired(false);

        builder.Property(s => s.PageIndex)
                 .HasColumnType("int");

        builder.Property(s => s.PageCount)
                 .HasColumnType("int");

        builder.Property(s => s.AddApiHotelStatus)
                 .HasColumnType("tinyint");

    }
}
