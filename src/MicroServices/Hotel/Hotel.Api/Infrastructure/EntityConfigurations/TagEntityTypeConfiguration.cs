using Contracts.Common.Hotel.Enums;
using Contracts.Common.Scenic.Enums;
using EfCoreExtensions.EntityConfigurationsBase;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Hotel.Api.Infrastructure.EntityConfigurations;

public class TagEntityTypeConfiguration : TenantBaseConfiguration<Tag>, IEntityTypeConfiguration<Tag>
{
    public void Configure(EntityTypeBuilder<Tag> builder)
    {
        base.ConfigureBase(builder);

        builder.Property(t => t.Name)
            .HasColumnType("varchar(20)");

        builder.Property(t => t.Describe)
            .HasColumnType("varchar(500)");

        builder.Property(t => t.CreateTime)
            .HasColumnType("datetime");

        builder.Property(s => s.Sort)
            .HasColumnType("int");

        builder.Property(t => t.EnName)
            .HasColumnType("varchar(50)");

        builder.Property(t => t.ShowPageTypes)
            .HasColumnType("int");

        builder.HasIndex(t => new { t.TenantId, t.Name }).IsUnique();
    }
}