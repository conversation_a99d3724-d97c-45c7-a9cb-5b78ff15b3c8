using EfCoreExtensions.EntityConfigurationsBase;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Hotel.Api.Infrastructure.EntityConfigurations;

public class HotelCombinationSkuEntityTypeConfiguration : TenantBaseConfiguration<HotelCombinationSku>, IEntityTypeConfiguration<HotelCombinationSku>
{
    public void Configure(EntityTypeBuilder<HotelCombinationSku> builder)
    {
        base.ConfigureBase(builder);

        builder.Property(x => x.HotelCombinationId)
            .HasColumnType("bigint");

        builder.Property(x => x.HotelId)
            .HasColumnType("bigint");

        builder.Property(x => x.HotelZHName)
            .HasColumnType("varchar(100)");

        builder.Property(x => x.HotelENName)
            .HasColumnType("varchar(200)");

        builder.Property(x => x.HotelRoomId)
            .HasColumnType("bigint");

        builder.Property(x => x.HotelRoomZHName)
            .HasColumnType("varchar(100)");

        builder.Property(x => x.HotelRoomENName)
            .HasColumnType("varchar(100)");

        builder.Property(x => x.PriceStrategyId)
                .HasColumnType("varchar(100)");

        builder.Property(x => x.IsDirect)
                .HasColumnType("tinyint(1)");

        builder.Property(x => x.Tag)
                .HasColumnType("int");

        builder.Property(x => x.PriceStrategyName)
            .HasColumnType("varchar(200)");

        builder.Property(x => x.Nights)
            .HasColumnType("int");

        builder.Property(x => x.NumberOfRoom)
            .HasColumnType("int");

        builder.Property(x => x.MinAdvHours)
            .HasColumnType("int");

        builder.Property(x => x.MinDays)
            .HasColumnType("int");

        builder.Property(x => x.MaxDays)
            .HasColumnType("int");

        builder.Property(x => x.MaxOccupancy)
            .HasColumnType("int");

        builder.Property(x => x.CreateTime)
            .HasColumnType("datetime");
        builder.Property(x => x.UpdateTime)
            .HasColumnType("datetime");

        builder.Property(x => x.Name)
            .HasColumnType("varchar(200)");

        builder.Property(x => x.DeletionTime)
            .HasColumnType("datetime");
    }
}
