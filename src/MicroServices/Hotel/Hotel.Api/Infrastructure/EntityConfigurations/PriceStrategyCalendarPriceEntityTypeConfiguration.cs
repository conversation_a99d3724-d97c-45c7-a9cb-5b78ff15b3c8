using EfCoreExtensions.EntityConfigurationsBase;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Hotel.Api.Infrastructure.EntityConfigurations
{
    public class PriceStrategyCalendarPriceEntityTypeConfiguration : TenantBaseConfiguration<Model.PriceStrategyCalendarPrice>, IEntityTypeConfiguration<Model.PriceStrategyCalendarPrice>
    {
        public void Configure(EntityTypeBuilder<Model.PriceStrategyCalendarPrice> builder)
        {
            base.ConfigureBase(builder);

            builder.Property(s => s.HotelId)
                .HasColumnType("bigint");

            builder.Property(s => s.PriceStrategyId)
                .HasColumnType("bigint");

            builder.Property(s => s.Date)
                .HasColumnType("datetime");

            builder.Property(s => s.CostPrice)
                .HasColumnType("decimal(18,2)");

            builder.Property(s => s.SalePrice)
                .HasColumnType("decimal(18,2)");

            builder.Property(s => s.CreateTime)
                .HasColumnType("datetime");

            //索引
            builder.HasIndex(s => s.HotelId);
            builder.HasIndex(s => s.PriceStrategyId);
            builder.HasIndex(s => new { s.TenantId, s.PriceStrategyId, s.Date }).IsUnique();
        }
    }
}
