using Contracts.Common.Resource.Enums;
using Newtonsoft.Json;

namespace Contracts.Common.Resource.DTOs.FileResource;

public class ParseAndUploadInput
{
    /// <summary>
    /// 租户id
    /// </summary>
    public long TenantId { get; set; }

    /// <summary>
    /// 是否pdf转单张图片
    /// </summary>
    public bool IsPdfToSingleImage { get; set; } = false;

    /// <summary>
    /// 是否图片转pdf
    /// </summary>
    public bool IsOneImageToPdf { get; set; }  = false;

    /// <summary>
    /// 是否默认异步发布
    /// </summary>
    public bool IsPublishAsync { get; set; } = true;

    /// <summary>
    /// oss对象公开读权限
    /// </summary>
    public bool OssObjectPublicReadAcl { get; set; } = false;
    
    /// <summary>
    /// 数据流
    /// </summary>
    public List<ParseStream> Streams { get; set; }
}

public class ParseStream
{
    /// <summary>
    /// 数据流
    /// </summary>
    [JsonConverter(typeof(MemoryStreamJsonConverter))]
    public Stream Stream { get; set; }

    /// <summary>
    /// 文件类型
    /// </summary>
    public FileType FileType { get; set; }
}

public class MemoryStreamJsonConverter : JsonConverter
{
    public override bool CanConvert(Type objectType)
    {
        return typeof(MemoryStream).IsAssignableFrom(objectType);
    }

    public override object ReadJson(JsonReader reader, Type objectType, object existingValue, JsonSerializer serializer)
    {
        var bytes = serializer.Deserialize<byte[]>(reader);
        return bytes != null ? new MemoryStream(bytes) : new MemoryStream();
    }

    public override void WriteJson(JsonWriter writer, object value, JsonSerializer serializer)
    {
        var bytes = ((MemoryStream)value).ToArray();
        serializer.Serialize(writer, bytes);
    }
}

