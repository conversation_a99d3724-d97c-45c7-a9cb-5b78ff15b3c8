using Contracts.Common.Tenant.Enums;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Contracts.Common.Resource.DTOs.ThirdHotelPricestrategy;
public class ThirdHotelPricestrategyOutput
{
    /// <summary>
    /// 资源酒店id
    /// </summary>
    public long ResourceHotelId { get; set; }

    /// <summary>
    /// 第三方平台 1-汇智
    /// </summary>
    public SupplierApiType SupplierApiType { get; set; }

    /// <summary>
    /// 第三方平台价格策略id
    /// </summary>
    public string PriceStrategyId { get; set; }

    /// <summary>
    /// 价格策略中文名称
    /// </summary>
    public string ZHName { get; set; }

    /// <summary>
    /// 价格策略英文名称
    /// </summary>
    public string? ENName { get; set; }

    /// <summary>
    /// 是否有效
    /// </summary>
    public bool Enabled { get; set; }
}
