using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Contracts.Common.Resource.DTOs.HopHotel;
public class SyncHotelInfoOutput
{
    /// <summary>
    /// 本次执行最大的HOP Hid
    /// </summary>
    public int? MaxId { get; set; }

    /// <summary>
    /// 本次执行数量
    /// </summary>
    public int Count { get; set; }

    /// <summary>
    /// 是否已结束
    /// </summary>
    public bool IsFinished { get; set; }
}