using Contracts.Common.Resource.Enums;

namespace Contracts.Common.Resource.DTOs.Hotel;

public class GetSimpleInfoOutput
{
    public long Id { get; set; }

    /// <summary>
    /// 中文名称
    /// </summary>
    public string ZHName { get; set; }

    /// <summary>
    /// 英文名称
    /// </summary>
    public string? ENName { get; set; }
    public string Address { get; set; }

    public string EnAddress { get; set; }

    /// <summary>
    /// 联系电话
    /// </summary>
    public string? Telephone { get; set; }

    /// <summary>
    /// 经度
    /// </summary>
    public double? Longitude { get; set; }

    /// <summary>
    /// 纬度
    /// </summary>
    public double? Latitude { get; set; }
    
    /// <summary>
    /// 坐标系默认BD09
    /// </summary>
    public CoordinateType CoordinateType { get; set; }  = CoordinateType.BD09;

    /// <summary>
    /// 首图
    /// </summary>
    public string FirstPhoto { get; set; }
    
    /// <summary>
    /// 基础设施列表
    /// </summary>
    public List<long> FacilitiesIds { get; set; }
}