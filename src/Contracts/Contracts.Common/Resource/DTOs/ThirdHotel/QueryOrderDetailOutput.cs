using Contracts.Common.Hotel.Enums;
using Contracts.Common.Resource.Enums;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Contracts.Common.Resource.DTOs.ThirdHotel;
public class QueryOrderDetailOutput
{
    /// <summary>
    /// 第三方酒店id
    /// </summary>
    public string HotelId { get; set; }

    /// <summary>
    /// 第三方房型id
    /// </summary>
    public string RoomId { get; set; }

    /// <summary>
    /// 第三方报价计划id
    /// </summary>
    public string PricestrategyId { get; set; }

    /// <summary>
    /// 供应商订单id
    /// </summary>
    public string SupplierOrderId { get; set; }

    /// <summary>
    /// 订单状态
    /// </summary>
    public SupplierApiOrderStatus Status { get; set; }

    /// <summary>
    /// 入住日期
    /// </summary>
    public DateTime CheckIn { get; set; }

    /// <summary>
    /// 离店日期
    /// </summary>
    public DateTime CheckOut { get; set; }

    /// <summary>
    /// 房间数
    /// </summary>
    public int RoomNum { get; set; }

    /// <summary>
    /// 采购总价
    /// </summary>
    public decimal TotalPrice { get; set; }

    /// <summary>
    /// 入住人
    /// </summary>
    public IEnumerable<HotelOrderGuestDto> Guests { get; set; }

    /// <summary>
    /// 取消政策
    /// </summary>
    [Obsolete]
    public ThirdPriceStrategyCancelRuleOutput? CancelRule => CancelRules?.FirstOrDefault() ?? new ThirdPriceStrategyCancelRuleOutput { CancelRulesType = CancelRulesType.CannotCancel };

    public List<ThirdPriceStrategyCancelRuleOutput>? CancelRules { get; set; }

    /// <summary>
    /// 含早数
    /// </summary>
    [Obsolete]
    public int PriceStrategyNumberOfBreakfast { get; set; }

    /// <summary>
    /// 餐食类型
    /// </summary>
    public BoardCodeType? BoardCodeType { get; set; }

    /// <summary>
    /// 餐食数量
    /// </summary>
    public int BoardCount { get; set; }

    /// <summary>
    /// 确认号
    /// </summary>
    public string? ConfirmCode { get; set; }
}