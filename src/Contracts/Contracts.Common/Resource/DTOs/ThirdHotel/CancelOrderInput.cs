using Contracts.Common.Tenant.Enums;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Contracts.Common.Resource.DTOs.ThirdHotel;
public class CancelOrderInput
{
    public long TenantId { get; set; }

    /// <summary>
    /// 指定第三方平台
    /// </summary>
    public SupplierApiType SupplierApiType { get; set; }

    /// <summary>
    /// 主单id
    /// </summary>
    public long? BaseOrderId { get; set; }

    /// <summary>
    /// 供应商订单id
    /// </summary>
    public string? SupplierOrderId { get; set; }
}