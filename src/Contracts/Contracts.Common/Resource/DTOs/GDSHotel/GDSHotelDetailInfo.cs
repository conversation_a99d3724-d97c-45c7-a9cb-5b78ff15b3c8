using Contracts.Common.Resource.Enums;
using System.Text.Json.Serialization;
using NetTopologySuite.Geometries;

namespace Contracts.Common.Resource.DTOs.GDSHotel;
public class GDSHotelDetailInfo
{
    public long Id { get; set; }
    /// <summary>
    /// 酒店类型
    /// </summary>
    public Contracts.Common.Hotel.Enums.HotelType HotelType { get; set; } = Contracts.Common.Hotel.Enums.HotelType.Hotel;

    /// <summary>
    /// 中文名称
    /// </summary>
    public string ZHName { get; set; }

    /// <summary>
    /// 英文名称
    /// </summary>
    public string? ENName { get; set; }

    /// <summary>
    /// 国家
    /// </summary>
    public int CountryCode { get; set; }

    public string CountryName { get; set; }

    public string EnCountryName { get; set; }

    /// <summary>
    /// 省
    /// </summary>
    public int ProvinceCode { get; set; }

    public string ProvinceName { get; set; }

    public string EnProvinceName { get; set; }

    /// <summary>
    /// 市
    /// </summary>
    public int CityCode { get; set; }

    public string CityName { get; set; }

    public string EnCityName { get; set; }

    /// <summary>
    /// 区
    /// </summary>
    public int DistrictCode { get; set; }

    public string? DistrictName { get; set; }

    /// <summary>
    /// 详细地址
    /// </summary>
    public string Address { get; set; }

    /// <summary>
    /// 英文地址
    /// </summary>
    public string? ENAddress { get; set; }

    /// <summary>
    /// 经度
    /// </summary>
    public double? Longitude { get; set; }

    /// <summary>
    /// 纬度
    /// </summary>
    public double? Latitude { get; set; }

    /// <summary>
    /// 经纬度坐标
    /// </summary>
    [JsonIgnore]
    public Point Location { get; private set; }

    /// <summary>
    /// 经纬度坐标类型
    /// </summary>
    public CoordinateType CoordinateType { get; set; }

    /// <summary>
    /// 设置经纬度
    /// </summary>
    /// <param name="lon">经度</param>
    /// <param name="lat">纬度</param>
    public void SetLocation(double lon, double lat)
    {
        //错误的地理坐标
        if (Math.Abs(lon) > 180)
            throw new ArgumentOutOfRangeException(nameof(lon));
        if (Math.Abs(lat) > 90)
            throw new ArgumentOutOfRangeException(nameof(lat));

        var geometryFactory = NetTopologySuite.NtsGeometryServices.Instance.CreateGeometryFactory(srid: 4326);
        Location = geometryFactory.CreatePoint(new Coordinate(lon, lat));
    }

    /// <summary>
    /// 星级
    /// </summary>
    public decimal? StarLevel { get; set; }

    /// <summary>
    /// 开业日期
    /// </summary>
    public DateTime OpeningDate { get; set; }

    /// <summary>
    /// 最近装修日期
    /// </summary>
    public DateTime? DecorateDate { get; set; }

    /// <summary>
    /// 每日营业时间 - 开始
    /// </summary>
    public TimeSpan ServiceTimeBegin { get; set; }

    /// <summary>
    /// 每日营业时间 - 结束
    /// </summary>
    public TimeSpan ServiceTimeEnd { get; set; }

    /// <summary>
    /// 营业结束时间是否次日
    /// </summary>
    public bool ServiceEndtimeInNextDay { get; set; }

    /// <summary>
    /// 楼层数
    /// </summary>
    public int Floors { get; set; }

    /// <summary>
    /// 房间数 
    /// </summary>
    public int Rooms { get; set; }

    /// <summary>
    /// 联系电话
    /// </summary>
    public string? Telephone { get; set; }

    /// <summary>
    /// 联系传真
    /// </summary>
    public string? Telefax { get; set; }

    /// <summary>
    /// 简介
    /// </summary>
    public string? Intro { get; set; }

    /// <summary>
    /// 英文简介
    /// </summary>
    public string? ENIntro { get; set; }

    /// <summary>
    /// 周边设施
    /// </summary>
    public string? SurroundingFacilities { get; set; }

    /// <summary>
    /// 英文周边设施
    /// </summary>
    public string? ENSurroundingFacilities { get; set; }

    /// <summary>
    /// 入住政策
    /// </summary>
    public string? CheckinPolicy { get; set; }

    /// <summary>
    /// 英文入住政策
    /// </summary>
    public string? ENCheckinPolicy { get; set; }

    /// <summary>
    /// 注意事项
    /// </summary>
    public string? ImportantNotices { get; set; }

    /// <summary>
    /// 是否上架
    /// </summary>
    public bool Enabled { get; set; }

    public DateTime CreateTime { get; set; } = DateTime.Now;

    public DateTime? UpdateTime { get; set; } = DateTime.Now;

    /// <summary>
    /// 无烟客房/楼层
    /// </summary>
    public string? NonSmokingRoomsOrFloors { get; set; }

    /// <summary>
    /// 是否禁止更新 默认false
    /// </summary>
    public bool UpdateDisabled { get; set; } = false;

    /// <summary>
    /// 发单邮箱
    /// </summary>
    public string? ReceiptEmail { get; set; }

    /// <summary>
    /// 酒店品牌
    /// </summary>
    public string? Brand { get; set; }

    /// <summary>
    /// 官方网址
    /// </summary>
    public string? OfficialWebsite { get; set; }

    /// <summary>
    /// 是否置顶
    /// </summary>
    public bool OnTop { get; set; }

    /// <summary>
    /// 品牌Code
    /// </summary>
    public string? BrandCode { get; set; }

    /// <summary>
    /// 品牌英文名
    /// </summary>
    public string? ENBrand { get; set; }

    /// <summary>
    /// 集团Code
    /// </summary>
    public string? ChainCode { get; set; }

    /// <summary>
    /// 集团英文名
    /// </summary>
    public string? ENChainName { get; set; }

    /// <summary>
    /// 集团中文名
    /// </summary>
    public string? ChainName { get; set; }

    /// <summary>
    /// 第三方GDS酒店id
    /// </summary>
    public long? ThirdHotelId { get; set; }

    public bool IsExclusivePrivileges { 
        get { 
            return ExclusivePrivileges.Any();
        } 
    }

    /// <summary>
    /// 权益
    /// </summary>
    public List<GDSHotelExclusivePrivilegeOutput> ExclusivePrivileges { get; set; } = new();

    public GDSSabreHotelCodeType GDSSabreHotelCodeType { get; set; }
}
