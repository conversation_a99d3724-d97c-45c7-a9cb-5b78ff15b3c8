using Contracts.Common.Notify.Enums;

namespace Contracts.Common.Notify.DTOs.CustomerNotify
{
    public class AddCustomerNotifyLibraryInput
    {
        public NotifyChannel NotifyChannel { get; set; }
        public NotifyEventType NotifyEventType { get; set; }

        public NotifyEventSubType NotifyEventSubType { get; set; }

        public string WechatTemplateNo { get; set; }

        public string WechatContentExample { get; set; }

        public string SmsTemplateNo { get; set; }

        public string SmsContentExample { get; set; }
        public string EmailTemplateTitle { get; set; }
        public string EmailTemplateContent { get; set; }
        /// <summary>
        /// 关键词
        /// </summary>
        public string Keywords { get; set; }
    }
}
