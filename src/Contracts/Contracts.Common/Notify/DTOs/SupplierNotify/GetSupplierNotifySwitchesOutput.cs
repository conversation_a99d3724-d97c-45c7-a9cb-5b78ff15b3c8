using Contracts.Common.Notify.Enums;

namespace Contracts.Common.Notify.DTOs.SupplierNotify
{
    public class GetSupplierNotifySwitchesOutput
    {
        public NotifyEventType NotifyEventType { get; set; }
        public List<SupplierNotifyEventSubItem> Items { get; set; }
    }

    public class SupplierNotifyEventSubItem
    {
        public long Id { get; set; }
        public NotifyEventSubType NotifyEventSubType { get; set; }
        public string WechatTemplateNo { get; set; }
        public string WechatTemplateCode { get; set; }
        public string WechatContentExample { get; set; }
        public bool IsOpen { get; set; }
        public int Sort { get; set; }
    }
}
