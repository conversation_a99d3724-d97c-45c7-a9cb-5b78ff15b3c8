using Contracts.Common.Notify.Enums;

namespace Contracts.Common.Notify.DTOs.StaffNotify
{
    public class GetStaffNotifyLibraryOutput
    {
        public long Id { get; set; }

        public NotifyEventType NotifyEventType { get; set; }

        public NotifyEventSubType NotifyEventSubType { get; set; }

        public string WechatTemplateNo { get; set; }

        public string WechatTemplateCode { get; set; }

        public string WechatContentExample { get; set; }

        /// <summary>
        /// 邮件模板
        /// </summary>
        public string? EmailTemplateTitle { get; set; }

        /// <summary>
        /// 邮件模板
        /// </summary>
        public string? EmailTemplateContent { get; set; }
    }
}
