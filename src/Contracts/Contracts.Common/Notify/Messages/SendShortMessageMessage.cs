namespace Contracts.Common.Notify.Messages
{
    public class SendShortMessageMessage
    {
        public long TenantId { get; set; }

        /// <summary>
        /// 短信模板Id
        /// <para>templateId: 181304, content: {1} (手机动态验证码，10分钟内有效，请完成验证)，如非本人操作，请忽略本短信。</para>
        /// </summary>
        public string TemplateId { get; set; }

        public string PhoneNumber { get; set; }

        /// <summary>
        /// 按顺序填充content中的变量{}
        /// </summary>
        public IEnumerable<ShortMessageVariable> Variables { get; set; }
    }

    public class ShortMessageVariable
    {
        public ShortMessageVariable(string value, ShortMessageValueType valueType = ShortMessageValueType.Text)
        {
            ValueType = valueType;
            Value = value;
        }

        /// <summary>
        /// 类型
        /// </summary>
        public ShortMessageValueType ValueType { get; set; } = ShortMessageValueType.Text;

        /// <summary>
        /// 值
        /// </summary>
        public string Value { get; set; }
    }

    public enum ShortMessageValueType
    {
        /// <summary>
        /// 文本
        /// </summary>
        Text,

        /// <summary>
        /// 链接
        /// </summary>
        Link
    }
}
