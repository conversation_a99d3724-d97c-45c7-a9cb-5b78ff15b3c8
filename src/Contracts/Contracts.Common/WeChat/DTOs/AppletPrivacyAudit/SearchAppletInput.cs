using Contracts.Common.WeChat.Enums;

namespace Contracts.Common.WeChat.DTOs.AppletPrivacyAudit;

public class SearchAppletInput : PagingInput
{
    /// <summary>
    /// 主体名称
    /// </summary>
    public string PrincipalName { get; set; }

    /// <summary>
    /// 隐私协议审核状态
    /// </summary>
    public WechatAppletPrivacyAuditStatus? PrivacyAuditStatus { get; set; }

    /// <summary>
    /// 地理位置审核状态
    /// </summary>
    public WechatAppletPrivacyAuditStatus? LocationAuditStatus { get; set; }

    /// <summary>
    /// 小程序域名配置审核状态
    /// </summary>
    public WechatAppletPrivacyAuditStatus? DomainAuditStatus { get; set; }
}