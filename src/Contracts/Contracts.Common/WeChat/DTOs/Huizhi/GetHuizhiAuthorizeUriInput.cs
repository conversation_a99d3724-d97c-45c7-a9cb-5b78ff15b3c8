namespace Contracts.Common.WeChat.DTOs.Huizhi
{
    public class GetHuizhiAuthorizeUriInput
    {
        /// <summary>
        /// 重定向地址 微信配置的授权域名地址
        /// </summary>
        public string RedirectUrl { get; set; }

        /// <summary>
        /// snsapi_base 和 snsapi_userinfo
        /// </summary>
        public string Scope { get; set; } = "snsapi_base";

        /// <summary>
        /// 重定向后会带上 state 参数，开发者可以填写任意参数值，最多 128 字节
        /// </summary>
        public string State { get; set; }
    }
}
