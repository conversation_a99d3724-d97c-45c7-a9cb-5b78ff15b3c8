namespace Contracts.Common.WeChat.DTOs
{
    public class GetWechatMpConfigOutput
    {
        /// <summary>
        /// 配置id
        /// </summary>
        public long Id { get; set; }

        /// <summary>
        /// APPID
        /// </summary>
        public string AppId { get; set; }

        /// <summary>
        /// APPSecret
        /// </summary>
        public string AppSecret { get; set; }

        /// <summary>
        /// 当前步骤 0-填写配置 1-公众号授权 2-配置完成
        /// </summary>
        public int Step { get; set; }

        /// <summary>
        /// 是否授权
        /// </summary>
        public bool IsManaged { get; set; }

        /// <summary>
        /// 微信授权二维码链接(非托管为空)
        /// </summary>
        public string AuthorizationCodeUrl { get; set; }

        public ManagedInfo ManagedInfo { get; set; }
    }


    /// <summary>
    /// 非托管信息
    /// </summary>
    public class UnManagedInfo
    {
        /// <summary>
        /// mp.txt地址
        /// </summary>
        public string MpTxtPath { get; set; }

        /// <summary>
        /// 白名单
        /// </summary>
        public string WhiteList { get; set; }

        /// <summary>
        /// 安全域名
        /// </summary>
        public string SecureDomain { get; set; }

    }



    /// <summary>
    /// 托管信息
    /// </summary>
    public class ManagedInfo
    {
        /// <summary>
        /// 昵称
        /// </summary>
        public string NickName { get; set; }

        /// <summary>
        /// 头像
        /// </summary>
        public string HeadImg { get; set; }

        /// <summary>
        /// 主体名称
        /// </summary>
        public string PrincipalName { get; set; }

        /// <summary>
        /// 主体二维码图片的 URL
        /// </summary>
        public string QrcodeUrl { get; set; }

        /// <summary>
        /// 得到授权码时间
        /// </summary>
        public DateTime AuthorizationCodeTime { get; set; }
    }




}
