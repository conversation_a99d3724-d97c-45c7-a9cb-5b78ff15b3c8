namespace Contracts.Common.Tenant.Enums;

public enum CreditBusinessType
{
    /// <summary>
    /// 设置授信额度
    /// </summary>
    SetCreditLine = 1,

    /// <summary>
    /// 订单支付
    /// </summary>
    OrderPay = 2,

    /// <summary>
    /// 修改订单
    /// </summary>
    OrderEditPay = 3,

    /// <summary>
    /// 订单退订
    /// </summary>
    OrderCancel = 4,

    /// <summary>
    /// 预约加价
    /// </summary>
    ReservationOrderPay = 5,

    /// <summary>
    /// 抵充单
    /// </summary>
    OffsetOrder = 6,

    /// <summary>
    /// 预约拒单
    /// </summary>
    ReservationOrderRefuse = 7,

    /// <summary>
    /// 收款单结算
    /// </summary>
    ReceiptSettlement = 8,

    /// <summary>
    /// 额度充值
    /// </summary>
    CreditCharge = 9,
}