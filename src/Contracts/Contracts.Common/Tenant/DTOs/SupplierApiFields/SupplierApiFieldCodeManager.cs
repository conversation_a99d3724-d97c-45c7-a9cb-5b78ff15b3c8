using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Contracts.Common.Tenant.DTOs.SupplierApiFields;
public class SupplierApiFieldCodeManager
{
    public static class Common
    {
        /// <summary>
        /// 导图
        /// </summary>
        public const string GuideUrl = "GuideUrl";
    }


    /// <summary>
    /// 汇智酒店
    /// </summary>
    public static class HopCode
    {
        public const string HopKey = "Hop_Key";

        public const string HopSecret = "Hop_Secret";
    }

    /// <summary>
    /// GDS
    /// </summary>
    public static class GDSCode
    {
        public const string GDSApiID = "GDS_ApiID";

        public const string GDSPCC = "GDS_PCC";

        public const string GDSPassword = "GDS_Password";

        public const string GDSDomain = "GDS_Domain";

        public const string GDSHost = "GDS_Host";

        public const string GDSAddressLine = "GDS_AddressLine";

        public const string GDSCityName = "GDS_CityName";

        public const string GDSCountryCode = "GDS_CountryCode";

        public const string GDSPostalCode = "GDS_PostalCode";

        public const string GDSStateCode = "GDS_StateCode";

        public const string GDSStreetNmbr = "GDS_StreetNmbr";

    }

    /// <summary>
    /// 客路
    /// </summary>
    public static class GuestRoutePlayCode
    {
        public const string GuestRoutePlayKey = "GuestRoutePlay_Key";

        public const string GuestRoutePlaySecret = "GuestRoutePlay_Secret";

    }

    /// <summary>
    /// Globaltix
    /// </summary>
    public static class GlobaltixCode
    {
        public const string GlobaltixUserName = "Globaltix_UserName";

        public const string GlobaltixPassword = "Globaltix_Password";

        public const string GlobaltixNotificationSecret = "Globaltix_NotificationSecret";

    }

    /// <summary>
    /// TreepFlyingEarth
    /// </summary>
    public static class TreepFlyingEarthCode
    {
        public const string TreepFlyingEarthKey = "TreepFlyingEarth_Key";

        public const string TreepFlyingEarthSecret = "TreepFlyingEarth_Secret";

    }

    /// <summary>
    /// Exoz
    /// </summary>
    public static class ExozCode
    {
        public const string ExozUserName = "Exoz_UserName";

        public const string ExozPassword = "Exoz_Password";

        public const string ExozPartnerCode = "Exoz_PartnerCode";

        public const string ExozCatalogueApiHost = "Exoz_CatalogueApiHost";

    }

    /// <summary>
    /// CA
    /// </summary>
    public static class CACode
    {
        public const string CAKey = "CA_Key";

        public const string CASecret = "CA_Secret";

    }

    /// <summary>
    /// Mozio
    /// </summary>
    public static class MozioCode
    {
        public const string MozioApiKey = "Mozio_ApiKey";

    }

    /// <summary>
    /// Sctt
    /// </summary>
    public static class ScttCode
    {
        public const string ScttMerchantId = "Sctt_MerchantId";

    }

    /// <summary>
    /// Eyounz
    /// </summary>
    public static class EyounzCode
    {
        public const string EyounzApiKey = "Eyounz_ApiKey";

    }

    /// <summary>
    /// PingAnHopeInsurance
    /// </summary>
    public static class PingAnHopeInsuranceCode
    {
        public const string PAHInsuranceUserName = "PAHInsurance_UserName";

        public const string PAHInsurancePassword = "PAHInsurance_Password";
    }


    /// <summary>
    /// CaoYueTianKong
    /// </summary>
    public static class CaoYueTianKong
    {
        public const string CaoYueTianKongOutAccountId = "CaoYueTianKong_OutAccountId";

    }

    /// <summary>
    /// YouXia
    /// </summary>
    public static class YouXiaCode
    {
        public const string YouXiaHost = "YouXia_Host";

        public const string YouXiaKey = "YouXia_AppKey";

        public const string YouXiaSecret = "YouXia_AppSecret";

    }
    
    /// <summary>
    /// ToursGroup
    /// </summary>
    public static class ToursGroup
    {
        public const string ToursGroupApiKey = "ToursGroup_ApiKey";
    }
}
