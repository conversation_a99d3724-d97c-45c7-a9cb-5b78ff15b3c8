using Contracts.Common.Reflection;

namespace Contracts.Common.Tenant.DTOs.Agency;
public class UpdateAgencyPriceGroupInput
{
    public long Id { get; set; }

    /// <summary>
    /// 分销商价格分组id
    /// </summary>
    public long PriceGroupId { get; set; }

    /// <summary>
    /// 分销商价格分组名称
    /// </summary>
    [PropertyMsg(Name = "分销商价格分组")]
    public string PriceGroupName { get; set; }

    /// <summary>
    /// 销售BD
    /// </summary>
    public long? SalespersonId { get; set; }

    /// <summary>
    /// 销售BD名称
    /// </summary>
    [PropertyMsg(Name = "销售BD")]
    public string SalespersonName { get; set; }

    /// <summary>
    /// 公司全称 
    /// </summary>
    [PropertyMsg(Name = "公司全称")]
    public string FullName { get; set; }

    /// <summary>
    /// 是否自定义价格分组
    /// </summary>
    [PropertyMsg(Name = "是否自定义价格分组")]
    public bool IsCustom { get; set; }

    /// <summary>
    /// KA业务定义（暂定） = 3个月内有机会或已单月10万GMV
    /// </summary>
    [PropertyMsg(Name = "KA业务定义")]
    public bool IsKA { get; set; }

    /// <summary>
    /// 是否会员
    /// </summary>
    public bool IsVip { get; set; }
}
