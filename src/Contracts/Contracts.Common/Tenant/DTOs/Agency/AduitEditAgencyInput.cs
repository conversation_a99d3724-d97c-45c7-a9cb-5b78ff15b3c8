using Contracts.Common.Reflection;
using Contracts.Common.Tenant.Enums;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Contracts.Common.Tenant.DTOs.Agency;
public class AduitEditAgencyInput
{
    public long Id { get; set; }

    /// <summary>
    /// 分销商类型
    /// </summary>
    public AgencyType AgencyType { get; set; }

    /// <summary>
    /// 公司全称 
    /// </summary>
    [PropertyMsg(Name = "名称")]
    public string FullName { get; set; }

    /// <summary>
    /// 国家编码
    /// </summary>
    public int? CountryCode { get; set; }

    /// <summary>
    /// 国家名字
    /// </summary>
    [PropertyMsg(Name = "国家")]
    public string? CountryName { get; set; }

    /// <summary>
    /// 省编码
    /// </summary>
    public int? ProvinceCode { get; set; }

    /// <summary>
    /// 省名字
    /// </summary>
    [PropertyMsg(Name = "省")]
    public string? ProvinceName { get; set; }

    /// <summary>
    /// 城市编码
    /// </summary>
    public int? CityCode { get; set; }

    /// <summary>
    /// 城市名字
    /// </summary>
    [PropertyMsg(Name = "城市")]
    public string? CityName { get; set; }

    /// <summary>
    /// 公司地址
    /// </summary>
    [PropertyMsg(Name = "公司地址")]
    public string? Address { get; set; }

    /// <summary>
    /// 联系人姓名
    /// </summary>
    [PropertyMsg(Name = "联系人")]
    public string Contact { get; set; }

    /// <summary>
    /// 联系人电话
    /// </summary>
    [PropertyMsg(Name = "联系电话", IsSensitive = true, SensitiveType = SensitiveDataType.Phone)]
    public string ContactNumber { get; set; }

    /// <summary>
    /// 联系人国家区号
    /// </summary>
    public string ContactCountryDialCode { get; set; }

    /// <summary>
    /// 业务体量
    /// </summary>
    [PropertyMsg(Name = "业务体量")]
    public int? BusinessVolume { get; set; }

    /// <summary>
    /// 所属行业
    /// </summary>
    [PropertyMsg(Name = "所属行业")]
    public AgencyIndustryType? IndustryInvolved { get; set; }

    /// <summary>
    /// 客户潜在规模
    /// </summary>
    [PropertyMsg(Name = "客户潜在规模")]
    public long CustomerPotentialSize { get; set; }

    /// <summary>
    /// 是否创建分销商用户,使用邮件注册账号
    /// </summary>
    public bool IsCreateAgencyUser { get; set; } = false;

    #region ApiSetting

    /// <summary>
    /// API类型
    /// </summary>
    public AgencyApiType AgencyApiType { get; set; }

    /// <summary>
    /// 飞猪-飞猪店铺名称
    /// </summary>
    public string? FeiZhuShopName { get; set; }

    /// <summary>
    /// 抖音 - account_id
    /// </summary>
    public string AccountId { get; set; }

    public string Code { get; set; }

    public string Account { get; set; }

    public string Password { get; set; }

    #region 携程玩乐配置项
    public string AesKey { get; set; }
    public string AesLv { get; set; }
    #endregion 
    #endregion
}
