using Contracts.Common.Tenant.Enums;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Contracts.Common.Tenant.DTOs.B2BWhiteLabelConfig;
public class B2BWhiteLabelConfigSearchInput
{
    public long AgencyId { get; set; }
}


public class B2BWhiteLabelConfigSearchOutput
{
    /// <summary>
    /// 配置id
    /// </summary>
    public long Id { get; set; }

    /// <summary>
    /// 页面类型
    /// </summary>
    public B2BWhiteLabelPageType PageType { get; set; }

    /// <summary>
    /// 是否启用
    /// </summary>
    public bool Enabled { get; set; }

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreateTime { get; set; }

    /// <summary>
    /// 修改时间
    /// </summary>
    public DateTime? UpdateTime { get; set; }
}
