using Contracts.Common.Tenant.Enums;

namespace Contracts.Common.Tenant.DTOs.B2BIndexPage;
public class AddComponentInput
{
    /// <summary>
    /// 类型
    /// </summary>
    public B2BComponentType B2BComponentType { get; set; }

    /// <summary>
    /// 组件项
    /// </summary>
    public List<B2BIndexPageComponentItemDto> ComponentItems { get; set; }

    /// <summary>
    /// 类型
    /// </summary>
    public B2BIndexPageType B2BIndexPageType { get; set; } = B2BIndexPageType.B2B;
}
