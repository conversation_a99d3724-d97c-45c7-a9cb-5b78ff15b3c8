namespace Contracts.Common.Tenant.DTOs.AgencyPriceGroup;

public class PriceGroupDetailOutput
{
    /// <summary>
    /// 分组id
    /// </summary>
    public long Id { get; set; }
    
    /// <summary>
    /// 分组名称
    /// </summary>
    public string Name { get; set; }

    /// <summary>
    /// 备注
    /// </summary>
    public string? Remark { get; set; }

    /// <summary>
    /// 是否启用状态
    /// </summary>
    public bool Enable { get; set; }

    /// <summary>
    /// 是否默认
    /// </summary>
    public bool Default { get; set; }
}