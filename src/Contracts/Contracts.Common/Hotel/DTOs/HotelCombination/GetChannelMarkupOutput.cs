using Contracts.Common.Hotel.Enums;
using Contracts.Common.Order.Enums;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Contracts.Common.Hotel.DTOs.HotelCombination;

public class GetChannelMarkupOutput
{
    public long? Id { get; set; }

    /// <summary>
    /// 酒店打包产品id
    /// </summary>
    public long HotelCombinationId { get; set; }

    /// <summary>
    /// 规格id
    /// </summary>
    public long SkuId { get; set; }

    /// <summary>
    /// 渠道
    /// </summary>
    public SellingChannels SellingChannel { get; set; }

    /// <summary>
    /// 同步开关
    /// </summary>
    public bool Enabled { get; set; }

    /// <summary>
    /// 加价规则
    /// </summary>
    public List<MarkupRuleDto> MarkupRules { get; set; } = new();
}

public class MarkupRuleDto
{
    public long Id { get; set; }

    public DateTime BeginDate { get; set; }

    public DateTime EndDate { get; set; }

    /// <summary>
    /// 周集合 0-6:周日-周一
    /// </summary>
    public string ValidWeek { get; set; }

    public DayOfWeek[] ValidWeeks
    {
        get
        {
            if (string.IsNullOrWhiteSpace(ValidWeek))
                return Array.Empty<DayOfWeek>();

            return ValidWeek.Split(",").ToList().ConvertAll(x => Enum.Parse<DayOfWeek>(x)).ToArray();
        }
    }

    /// <summary>
    /// 加价基础类型
    /// </summary>
    public MarkupPriceType PriceType { get; set; }

    /// <summary>
    /// 加价方式
    /// </summary>
    public MarkupType MarkupType { get; set; }

    /// <summary>
    /// 值
    /// </summary>
    public decimal Value { get; set; }
}