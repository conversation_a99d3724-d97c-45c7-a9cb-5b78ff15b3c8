using Contracts.Common.Hotel.Enums;
using Contracts.Common.Order.Enums;

namespace Contracts.Common.Hotel.DTOs;
public class ChannelMarkupDto
{
    /// <summary>
    /// 酒店Id
    /// </summary>
    public long HotelId { get; set; }

    /// <summary>
    /// 价格策略Id
    /// </summary>
    public long PriceStrategyId { get; set; }

    /// <summary>
    /// 渠道
    /// </summary>
    public SellingChannels ChannelType { get; set; }

    /// <summary>
    /// 加价基础类型
    /// </summary>
    public MarkupPriceType PriceType { get; set; }

    /// <summary>
    /// 加价方式
    /// </summary>
    public MarkupType MarkupType { get; set; }

    /// <summary>
    /// 值，0.01 表示 1%
    /// </summary>
    public decimal Value { get; set; }

    /// <summary>
    /// 是否上架
    /// </summary>
    public bool Enabled { get; set; }

    public DateTime CreateTime { get; set; } = DateTime.Now;

    public DateTime? UpdateTime { get; set; } = DateTime.Now;
}
