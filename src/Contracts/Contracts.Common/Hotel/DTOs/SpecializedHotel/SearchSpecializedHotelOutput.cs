using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Contracts.Common.Hotel.DTOs.SpecializedHotel;
public class SearchSpecializedHotelOutput
{
    /// <summary>
    /// 专题ID
    /// </summary>
    public long Id { get; set; }
    /// <summary>
    /// 专题名称
    /// </summary>
    public string Name { get; set; }
    /// <summary>
    /// 状态
    /// </summary>
    public bool Enable { get; set; }
    /// <summary>
    /// 优先级
    /// </summary>
    public int Sort { get; set; } = 1;

    /// <summary>
    /// 酒店数量
    /// </summary>
    public int HotelCount { get; set; }
}
