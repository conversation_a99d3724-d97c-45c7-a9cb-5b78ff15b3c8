using Contracts.Common.Hotel.Enums;

namespace Contracts.Common.Hotel.DTOs.PriceStrategy;

public class CancelRule
{
    /// <summary>
    /// 取消规则id
    /// </summary>
    public long CancelRuleId { get; set; }
    
    /// <summary>
    /// 取消策略类型
    /// </summary>
    public CancelRulesType CancelRulesType { get; set; }

    /// <summary>
    /// 入住日前x天
    /// </summary>
    public int BeforeCheckInDays { get; set; }

    /// <summary>
    /// 入住日前x天时间 如14:00
    /// </summary>
    public TimeSpan? BeforeCheckInTime { get; set; }

    /// <summary>
    /// 入住当日 - 时间 如14:00
    /// </summary>
    public TimeSpan CheckInDateTime { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public string CheckInDateTimeStr { get { return CheckInDateTime.ToString(@"hh\:mm"); } }

    /// <summary>
    /// 取消收费类型
    /// </summary>
    public CancelChargeType CancelChargeType { get; set; }

    /// <summary>
    /// 收费值
    /// </summary>
    public int ChargeValue { get; set; }

    /// <summary>
    /// 可退改 退改描述
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    /// 收费价格
    /// </summary>
    public decimal? Amount { get; set; }
}