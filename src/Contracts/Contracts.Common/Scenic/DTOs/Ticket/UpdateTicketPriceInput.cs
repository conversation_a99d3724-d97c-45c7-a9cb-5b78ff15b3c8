using Contracts.Common.Product.Enums;

namespace Contracts.Common.Scenic.DTOs.Ticket
{
    public class UpdateTicketPriceInput
    {
        public long TicketsId { get; set; }

        /// <summary>
        /// 有效期类型
        /// </summary>
        public ProductValidityType ValidityType { get; set; }

        /// <summary>
        /// 有效期 - 起
        /// </summary>
        public DateTime? ValidityBegin { get; set; }

        /// <summary>
        /// 有效期 - 止
        /// </summary>
        public DateTime? ValidityEnd { get; set; }

        /// <summary>
        /// 购买之后x天有效
        /// </summary>
        public int AfterPurchaseDays { get; set; }

        /// <summary>
        /// 成本价
        /// </summary>
        public decimal CostPrice { get; set; }

        /// <summary>
        /// 售价
        /// </summary>
        public decimal? SellingPrice { get; set; }

        /// <summary>
        /// 划线价
        /// </summary>
        public decimal LinePrice { get; set; }

        /// <summary>
        /// 总库存 库存无变更可不传
        /// </summary>
        public int? Stock { get; set; }

        /// <summary>
        /// 飞猪渠道价
        /// </summary>
        public decimal? FeiZhuChannelPrice { get; set; }
        
        #region 产品价格配置信息

        /// <summary>
        /// 产品价格配置- 价格基准类型
        /// </summary>
        public PriceBasisType PriceBasisType { get; set; }

        /// <summary>
        /// 产品价格配置- 价格调整类型
        /// </summary>
        public PriceAdjustmentType PriceAdjustmentType { get; set; }

        /// <summary>
        /// 产品价格配置- 价格调整值
        /// </summary>
        public decimal? PriceAdjustmentValue { get; set; }

        #endregion
    }
}
