using Contracts.Common.Scenic.Enums;

namespace Contracts.Common.Scenic.DTOs;

public class GetScenicAndTicketsInput : PagingInput
{
    public string ScenicSpotName { get; set; }
    
    /// <summary>
    /// 门票名称
    /// </summary>
    public string TicketName { get; set; }

    public ScenicTicketsType TicketsType { get; set; }

    /// <summary>
    /// 是否上下架 (门票)
    /// </summary>
    public bool? Enabled { get; set; }

    /// <summary>
    /// 运营模式
    /// </summary>
    public Common.Hotel.Enums.OperatingModel OperatingModel { get; set; }

    /// <summary>
    /// 供应商id
    /// </summary>
    public long? SupplierId { get; set; }

    /// <summary>
    /// 门票id
    /// </summary>
    public IEnumerable<long> TicketIds { get; set; } = Enumerable.Empty<long>();
    
    /// <summary>
    /// 景区id
    /// </summary>
    public long? ScenicSpotId { get; set; }
    
    /// <summary>
    /// 是否支持补差
    /// <value>[手工发货] - 支持订单补差</value>
    /// </summary>
    public bool? IsCompensation { get; set; }
    
    /// <summary>
    /// 是否B2B售卖
    /// </summary>
    public bool? B2bSellingStatus { get; set; }
    
    /// <summary>
    /// 国家编码
    /// </summary>
    public int? CountryCode { get; set; }

    /// <summary>
    /// 城市编码
    /// </summary>
    public int? CityCode { get; set; }
    
    /// <summary>
    /// 运营人Id
    /// </summary>
    public long? OperatorUserId { get; set; }

    /// <summary>
    /// 运营人助理Id
    /// </summary>
    public long? OperatorAssistantUserId { get; set; }

    /// <summary>
    /// 产品人Id
    /// </summary>
    public long? DevelopUserId { get; set; }
}
