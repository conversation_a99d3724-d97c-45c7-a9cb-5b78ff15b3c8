using System.ComponentModel;

namespace Contracts.Common.Payment.Enums
{
    /// <summary>
    /// 支付类型
    /// </summary>
    public enum PayType
    {
        /// <summary>
        /// 未支付
        /// </summary>
        [Description("未支付")]
        None,

        /// <summary>
        /// 易宝支付
        /// </summary>
        [Description("易宝支付")]
        YeePay = 1,

        /// <summary>
        /// 线下支付
        /// </summary>
        [Description("线下支付")]
        Offline = 2,

        /// <summary>
        /// 用户储值卡支付
        /// </summary>
        [Description("用户储值卡支付")]
        UserStoredValueCardPay = 4,

        /// <summary>
        /// 0元支付
        /// </summary>
        [Description("0元支付")]
        ZeroPay = 8,

        /// <summary>
        /// 分销额度支付
        /// </summary>
        [Description("分销额度支付")]
        AgencyCreditPay = 16,

        /// <summary>
        /// 预收款支付
        /// </summary>
        [Description("预收款支付")]
        AdvancePayment = 32,

        /// <summary>
        /// 信用卡担保
        /// </summary>
        [Description("信用卡担保")]
        CreditCardGuarantee = 64,

        /// <summary>
        /// Onerway 信用卡支付
        /// </summary>
        [Description("Onerway 信用卡支付")]
        Onerway = 128,

        /// <summary>
        /// 易宝支付-海外转账
        /// </summary>
        [Description("易宝支付-海外转账")]
        YeepayCollection = 256,
    }
}
