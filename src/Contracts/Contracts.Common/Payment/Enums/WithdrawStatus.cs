namespace Contracts.Common.Payment.Enums;

public enum WithdrawStatus
{
    /// <summary>
    /// 待处理
    /// </summary>
    PENDING = 0,

    /// <summary>
    /// 请求已接收
    /// </summary>
    REQUEST_RECEIVE = 1,

    /// <summary>
    /// 请求已受理
    /// </summary>
    REQUEST_ACCEPT = 2,

    /// <summary>
    /// 已到账
    /// </summary>
    SUCCESS = 3,

    /// <summary>
    /// 失败
    /// </summary>
    FAIL = 4,

    /// <summary>
    /// 银行处理中
    /// </summary>
    REMITING = 5
}
