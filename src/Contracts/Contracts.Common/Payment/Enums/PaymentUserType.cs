using System.ComponentModel;

namespace Contracts.Common.Payment.Enums
{
    /// <summary>
    /// 收/付款方类型
    /// </summary>
    public enum PaymentUserType
    {
        None = 0,

        /// <summary>
        /// 平台
        /// </summary>
        [Description("平台")]
        Platform = 1,

        /// <summary>
        /// 散客
        /// </summary>
        [Description("散客")]
        Individual = 2,

        /// <summary>
        /// 供应商
        /// </summary>
        [Description("供应商")]
        Supplier = 3,

        /// <summary>
        /// 支付平台 易宝
        /// </summary>
        [Description("支付平台")]
        PaymentPlatform = 4,
    }
}
