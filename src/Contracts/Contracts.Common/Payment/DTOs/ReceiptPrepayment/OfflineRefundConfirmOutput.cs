using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Contracts.Common.Payment.DTOs.ReceiptPrepayment;
public class OfflineRefundConfirmOutput
{
    /// <summary>
    /// 关联充值记录id
    /// </summary>
    public long ChargeId { get; set; }

    /// <summary>
    /// 退款金额
    /// </summary>
    public decimal RefundAmount { get; set; }

    /// <summary>
    /// 币种
    /// </summary>
    public string CurrencyCode { get; set; }
}
