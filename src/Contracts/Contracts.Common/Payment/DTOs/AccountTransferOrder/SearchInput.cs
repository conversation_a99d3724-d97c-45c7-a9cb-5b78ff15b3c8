using Contracts.Common.Payment.Enums;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Contracts.Common.Payment.DTOs.AccountTransferOrder;
public class SearchInput
{
    /// <summary>
    /// 用途类型
    /// </summary>
    public AccountTransferOrderUsageType[]? UsageTypes { get; set; }

    public DateTime? BeginDate { get; set; }

    public DateTime? EndDate { get; set; }

    /// <summary>
    /// 转账状态
    /// REQUEST_RECEIVE:请求已接收(正在处理中, 收到最终结果前请勿重复下单)
    /// SUCCESS:转账成功
    /// FAIL:失败(该笔订单转账失败, 可重新发起转账)
    /// </summary>
    public TransferStatus? TransferStatus { get; set; }
}