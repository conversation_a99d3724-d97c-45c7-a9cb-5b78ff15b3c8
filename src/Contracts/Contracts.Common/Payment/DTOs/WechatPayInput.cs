using Contracts.Common.Payment.Enums;

namespace Contracts.Common.Payment.DTOs;

public class WechatPayInput
{
    /// <summary>
    /// 微信静默登录code
    /// </summary>
    public string Code { get; set; }

    /// <summary>
    /// 支付单类型 1-订单支付 2-预约单支付 可选
    /// </summary>
    public OrderPaymentType OrderPaymentType { get; set; } = OrderPaymentType.OrderPay;

    /// <summary>
    /// 主单id
    /// </summary>
    public long OrderId { get; set; }
}
