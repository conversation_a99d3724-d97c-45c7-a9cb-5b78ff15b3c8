using Contracts.Common.Order.Enums;
using Contracts.Common.Payment.Enums;

namespace Contracts.Common.Payment.Messages;

public class AgencyCreditOrderEditPayRecordResultMessage
{
    public long TenantId { get; set; }

    public OrderType OrderType { get; set; }

    public long OrderId { get; set; }

    /// <summary>
    /// 本次变更金额
    /// </summary>
    public decimal ChangeAmount { get; set; }

    /// <summary>
    /// 订单总额
    /// </summary>
    public decimal OrderAmount { get; set; }

    public bool IsSuccessed { get; set; }

    public string Message { get; set; }
}
