using Contracts.Common.Reflection;

namespace Contracts.Common.User.DTOs
{
    public class AddStaffInput
    {
        /// <summary>
        /// 账号
        /// </summary>
        [PropertyMsg(Name = "账号")]
        public string Account { get; set; }

        /// <summary>
        /// 密码
        /// </summary>
        [PropertyMsg(Name = "密码", IsSensitive = true, SensitiveType = SensitiveDataType.Password)]
        public string Password { get; set; }

        /// <summary>
        /// 姓名
        /// </summary>
        [PropertyMsg(Name = "姓名")]
        public string Name { get; set; }

        /// <summary>
        /// 昵称
        /// </summary>
        [PropertyMsg(Name = "昵称")]
        public string NickName { get; set; }

        /// <summary>
        /// 手机号
        /// </summary>
        [PropertyMsg(Name = "手机号", IsSensitive = true, SensitiveType = SensitiveDataType.Phone)]
        public string PhoneNumber { get; set; }

        /// <summary>
        /// 角色Id
        /// </summary>
        public long RoleId { get; set; }

        /// <summary>
        /// 邮箱
        /// </summary>
        [PropertyMsg(Name = "邮箱", IsSensitive = true, SensitiveType = SensitiveDataType.Email)]
        public string? Email { get; set; }

        /// <summary>
        /// 工号
        /// </summary>
        [PropertyMsg(Name = "工号")]
        public string? JobId { get; set; }

        /// <summary>
        /// 联系二维码
        /// </summary>
        [PropertyMsg(Name = "联系二维码")]
        public string? Qrcode { get; set; }

        /// <summary>
        /// 邮箱密码
        /// </summary>
        [PropertyMsg(Name = "邮箱密码")]
        public string? EmailPassword { get; set; }
    }
}
