using System.ComponentModel;

namespace Contracts.Common.User.Enums;

public enum UserIdCardType
{
    None,
    
    /// <summary>
    /// 身份证
    /// </summary>
    [Description("身份证")]
    IdCard  = 1,
    
    /// <summary>
    /// 护照
    /// </summary>
    [Description("护照")]
    Passport = 2,
    
    /// <summary>
    /// 学生证
    /// </summary>
    [Description("学生证")]
    StudentCard = 3,
    
    /// <summary>
    /// 军人证
    /// </summary>
    [Description("军人证")]
    MilitaryServiceCard = 4,
    
    /// <summary>
    /// 军官证
    /// </summary>
    [Description("军官证")]
    MilitaryOfficerCard = 5,
    
    /// <summary>
    /// 驾驶证
    /// </summary>
    [Description("驾驶证")]
    DriverLicense = 6,
    
    /// <summary>
    /// 回乡证
    /// </summary>
    [Description("回乡证")]
    ReentryPermit = 7,
    
    /// <summary>
    /// 台胞证
    /// </summary>
    [Description("台胞证")]
    TaiwanResidents = 8,
    
    /// <summary>
    /// 警官证
    /// </summary>
    [Description("警官证")]
    PoliceOfficerIdCard = 9,
    
    /// <summary>
    /// 港澳通行证
    /// </summary>
    [Description("港澳通行证")]
    HongKongMacaoPermit = 10,
    
    /// <summary>
    /// 国际海员证
    /// </summary>
    [Description("国际海员证")]
    InternationalSeamanIdCard = 11,
    
    /// <summary>
    /// 台湾通行证
    /// </summary>
    [Description("台湾通行证")]
    TaiwanPass = 12,
    
    /// <summary>
    /// 士兵证
    /// </summary>
    [Description("士兵证")]
    MilitaryEnlistmentCard = 13,
    
    /// <summary>
    /// 外国人永久居留证
    /// </summary>
    [Description("外国人永久居留证")]
    PermanentResidencePermitForForeigners = 14,
    
    /// <summary>
    /// 其他
    /// </summary>
    [Description("其他")]
    Other = 99
}