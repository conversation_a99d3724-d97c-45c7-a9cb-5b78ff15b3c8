namespace Contracts.Common.User.Enums;

public enum CaptchaType
{
    #region TenantUser

    /// <summary>
    /// 租户员工_邮件_邮箱绑定
    /// </summary>
    TenantUser_Email_EmailBingding = 1000,

    /// <summary>
    /// 租户员工_邮件_登录安全验证
    /// </summary>
    TanantUser_Email_LoginVerification = 1001,

    /// <summary>
    /// 租户员工_短信_手机绑定
    /// </summary>
    TenantUser_SMS_PhoneBingding = 1100,

    /// <summary>
    /// 租户员工_短信_登录安全验证
    /// </summary>
    TenantUser_SMS_LoginVerification = 1101,

    #endregion


    #region AgencyUser

    /// <summary>
    /// 分销商_短信_注册验证
    /// </summary>
    AgencyUser_SMS_Register = 2100,

    /// <summary>
    /// 分销商_邮件_邮箱绑定验证
    /// </summary>
    AgencyUser_Email_EmailBingding = 2101,

    /// <summary>
    /// 分销商_邮件_支付密码验证
    /// </summary>
    AgencyUser_Email_PaymentPassword = 2102,

    /// <summary>
    /// 分销商游客_短信_登录安全验证
    /// </summary>
    AgencyVisitor_SMS_LoginVerification = 2103,

    #endregion


    #region CustomerUser

    /// <summary>
    /// C端用户_短信_注册验证
    /// </summary>
    CustomerUser_SMS_Register = 3100,

    /// <summary>
    /// C端用户_短信_登录验证
    /// </summary>
    CustomerUser_SMS_Login = 3101,

    #endregion
}
