using Contracts.Common.Product.Enums;

namespace Contracts.Common.User.Messages;

/// <summary>
/// 移除产品规格对应的佣金配置
/// </summary>
public class RemoveProductCommissionMessage
{
    /// <summary>
    /// 租户id
    /// </summary>
    public long TenantId { get; set; }

    /// <summary>
    /// 产品id
    /// </summary>
    public long ProductId { get; set; }

    /// <summary>
    /// 产品规格id
    /// </summary>
    public List<long> ProductSkuIds { get; set; } = new();
    
    /// <summary>
    /// 产品类型
    /// </summary>
    public ProductType ProductType { get; set; }
}