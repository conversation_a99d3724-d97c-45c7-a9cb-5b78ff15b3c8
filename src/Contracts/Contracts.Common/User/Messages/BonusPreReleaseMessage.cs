
using Contracts.Common.Order.Enums;
using Contracts.Common.User.Enums;

namespace Contracts.Common.User.Messages
{
    public class BonusPreReleaseMessage
    {
        public long TenantId { get; set; }

        public long BaseOrderId { get; set; }

        /// <summary>
        /// 订单类型
        /// </summary>
        public OrderType OrderType { get; set; }

        /// <summary>
        /// 购买人
        /// </summary>
        public long BuyerId { get; set; }

        /// <summary>
        /// 分享人
        /// </summary>
        public long SharerId { get; set; }

        /// <summary>
        /// 落单时间
        /// </summary>
        public DateTime CreateOrderTime { get; set; }

        public IEnumerable<SubOrder> SubOrders { get; set; }
    }

    public class SubOrder
    {
        public long SubOrderId { get; set; }

        /// <summary>
        /// 产品Sku/价格策略Id
        /// </summary>
        public long SkuId { get; set; }

        /// <summary>
        /// 产品/酒店名
        /// </summary>
        public string ProductName { get; set; }

        /// <summary>
        /// Sku/价格策略名
        /// </summary>
        public string ProductSkuName { get; set; }

        /// <summary>
        /// 产品售价 - 单价
        /// </summary>
        public decimal Price { get; set; }

        /// <summary>
        /// 份数
        /// </summary>
        public int Quantity { get; set; }
    }
}
