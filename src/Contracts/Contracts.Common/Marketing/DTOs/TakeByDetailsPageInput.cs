using Contracts.Common.Marketing.Enums;
using System.Text.Json.Serialization;

namespace Contracts.Common.Marketing.DTOs
{
    public class TakeByDetailsPageInput
    {
        [JsonIgnore]
        public long TenantId { get; set; }

        [JsonIgnore]
        public long UserId { get; set; }

        /// <summary>
        /// 分销商id
        /// </summary>
        public long? AgencyId { get; set; }

        /// <summary>
        /// 优惠券活动id
        /// </summary>
        public long CouponActivityId { get; set; }

        /// <summary>
        /// 优惠券id
        /// </summary>
        public long CouponId { get; set; }

        /// <summary>
        /// 使用渠道
        /// </summary>
        public CouponUseChannel UseChannel { get; set; }

        /// <summary>
        /// 指定领取的优惠券id 是否一次性领发放张数的券
        /// </summary>
        public bool TakeAtOneTime { get; set; } = false;
    }
}
