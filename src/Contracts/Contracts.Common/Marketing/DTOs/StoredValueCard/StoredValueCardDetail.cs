namespace Contracts.Common.Marketing.DTOs.StoredValueCard
{
    public class StoredValueCardDetail
    {
        /// <summary>
        /// 储值卡名称
        /// </summary>
        public string CardName { get; set; }

        /// <summary>
        /// 储值卡面
        /// </summary>
        public string Cover { get; set; }

        /// <summary>
        /// 储值卡说明
        /// </summary>
        public string Instruction { get; set; }

        /// <summary>
        /// 线上消费支持品类 位运算
        /// </summary>
        public List<int> ProductBusinessTypes { get; set; }

        /// <summary>
        /// 售价档位
        /// </summary>
        public List<StoredValueCard_Gear> Gears { get; set; }
    }

    public class StoredValueCard_Gear
    {
        public long GearId { get; set; }

        /// <summary>
        /// 售价
        /// </summary>
        public decimal Price { get; set; }

        /// <summary>
        /// 赠送金额
        /// </summary>
        public decimal GiftValue { get; set; }

        public bool IsGiftLevel { get; set; }

        /// <summary>
        /// 赠送会员
        /// </summary>
        public GearGift_MembershipLevel MembershipLevel { get; set; }

        public bool IsGiftCoupon { get; set; }

        /// <summary>
        /// 赠送优惠券
        /// </summary>
        public List<GearGift_Coupon> Coupons { get; set; }
    }

    public class GearGift_MembershipLevel
    {

        public long LevelId { get; set; }
        public int Month { get; set; }
    }

    public class GearGift_Coupon
    {
        public long CouponId { get; set; }
        public int Count { get; set; }
    }
}
