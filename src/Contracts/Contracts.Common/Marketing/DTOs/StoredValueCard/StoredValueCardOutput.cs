using Contracts.Common.Marketing.Enums;

namespace Contracts.Common.Marketing.DTOs.StoredValueCard
{
    public class StoredValueCardOutput : StoredValueCardInfoDto
    {
        /// <summary>
        /// 档位
        /// </summary>
        public IList<StoredValueCardGearInfo> GearInfos { get; set; }
    }


    public class StoredValueCardGearInfo
    {
        public long Id { get; set; }
        /// <summary>
        /// 售价
        /// </summary>
        public decimal Price { get; set; }

        /// <summary>
        /// 赠送金额
        /// </summary>
        public decimal GiftValue { get; set; }

        /// <summary>
        /// 赠送项
        /// </summary>
        public IEnumerable<StoredValueCardGearGiftInfo> GiftInfos { get; set; }
    }

    public class StoredValueCardGearGiftInfo
    {
        /// <summary>
        /// 赠送类型
        /// </summary>
        public StoredValueCardGearGiftType GiftType { get; set; }

        /// <summary>
        /// 赠送的项
        /// </summary>
        public IEnumerable<StoredValueCardGearGiftItemInfo> GiftItems { get; set; }
    }

    public class StoredValueCardGearGiftItemInfo
    {
        /// <summary>
        /// 赠送类型项id 比如:等级id 优惠券id
        /// </summary>
        public long GiftItemId { get; set; }

        /// <summary>
        /// 赠送数量 (等级：月份；优惠券：张数)
        /// </summary>
        public int Count { get; set; }
    }
}
