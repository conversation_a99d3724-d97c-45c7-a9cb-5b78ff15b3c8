using Contracts.Common.Marketing.Enums;
using Newtonsoft.Json;

namespace Contracts.Common.Marketing.DTOs.Lottery;
public class SearchLotteryRecordInput : PagingInput
{
    public string KeyWord { get; set; }

    public long LotteryId { get; set; }

    /// <summary>
    /// 发放状态
    /// </summary>
    public LotteryAwardGrantStatus? Status { get; set; }

    /// <summary>
    /// 奖品类型
    /// </summary>
    public LotteryAwardType? Type { get; set; }

    /// <summary>
    /// 操作人ID
    /// </summary>
    [JsonIgnore]
    public long? CustomerId { get; set; }
}
