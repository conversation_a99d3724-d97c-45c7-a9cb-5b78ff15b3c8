using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.Json.Serialization;
using System.Threading.Tasks;

namespace Contracts.Common.Marketing.DTOs.UserCoupon;
public class GetOfflineReceiptOrderUserCouponsInput
{
    /// <summary>
    /// 用户id
    /// </summary>
    [JsonIgnore]
    public long UserId { get; set; }

    /// <summary>
    /// 用户优惠券id
    /// </summary>
    public long? UserCouponId { get; set; }

    /// <summary>
    /// 线下收款码id
    /// </summary>
    public long OfflineReceiptId { get; set; }
    /// <summary>
    /// 付款金额
    /// </summary>
    public decimal OrderAmount { get; set; }
}