using Contracts.Common.Marketing.Enums;

namespace Contracts.Common.Marketing.DTOs.FlashSale;

public class FlashSalePreOrderOutput
{
    public long Id { get; set; }

    /// <summary>
    /// 活动标题
    /// </summary>
    public string Title { get; set; }

    /// <summary>
    /// 子标题、标签
    /// </summary>
    public string SubTitle { get; set; }

    /// <summary>
    /// 活动开始时间
    /// </summary>
    public DateTime BeginTime { get; set; }

    /// <summary>
    /// 活动结束时间
    /// </summary>
    public DateTime EndTime { get; set; }

    /// <summary>
    /// 限购数/SKU、人
    /// </summary>
    public int Limit { get; set; }

    /// <summary>
    /// 活动状态
    /// </summary>
    public FlashSaleStatus Status { get; set; }

    public List<FlashSaleItemDto> FlashSaleItems { get; set; }
}

public class FlashSaleItemDto
{
    public long Id { get; set; }

    /// <summary>
    /// 产品类型
    /// </summary>
    public FlashSaleItemsType ProductType { get; set; }

    /// <summary>
    /// 产品维度
    /// </summary>
    public long ProductId { get; set; }
    /// <summary>
    /// Sku维度
    /// </summary>
    public long SkuId { get; set; }

    /// <summary>
    ///  Sku子类维度 1-成人 2-儿童
    /// </summary>
    public int SkuSubClass { get; set; }

    /// <summary>
    /// 销售价格类型
    /// </summary>
    public FlashSaleItemsPriceType SellingPriceType { get; set; }

    public decimal SellingPriceValue { get; set; }

    /// <summary>
    /// 采购价类型
    /// </summary>
    public FlashSaleItemsPriceType CostPriceType { get; set; }

    public decimal? CostPriceValue { get; set; }

    public int AvailableInventory { get; set; }

    /// <summary>
    /// 用户可购买数量
    /// </summary>
    public int UserLimit { get; set; }
}