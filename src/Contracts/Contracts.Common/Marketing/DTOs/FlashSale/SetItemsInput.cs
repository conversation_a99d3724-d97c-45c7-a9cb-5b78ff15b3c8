using Contracts.Common.Marketing.Enums;

namespace Contracts.Common.Marketing.DTOs.FlashSale;

public class SetItemsInput
{
    public long FlashSaleId { get; set; }

    public List<Item> Items { get; set; } = new();
}

public class Item
{
    /// <summary>
    /// 产品类型
    /// </summary>
    public FlashSaleItemsType ProductType { get; set; }

    /// <summary>
    /// 产品维度
    /// </summary>
    public long ProductId { get; set; }

    public string ProductName { get; set; }

    /// <summary>
    /// Sku维度
    /// </summary>
    public long SkuId { get; set; }

    public string SkuName { get; set; }

    /// <summary>
    ///  Sku子类维度 成人/儿童/其它品类
    /// </summary>
    public int SkuSubClass { get; set; }

    /// <summary>
    /// 销售价格类型
    /// </summary>
    public FlashSaleItemsPriceType SellingPriceType { get; set; }

    public decimal SellingPriceValue { get; set; }

    /// <summary>
    /// 采购价类型
    /// </summary>
    public FlashSaleItemsPriceType CostPriceType { get; set; }

    public decimal? CostPriceValue { get; set; }

    public int Inventory { get; set; }
}