using Contracts.Common.Marketing.Enums;

namespace Contracts.Common.Marketing.DTOs.CouponActivity
{
    public class SearchCouponActivityOutput
    {
        public long ActivityId { get; set; }

        /// <summary>
        /// 活动名称
        /// </summary>
        public string ActivityName { get; set; }

        /// <summary>
        /// 活动类型
        /// </summary>
        public CouponActivityType CouponActivityType { get; set; }

        /// <summary>
        /// 活动开始日期
        /// </summary>
        public DateTime BeginDate { get; set; }

        /// <summary>
        /// 活动结束日期
        /// </summary>
        public DateTime EndDate { get; set; }

        /// <summary>
        /// 领取用户数
        /// </summary>
        public int UserCount { get; set; }

        /// <summary>
        /// 活动状态
        /// </summary>
        public bool Status { get; set; }

        /// <summary>
        /// 关联的优惠券数量
        /// </summary>
        public int ActivetyCouponCount { get; set; }

        /// <summary>
        /// 使用渠道
        /// </summary>
        public CouponUseChannel UseChannel { get; set; }
    }
}
