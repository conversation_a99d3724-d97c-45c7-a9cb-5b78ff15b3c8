using Contracts.Common.Marketing.Enums;

namespace Contracts.Common.Marketing.DTOs.CouponCodeUsed;

public class CouponUsedSearchOutput
{
    /// <summary>
    /// 核销id
    /// </summary>
    public long  Id{ get; set; }
    
    /// <summary>
    /// 用户id
    /// </summary>
    public long UserId { get; set; } 
    
    /// <summary>
    /// 券码
    /// </summary>
    public long Code { get; set; }

    /// <summary>
    /// 券码数量
    /// </summary>
    public int Count { get; set; }
    
    /// <summary>
    /// 优惠券名称
    /// </summary>
    public string CouponName { get; set; }
    
    /// <summary>
    /// 面额 折扣券:[0,1)  满减券:[0,999999]；
    /// 例子：9折：0.9 
    /// </summary>
    public decimal Amount { get; set; }

    /// <summary>
    /// 优惠券类型
    /// </summary>
    public CouponType CouponType { get; set; }
    
    /// <summary>
    /// 供应商
    /// </summary>
    public long? SupplierId { get; set; }
    
    /// <summary>
    /// 核销供应商名称
    /// </summary>
    public string SupplierName { get; set; }

    /// <summary>
    /// 内容说明
    /// </summary>
    public string ContentDesc { get; set; }

    /// <summary>
    /// 用户注册手机号
    /// </summary>
    public string PhoneNumber { get; set; }

    /// <summary>
    /// 核销备注
    /// </summary>
    public string Remark { get; set; }
    
    /// <summary>
    /// 券码核销时间
    /// </summary>
    public DateTime UsedTime { get; set; }
}