using Contracts.Common.Marketing.Enums;
using Contracts.Common.Product.Enums;

namespace Contracts.Common.Marketing.DTOs.PromotionTraceRecord;

public class ExportTraceStatisticsInput
{
    /// <summary>
    /// 追踪类型
    /// </summary>
    public PromotionTraceType[] TraceTypes { get; set; }

    /// <summary>
    /// 商品类型
    /// </summary>
    public ProductType[] ProductTypes { get; set; }
    
    /// <summary>
    /// 标的物标题, XX大酒店、产品名称等等
    /// </summary>
    public string TargetTitle { get; set; }
    
    /// <summary>
    /// 推广投放位置Id
    /// </summary>
    public long? PromotionPositionId { get; set; }

    /// <summary>
    /// 投放人角色类型
    /// </summary>
    public PromoterRoleType? PromoterRoleType { get; set; }
    
    /// <summary>
    /// 推广人名称
    /// </summary>
    public string PromoterName { get; set; }

    /// <summary>
    /// 开始日期
    /// </summary>
    public DateTime BeginDate { get; set; }
    
    /// <summary>
    /// 结束日期
    /// </summary>
    public DateTime EndDate { get; set; }

    /// <summary>
    /// 排序字段类型
    /// </summary>
    public TraceStatisticsSortFieldType SortFieldType { get; set; }

    /// <summary>
    /// 排序方向类型
    /// </summary>
    public SortDirectionType SortDirectionType { get; set; }
    
    /// <summary>
    /// 图标数据日期类型
    /// </summary>
    public string GraphDateType { get; set; }
}