using Contracts.Common.Product.Enums;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Contracts.Common.Product.DTOs.AgencyChannelCommissionSettings;
public class QueryAgencyChannelCommissionOutput 
{
    public long PriceGroupId { get; set; }

    /// <summary>
    /// 是否启用售卖
    /// </summary>
    public bool Enabled { get; set; }

    /// <summary>
    /// 产品类型
    /// </summary>
    public CommissionProductType ProductType { get; set; }

    /// <summary>
    /// 基准佣金类型
    /// </summary>
    public ChannelBaseCommissionType BaseCommissionType { get; set; } = ChannelBaseCommissionType.SupplierCommission;

    /// <summary>
    /// 佣金设置类型
    /// </summary>
    public ChannelCommissionSettingType CommissionSettingType { get; set; } = ChannelCommissionSettingType.Percent;

    /// <summary>
    /// 佣金设置对应值
    /// </summary>
    public decimal? CommissionSettingValue { get; set; }

    /// <summary>
    /// Base 固定值
    /// </summary>
    public decimal? BaseValue { get; set; }
}
