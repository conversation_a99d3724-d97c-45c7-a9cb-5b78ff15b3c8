using Contracts.Common.Product.Enums;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Contracts.Common.Product.DTOs.ProductInformationTemplate;
public class GetProductTempFieldsDetailInput
{
    /// <summary>
    /// 产品Id
    /// </summary>
    public long ProductId { get; set; }

    /// <summary>
    /// 套餐/规格Id
    /// </summary>
    public long ProductSkuId { get; set; }

    /// <summary>
    /// 产品类型
    /// </summary>
    public ProductType ProductType { get; set; }
}

public class BatchGetProductTempFieldsDetailInput
{
    /// <summary>
    /// 产品类型
    /// </summary>
    public ProductType ProductType { get; set; }
    
    public List<BatchGetProductTempFieldsDetailItem> Items { get; set; } = new();
}

public class BatchGetProductTempFieldsDetailItem
{
    /// <summary>
    /// 产品Id
    /// </summary>
    public long ProductId { get; set; }

    /// <summary>
    /// 套餐/规格Id
    /// </summary>
    public long ProductSkuId { get; set; }

}