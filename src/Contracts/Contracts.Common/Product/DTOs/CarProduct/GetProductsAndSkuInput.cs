using Contracts.Common.Product.Enums;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Contracts.Common.Product.DTOs.CarProduct;
public class GetProductsAndSkuInput : PagingInput
{
    /// <summary>
    /// 产品名称
    /// </summary>
    public string Name { get; set; }

    /// <summary>
    /// 供应商id
    /// </summary>
    public long? SupplierId { get; set; }

    public bool? Enabled { get; set; }

    /// <summary>
    /// 套餐id/服务项目id
    /// </summary>
    public IEnumerable<long> SkuIds { get; set; } = Enumerable.Empty<long>();
    
    /// <summary>
    /// 是否B2B售卖
    /// </summary>
    public bool? B2bSellingStatus { get; set; }
}

public class GetProductSkuAndServiceInput : PagingInput
{
    /// <summary>
    /// 产品名称
    /// </summary>
    public string? Name { get; set; }

    public bool? Enabled { get; set; }

    public CarProductSubClassPriceType? CarProductSubClassPriceType { get; set; }
}