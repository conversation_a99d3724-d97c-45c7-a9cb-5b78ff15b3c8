using Contracts.Common.Product.DTOs.ProductOperatorUser;
using Contracts.Common.Product.Enums;
using Contracts.Common.Scenic.Enums;

namespace Contracts.Common.Product.DTOs.LineProduct;

public class SearchLineProductOutput
{
    /// <summary>
    /// 产品Id
    /// </summary>
    public long Id { get; set; }

    /// <summary>
    /// 标题
    /// </summary>
    public string Title { get; set; }

    /// <summary>
    /// 出发国家名称
    /// </summary>
    public string DepartureCountryName { get; set; }

    /// <summary>
    /// 出发地城市Id
    /// </summary>
    public int DepartureCityId { get; set; }

    /// <summary>
    /// 出发地城市名称
    /// </summary>
    public string DepartureCityName { get; set; }

    /// <summary>
    /// 目的地国家名称
    /// </summary>
    public string DestinationCountryName { get; set; }

    /// <summary>
    /// 目的地城市Id
    /// </summary>
    public int DestinationCityId { get; set; }

    /// <summary>
    /// 目的地城市名称
    /// </summary>
    public string DestinationCityName { get; set; }

    /// <summary>
    /// 天数
    /// </summary>
    public int Days { get; set; }

    /// <summary>
    /// 晚数
    /// </summary>
    public int Nights { get; set; }

    /// <summary>
    /// 30天内最低价
    /// </summary>
    public decimal? Price { get; set; }

    /// <summary>
    /// 售卖数量
    /// </summary>
    public int Sales { get; set; }

    /// <summary>
    /// 售卖日期 - 起  Null表示不限
    /// </summary>
    public DateTime? SellingDateBegin { get; set; }

    /// <summary>
    /// 售卖日期 - 止  Null表示不限
    /// </summary>
    public DateTime? SellingDateEnd { get; set; }

    /// <summary>
    /// 上下架
    /// </summary>
    public bool Enabled { get; set; }

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreateTime { get; set; }

    /// <summary>
    /// 首图
    /// </summary>
    public string Picture { get; set; }

    /// <summary>
    /// 海报
    /// </summary>
    public string Poster { get; set; }

    /// <summary>
    /// 售价币种
    /// </summary>
    public string SaleCurrencyCode { get; set; }

    /// <summary>
    /// 采购价币种
    /// </summary>
    public string CostCurrencyCode { get; set; }

    /// <summary>
    /// 供应商
    /// </summary>
    public long SupplierId { get; set; }

    /// <summary>
    /// 更新时间
    /// </summary>
    public DateTime? UpdateTime { get; set; }

    /// <summary>
    /// 排序
    /// </summary>
    public int Sort { get; set; }

    /// <summary>
    /// 产品人id
    /// </summary>
    public long? DevelopUserId { get; set; }

    /// <summary>
    /// 运营人id
    /// </summary>
    public long? OperatorUserId { get; set; }

    /// <summary>
    /// 供应商名称
    /// </summary>
    public string? SupplierName { get; set; }

    /// <summary>
    /// B2b售卖状态
    /// </summary>
    public B2bSellingStatusType B2bSellingStatusType { get; set; }
    
    /// <summary>
    /// 采购来源类型
    /// </summary>
    public LineProductPurchaseSourceType PurchaseSourceType { get; set; }
    
    /// <summary>
    /// 价库类型
    /// </summary>
    public PriceInventoryType PriceInventoryType { get; set; }

    /// <summary>
    /// sku信息
    /// </summary>
    public List<SearchLineProductSkuItem> SkuItems { get; set; }
    
    /// <summary>
    /// 采购折扣比例[0-100] 2位小数
    /// <remarks>
    /// <para>如15表示为15%的折扣</para>
    /// <para>API对接直接读取供应端数据</para>
    /// </remarks>
    /// </summary>
    public decimal CostDiscountRate { get; set; }
    
    /// <summary>
    /// 供应商是否销售
    /// </summary>
    public bool? SupplierIsSale { get; set; }
    
    /// <summary>
    /// 平台运营人
    /// </summary>
    public List<ProductOperatorUserDto> ProductOperatorUser { get; set; } = new();
}

public class SearchLineProductSummary
{
    public int Total { get; set; }
    public int EnabledCount { get; set; }
    public int DisabledCount { get; set; }
}

/// <summary>
/// 返回时段场次sku信息
/// </summary>
public class SearchLineProductSkuItem
{
    /// <summary>
    /// skuId
    /// </summary>
    public long SkuId { get; set; }

    /// <summary>
    /// sku名称
    /// </summary>
    public string SkuName { get; set; }
    
    /// <summary>
    /// 采购折扣比例[0-100] 2位小数
    /// <remarks>
    /// <para>如15表示为15%的折扣</para>
    /// <para>API对接直接读取供应端数据</para>
    /// </remarks>
    /// </summary>
    public decimal CostDiscountRate { get; set; }
}
