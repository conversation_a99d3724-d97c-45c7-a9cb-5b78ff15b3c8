using Contracts.Common.Product.Enums;

namespace Contracts.Common.Product.DTOs.ProductPriceAdjustment;

/// <summary>
/// 价格调整计算
/// </summary>
public class CalcPriceAdjustmentInput
{
    /// <summary>
    /// 产品价格配置- 价格基准类型
    /// </summary>
    public PriceBasisType PriceBasisType { get; set; }

    /// <summary>
    /// 产品价格配置- 价格调整类型
    /// </summary>
    public PriceAdjustmentType PriceAdjustmentType { get; set; }

    /// <summary>
    /// 产品价格配置- 价格调整值
    /// </summary>
    public decimal? PriceAdjustmentValue { get; set; }

    /// <summary>
    /// 采购价
    /// </summary>
    public decimal? CostPrice { get; set; }

    /// <summary>
    /// 采购价转售价的汇率
    /// </summary>
    public decimal CostPriceExchangeRate { get; set; }
}