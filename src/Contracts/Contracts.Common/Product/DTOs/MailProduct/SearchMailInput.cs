using Contracts.Common.Product.Enums;

namespace Contracts.Common.Product.DTOs.MailProduct
{
    public class SearchMailInput : PagingInput
    {
        public string KeyWord { get; set; }
        public decimal? MinPrice { get; set; }
        public decimal? MaxPrice { get; set; }
        public List<long> GroupIds { get; set; }
        public ProductSearchStatusType Status { get; set; }

        /// <summary>
        /// 售卖状态
        /// </summary>
        public SaleStatus? SaleStatus { get; set; }

        public SearchOrderByType SearchOrderByType { get; set; }

        /// <summary>
        /// 供应商id
        /// </summary>
        public long? SupplierId { get; set; }
    }
}
