using Contracts.Common.Scenic.Enums;

namespace Contracts.Common.Product.DTOs.LineProductOpenChannelSetting;

public class UpdateOpenChannelSyncSettingInput
{
    /// <summary>
    /// 线路产品Id
    /// </summary>
    public long LineProductId { get; set; }
    
    /// <summary>
    /// 配置信息
    /// </summary>
    public List<LineProductOpenChannelSettingInfo> SettingInfos { get; set; } = new();
}

public class UpdateOpenChannelTimelinessSettingInput
{
    /// <summary>
    /// 线路产品Id
    /// </summary>
    public long LineProductId { get; set; }

    /// <summary>
    /// 是否开启时效渠道
    /// </summary>
    public bool IsChannelTimeliness { get; set; }
    
    /// <summary>
    /// 时效渠道配置列表
    /// </summary>
    public List<TimelinessChannelSettingInfo> TimelinessChannelSettingInfos { get; set; } = new();
}

public class TimelinessChannelSettingInfo
{
    /// <summary>
    /// 时效同步渠道类型
    /// </summary>
    public PriceInventorySyncChannelType TimelinessChannelType { get; set; }
    
    /// <summary>
    /// 时效触发场景类型
    /// </summary>
    public OpenChannelTimelinessTriggerType TimelinessTriggerType { get; set; }
}