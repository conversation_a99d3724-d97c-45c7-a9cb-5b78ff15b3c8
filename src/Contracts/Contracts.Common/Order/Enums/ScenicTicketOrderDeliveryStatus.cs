namespace Contracts.Common.Order.Enums;

/// <summary>
/// 门票订单发货状态
/// </summary>
public enum ScenicTicketOrderDeliveryStatus
{
    /// <summary>
    /// 成功
    /// </summary>
    Success = 0,
    
    /// <summary>
    /// 发货中.待发货
    /// </summary>
    WaitingForDeliver = 1,
    
    /// <summary>
    /// 发货失败
    /// </summary>
    DeliveryFail = 2,
    
    /// <summary>
    /// 接口同步失败
    /// </summary>
    SyncFailed = 3,
    
    /// <summary>
    /// 供应端拒单
    /// </summary>
    Rejected = 4,
    
    /// <summary>
    /// 供应端采购中
    /// </summary>
    Purchasing = 5,
    
    /// <summary>
    /// 支付处理中
    /// </summary>
    PayProcessing = 6,
}