namespace Contracts.Common.Order.Enums
{
    /// <summary>
    /// 储值卡订单类型
    /// </summary>
    public enum StoredValueCardOrderStatus
    {
        /// <summary>
        /// 待支付
        /// </summary>
        WaitingForPay = 1,

        /// <summary>
        /// 待完成 
        /// </summary>
        UnFinished = 2,

        /// <summary>
        /// 已完成
        /// </summary>
        Finished = 3,

        /// <summary>
        /// 已关闭
        /// </summary>
        Closed = 4,

        /// <summary>
        /// 退款中
        /// </summary>
        Refunding = 5,

        /// <summary>
        /// 已退款
        /// </summary>
        Refunded = 6,
    }
}
