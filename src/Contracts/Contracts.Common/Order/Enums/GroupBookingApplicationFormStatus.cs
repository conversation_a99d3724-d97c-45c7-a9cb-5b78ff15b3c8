using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Contracts.Common.Order.Enums;
public enum GroupBookingApplicationFormStatus
{
    /// <summary>
    /// 新询单
    /// </summary>
    [Description("新询单")]
    NewApplication = 0,

    /// <summary>
    /// 待询价
    /// </summary>
    [Description("待询价")]
    WaitForInquiry = 1,

    /// <summary>
    /// 已询价（报价审核）
    /// </summary>
    [Description("已询价")]
    Inquiried = 2,

    /// <summary>
    /// 已报价
    /// </summary>
    [Description("已报价")]
    Quoted = 3,

    /// <summary>
    /// 确认报价
    /// </summary>
    [Description("确认报价")]
    QuotationConfirmed = 4,

    /// <summary>
    /// 待审预订单
    /// </summary>
    [Description("待审预订单")]
    WaitForAuditPreOrder = 5,

    /// <summary>
    /// 预订单
    /// </summary>
    [Description("预订单")]
    PreOrdered = 6,

    /// <summary>
    /// 已取消
    /// </summary>
    [Description("已取消")]
    Cancellation = 7,

    /// <summary>
    /// 无法报价
    /// </summary>
    [Description("无法报价")]
    UnableQuoted = 8,

}