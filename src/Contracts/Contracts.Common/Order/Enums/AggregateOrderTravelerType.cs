using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Contracts.Common.Order.Enums;
public enum AggregateOrderTravelerType
{
    /// <summary>
    /// 成人
    /// </summary>
    Adult = 1,

    /// <summary>
    /// 儿童
    /// </summary>
    Child = 2,
    
    /// <summary>
    /// 婴儿
    /// </summary>
    Baby = 3,
    
    /// <summary>
    /// 长者
    /// </summary>
    Elderly = 4,

    /// <summary>
    /// 长者
    /// </summary>
    Other = 5,
}
