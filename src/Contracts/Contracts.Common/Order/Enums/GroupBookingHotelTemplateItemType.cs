using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Contracts.Common.Order.Enums;

public enum GroupBookingHotelTemplateItemType
{
    /// <summary>
    /// 询价邮箱
    /// </summary>
    Quotation = 1,

    /// <summary>
    /// 再次询价邮箱
    /// </summary>
    RevisedQuotation = 2,

    /// <summary>
    /// 已有其他酒店报价通知
    /// </summary>
    OtherQuoted = 3,

    /// <summary>
    /// 报价已过期提示
    /// </summary>
    ExpiredQuotation = 4,

    /// <summary>
    /// 报价已结束提示
    /// </summary>
    EndedQuotation = 5,
}