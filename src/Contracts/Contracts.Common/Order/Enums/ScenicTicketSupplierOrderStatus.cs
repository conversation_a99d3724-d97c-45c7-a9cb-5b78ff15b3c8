namespace Contracts.Common.Order.Enums;
/// <summary>
/// 门票供货方订单状态
/// </summary>
public enum ScenicTicketSupplierOrderStatus
{
    /// <summary>
    /// 创建订单失败
    /// </summary>
    CreateFail = 0,
    
    /// <summary>
    /// 待支付
    /// </summary>
    WaitingForPay = 1,
    
    /// <summary>
    /// (已付款)待发货
    /// </summary>
    WaitingForDeliver = 2,
    
    /// <summary>
    /// 未发货,已退款
    /// </summary>
    Refunded = 3,
    
    /// <summary>
    /// 发货成功.已发货
    /// </summary>
    Delivered = 4,
    
    /// <summary>
    /// 拒单
    /// </summary>
    Reject = 5,
    
    /// <summary>
    /// 发货失败
    /// </summary>
    DeliveryFail = 6,
    
    /// <summary>
    /// 采购中
    /// </summary>
    Purchasing = 7,

    /// <summary>
    /// 支付处理中
    /// </summary>
    PayProcessing = 8,
}