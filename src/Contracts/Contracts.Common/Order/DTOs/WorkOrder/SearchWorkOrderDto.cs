using Contracts.Common.Order.Enums;

namespace Contracts.Common.Order.DTOs.WorkOrder;
public class SearchWorkOrderDto
{
    public long Id { get; set; }

    public long AgencyId { get; set; }

    public string AgencyFullName { get; set; }

    public DateTime CreateTime { get; set; }

    /// <summary>
    /// HOP工单号
    /// </summary>
    public string? HopWorkOrderNumber { get; set; }

    public long BaseOrderId { get; set; }

    public long SubOrderId { get; set; }

    /// <summary>
    /// 工单类型
    /// </summary>
    public string WorkOrderType { get; set; }

    /// <summary>
    /// 订单状态
    /// </summary>
    public WorkOrderStatus Status { get; set; }

    public long TenantId { get; set; }

    /// <summary>
    /// 酒店名称
    /// </summary>
    public string HotelName { get; set; }

    public string? EnHotelName { get; set; }

    /// <summary>
    /// 最新一条回复
    /// </summary>
    public string LatestReplyContent { get; set; }

    /// <summary>
    /// 更新时间
    /// </summary>
    public DateTime? UpdateTime { get; set; }

    /// <summary>
    /// 能否评价
    /// </summary>
    public bool CanItBeEvaluated { get; set; }

    /// <summary>
    /// 创建人姓名
    /// </summary>
    public string CreatorName { get; set; }

    /// <summary>
    /// 认领人
    /// </summary>
    public string? QuoteStaff { get; set; }
}
