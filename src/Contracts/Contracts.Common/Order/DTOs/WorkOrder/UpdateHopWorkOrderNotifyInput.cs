using Contracts.Common.Order.DTOs.HotelApiOrder;

namespace Contracts.Common.Order.DTOs.WorkOrder;

/// <summary>
/// 更新hop工单通知
/// </summary>
public class UpdateHopWorkOrderNotifyInput
{
    public Head Head { get; set; }

    public UpdateHopWorkOrderNotifyData Data { get; set; }

}

public class UpdateHopWorkOrderNotifyData
{
    /// <summary>
    /// hop工单号
    /// </summary>
    public string OrderWorkId { get; set; }

    /// <summary>
    /// hop工单状态
    /// </summary>
    public string OrderWorkStatus { get; set; }

    /// <summary>
    /// 认领人
    /// </summary>
    public string Quote_staff { get; set; }

}