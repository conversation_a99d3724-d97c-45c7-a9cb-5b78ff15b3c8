using Contracts.Common.Order.DTOs.OpenSupplierOrder;
using Contracts.Common.Order.DTOs.OrderFieldInformation;
using Contracts.Common.Order.Enums;

namespace Contracts.Common.Order.DTOs.ScenicTicketOrder;

public class CreateByManualInput
{
    /// <summary>
    /// 同步失败订单id
    ///<remarks>同步失败订单需要将id赋值到baseOrder中</remarks>
    /// </summary>
    public long? SyncFailOrderId { get; set; }
    
    /// <summary>
    /// 当前用户类型
    /// </summary>
    public UserType UserType { get; set; }

    #region 订单基础信息

    /// <summary>
    /// 景区Id
    /// </summary>
    public long ScenicSpotId { get; set; }

    /// <summary>
    /// 景区门票Id
    /// </summary>
    public long ScenicTicketId { get; set; }

    /// <summary>
    /// 分销商id
    /// </summary>
    public long AgencyId { get; set; }

    /// <summary>
    /// 分销商名称
    /// </summary>
    public string AgencyName { get; set; }

    /// <summary>
    /// 售卖平台
    /// </summary>
    public SellingPlatform SellingPlatform { get; set; } = SellingPlatform.System;
    
    /// <summary>
    /// 来源渠道
    /// </summary>
    public SellingChannels SellingChannels { get; set; } = SellingChannels.B2b;


    public string[] ChannelOrderNo { get; set; } = Array.Empty<string>();

    /// <summary>
    /// 订单备注
    /// </summary>
    public string Remark { get; set; }
    
    /// <summary>
    /// 采购备注
    /// </summary>
    public string SupplierOrderRemark { get; set; }

    /// <summary>
    /// 采购单号
    /// </summary>
    public string? SupplierOrderId { get; set; }

    /// <summary>
    /// 出行时段id
    /// </summary>
    public long? TravelTimeSlotId { get; set; }

    /// <summary>
    /// 出行日期
    /// </summary>
    public DateTime TravelDate { get; set; } = DateTime.Today;

    #endregion

    #region 联系信息

    /// <summary>
    /// 联系人
    /// </summary>
    public string ContactsName { get; set; }

    /// <summary>
    /// 联系人手机号
    /// </summary>
    public string ContactsPhoneNumber { get; set; }

    /// <summary>
    /// 联系人邮箱
    /// </summary>
    public string ContactsEmail { get; set; }

    /// <summary>
    /// 出行人信息
    /// </summary>
    public List<TicketOrderTravelerInfo> TravelerInfos { get; set; } = new List<TicketOrderTravelerInfo>();

    #endregion

    #region 销售信息

    /// <summary>
    /// 购买数量
    /// </summary>
    public int Quantity { get; set; }

    /// <summary>
    /// 采购单价
    /// </summary>
    public decimal CostPrice { get; set; }

    /// <summary>
    /// 售卖单价
    /// </summary>
    public decimal SellingPrice { get; set; }

    /// <summary>
    /// 优惠总额
    /// </summary>
    public decimal DiscountAmount { get; set; }

    /// <summary>
    /// 实收金额 可选参数
    /// </summary>
    public decimal? PaymentAmount { get; set; }

    #endregion

    #region 组合订单

    /// <summary>
    /// 组合订单id
    /// </summary>
    public long? TicketsCombinationOrderId { get; set; }

    #endregion

    /// <summary>
    /// 订单字段信息
    /// </summary>
    public new List<SaveOrderFieldInformationTypeDto> OrderFields { get; set; } = new();
    
    /// <summary>
    /// 供应商 - 订单附加信息
    /// </summary>
    public List<AddOpenSupplierOrderExtraInfoItem> OrderExtraInfos { get; set; } = new();
}