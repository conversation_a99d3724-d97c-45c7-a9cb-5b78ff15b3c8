using Contracts.Common.Order.DTOs.ScenicTicketSupplierOrder;
using Contracts.Common.Order.Enums;

namespace Contracts.Common.Order.DTOs.ScenicTicketOrder.OTA;

public class ScenicTicketOTAOrderDeliveryInput
{
    /// <summary>
    /// OTA订单号
    /// </summary>
    public List<string> OtaOrderIds { get; set; }

    /// <summary>
    /// saas平台售卖类型
    /// </summary>
    public SellingPlatform SellingPlatform { get; set; }

    /// <summary>
    /// 手工单对应渠道类型
    /// </summary>
    public OtaChannelType SystemOtaChannelType { get; set; }
    
    /// <summary>
    /// 凭证列表
    /// </summary>
    public List<SupplierOrderVoucherItem> Vouchers { get; set; }

    /// <summary>
    /// 租户id
    /// </summary>
    public long TenantId { get; set; }
}