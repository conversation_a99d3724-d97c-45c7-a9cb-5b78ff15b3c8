using Contracts.Common.Order.DTOs.OrderFieldInformation;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Contracts.Common.Order.DTOs.ScenicTicketOrder;
public class UpdateTravelInfoInput
{
    public long BaseOrderId { get; set; }

    /// <summary>
    /// 操作人信息
    /// </summary>
    public OperationUserDto OperationUser { get; set; }

    /// <summary>
    /// 订单字段信息
    /// </summary>
    public List<OrderFieldInformationTypeOutput> OrderFields { get; set; } = new();
}
