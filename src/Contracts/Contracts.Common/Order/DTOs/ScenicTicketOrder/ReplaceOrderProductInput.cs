using Contracts.Common.Scenic.DTOs.Ticket;
using Contracts.Common.Scenic.Enums;
using Contracts.Common.Tenant.Enums;

namespace Contracts.Common.Order.DTOs.ScenicTicketOrder;

/// <summary>
/// 替换产品
/// </summary>
public class ReplaceOrderProductInput
{
    /// <summary>
    /// 订单id
    /// </summary>
    public long BaseOrderId { get; set; }
    
    /// <summary>
    /// 景区门票Id
    /// </summary>
    public long TicketId { get; set; }

    /// <summary>
    /// 时段id
    /// </summary>
    public long? TimeSlotId { get; set; }

    /// <summary>
    /// 时段
    /// </summary>
    public TimeSpan? TimeSlot { get; set; }

    #region 价格&币种&汇率

    /// <summary>
    /// 供应商采购币种
    /// </summary>
    public string CostCurrencyCode { get; set; }

    /// <summary>
    /// 成本币种转原价币种汇率 =>供应商币种:商户币种=1:rate
    /// </summary>
    public decimal CostExchangeRate { get; set; }

    /// <summary>
    /// 成本价(采购价) 实际成本价格
    /// </summary>
    public decimal CostPrice { get; set; }

    /// <summary>
    /// 价格 实际售卖价格
    /// </summary>
    public decimal Price { get; set; }

    #endregion
    
    /// <summary>
    /// 供应商api类型
    /// </summary>
    public SupplierApiType? SupplierApiType { get; set; }

    /// <summary>
    /// 门票详情
    /// </summary>
    public GetTicketDetailOutput TicketDetail { get; set; }

    /// <summary>
    /// 操作人信息
    /// </summary>
    public OperationUserDto OperationUserDto { get; set; }
    
    /// <summary>
    /// 出行日期
    /// </summary>
    public DateTime? TravelDate { get; set; }
}