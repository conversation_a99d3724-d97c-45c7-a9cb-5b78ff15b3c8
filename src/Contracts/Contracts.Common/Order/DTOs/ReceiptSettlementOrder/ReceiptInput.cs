using Contracts.Common.Order.Enums;

namespace Contracts.Common.Order.DTOs.ReceiptSettlementOrder;

public class ReceiptInput
{
    /// <summary>
    /// 结算id
    /// </summary>
    public long SettlementOrderId { get; set; }

    /// <summary>
    /// 收款方式
    /// </summary>
    public ReceiptSettlementTransferType SettlementTransferType { get; set; }

    /// <summary>
    /// 开户名称
    /// </summary>
    public string AccountName { get; set; }

    /// <summary>
    /// 银行编号
    /// </summary>
    public string BankCode { get; set; }

    /// <summary>
    /// 银行名称
    /// </summary>
    public string BankName { get; set; }

    /// <summary>
    /// 银行账号
    /// </summary>
    public string BankAccount { get; set; }

    /// <summary>
    /// 转账凭证图片
    /// </summary>
    public string ProofImg { get; set; }

    /// <summary>
    /// 备注
    /// </summary>
    public string Remark { get; set; }

    /// <summary>
    /// 分行名称
    /// </summary>
    public string BranchName { get; set; }

    /// <summary>
    /// 实收金额
    /// </summary>
    public decimal? ReceivedAmount { get; set; }
    
    /// <summary>
    /// 实收总额币种
    /// </summary>
    public string ReceivedAmountCurrencyCode { get; set; }

    /// <summary>
    /// 收款时间
    /// </summary>
    public DateTime PayeeTime { get; set; } = DateTime.Now;
}