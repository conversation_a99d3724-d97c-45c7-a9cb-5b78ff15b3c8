using Contracts.Common.Order.Enums;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Contracts.Common.Order.DTOs.ReceiptSettlementOrder;
public class SearchDetailExportInput
{
    /// <summary>
    /// 订单类别
    /// </summary>
    public List<OrderType> OrderTypes { get; set; }

    /// <summary>
    /// 收款单单号
    /// </summary>
    public List<long> ReceiptSettlementOrderId { get; set; }

    /// <summary>
    /// 付款单类别
    /// </summary>
    public List<ReceiptSettlementBusinessType> ReceiptSettlementBusinessTypes { get; set; }
}
