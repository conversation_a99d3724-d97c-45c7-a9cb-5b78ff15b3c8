using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Contracts.Common.Order.DTOs.HotelOrder;
public class HotelOrderCalendarPriceDto
{
    public long HotelOrderId { get; set; }

    /// <summary>
    /// 日期
    /// </summary>
    public DateTime Date { get; set; }

    /// <summary>
    /// 成本价/采购价
    /// </summary>
    public decimal CostPrice { get; set; }

    /// <summary>
    /// 基础售价（渠道加价之后的值）
    /// </summary>
    public decimal SalePrice { get; set; }

    /// <summary>
    /// 会员价
    /// </summary>
    public decimal? VipPrice { get; set; }
}