using Contracts.Common.Order.Enums;
using Contracts.Common.Payment.Enums;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Contracts.Common.Order.DTOs.HotelOrder;
public class HotelOrderPriceDto
{
    /// <summary>
    /// 
    /// </summary>
    public HotelOrderSubType OrderSubType { get; set; }

    /// <summary>
    /// 原价币种 =>商户售卖币种
    /// </summary>
    public string OrgPriceCurrencyCode { get; set; } = Currency.CNY.ToString();

    /// <summary>
    /// 原售价
    /// </summary>
    public decimal OrgPrice { get; set; }

    /// <summary>
    /// 原成本价
    /// </summary>
    public decimal OrgCostPrice { get; set; }

    /// <summary>
    /// 售价类型枚举 0-原价 1-会员价 2-抢购价
    /// </summary>
    public OrderPriceType PriceType { get; set; }

    /// <summary>
    /// 支付币种 =>用户支付币种，B2B为分销商币种
    /// </summary>
    public string PaymentCurrencyCode { get; set; } = Currency.CNY.ToString();

    /// <summary>
    /// 价格 实际售卖价格
    /// </summary>
    public decimal Price { get; set; }

    /// <summary>
    /// 成本价类型枚举 0-原价 1-会员价 2-抢购价
    /// </summary>
    public OrderPriceType CostPriceType { get; set; }

    /// <summary>
    /// 成本币种 =>供应商币种
    /// </summary>
    public string CostCurrencyCode { get; set; } = Currency.CNY.ToString();

    /// <summary>
    /// 成本价(采购价) 实际成本价格
    /// </summary>
    public decimal CostPrice { get; set; }

    /// <summary>
    /// 数量
    /// </summary>
    public int Quantity { get; set; }

    /// <summary>
    /// 成本币种转原价币种汇率 =>供应商币种:商户币种=1:rate
    /// </summary>
    public decimal CostExchangeRate { get; set; } = 1;

    /// <summary>
    /// 原价币种转支付币种汇率 =>商户币种:支付币种=1:rate
    /// </summary>
    public decimal ExchangeRate { get; set; } = 1;
}
