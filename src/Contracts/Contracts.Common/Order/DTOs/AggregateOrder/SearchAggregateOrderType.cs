using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Contracts.Common.Order.DTOs.AggregateOrder;
public enum SearchAggregateOrderType
{
    #region 酒店
    /// <summary>
    /// 酒店
    /// </summary>
    Hotel = 10,

    /// <summary>
    /// 酒店
    /// </summary>
    SaasHotel = 11,
    /// <summary>
    /// 高定酒店
    /// </summary>
    GDSHotel = 12,
    #endregion

    #region 景区门票
    /// <summary>
    /// 景区门票
    /// </summary>
    ScenicTicket = 20,

    /// <summary>
    /// 景区门票-预订票
    /// </summary>
    ScenicTicketReservation = 21,

    /// <summary>
    /// 景区门票-期票
    /// </summary>
    ScenicTicketPromissoryNote = 22,
    #endregion

    /// <summary>
    /// 旅游线路
    /// </summary>
    TravelLineOrder = 30,

    #region 接送车
    /// <summary>
    /// 接送车
    /// </summary>
    CarHailing = 40,

    /// <summary>
    /// 接送车-定制
    /// </summary>
    CarHailingCustom = 41,

    /// <summary>
    /// 接送车-接送机
    /// </summary>
    CarHailingAirportTransfer = 42,
    #endregion

    #region 票券
    /// <summary>
    /// 票券
    /// </summary>
    Ticket = 50,

    /// <summary>
    /// 票券-酒店套餐
    /// </summary>
    TicketHotelPackages = 51,

    /// <summary>
    /// 票券-房券
    /// </summary>
    TicketRoomVoucher = 52,

    /// <summary>
    /// 票券-餐饮
    /// </summary>
    TicketCatering = 53,
    #endregion

    #region 用车
    /// <summary>
    /// 用车
    /// </summary>
    CarProduct = 60,

    /// <summary>
    /// 用车 - 接机
    /// </summary>
    CarProductAirportPickUp = 61,

    /// <summary>
    /// 用车 - 送机
    /// </summary>
    CarProductAirportDropOff = 62,

    /// <summary>
    /// 用车-点对点
    /// </summary>
    CarProductPointToPointTransfer = 63,

    /// <summary>
    /// 用车-包车
    /// </summary>
    CarProductCarChartered = 64,


    #endregion
}
