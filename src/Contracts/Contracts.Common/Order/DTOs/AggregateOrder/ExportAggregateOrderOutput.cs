using Contracts.Common.Hotel.Enums;
using Contracts.Common.Order.Enums;
using Contracts.Common.Payment.Enums;
using Contracts.Common.Product.Enums;
using Contracts.Common.Scenic.Enums;
using Contracts.Common.Tenant.Enums;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Contracts.Common.Order.DTOs.AggregateOrder;
public class ExportAggregateOrderOutput
{
    /// <summary>
    /// 主单id
    /// </summary>
    public long BaseOrderId { get; set; }

    /// <summary>
    /// 最新备注
    /// </summary>
    public string Remark { get; set; }

    /// <summary>
    /// 标记
    /// </summary>
    public string OrderMark { get; set; }

    /// <summary>
    /// 团号 可选项
    /// </summary>
    public string GroupNo { get; set; }

    /// <summary>
    /// 售卖平台
    /// </summary>
    public SellingPlatform SellingPlatform { get; set; }

    #region 状态相关
    /// <summary>
    /// 订单状态
    /// </summary>
    public AggregateOrderStatus OrderStatus { get; set; }

    /// <summary>
    /// 订单确认状态 未确认 确认中 确认失败 已确认
    /// </summary>
    public AggregateOrderConfirmStatus? ConfirmStatus { get; set; }

    /// <summary>
    /// 订单出行状态 未出行/入住 已入住
    /// </summary>
    public AggregateOrderTravelStatus? TravelStatus { get; set; }
    #endregion

    #region 价格源相关
    /// <summary>
    /// 汇智酒店 - 0-非主推  1-主推
    /// </summary>
    public bool? StaffTag { get; set; }
    /// <summary>
    /// 汇智酒店 - 售卖标签
    /// </summary>
    public ApiHotelTag? Tags { get; set; }

    /// <summary>
    /// 门票 - 门票价格源
    /// </summary>
    public CredentialSourceType? CredentialSourceType { get; set; }

    public SupplierApiType SupplierApiType { get; set; }

    /// <summary>
    /// 价格源
    /// </summary>
    public SearchPriceSourceType? PriceSourceType { get; set; }
    
    /// <summary>
    /// 线路 - 产品采购类型
    /// </summary>
    public LineProductPurchaseSourceType? LineProductPurchaseSourceType { get; set; }

    /// <summary>
    /// 用车产品-采购来源类型
    /// </summary>
    public CarProductPurchaseSourceType? CarProductPurchaseSourceType { get; set; }
    #endregion

    /// <summary>
    /// 产品id/房型id
    /// </summary>
    public long ProductId { get; set; }
    /// <summary>
    /// 产品名称/房型名称
    /// </summary>
    public string ProductName { get; set; }

    #region 订单金额支付数据

    /// <summary>
    /// 份数 间数
    /// </summary>
    public int Quantity { get; set; }

    /// <summary>
    /// 夜数 - 酒店时有晚数
    /// </summary>
    public int? NightsCount { get; set; }

    /// <summary>
    /// 订单总额 = DiscountAmount + Payment.Amount
    /// </summary>
    public decimal TotalAmount { get; set; }

    /// <summary>
    /// 优惠金额
    /// </summary>
    public decimal DiscountAmount { get; set; }

    /// <summary>
    /// 支付金额
    /// </summary>
    public decimal PaymentAmount { get; set; }

    /// <summary>
    /// 订单汇率
    /// </summary>
    public decimal ExchangeRate { get; set; }

    /// <summary>
    /// 取消总额 - 人民币
    /// </summary>
    public decimal? RefundTotalAmount { get; set; }

    /// <summary>
    /// 取消总额
    /// </summary>
    public decimal? CurrencyCodeRefundTotalAmount { get; set; }

    /// <summary>
    /// 支付金额-实收人民币 PaymentAmount/ExchangeRate
    /// </summary>
    public decimal PaymentAmountTenantCurrency { get; set; }

    #region Payment

    /// <summary>
    /// 订单支付币种
    /// </summary>
    public string PaymentCurrencyCode { get; set; } = string.Empty;

    /// <summary>
    /// 支付类型
    /// </summary>
    public PayType PaymentType { get; set; }

    #endregion
    #endregion

    #region 订单时间数据

    /// <summary>
    /// 下单时间
    /// </summary>
    public DateTime CreateTime { get; set; }

    /// <summary>
    /// 确认时间
    /// </summary>
    public DateTime? ConfirmTime { get; set; }

    /// <summary>
    /// 入住/出行日期
    /// </summary>
    public DateTime? TravelDate { get; set; }

    /// <summary>
    /// 离店/完成日期
    /// </summary>
    public DateTime? FinishDate { get; set; }

    /// <summary>
    /// 出行开始日期（线路）
    /// </summary>
    public DateTime? TravelBeginDate { get; set; }

    /// <summary>
    /// 出行结束日期（线路）
    /// </summary>
    public DateTime? TravelEndDate { get; set; }

    /// <summary>
    /// 订单关闭时间
    /// </summary>
    public DateTime? ClosedDate { get; set; }

    #endregion


    /// <summary>
    /// 采购单号
    /// </summary>
    public string SupplierOrderId { get; set; }

    /// <summary>
    /// 确认号
    /// </summary>
    public string ConfirmCode { get; set; }

    /// <summary>
    /// 票券 - 券码
    /// </summary>
    public string TicketCodes { get; set; }

    /// <summary>
    /// 部门id 分销商所属部门
    /// </summary>
    public long TenantDeaprtmentId { get; set; }

    /// <summary>
    /// 分销商id
    /// </summary>
    public long AgencyId { get; set; }

    /// <summary>
    /// 分销商
    /// </summary>
    public string AgencyName { get; set; }

    /// <summary>
    /// 供应商id
    /// </summary>
    public long SupplierId { get; set; }

    /// <summary>
    /// 下单用户Id
    /// </summary>
    public long UserId { get; set; }

    /// <summary>
    /// 下单用户昵称-创建人
    /// </summary>
    public string UserNickName { get; set; }

    /// <summary>
    /// 销售BD
    /// </summary>
    public long? SalespersonId { get; set; }

    /// <summary>
    /// 销售BD
    /// </summary>
    public string SalespersonName { get; set; }

    /// <summary>
    /// 产品人id
    /// </summary>
    public long? DevelopUserId { get; set; }

    /// <summary>
    /// 运营人id
    /// </summary>
    public long? OperatorUserId { get; set; }

    /// <summary>
    /// 跟单人id
    /// </summary>
    public long? TrackingUserId { get; set; }

    /// <summary>
    /// 退款状态
    /// </summary>
    public AggregateOrderRefundStatus RefundStatus { get; set; }

    /// <summary>
    /// 线路 - 保险状态
    /// </summary>
    public InsureOrderStatus? Status { get; set; }

    /// <summary>
    /// 国家
    /// </summary>
    public int CountryCode { get; set; }

    /// <summary>
    /// 省份
    /// </summary>
    public int ProvinceCode { get; set; }

    /// <summary>
    /// 城市编码
    /// </summary>
    public int CityCode { get; set; }

    /// <summary>
    /// 订单类型
    /// </summary>
    public OrderType OrderType { get; set; }

    /// <summary>
    /// 线路 - 用车类型
    /// </summary>
    public CarHailingType? CarHailingType { get; set; }

    /// <summary>
    /// 门票 - 门票类型
    /// </summary>
    public ScenicTicketsType? TicketsType { get; set; }

    /// <summary>
    /// 票券 - 票券业务类型
    /// </summary>
    public TicketBusinessType? TicketBusinessType { get; set; }

    /// <summary>
    /// 用车 - 用车类型
    /// </summary>
    public CarProductType? CarProductType { get; set; }

    /// <summary>
    /// 用车 - 接送机细类
    /// </summary>
    public AirportTransferType? AirportTransferType { get; set; }

    /// <summary>
    /// 采购总额
    /// </summary>
    public decimal TotalCost { get; set; }
    
    /// <summary>
    /// 采购总折扣金额
    /// </summary>
    public decimal CostDiscountAmount { get; set; }
    
    /// <summary>
    /// 供应端采购折扣比例[0-100] 2位小数
    /// <remarks>如15表示为15%的折扣</remarks>
    /// </summary>
    public decimal CostDiscountRate { get; set; }

    /// <summary>
    /// 采购币种
    /// </summary>
    public string CostCurrencyCode { get; set; } = string.Empty;

    /// <summary>
    /// 成本汇率
    /// </summary>
    public decimal CostExchangeRate { get; set; }

    /// <summary>
    /// 采购总额-人民币 TotalCost*CostExchangeRate
    /// </summary>
    public decimal TotalCostTenantCurrency { get; set; }

    /// <summary>
    /// 酒店取消政策      
    /// </summary>
    public string HotelOrderCancelRule { get; set; }

    /// <summary>
    /// 票券 - 有效期 - 起
    /// </summary>
    public DateTime? SkuValidityBegin { get; set; }

    /// <summary>
    /// 票券 - 有效期 - 止
    /// </summary>
    public DateTime? SkuValidityEnd { get; set; }

    /// <summary>
    /// 接送车 - 上车点
    /// </summary>
    public string DeparturePointName { get; set; } = string.Empty;

    /// <summary>
    /// 接送车 - 下车点
    /// </summary>
    public string DestinationPointName { get; set; } = string.Empty;

    /// <summary>
    /// 订单已过期
    /// </summary>
    public bool OrderExpired { get; set; }

    /// <summary>
    /// 售卖渠道
    /// </summary>
    public SellingChannels SellingChannels { get; set; }

    /// <summary>
    /// 售卖渠道单号
    /// </summary>
    public string ChannelOrderNo { get; set; } = string.Empty;

    /// <summary>
    /// 联系人
    /// </summary>
    public string ContactsName { get; set; } = string.Empty;

    /// <summary>
    /// 联系人手机号
    /// </summary>
    public string ContactsPhoneNumber { get; set; } = string.Empty;

    /// <summary>
    /// 联系人邮箱
    /// </summary>
    public string ContactsEmail { get; set; } = string.Empty;

    /// <summary>
    /// 商品类型
    /// </summary>
    public SearchAggregateOrderType? AggregateOrderType { get; set; }

    /// <summary>
    /// 订单状态
    /// </summary>
    public SearchAggregateOrderStatus AggregateOrderStatus { get; set; }

    /// <summary>
    /// 下单用户手机
    /// </summary>
    public string? UserPhone { get; set; }

    /// <summary>
    /// 汇智酒店-是否团房
    /// </summary>
    public bool? IsGroupRoom { get; set; }

    /// <summary>
    /// 连续单号
    /// </summary>
    public string? SeriesNumber { get; set; }

    /// <summary>
    /// 门票-门票发货失败码
    /// </summary>
    public int? DeliveryErrorCode { get; set; }

    /// <summary>
    /// 门票-门票发货失败msg
    /// </summary>
    public string? DeliveryErrorMsg { get; set; }

    /// <summary>
    /// 资源id/酒店id
    /// </summary>
    public long ResourceId { get; set; }

    /// <summary>
    /// 资源名称/酒店名称
    /// </summary>
    public string ResourceName { get; set; }

    /// <summary>
    /// 规格id/报价策略id
    /// </summary>
    public long ProductSkuId { get; set; }

    /// <summary>
    /// 规格名称/报价策略名称
    /// </summary>
    public string ProductSkuName { get; set; }

    /// <summary>
    /// 出行人数
    /// </summary>
    public string? TravelerCount { get; set; }

    /// <summary>
    /// 出行人数名称
    /// </summary>
    public string? TravelerNames { get; set; }

    /// <summary>
    /// 出行人信息 json格式
    /// </summary>
    public string Travelers { get; set; }

    /// <summary>
    /// 乘客数 - 用车
    /// </summary>
    public int? Passengers { get; set; }

    /// <summary>
    /// vcc支付状态
    /// </summary>
    public OrderVirtualCreditCardPaymentStatus? VccPaymentStatus { get; set; }

    /// <summary>
    /// 入账抵充金额
    /// </summary>
    public decimal ReceiptOffsetReceivedAmount { get;set;}

    /// <summary>
    /// 入账抵充金额货币
    /// </summary>
    public string ReceiptOffsetReceivedAmountCurrencyCode { get; set; }

    /// <summary>
    /// 入账抵充金额（人民币）
    /// </summary>
    public decimal ReceiptOffsetReceivedAmountTarget { get; set; }

    /// <summary>
    /// 出账抵充金额
    /// </summary>
    public decimal SettlementOffsetTotalAmount { get; set; }

    /// <summary>
    /// 出账抵充金额货币
    /// </summary>
    public string SettlementOffsetTotalAmountCurrencyCode { get; set; } = Currency.CNY.ToString();

    /// <summary>
    /// 出账抵充金额（原币）
    /// </summary>
    public decimal SettlementOffsetPaymentAmount { get; set; }

    /// <summary>
    /// 出账抵充金额货币（原币）
    /// </summary>
    public string SettlementOffsetPaymentAmountCurrencyCode { get; set; }

    [JsonIgnore]
    public string TravelersCountJson { get; set; }

    #region 
    /// <summary>
    /// 供应商名称
    /// </summary>
    public string SupplierName { get; set; }

    /// <summary>
    /// 结算周期
    /// </summary>
    public SupplierSettlementPeriod? SettlementPeriod { get; set; }

    /// <summary>
    /// 产品人
    /// </summary>
    public string? DevelopUserName { get; set; }

    /// <summary>
    /// 产品人手机
    /// </summary>
    public string? DevelopUserPhone { get; set; }

    /// <summary>
    /// 运营人
    /// </summary>
    public string? OperatorUserName { get; set; }

    /// <summary>
    /// 运营人手机
    /// </summary>
    public string? OperatorUserPhone { get; set; }

    /// <summary>
    /// 跟单人
    /// </summary>
    public string? TrackingUserName { get; set; }

    /// <summary>
    /// 跟单人手机
    /// </summary>
    public string? TrackingUserPhone { get; set; }

    /// <summary>
    /// 国家
    /// </summary>
    public string? CountryName { get; set; }

    /// <summary>
    /// 省份
    /// </summary>
    public string? ProvinceCodeName { get; set; }

    /// <summary>
    /// 城市编码
    /// </summary>
    public string? CityCodeName { get; set; }


    /// <summary>
    /// 下单用户昵称-创建人手机
    /// </summary>
    public string CreateUserPhone { get; set; }

    /// <summary>
    /// 销售BD手机
    /// </summary>
    public string SalespersonPhone { get; set; }

    /// <summary>
    /// 部门名称
    /// </summary>
    public string? TenantDepartmentName { get; set; }

    /// <summary>
    /// 是否会员
    /// </summary>
    public bool? IsVip { get; set; }
    /// <summary>
    /// 等级
    /// </summary>
    public string Level { get; set; }
    /// <summary>
    /// 所属行业
    /// </summary>
    public AgencyIndustryType? IndustryType { get; set; }
    /// <summary>
    /// 状态
    /// </summary>
    public AgencyRecencyStatus AgencyRecencyStatus { get; set; }

    #endregion

    /// <summary>
    /// 保险金额 线路
    /// </summary>
    public decimal? InsurePurchaseAmount { get; set; }

    /// <summary>
    /// 保险金额币种
    /// </summary>
    public string InsurePurchaseAmountCurrencyCode { get; set; } = Currency.CNY.ToString();

    /// <summary>
    /// 运营助理Id
    /// </summary>
    public long? OperatorAssistantUserId { get; set; }

    /// <summary>
    /// 运营助理
    /// </summary>
    public string? OperatorAssistantUserName { get; set; }

    /// <summary>
    /// 运营助理手机
    /// </summary>
    public string? OperatorAssistantUserPhone { get; set; }

    /// <summary>
    /// 订单分类
    /// </summary>
    public List<OrderCategory> OrderCategoryList { get; set; } = new();

    /// <summary>
    /// 补差关联订单id数据
    /// <value>逗号分隔</value>
    /// </summary>
    public string CompensationRelatedOrderIds { get; set; }

    /// <summary>
    /// 是否补差订单
    /// </summary>
    public bool IsCompensationOrder { get; set; }
}
