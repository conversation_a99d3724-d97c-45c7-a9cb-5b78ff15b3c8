using Contracts.Common.Order.Enums;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Contracts.Common.Order.DTOs.BatchOrderOperationTask;
public class SearchOrderOperationTaskInput : PagingInput
{

    /// <summary>
    /// 操作人Id
    /// </summary>
    public long? OperationUserId { get; set; }

    /// <summary>
    /// 任务类型
    /// </summary>
    public TaskType? TaskType { get; set; }

    /// <summary>
    /// 状态
    /// </summary>
    public OperationTaskStatus? TaskStatus { get; set; }

    /// <summary>
    /// 处理详情
    /// </summary>
    public TaskResultType? TaskResultType { get; set; }

    /// <summary>
    /// 来源类型 0-订单 1-财务 默认订单列表
    /// </summary>
    public OperationTaskSourceType SourceType { get; set; } = OperationTaskSourceType.Order;

}
