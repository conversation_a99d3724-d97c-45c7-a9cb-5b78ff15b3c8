using Contracts.Common.Order.Enums;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Contracts.Common.Order.DTOs.Kingdee;
public class BiilPushDetailStatInput
{
    public long[] KingdeePushIds { get; set; }
}

public class BiilPushDetailStatOutput
{
    public long KingdeePushId { get; set; }

    public KingdeeBillType BillType { get; set; }

    public DateTime BeginDate { get; set; }

    public DateTime EndDate { get; set; }

    public KingdeeFormStatus Status { get; set; }

    public int Count { get; set; }
}
