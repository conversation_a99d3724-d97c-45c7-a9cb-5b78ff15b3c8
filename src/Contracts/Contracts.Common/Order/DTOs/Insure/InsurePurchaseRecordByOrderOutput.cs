using Contracts.Common.Order.Enums;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Contracts.Common.Order.DTOs.Insure;
public class InsurePurchaseRecordByOrderOutput : InsureProductInfo
{
    public long Id { get; set; }

    /// <summary>
    /// 保险状态
    /// </summary>
    public InsureOrderStatus Status { get; set; }

    public bool IsAuto { get; set; }

    /// <summary>
    /// 购买时间
    /// </summary>
    public DateTime? CreateTime { get; set; }

    public List<InsurePurchaseRecordDetailByOrderOutput> Details { get; set; }

    /// <summary>
    /// 总价格
    /// </summary>
    public decimal? TotalAmount { get; set; }
}


public class InsurePurchaseRecordDetailByOrderOutput
{
    /// <summary>
    /// 购买时间
    /// </summary>
    public DateTime CreateTime { get; set; }

    public long BaseOrderId { get; set; }
    /// <summary>
    /// 保单号
    /// </summary>
    public string PolicyNo { get; set; }

    /// <summary>
    /// 起保日
    /// </summary>
    public DateTime? StartTime { get; set; }

    /// <summary>
    /// 终保日
    /// </summary>
    public DateTime? EndTime { get; set; }

    /// <summary>
    /// 保险产品
    /// </summary>
    public string PlanFullName { get; set; }

    /// <summary>
    /// 投保人
    /// </summary>
    public string Name { get; set; }

    /// <summary>
    /// 价格
    /// </summary>
    public decimal Price { get; set; }

    /// <summary>
    /// 状态-true 购买成功
    /// </summary>
    public bool Status { get; set; } = true;

    /// <summary>
    /// 错误信息
    /// </summary>
    public string ErrorMsg { get; set; }

    /// <summary>
    /// 数据Id
    /// </summary>
    public long DataId { get; set; }
}

public class GetInsureStatusFlowProcessOutput
{
    public long InsurePurchaseRecordId { get; set; }
}

public class GetInsureStatusFlowProcessInput : GetInsureStatusFlowProcessOutput
{
}