namespace Contracts.Common.Order.DTOs.HotelApiOrder;

public class HopOrderStatusNotifyInput
{
    public Head head { get; set; }
    /// <summary>
    /// 汇智国际旅游订单号
    /// </summary>
    public string orderid { get; set; }

    /// <summary>
    /// 分销商单号 团房单号为拼接字符串(申请单号,价格策略数量,订单号)
    /// </summary>
    public string outerorderId { get; set; }

    /// <summary>
    /// 推送的订单状态，1确认 13拒单
    /// </summary>
    public int orderstatus { get; set; }

    /// <summary>
    /// 确认号
    /// </summary>
    public string hotelaffirmno { get; set; }
}

public class Head
{
    public string appKey { get; set; }
    public string timestamp { get; set; }
    public string sign { get; set; }
}
