using Contracts.Common.Notify.DTOs.StaffNotify;
using Contracts.Common.Order.Enums;

namespace Contracts.Common.Order.DTOs.MessageNotify;
public class GroupRoomPaymentReminderNotifyDto
{
    public long TenantId { get; set; }

    public string ContactPhone { get; set; }
    /// <summary>
    /// 售卖平台
    /// </summary>
    public SellingPlatform SellingPlatform { get; set; } = SellingPlatform.B2BWeb;

    /// <summary>
    /// 指定酒店名称
    /// </summary>
    public string? HotelName { get; set; }

    /// <summary>
    /// 入住日期
    /// </summary>
    public DateTime? CheckInDate { get; set; }

    /// <summary>
    /// 离店日期
    /// </summary>
    public DateTime? CheckOutDate { get; set; }

    /// <summary>
    /// 间数
    /// </summary>
    public int RoomCount { get; set; }

    /// <summary>
    /// 晚数
    /// </summary>
    public int NightCount { get; set; }

    /// <summary>
    /// 含早数
    /// </summary>
    public int NumberOfBreakfast { get; set; }

    /// <summary>
    /// 首付金额
    /// </summary>
    public decimal InitialPaymentAmount { get; set; }

    /// <summary>
    /// 首付日期
    /// </summary>
    public DateTime? InitialPaymentDate { get; set; }

    /// <summary>
    /// 尾款金额
    /// </summary>
    public decimal FinalPaymentAmount { get; set; }

    /// <summary>
    /// 尾款日期
    /// </summary>
    public DateTime? FinalPaymentDate { get; set; }

    public string PaymentCurrencyCode { get; set; }

    /// <summary>
    /// 品牌名称
    /// </summary>
    public string BrandName { get; set; }

    /// <summary>
    /// 房型名称
    /// </summary>
    public string RoomName { get; set; }

    /// <summary>
    /// 表单id
    /// </summary>
    public long GroupBookingApplicationFormId { get; set; }

    /// <summary>
    /// 分销商Id
    /// </summary>
    public long? AgencyId { get; set; }

    /// <summary>
    /// 申请人id
    /// </summary>
    public long? ApplicantId { get; set; }

    /// <summary>
    /// 供应商id
    /// </summary>
    public long? SupplierId { get; set; }

    /// <summary>
    /// 分销商名称
    /// </summary>
    public string AgencyName { get; set; }

    public GroupRoomPaymentReminderNotifyRegionDto[]? RegionCodes { get; set; }

    public StaffUserDto[] Users { get; set; } = Array.Empty<StaffUserDto>();
}

public class GroupRoomPaymentReminderNotifyRegionDto
{
    /// <summary>
    /// 国家与区域代码
    /// </summary>
    public int? CountryCode { get; set; }

    /// <summary>
    /// 省份代码
    /// </summary>
    public int? ProvinceCode { get; set; }
}