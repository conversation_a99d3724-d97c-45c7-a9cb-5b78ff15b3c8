using Contracts.Common.Notify.DTOs.StaffNotify;
using Contracts.Common.Notify.Enums;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Contracts.Common.Order.DTOs.MessageNotify;
public class HotelGroupHotelQuotationReminderDto
{
    public long TenantId { get; set; }

    public long AgencyId { get; set; }

    /// <summary>
    /// 分销商名称
    /// </summary>
    public string AgencyName { get; set; }

    /// <summary>
    /// 申请单Id
    /// </summary>
    public long ApplicationFormId { get; set; }

    /// <summary>
    /// Hotel_GroupRoom_Quoted Hotel_GroupRoom_UnableQuote Hotel_GroupRoom_Expired
    /// </summary>
    public NotifyEventSubType NotifyEventSubType { get; set; }

    public SendToTheRole SendToTheRole { get; set; }

    public List<HotelQuotationReminderDto> Reminders { get; set; } = new();

}

public class HotelQuotationReminderDto
{
    /// <summary>
    /// 发送到的员工列表
    /// </summary>
    public List<StaffUserDto> Users { get; set; }

    /// <summary>
    /// 酒店名称
    /// </summary>
    public string HotelName { get; set; }

    /// <summary>
    /// 国家编码
    /// </summary>
    public int? CountryCode { get; set; }

    public int? ProvinceCode { get; set; }
}
