using Contracts.Common.Order.Enums;

namespace Contracts.Common.Order.DTOs.MessageNotify;
public class GroupRoomFinalPaymentNotifyDto
{

    public long TenantId { get; set; }

    public string Phone { get; set; }


    /// <summary>
    /// 指定酒店名称
    /// </summary>
    public string? HotelName { get; set; }

    /// <summary>
    /// 售卖平台
    /// </summary>
    public SellingPlatform SellingPlatform { get; set; } = SellingPlatform.B2BWeb;

    /// <summary>
    /// 入住日期
    /// </summary>
    public DateTime? CheckInDate { get; set; }

    /// <summary>
    /// 离店日期
    /// </summary>
    public DateTime? CheckOutDate { get; set; }

    /// <summary>
    /// 品牌名称
    /// </summary>
    public string BrandName { get; set; }
}
