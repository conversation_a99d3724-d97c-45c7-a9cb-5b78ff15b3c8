using Contracts.Common.Order.Enums;
using Contracts.Common.Product.Enums;

namespace Contracts.Common.Order.DTOs.ReservationOrder
{
    public class ReservationOrderInfo
    {
        public long Id { get; set; }

        /// <summary>
        /// 售卖平台
        /// </summary>
        public SellingPlatform SellingPlatform { get; set; }

        /// <summary>
        /// 订单状态
        /// </summary>
        public ReservationStatus Status { get; set; }

        /// <summary>
        /// 是否标记发单
        /// </summary>
        public bool IsSendPDF { get; set; }

        /// <summary>
        /// 出行开始时间
        /// </summary>
        public DateTime TravelDateBegin { get; set; }

        /// <summary>
        /// 出行结束时间
        /// </summary>
        public DateTime TravelDateEnd { get; set; }

        /// <summary>
        /// 包含晚数
        /// </summary>
        public int SkuNumberOfNights { get; set; }

        /// <summary>
        /// 出行天数
        /// </summary>
        public int TravelDateCount
        {
            get
            {
                return TravelDateEnd.Subtract(TravelDateBegin).Days;
            }
        }

        /// <summary>
        /// 预约数量
        /// </summary>
        public int Quantity { get; set; }

        /// <summary>
        /// 下单人
        /// </summary>
        public string ContactsName { get; set; }

        /// <summary>
        /// 联系电话
        /// </summary>
        public string ContactsPhoneNumber { get; set; }

        /// <summary>
        /// 产品名称
        /// </summary>
        public string ProductName { get; set; }

        /// <summary>
        /// 规格名称
        /// </summary>
        public string ProductSkuName { get; set; }

        /// <summary>
        /// 票券类型
        /// </summary>
        public TicketBusinessType ProductTicketBusinessType { get; set; }
    }
}
