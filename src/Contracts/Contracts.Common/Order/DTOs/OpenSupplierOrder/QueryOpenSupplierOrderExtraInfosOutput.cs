using Contracts.Common.Product.Enums;

namespace Contracts.Common.Order.DTOs.OpenSupplierOrder;

public class QueryOpenSupplierOrderExtraInfosOutput
{
    public long BaseOrderId { get; set; }

    /// <summary>
    /// 订单附加信息数据
    /// </summary>
    public List<QueryOpenSupplierOrderExtraInfoItem> OrderExtraInfos { get; set; } = new();
}

public class QueryOpenSupplierOrderExtraInfoItem
{
    /// <summary>
    /// 附加信息标识符类型
    /// </summary>
    public ApiSkuExtraInfoDataType DataType { get; set; }
    
    /// <summary>
    /// 选择类型
    /// </summary>
    public ApiSkuExtraInfoValueType ValueType { get; set; }
    
    /// <summary>
    /// 附加信息选项key
    /// <value>非下拉无选项key</value>
    /// </summary>
    public string? OptionKey { get; set; }
    
    /// <summary>
    /// 附加信息选项value
    /// </summary>
    public string OptionValue { get; set; }
}