using Contracts.Common.Order.DTOs.InvoiceTitle;
using Contracts.Common.Order.Enums;
using Contracts.Common.User.Enums;

namespace Contracts.Common.Order.DTOs.Invoice;
public class ApplyInput
{
    /// <summary>
    /// 发票接收邮箱
    /// </summary>
    public string EmailAddress { get; set; }

    /// <summary>
    /// 发票抬头Id
    /// </summary>
    public long? InvoiceTitleId { get; set; }

    /// <summary>
    /// 发票内容
    /// </summary>
    public string ContentType { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public long OrderId { get; set; }

    /// <summary>
    /// 开票金额(价税合计)
    /// </summary>
    public decimal InvoiceAmount { get; set; }

    /// <summary>
    /// 开票来源
    /// </summary>
    public InvoiceSourceChannel SourceChannel { get; set; }

    /// <summary>
    /// 创建人Id
    /// </summary>
    public long CreatorId { get; set; }

    /// <summary>
    /// 创建人名称
    /// </summary>
    public string? CreatorName { get; set; }

    /// <summary>
    /// 抬头类型
    /// </summary>
    public InvoiceTitleType TitleType { get; set; }

    /// <summary>
    /// 发票抬头
    /// </summary>
    public InvoiceTitleDto? InvoiceTitleDto { get; set; }

    /// <summary>
    /// 操作人信息
    /// </summary>
    public OperationUserDto OperationUser { get; set; }

    /// <summary>
    /// 系统来源端
    /// </summary>
    public SystemType SystemType { get; set; }

}