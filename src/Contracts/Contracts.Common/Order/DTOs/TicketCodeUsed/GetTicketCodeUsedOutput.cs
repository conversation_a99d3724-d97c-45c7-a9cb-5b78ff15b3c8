using Contracts.Common.Order.Enums;

namespace Contracts.Common.Order.DTOs.TicketCodeUsed
{
    public class GetTicketCodeUsedOutput
    {
        public OrderType OrderType { get; set; }
        public long BaseOrderId { get; set; }
        public long SubOrderId { get; set; }
        public long ReservationOrderId { get; set; }
        public string ResourceName { get; set; }
        public string ProductName { get; set; }
        public string ProductSkuName { get; set; }
        public string ImagePath { get; set; }
        public string ContactsName { get; set; }
        public string ContactsPhoneNumber { get; set; }
        public int Count { get; set; }
        public string Traveler { get; set; }
        public DateTime? TravelDateBegin { get; set; }
        public DateTime? TravelDateEnd { get; set; }
        public int TravelDateCount { get; set; }
        /// <summary>
        /// 核销备注
        /// </summary>
        public string Message { get; set; }
        /// <summary>
        /// 订单创建时间
        /// </summary>
        public DateTime CreateTime { get; set; }
        /// <summary>
        /// 核销时间
        /// </summary>
        public DateTime UsedTime { get; set; }
    }
}
