using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Contracts.Common.Order.DTOs.MailOrder;
public class MailOrderReceiverAddressDto
{
    /// <summary>
    /// 主单号
    /// </summary>
    public long BaseOrderId { get; set; }

    /// <summary>
    /// 客户的收件人地址Id
    /// </summary>
    public long ReceiverAddressId { get; set; }

    /// <summary>
    /// 姓名
    /// </summary>
    public string Name { get; set; }

    /// <summary>
    /// 手机号
    /// </summary>
    public string PhoneNumber { get; set; }

    /// <summary>
    /// 省
    /// </summary>
    public int ProvinceCode { get; set; }
    public string ProvinceName { get; set; }

    /// <summary>
    /// 市
    /// </summary>
    public int CityCode { get; set; }
    public string CityName { get; set; }

    /// <summary>
    /// 区
    /// </summary>
    public int DistrictCode { get; set; }
    public string DistrictName { get; set; }

    /// <summary>
    /// 详细地址
    /// </summary>
    public string Address { get; set; }
}
