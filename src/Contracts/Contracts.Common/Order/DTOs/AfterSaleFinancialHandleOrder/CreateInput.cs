using Contracts.Common.Order.Enums;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Contracts.Common.Order.DTOs.AfterSaleFinancialHandleOrder;
public class CreateInput
{
    /// <summary>
    /// 抵冲单id
    /// </summary>
    public long OffsetOrderId { get; set; }

    /// <summary>
    /// 业务类型
    /// </summary>
    public OffsetOrderBusinessType BusinessType { get; set; }

    /// <summary>
    /// 售后类型
    /// </summary>
    public AfterSaleFinancialHandleType HandleType { get; set; }

    /// <summary>
    /// 金额(用正负区分)
    /// </summary>
    public decimal Amount { get; set; }

    public string CurrencyCode { get; set; }

    /// <summary>
    /// 分销商id
    /// </summary>
    public long AgencyId { get; set; }

    public string AgencyName { get; set; }

    public long TenantId { get; set; }
}