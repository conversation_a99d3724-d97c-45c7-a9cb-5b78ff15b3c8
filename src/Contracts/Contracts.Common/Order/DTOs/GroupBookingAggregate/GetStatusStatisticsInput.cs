using Contracts.Common.Order.Enums;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Contracts.Common.Order.DTOs.GroupBookingAggregate;
public class GetStatusStatisticsInput
{
    public DateTime StartDate { get; set; }

    public DateTime EndDate { get; set; }

    public List<long> AreaIds { get; set; } = new();

    /// <summary>
    /// 跟进运营id 商户员工id
    /// </summary>
    public List<long?> OperatorUserIds { get; set; } = new();


    /// <summary>
    /// 国家/地区代码列表
    /// </summary>
    public List<int> CountryCodes { get; set; } = new();

    /// <summary>
    /// 统计维度类型
    /// </summary>
    public StatisticalDimensionType DimensionType { get; set; } = StatisticalDimensionType.Area;
}
