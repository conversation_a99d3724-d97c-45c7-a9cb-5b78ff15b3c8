using Contracts.Common.Order.Enums;
using Contracts.Common.Scenic.Enums;

namespace Contracts.Common.Order.DTOs.ScenicTicketPurchaseOrder;

public class ExportPurchaseOrderListInput
{
    /// <summary>
    /// 批次名称
    /// </summary>
    public string? BatchName { get; set; }
    
    /// <summary>
    /// 门票Id
    /// </summary>
    public long? TicketId { get; set; }
    
    /// <summary>
    /// 关联供应商Id
    /// </summary>
    public long? SupplierId { get; set; }
    
    /// <summary>
    /// 景点Id
    /// </summary>
    public long? ScenicSpotId { get; set; }

    /// <summary>
    /// 创建时间-起
    /// </summary>
    public DateTime? CreateTimeBegin { get; set; }
    
    /// <summary>
    /// 创建时间-止
    /// </summary>
    public DateTime? CreateTimeEnd { get; set; }
    
    /// <summary>
    /// 有效期结束时间 - 起
    /// </summary>
    public DateTime? ValidityEndTimeBegin { get; set; }

    /// <summary>
    /// 有效期结束时间 - 止
    /// </summary>
    public DateTime? ValidityEndTimeEnd { get; set; }
    
    /// <summary>
    /// 门票类型
    /// </summary>
    public ScenicTicketsType? TicketsType { get; set; }
        
    /// <summary>
    /// 出库状态
    /// true-已出库 false-未出库 null-全部
    /// </summary>
    /// <returns></returns>
    public bool? Outbound { get; set; }

    /// <summary>
    /// 采购单号
    /// </summary>
    public string? SupplierOrderId { get; set; }
    
    /// <summary>
    /// 有效期预警类型
    /// </summary>
    public PurchaseOrderValidDaysWarningType? ValidDaysWarningType { get; set; }
}