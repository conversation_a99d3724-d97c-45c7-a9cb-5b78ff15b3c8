namespace Contracts.Common.Permission.Const;
public static class AgencyUserAclKey
{
    /// <summary>
    /// 直连酒店分级标签查看权限
    /// </summary>
    public const string HotelLabelDirect = "hotel.label.direct";

    /// <summary>
    /// 主推酒店标签查看权限
    /// </summary>
    public const string HotelLabelMain = "hotel.label.main";

    /// <summary>
    /// 下单
    /// </summary>
    public const string AllOrderCreate = "all.order.create";

    /// <summary>
    /// 额度支付
    /// </summary>
    public const string AccountCreditPay = "account.credit.pay";

    /// <summary>
    /// 企业钱包
    /// </summary>
    public const string PageCompanyWallet = "page.companyWallet";

    /// <summary>
    /// 财务结算
    /// </summary>
    public const string PageSettlement = "page.settlement";

    /// <summary>
    /// 申请订单发票
    /// </summary>
    public const string InvoiceAllApply = "invoice.all.apply";

    /// <summary>
    /// 发票列表
    /// </summary>
    public const string PageInvoicelist = "page.invoicelist";

    /// <summary>
    /// 申请团房
    /// </summary>
    public const string GrouproomApply = "grouproom.apply";

    /// <summary>
    /// 使用优惠券
    /// </summary>
    public const string CouponAllUse = "coupon.all.use";

    /// <summary>
    /// 账号管理
    /// </summary>
    public const string PageUserManage = "page.usermanage";

    /// <summary>
    /// 酒店订单
    /// </summary>
    public const string PageOrderHotel = "page.order.hotel";

    /// <summary>
    /// 酒店套餐订单
    /// </summary>
    public const string PageOrderHotelPackage = "page.order.hotelpackage";

    /// <summary>
    /// 房券订单
    /// </summary>
    public const string PageOrderRoomvoucher = "page.order.roomvoucher";

    /// <summary>
    /// 餐饮订单
    /// </summary>
    public const string PageOrderCatering = "page.order.catering";

    /// <summary>
    /// 线路订单
    /// </summary>
    public const string PageOrderLine = "page.order.line";

    /// <summary>
    /// 门票订单
    /// </summary>
    public const string PageOrderScenicticket = "page.order.scenicticket";

    /// <summary>
    /// 查看所有订单
    /// </summary>
    public const string OrderViewallData = "order.viewalldata";

    /// <summary>
    /// 工单列表
    /// </summary>
    public const string PageWorkOrderList = "page.workorder.list";

    /// <summary>
    /// 延时支付
    /// </summary>
    public const string OrderDeferredPayment = "order.deferredpayment";
}
