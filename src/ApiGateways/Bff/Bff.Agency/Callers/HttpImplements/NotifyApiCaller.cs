using Common.Caller;
using Common.ServicesHttpClient;
using Microsoft.Extensions.Options;
using EfCoreExtensions.Abstract;
using SiteMessage = Contracts.Common.Notify.DTOs.SiteMessage;

namespace Bff.Agency.Callers.HttpImplements;

public class NotifyApiCaller : HttpCallerBase, INotifyApiCaller
{
    public NotifyApiCaller(IOptions<ServicesAddress> servicesAddress, IHttpClientFactory httpClientFactory)
       : base(servicesAddress.Value.Notify, httpClientFactory)
    {
    }

    #region SiteMessage

    public Task<SiteMessage.DetailOutput> SiteMessageRead(SiteMessage.ReadInput input)
    {
        var relativePath = "/SiteMessage/Read";
        return PostAsync<SiteMessage.ReadInput, SiteMessage.DetailOutput>(relativePath, input);
    }

    public Task<SiteMessage.DetailOutput> SiteMessageDetail(SiteMessage.DetailInput input)
    {
        var relativePath = "/SiteMessage/Detail";
        return PostAsync<SiteMessage.DetailInput, SiteMessage.DetailOutput>(relativePath, input);
    }

    public Task<PagingModel<SiteMessage.SearchOutput>> SiteMessageSearch(SiteMessage.SearchInput input)
    {
        var relativePath = "/SiteMessage/Search";
        return PostAsync<SiteMessage.SearchInput, PagingModel<SiteMessage.SearchOutput>>(relativePath, input);
    }

    public Task SiteMessageBatchRead(SiteMessage.BatchReadInput input)
    {
        var relativePath = "/SiteMessage/BatchRead";
        return PostAsync<SiteMessage.BatchReadInput>(relativePath, input);
    }

    #endregion
}
