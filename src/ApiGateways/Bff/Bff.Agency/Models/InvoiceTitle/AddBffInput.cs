using Contracts.Common.Order.Enums;
using Contracts.Common.Reflection;

namespace Bff.Agency.Models.InvoiceTitle;

public class AddBffInput
{
    /// <summary>
    /// 抬头类型
    /// </summary>
    public InvoiceTitleType? TitleType { get; set; }

    /// <summary>
    /// 抬头
    /// </summary>
    [PropertyMsg(Name = "抬头名称")]
    public string Title { get; set; }

    /// <summary>
    /// 纳税识别号
    /// </summary>
    [PropertyMsg(Name = "纳税识别号")]
    public string? ITIN { get; set; }

    /// <summary>
    /// 地址
    /// </summary>
    [PropertyMsg(Name = "地址")]
    public string? Address { get; set; }

    /// <summary>
    /// 联系电话
    /// </summary>
    [PropertyMsg(Name = "联系电话", IsSensitive = true, SensitiveType = SensitiveDataType.Phone)]
    public string? ContactNumber { get; set; }

    /// <summary>
    /// 开户行
    /// </summary>
    [PropertyMsg(Name = "开户行")]
    public string? BankName { get; set; }

    /// <summary>
    /// 银行账号
    /// </summary>
    [PropertyMsg(Name = "银行账号", IsSensitive = true)]
    public string? BankNumber { get; set; }
}
