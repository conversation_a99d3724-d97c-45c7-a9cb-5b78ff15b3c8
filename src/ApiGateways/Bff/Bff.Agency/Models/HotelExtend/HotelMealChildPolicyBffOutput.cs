using Contracts.Common.Resource.Enums;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Bff.Agency.Models.Hotel;
public class HotelMealChildPolicyBffOutput
{
    /// <summary>
    /// 酒店id
    /// </summary>
    public long HotelId { get; set; }

    /// <summary>
    /// 范围值 小
    /// </summary>
    public string Min { get; set; }

    /// <summary>
    /// 范围值 大
    /// </summary>
    public string Max { get; set; }

    /// <summary>
    /// 限制类型
    /// </summary>
    public HotelChildExistingBedPolicyType LimitType { get; set; }

    /// <summary>
    /// 价格
    /// </summary>
    public decimal Amount { get; set; }

    /// <summary>
    /// 币种
    /// </summary>
    public string Currency { get; set; }

    public DateTime CreateTime { get; set; } = DateTime.Now;

    public DateTime? UpdateTime { get; set; } = DateTime.Now;
}
