using Contracts.Common.Product.DTOs.CarProduct;
using Contracts.Common.Product.DTOs.CarProductSku;
using Contracts.Common.Product.DTOs.CarProductSkuCalendarPrice;
using Contracts.Common.Product.Enums;

namespace Bff.Agency.Models.CarProduct;

public class BffCarProductSkuDetailCalendarPricesOutput : CarProductSkuDetailDto
{
    public new List<BffCarProductSkuCalendarPricesOutput> CalendarPrices { get; set; } = new();

    /// <summary>
    /// sku B2B最小售价
    /// </summary>
    public decimal? ChannelPrice { get; set; }

}

public class BffCarProductSkuCalendarPricesOutput
{
    /// <summary>
    /// 是否售卖
    /// </summary>
    public bool IsSale { get; set; } = false;


    #region CalendarPrice
    /// <summary>
    /// 日期
    /// </summary>
    public DateTime Date { get; set; }

    /// <summary>
    /// 可用库存
    /// </summary>
    public int AvailableQuantity { get; set; }

    /// <summary>
    /// B2B售价
    /// </summary>
    public decimal? ChannelPrice { get; set; }
    #endregion

    /// <summary>
    /// 服务项目
    /// </summary>
    public new List<BffCarProductSkuServiceItemOutput> ServiceItems { get; set; } = new();
}

public class BffCarProductSkuServiceItemOutput : CarProductSkuServiceItemOutput
{
    /// <summary>
    /// 服务日历价
    /// </summary>
    public new List<BffCarProductCalendarPriceAndQuantityInfo> ServicePriceAndQuantityInfos { get; set; } = new();
}

public class BffCarProductCalendarPriceAndQuantityInfo
{
    public long Id { get; set; }
    /// <summary>
    /// 日期
    /// </summary>
    public DateTime Date { get; set; }

    /// <summary>
    /// 可用库存
    /// </summary>
    public int AvailableQuantity { get; set; }

    /// <summary>
    /// 售卖状态
    /// </summary>
    public bool Enabled { get; set; }

    /// <summary>
    /// B2b售价
    /// </summary>
    public decimal? ChannelPrice { get; set; }
}

