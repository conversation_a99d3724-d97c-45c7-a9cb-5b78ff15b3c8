using Swashbuckle.AspNetCore.Annotations;

namespace Bff.Agency.Models.LineProduct;

public class QueryLineProductDailyMinimumPriceBffInput
{
    /// <summary>
    /// 产品id
    /// </summary>
    [SwaggerSchema(Nullable = false)]
    public long ProductId { get; set; }

    /// <summary>
    /// 开始日期
    /// <remarks>默认今天</remarks>
    /// </summary>
    [SwaggerSchema(Nullable = true)]
    public DateTime StartDate { get; set; } = DateTime.Today;
    
    /// <summary>
    /// 结束日期
    /// <remarks>默认基于开始增加179天</remarks>
    /// </summary>
    [SwaggerSchema(Nullable = true)]
    public DateTime EndDate { get; set; } = DateTime.Today.AddDays(179);
}