using Contracts.Common.Hotel.DTOs.Tag;

namespace Bff.Agency.Models.B2BIndexPage;

public class SearchApiHotelListOutput
{
    /// <summary>
    /// 酒店id
    /// </summary>
    public long Id { get; set; }

    /// <summary>
    /// 中文名称
    /// </summary>
    public string ZHName { get; set; }

    /// <summary>
    /// 英文名称
    /// </summary>
    public string ENName { get; set; }

    /// <summary>
    /// 国家
    /// </summary>
    public int CountryCode { get; set; }

    /// <summary>
    /// 国家名称
    /// </summary>
    public string CountryName { get; set; }

    /// <summary>
    /// 省
    /// </summary>
    public int ProvinceCode { get; set; }

    /// <summary>
    /// 省份名称
    /// </summary>
    public string ProvinceName { get; set; }

    /// <summary>
    /// 市
    /// </summary>
    public int CityCode { get; set; }

    /// <summary>
    /// 城市名称
    /// </summary>
    public string CityName { get; set; }

    /// <summary>
    /// 区
    /// </summary>
    public int DistrictCode { get; set; }

    /// <summary>
    /// 区名称
    /// </summary>
    public string DistrictName { get; set; }

    /// <summary>
    /// 星级
    /// </summary>
    public decimal StarLevel { get; set; }

    /// <summary>
    /// 首图
    /// </summary>
    public string FirstPhoto { get; set; }

    /// <summary>
    /// 汇智酒店的标签
    /// </summary>
    public IEnumerable<GetTagsOutput> Tags { get; set; }
}
