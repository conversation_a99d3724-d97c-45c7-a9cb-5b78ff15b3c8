using AutoMapper;
using Bff.Agency.Callers;
using Bff.Agency.Models.GDSHotel;
using Bff.Agency.Models.Hotel;
using Cit.Storage.Redis;
using Contracts.Common.Product.DTOs.AgencyChannelCommissionSettings;
using Contracts.Common.Product.Enums;
using Contracts.Common.Resource.DTOs;
using Contracts.Common.Resource.DTOs.GDSHotel;
using Contracts.Common.Resource.DTOs.Hotel;
using Contracts.Common.Resource.DTOs.StandardHotelRoomCode;
using Contracts.Common.Resource.DTOs.YouxiaHotel;
using Contracts.Common.Resource.Enums;
using Contracts.Common.Tenant.Enums;
using Newtonsoft.Json;
using System.Text.RegularExpressions;

public class GDSHotelService : IGDSHotelService
{
    private readonly string GDSHOTEL_MinPrice = "gds:{0}:minprice:{1}:{2}";

    private readonly IMapper _mapper;
    private readonly IResourceApiCaller _resourceApiCaller;
    private readonly IRedisClient _redisClient;
    private readonly IProductApiCaller _productApiCaller;
    private readonly ITenantApiCaller _tenantApiCaller;

    private readonly ILogger<GDSHotelService> _logger;

    public GDSHotelService(IMapper mapper,
        IResourceApiCaller resourceApiCaller,
        IRedisClient redisClient,
        IProductApiCaller productApiCaller,
        ITenantApiCaller tenantApiCaller,
        ILogger<GDSHotelService> logger)
    {
        _mapper = mapper;
        _resourceApiCaller = resourceApiCaller;
        _redisClient = redisClient;
        _productApiCaller = productApiCaller;
        _tenantApiCaller = tenantApiCaller;
        _logger = logger;
    }

    public async Task<List<SearchGDSHotelMinPriceOutput>> SearchHotelMinPrice(SearchGDSHotelMinPriceBffInput input)
    {
        var result = new List<SearchGDSHotelMinPriceOutput>();
        var searchInput = _mapper.Map<SearchGDSHotelMinPriceInput>(input);
        searchInput.Hotels = input.HotelIds.Select(x => new SearchGDSHotelDetailMinPriceInput { 
            HotelId = x,
        }).ToList();
        //校验分销商信息
        var agencyInfo = await _tenantApiCaller.GetAgencyDetail(input.Provider);
        if (agencyInfo.PriceGroupId is null or <= 0) return result;
        var priceGroupId = agencyInfo.PriceGroupId.Value;

        result = await GetGDSHotelMinPriceCache(searchInput);
        if (result.Count() == input.HotelIds.Count())
            return result.Where(x => x.Price > 0).ToList();

        // 并行任务，并独立处理异常
        var sabreMinPriceTask = ExecuteGdsSearchAsync(
            () => SearchGDSHotelMinPrice(input, agencyInfo.PriceGroupId),
            "Sabre 查询最低价");

        var youxiaMinPriceTask = ExecuteGdsSearchAsync(
            () => SearchYouxiaHotelMinPrice(input, agencyInfo.PriceGroupId),
            "youxia 查询最低价");

        Task.WaitAll(sabreMinPriceTask, youxiaMinPriceTask);

        var gdsMinPrices = new List<SearchGDSHotelMinPriceOutput>();
        var sabreMinPrices = sabreMinPriceTask.Result;
        var youxiaMinPrices = youxiaMinPriceTask.Result;

        //var gdsMinPriceTask = SearchGDSHotelMinPrice(input, agencyInfo.PriceGroupId);
        //var youxiaMinPriceTask = SearchYouxiaHotelMinPrice(input, agencyInfo.PriceGroupId);
        //Task.WaitAll(gdsMinPriceTask, youxiaMinPriceTask);

        //var gdsMinPrices = gdsMinPriceTask.Result;
        //var youxiaMinPrices = youxiaMinPriceTask.Result;

        var minPrices = gdsMinPrices.Union(sabreMinPrices).Union(youxiaMinPrices).ToList();
        foreach (var minPrice in minPrices.GroupBy(x=> x.HotelId))
        {
            if (minPrice.Any(x => x.Price > 0))
            {
                var price = minPrice.Where(x => x.Price > 0).Select(x => x.Price).Min();
                var gdsHotelMinPrice = new SearchGDSHotelMinPriceOutput
                {
                    HotelId = minPrice.Key,
                    Price = price,
                    CurrencyCode = "CNY",
                };
                result.Add(gdsHotelMinPrice);
                await SetGDSHotelMinPriceCache(new List<SearchGDSHotelMinPriceOutput> { gdsHotelMinPrice }, searchInput);
            }
            else if (minPrice.Count() == 1 && minPrice.Any(x => x.Price == -1))
            {
                var gdsHotelMinPrice = new SearchGDSHotelMinPriceOutput
                {
                    HotelId = minPrice.Key,
                    Price = -1,
                    CurrencyCode = "CNY",
                };
                await SetGDSHotelMinPriceCache(new List<SearchGDSHotelMinPriceOutput> { gdsHotelMinPrice }, searchInput);
            }
        }

        result = result.Where(x => x.Price > 0).ToList();
        return result;
    }

    #region sabre
    public async Task<List<SearchGDSHotelPriceStrategyOutput>> SearchSabrePriceStrategy(SearchGDSHotelPriceStrategyInput input, long? agencyGroupId)
    {
        var result = new List<SearchGDSHotelPriceStrategyOutput>();
        var commissionSettings = await _productApiCaller.QueryAgencyChannelCommissionSettings(new QueryAgencyChannelCommissionInput
        {
            PriceGroupId = agencyGroupId ?? 0,
            ProductTypes = new List<CommissionProductType> { CommissionProductType.GDSHotel },
        });
        if (commissionSettings?.Any() is not true)
            return result;

        var gdsHotel = await _resourceApiCaller.GDSHotelDetail(input.HotelId);
        if (gdsHotel == null || !gdsHotel.ThirdHotelId.HasValue)
            return result;

        input.GDSHotelId = gdsHotel.ThirdHotelId.Value.ToString();
        input.GDSSabreHotelCodeType = gdsHotel.GDSSabreHotelCodeType;

        result = await _resourceApiCaller.SearchGDSPriceStrategy(input);
        var commissionSetting = commissionSettings?.FirstOrDefault();
        foreach (var room in result)
        {
            foreach (var priceStrategy in room.PriceStrategies)
            {
                decimal? supplierCommissionFee = priceStrategy?.Commission?.Amount;
                decimal? suppliercommissionRate = priceStrategy?.Commission?.Percent;
                var commissionRate = (suppliercommissionRate * commissionSetting?.CommissionSettingValue) / 100;
                var commissionFee = supplierCommissionFee * commissionSetting?.CommissionSettingValue / 100;
                // 保留两位小数并向下取整
                commissionFee = Math.Floor(commissionFee!.Value * 100) / 100;
                priceStrategy.Commission.CurrencyCode = input.CurrencyCode;
                priceStrategy.Commission.Amount = commissionFee ?? 0;
                priceStrategy.Commission.Percent = commissionRate ?? 0;
                priceStrategy.HotelId = input.HotelId;
            }
        }

        return result;
    }

    private async Task<List<SearchGDSHotelMinPriceOutput>> SearchGDSHotelMinPrice(SearchGDSHotelMinPriceBffInput input, long? agencyGroupId)
    {
        var result = new List<SearchGDSHotelMinPriceOutput>();

        var commissionSettings = await _productApiCaller.QueryAgencyChannelCommissionSettings(new QueryAgencyChannelCommissionInput
        {
            PriceGroupId = agencyGroupId ?? 0,
            ProductTypes = new List<CommissionProductType> { CommissionProductType.GDSHotel },
        });

        if (commissionSettings?.Any() is not true)
            return result;

        var searchHotelIds = input.HotelIds;
        var searchInput = _mapper.Map<SearchGDSHotelMinPriceInput>(input);
        //Sabre api
        if (input.Nationality == null)
            searchInput.Nationality = "CN";
        else
            searchInput.Nationality = input.Nationality.IsoCode;
        searchInput.Hotels = input.HotelIds
            .Select(x => new SearchGDSHotelDetailMinPriceInput
            {
                HotelId = x,
            })
            .ToList();

        result = await _resourceApiCaller.SearchGDSHotelMinPrice(searchInput);
        return result;
    }
    #endregion

    #region youxia
    public async Task<List<SearchGDSHotelPriceStrategyOutput>> SearchYouxiaPriceStrategy(SearchGDSHotelPriceStrategyInput input, long? agencyGroupId)
    {
        var result = new List<SearchGDSHotelPriceStrategyOutput>();

        var commissionSettings = await _productApiCaller.QueryAgencyChannelCommissionSettings(new QueryAgencyChannelCommissionInput
        {
            PriceGroupId = agencyGroupId ?? 0,
            ProductTypes = new List<CommissionProductType> { CommissionProductType.YouXiaHotel },
        });
        if (commissionSettings?.Any() is not true)
            return result;

        var hotelBinds = await _resourceApiCaller.GetGDSThirdHotelBinds(new GetGDSThirdHotelBindsInput
        {
            HotelIds = new List<long> { input.HotelId }
        });
        if (hotelBinds.Any() is false)
            return result;
        var youxiaBind = hotelBinds.FirstOrDefault(x => x.SupplierApiType == SupplierApiType.Youxia);
        if (youxiaBind == null)
            return result;

        result = await _resourceApiCaller.SearchYouxiaHotelPriceStrategy(new SearchYouxiaHotelPriceStrategyInput
        {
            Adults = input.Adults,
            LiveDate = input.LiveDate,
            LeaveDate = input.LeaveDate,
            TenantId = input.TenantId,
            YouxiaHotelId = Convert.ToInt64(youxiaBind.ThirdHotelId),
            HotelId = input.HotelId,
        });

        var commissionSetting = commissionSettings?.FirstOrDefault();
        foreach (var room in result)
        {
            foreach (var priceStrategy in room.PriceStrategies)
            {
                decimal? supplierCommissionFee = priceStrategy?.Commission?.Amount;
                decimal? suppliercommissionRate = priceStrategy?.Commission?.Percent;
                var commissionRate = (suppliercommissionRate * commissionSetting?.CommissionSettingValue) / 100;
                var commissionFee = supplierCommissionFee * commissionSetting?.CommissionSettingValue / 100;
                // 保留两位小数并向下取整
                commissionFee = Math.Floor(commissionFee!.Value * 100) / 100;
                priceStrategy.Commission.CurrencyCode = input.CurrencyCode;
                priceStrategy.Commission.Amount = commissionFee ?? 0;
                priceStrategy.Commission.Percent = commissionRate ?? 0;
                priceStrategy.HotelId = input.HotelId;
            }
        }

        return result;
    }

    private async Task<List<SearchGDSHotelMinPriceOutput>> SearchYouxiaHotelMinPrice(SearchGDSHotelMinPriceBffInput input, long? agencyGroupId)
    {
        var result = new List<SearchGDSHotelMinPriceOutput>();

        var commissionSettings = await _productApiCaller.QueryAgencyChannelCommissionSettings(new QueryAgencyChannelCommissionInput
        {
            PriceGroupId = agencyGroupId ?? 0,
            ProductTypes = new List<CommissionProductType> { CommissionProductType.YouXiaHotel },
        });

        if (commissionSettings?.Any() is not true)
            return result;

        var hotelBinds = await _resourceApiCaller.GetGDSThirdHotelBinds(new GetGDSThirdHotelBindsInput
        {
            HotelIds = input.HotelIds
        });
        var youxiaBinds = hotelBinds.Where(x => x.SupplierApiType == SupplierApiType.Youxia)
            .ToList();
        if (youxiaBinds.Any() is false)
            return result;
        result = await _resourceApiCaller.SearchYouxiaHotelMinPrice(new SearchYouxiaHotelMinPriceInput
        {
            Adults = input.MaximumOccupancy,
            LiveDate = input.LiveDate,
            LeaveDate = input.LeaveDate,
            Hotels = youxiaBinds.Select(x => new SearchYouxiaHotelDetailMinPriceInput
            {
                HotelId = x.HotelId,
                YouxiaHotelId = Convert.ToInt64(x.ThirdHotelId),
            }).ToList(),
            TenantId = input.TenantId,
        });
        return result;
    }
    #endregion

    #region private
    private async Task<List<SearchGDSHotelMinPriceOutput>> GetGDSHotelMinPriceCache(SearchGDSHotelMinPriceInput input)
    {
        var result = new List<SearchGDSHotelMinPriceOutput>();
        var keys = input.Hotels.Select(x => x.HotelId).Select(x => string.Format(GDSHOTEL_MinPrice, x,
            input.LiveDate.ToString("yyyyMMdd"), input.LeaveDate.ToString("yyyyMMdd"))).ToList();

        var cacheDatas = await _redisClient.StringGetAsync(keys);
        foreach (var item in cacheDatas)
        {
            if (item.HasValue && !string.IsNullOrEmpty(item.ToString()))
                result.Add(JsonConvert.DeserializeObject<SearchGDSHotelMinPriceOutput>(item.ToString()));
        }
        return result;
    }

    private async Task SetGDSHotelMinPriceCache(List<SearchGDSHotelMinPriceOutput> datas, SearchGDSHotelMinPriceInput input)
    {
        foreach (var item in datas)
        {
            var key = string.Format(GDSHOTEL_MinPrice, item.HotelId, input.LiveDate.ToString("yyyyMMdd"), input.LeaveDate.ToString("yyyyMMdd"));
            await _redisClient.StringSetAsync(key, item, TimeSpan.FromHours(2));
        }
    }
    #endregion

    #region 废弃
    public async Task<List<GetSaleStrategyBffOutput>> SearchGDSPriceStrategy(SearchGDSHotelPriceStrategyInput input)
    {
        var result = new List<GetSaleStrategyBffOutput>();
        var b2bPriceStrategiesByGDS = new List<SearchGDSHotelPriceStrategyOutput>();
        //查询GDS价格策略
        var gdsPriceStrategiesTask = _resourceApiCaller.SearchGDSPriceStrategy(input);
        var standardHotelRoomCodesTask = _resourceApiCaller.SearchStandardHotelRoomCodes(new SearchStandardHotelRoomCodeInpuy { });
        var thirdCodeMappingsTask = _resourceApiCaller.GetThirdCodeMapping(new GetThirdCodeMappingInput
        {
            SupplierApiType = SupplierApiType.GDS
        });
        //查询资源酒店房型信息
        var resourceRoomsTask = _resourceApiCaller.GetRoomsByHotelIds(new GetRoomsByHotelIdsInput
        {
            HotelIds = new List<long>
            {
                input.HotelId
            }
        });

        Task.WaitAll(gdsPriceStrategiesTask,
            standardHotelRoomCodesTask,
            thirdCodeMappingsTask,
            resourceRoomsTask);
        var gdsPriceStrategies = gdsPriceStrategiesTask.Result;
        var standardHotelRoomCodes = standardHotelRoomCodesTask.Result;
        var thirdCodeMappings = thirdCodeMappingsTask.Result;
        var resourceRooms = resourceRoomsTask.Result?.FirstOrDefault();

        //转为标准房型
        var standardBaseRooms = GetBaseRooms(resourceRooms?.Rooms?.ToList(), standardHotelRoomCodes)
            .Where(x => !x.BaseRoomName.Equals("Others"))
            .ToList(); ;
        foreach (var gdsPriceStrategy in gdsPriceStrategies)
        {
            //转为标准房型
            var baseRoomCodesByGDS = GetBaseRoomsByGDS<SearchGDSHotelPriceStrategyOutput>(gdsPriceStrategy, standardHotelRoomCodes, thirdCodeMappings);
            gdsPriceStrategy.BaseRoomName = BuildBaseRoomName(baseRoomCodesByGDS);
            var baseRoomCodeIds = baseRoomCodesByGDS.Select(x => x.Id).ToList();
            //没有资源酒店房型直接返回GDS房型
            if (resourceRooms == null || baseRoomCodesByGDS.Any() is false)
            {
                if (gdsPriceStrategies.Any() is false)
                    return result;
                b2bPriceStrategiesByGDS.Add(gdsPriceStrategy);
            }
            else
            {
                var matchRooms = standardBaseRooms
                    .Where(x => x.StandardHotelRoomCodes
                        .Where(f => baseRoomCodeIds.Contains(f.Id)).Count() == x.StandardHotelRoomCodes.Count());
                if (matchRooms.Any() is false)
                {
                    baseRoomCodeIds = baseRoomCodesByGDS
                        .Where(x => x.Type != StandardHotelRommCodeType.View)
                        .Select(x => x.Id).ToList();
                    //排除景观在匹配一次
                    matchRooms = standardBaseRooms
                        .Where(x => x.StandardHotelRoomCodes
                            .Where(f => baseRoomCodeIds.Contains(f.Id)).Count() == x.StandardHotelRoomCodes.Count());
                }
                //都匹配不上,直接作为GDS房型
                if (matchRooms.Any() is false)
                {
                    b2bPriceStrategiesByGDS.Add(gdsPriceStrategy);
                    continue;
                }
                bool isMatch = false;
                //匹配第一条数据
                foreach (var matchRoom in matchRooms)
                {
                    var matchRoomBedType = JsonConvert.DeserializeObject<List<BedType>>(matchRoom.Room.BedType);
                    //匹配床型
                    if (!AreJsonContentsEqual(matchRoomBedType, gdsPriceStrategy.BedType))
                        continue;
                    var hopRoom = _mapper.Map<GetSaleStrategyBffOutput>(matchRoom.Room);
                    var b2bPriceStrategy = _mapper.Map<GetSaleStrategyBffOutput>(gdsPriceStrategy);

                    if (result.Any(x => x.RoomId == matchRoom.Room.Id) is true)
                    {
                        hopRoom = result.FirstOrDefault(x => x.RoomId == matchRoom.Room.Id);
                        hopRoom.PriceStrategies.AddRange(b2bPriceStrategy.PriceStrategies);
                    }
                    else
                    {
                        hopRoom.PriceStrategies.AddRange(b2bPriceStrategy.PriceStrategies);
                        result.Add(hopRoom);
                    }
                    isMatch = true;
                    break;
                }
                if(!isMatch)
                    b2bPriceStrategiesByGDS.Add(gdsPriceStrategy);
            }
        }
        foreach (var b2bPriceStrategyByGDS in b2bPriceStrategiesByGDS)
        {
            var room = _mapper.Map<GetSaleStrategyBffOutput>(b2bPriceStrategyByGDS);
            result.Add(room);
        }

        return result;
    }

    public async Task<GDSHotelPriceCheckBffOutput> PriceCheck(GDSHotelPriceCheckInput input)
    {
        //查询GDS价格策略
        var gdsPriceCheck = await _resourceApiCaller.PriceCheck(input);
        var result = await BuildPriceCheck(gdsPriceCheck, input.HotelId);
        return result;
    }

    public async Task<GDSHotelPriceCheckBffOutput> BuildPriceCheck(GDSHotelPriceCheckOutput gdsPriceCheck, long resourceHotelId)
    {
        var b2bPriceStrategiesByGDS = new List<SearchGDSHotelPriceStrategyOutput>();
        var standardHotelRoomCodesTask = _resourceApiCaller.SearchStandardHotelRoomCodes(new SearchStandardHotelRoomCodeInpuy { });
        var thirdCodeMappingsTask = _resourceApiCaller.GetThirdCodeMapping(new GetThirdCodeMappingInput
        {
            SupplierApiType = SupplierApiType.GDS
        });
        //查询资源酒店房型信息
        var resourceRoomsTask = _resourceApiCaller.GetRoomsByHotelIds(new GetRoomsByHotelIdsInput
        {
            HotelIds = new List<long>
            {
                resourceHotelId
            }
        });

        Task.WaitAll(standardHotelRoomCodesTask,
            thirdCodeMappingsTask,
            resourceRoomsTask);
        var gdsPriceStrategy = gdsPriceCheck.Room;
        var standardHotelRoomCodes = standardHotelRoomCodesTask.Result;
        var thirdCodeMappings = thirdCodeMappingsTask.Result;
        var resourceRooms = resourceRoomsTask.Result?.FirstOrDefault();
        //转为标准房型
        var standardBaseRooms = GetBaseRooms(resourceRooms?.Rooms?.ToList(), standardHotelRoomCodes);

        var baseRoomCodesByGDS = GetBaseRoomsByGDS<GDSHotelRoomInfo>(gdsPriceStrategy, standardHotelRoomCodes, thirdCodeMappings);
        var result = _mapper.Map<GDSHotelPriceCheckBffOutput>(gdsPriceCheck);

        var baseRoomCodeIds = baseRoomCodesByGDS.Select(x => x.Id).ToList();
        if (resourceRooms == null)
            return result;
        var matchRooms = standardBaseRooms
                    .Where(x => x.StandardHotelRoomCodes
                        .Where(f => baseRoomCodeIds.Contains(f.Id)).Count() == x.StandardHotelRoomCodes.Count());
        if (matchRooms.Any() is false)
        {
            baseRoomCodeIds = baseRoomCodesByGDS
                .Where(x => x.Type != StandardHotelRommCodeType.View)
                .Select(x => x.Id).ToList();
            //排除景观在匹配一次
            matchRooms = standardBaseRooms
                .Where(x => x.StandardHotelRoomCodes
                    .Where(f => baseRoomCodeIds.Contains(f.Id)).Count() == x.StandardHotelRoomCodes.Count());
        }
        //都匹配不上,直接作为GDS房型
        if (matchRooms.Any() is false)
            return result;

        //匹配第一条数据
        foreach (var matchRoom in matchRooms)
        {
            if (string.IsNullOrEmpty(matchRoom.Room.BedType) || gdsPriceStrategy.BedType == null)
                continue;
            var matchRoomBedType = JsonConvert.DeserializeObject<List<BedType>>(matchRoom.Room.BedType);
            //匹配床型
            if (!AreJsonContentsEqual(matchRoomBedType, gdsPriceStrategy.BedType))
                continue;
            result.Room = _mapper.Map<GetSaleStrategyBffOutput>(matchRoom);
            var b2bPriceStrategy = _mapper.Map<GetSaleStrategyBffOutput>(gdsPriceStrategy);
            result.Room.PriceStrategies = b2bPriceStrategy.PriceStrategies;
            break;
        }

        return result;
    }

    #region 基础转换
    record StandardBaseRoom(RoomInfo Room, List<SearchStandardHotelRoomCodeOutput> StandardHotelRoomCodes, string BaseRoomName);

    private List<StandardBaseRoom> GetBaseRooms(List<RoomInfo> rooms, List<SearchStandardHotelRoomCodeOutput> baseCodes)
    {
        var hopBaseRooms = new List<StandardBaseRoom>();
        if (rooms == null || baseCodes == null)
            return hopBaseRooms;
        foreach (var room in rooms.Where(x => !string.IsNullOrEmpty(x.ENName)))
        {
            var baseRoomName = string.Empty;
            var baseRoomList = new Dictionary<StandardHotelRommCodeType, string>();
            var baseCodeIds = new List<SearchStandardHotelRoomCodeOutput>();
            foreach (var baseCodesGroup in baseCodes.GroupBy(x => x.Type))
            {
                foreach (var baseCode in baseCodesGroup.OrderBy(x => x.Id))
                {
                    foreach (var key in baseCode.Keywords)
                    {
                        if (baseRoomList.ContainsKey(baseCodesGroup.Key))
                            continue;

                        var newKey = key.Trim();
                        string pattern = @"\b" + newKey + @"\b";
                        bool isMatch = Regex.IsMatch(room.ENName, pattern, RegexOptions.IgnoreCase);
                        if (isMatch)
                        {
                            baseCodeIds.Add(baseCode);
                            if (newKey == "Smoking")
                            {
                                if (room.ENName.Contains("Non-Smoking") || room.ENName.Contains("Non Smoking"))
                                {
                                    var nonSmoking = baseCodes.FirstOrDefault(x => x.Name == "Non-Smoking");
                                    baseRoomList.Add(baseCode.Type, "Non-Smoking");
                                }
                                else
                                {
                                    baseRoomList.Add(baseCode.Type, "Smoking");
                                }
                                break;
                            }
                            else
                            {
                                baseRoomList.Add(baseCode.Type, baseCode.Name);
                                break;
                            }
                        }
                    }
                }
            }

            if (baseRoomList.Any())
            {
                if (baseRoomList.Count() == 1 && baseRoomList.ContainsValue("Room"))
                    baseRoomName = "Others";
                else
                {
                    var qianmianObj = baseRoomList.Where(x => x.Key < StandardHotelRommCodeType.View).ToList();
                    var qianStr = string.Join(" ", qianmianObj.Select(x => x.Value));
                    if (!qianmianObj.Exists(x => x.Key == StandardHotelRommCodeType.Room))
                        qianStr = string.IsNullOrEmpty(qianStr) ? "Room" : qianStr + " Room";
                    var houmianObj = baseRoomList.Where(x => x.Key >= StandardHotelRommCodeType.View).ToList();
                    var houStr = string.Join(" ", houmianObj.Select(x => x.Value));
                    if (!string.IsNullOrEmpty(houStr))
                        houStr = " with " + houStr;
                    baseRoomName = qianStr + houStr;
                }
            }
            else
            {
                baseRoomName = "Others";
            }

            hopBaseRooms.Add(new StandardBaseRoom(room, baseCodeIds, baseRoomName));
        }
        return hopBaseRooms;
    }

    private string BuildBaseRoomName(List<SearchStandardHotelRoomCodeOutput> codes)
    {
        var baseRoomName = string.Empty;
        if (codes.Any())
        {
            if (codes.Count() == 1 && codes.FirstOrDefault().Name.Equals("Room"))
                baseRoomName = "Other Rooms";
            else
            {
                var qianmianObj = codes.Where(x => x.Type < StandardHotelRommCodeType.View).ToList();
                var qianStr = string.Join(" ", qianmianObj.Select(x => x.Name));
                if (!qianmianObj.Exists(x => x.Type == StandardHotelRommCodeType.Room))
                    qianStr = string.IsNullOrEmpty(qianStr) ? "Room" : qianStr + " Room";
                var houmianObj = codes.Where(x => x.Type >= StandardHotelRommCodeType.View).ToList();
                var houStr = string.Join(" ", houmianObj.Select(x => x.Name));
                if (!string.IsNullOrEmpty(houStr))
                    houStr = " with " + houStr;
                baseRoomName = qianStr + houStr;
            }
        }
        else
        {
            baseRoomName = "Other Rooms";
        }

        return baseRoomName;
    }

    /// <summary>
    /// 获取标准化后的GDS房型
    /// </summary>
    /// <param name="priceStrategy"></param>
    /// <param name="baseCodes"></param>
    /// <param name="thirdCodeMappings"></param>
    /// <returns></returns>
    private List<SearchStandardHotelRoomCodeOutput> GetBaseRoomsByGDS<T>(GDSHotelRoomInfo priceStrategy,
        List<SearchStandardHotelRoomCodeOutput> baseCodes,
        List<GetThirdCodeMappingOutput> thirdCodeMappings) where T : GDSHotelRoomInfo
    {
        var gdsRoomName = $"{priceStrategy.GDSRoomType} {priceStrategy.GDSRoomViewDescription} {priceStrategy.GDSRoomDescription}";
        var standardBaseRooms = GetBaseRooms(gdsRoomName, baseCodes);
        if (priceStrategy.GDSRoomTypeCode.HasValue)
        {
            var roomTypeMapping = thirdCodeMappings.FirstOrDefault(x => x.Code == priceStrategy.GDSRoomTypeCode.Value.ToString());
            if (roomTypeMapping?.Mapping != null)
            {
                foreach (var item in roomTypeMapping.Mapping)
                {
                    if (standardBaseRooms.Any(x => x.Id == item.StandardHotelRommCodeId))
                        continue;
                    var baseCode = baseCodes.FirstOrDefault(x => x.Id == item.StandardHotelRommCodeId);
                    standardBaseRooms.Add(baseCode);
                }
            }
        }
        if (priceStrategy.GDSRoomViewCode.HasValue)
        {
            var roomViewMapping = thirdCodeMappings.FirstOrDefault(x => x.Code == priceStrategy.GDSRoomViewCode.Value.ToString());
            if (roomViewMapping?.Mapping != null)
            {
                foreach (var item in roomViewMapping.Mapping)
                {
                    if (standardBaseRooms.Any(x => x.Id == item.StandardHotelRommCodeId))
                        continue;
                    var baseCode = baseCodes.FirstOrDefault(x => x.Id == item.StandardHotelRommCodeId);
                    standardBaseRooms.Add(baseCode);
                }
            }
        }

        return standardBaseRooms;
    }

    private List<SearchStandardHotelRoomCodeOutput> GetBaseRooms(string str, List<SearchStandardHotelRoomCodeOutput> baseCodes)
    {
        var result = new List<SearchStandardHotelRoomCodeOutput>();
        foreach (var baseCodesGroup in baseCodes.GroupBy(x => x.Type))
        {
            foreach (var baseCode in baseCodesGroup.OrderBy(x => x.Id))
            {
                bool isMatch = false;
                foreach (var key in baseCode.Keywords)
                {
                    if (result.Any(x => x.Id == baseCode.Id))
                        continue;

                    var newKey = key.Trim();
                    string pattern = @"\b" + newKey + @"\b";
                    isMatch = Regex.IsMatch(str, pattern, RegexOptions.IgnoreCase);
                    if (isMatch)
                    {
                        result.Add(baseCode);
                        break;
                    }
                }
                //同一类型只取第一个
                if (isMatch)
                    break;
            }
        }

        return result;
    }

    private bool AreJsonContentsEqual(List<BedType> hopRoomBedTypes, List<BedType> gdsBeTypes)
    {
        if (hopRoomBedTypes.Count() <= 0 || gdsBeTypes.Count() <= 0)
            return false;
        //或，只符合一个就行
        if (hopRoomBedTypes.Count > 1)
        {
            if(hopRoomBedTypes.Count != gdsBeTypes.Count)
                return false;
            var hopRoomBedTypeFalts = hopRoomBedTypes.SelectMany(bt => bt.sub.Select(s => (s.en, s.num))).ToList();
            var gdsBeTypeFalts = gdsBeTypes.SelectMany(bt => bt.sub.Select(s => (s.en, s.num))).ToList();
            return hopRoomBedTypeFalts.OrderBy(x => x).SequenceEqual(gdsBeTypeFalts.OrderBy(x => x));
        }
        else
        {
            var hopRoomBedType = hopRoomBedTypes.FirstOrDefault();
            if (hopRoomBedType.Equals("多张床"))
            {
                if (hopRoomBedType.sub.Count != gdsBeTypes.Count())
                    return false;
                //多张床一一对应
                var hopRoomBedTypeFalts = hopRoomBedTypes.SelectMany(bt => bt.sub.Select(s => (s.en, s.num))).ToList();
                var gdsBeTypeFalts = gdsBeTypes.SelectMany(bt => bt.sub.Select(s => (s.en, s.num))).ToList();
                return hopRoomBedTypeFalts.OrderBy(x => x).SequenceEqual(gdsBeTypeFalts.OrderBy(x => x));
            }
            else
            {
                if (gdsBeTypes.Count() > 1)
                    return false;
                //1对1
                var gdsBeType = gdsBeTypes.FirstOrDefault();
                var gdsBeTypeFalt = gdsBeType.sub.Select(s => (s.en, s.num)).ToList();
                var hopRoomBedTypeFalt = hopRoomBedType.sub.Select(s => (s.en, s.num)).ToList();
                return gdsBeTypeFalt.SequenceEqual(hopRoomBedTypeFalt);
            }
        }

        return false;
    }
    #endregion
    #endregion

    // 提取通用的GDS搜索执行逻辑
    private async Task<T> ExecuteGdsSearchAsync<T>(Func<Task<T>> searchMethod, string providerName) where T : new()
    {
        try
        {
            return await searchMethod();
        }
        catch (Exception ex)
        {
            _logger.LogError($"{providerName}请求失败: {ex.Message}", ex);
            return new T();
        }
    }
}
