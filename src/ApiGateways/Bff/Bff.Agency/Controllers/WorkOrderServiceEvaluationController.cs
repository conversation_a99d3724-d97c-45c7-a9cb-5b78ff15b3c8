using AutoMapper;
using Bff.Agency.Callers;
using Bff.Agency.Models.WorkOrderServiceEvaluation;
using Common.Jwt;
using Common.Swagger;
using Contracts.Common.Order.DTOs.WorkOrderServiceEvaluation;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace Bff.Agency.Controllers;

/// <summary>
/// 工单服务评价
/// </summary>
[Route("v1/[controller]/[action]")]
[ApiExplorerSettings(GroupName = nameof(ApiVersion.v1))]
[ApiController]
public class WorkOrderServiceEvaluationController : ControllerBase
{
    private readonly IOrderApiCaller _orderApiCaller;
    private readonly IMapper _mapper;

    public WorkOrderServiceEvaluationController(IOrderApiCaller orderApiCaller, IMapper mapper)
    {
        _orderApiCaller = orderApiCaller;
        _mapper = mapper;
    }

    [HttpPost]
    [Authorize]
    [ProducesResponseType((int)HttpStatusCode.OK)]
    public async Task<IActionResult> Create(CreateBffInput bffInput)
    {
        var input = _mapper.Map<CreateInput>(bffInput);
        var currentUser = HttpContext.User.ParseUserInfo<CurrentUser>();
        input.UserId = currentUser.UserId;
        input.UserName = currentUser.NickName;
        await _orderApiCaller.CreateWorkOrderServiceEvaluation(input);
        return Ok();
    }
}
