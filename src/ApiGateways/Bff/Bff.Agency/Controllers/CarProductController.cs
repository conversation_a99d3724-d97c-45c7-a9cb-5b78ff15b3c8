using Common.Swagger;
using Microsoft.AspNetCore.Mvc;
using Bff.Agency.Callers;
using Contracts.Common.Product.DTOs.CarProduct;
using Bff.Agency.Models.CarProduct;
using AutoMapper;
using Bff.Agency.Services.Interfaces;
using Contracts.Common.Resource.DTOs;
using Common.Jwt;
using Contracts.Common.Product.DTOs.AgencyChannelPriceSettings;
using Contracts.Common.Product.Enums;
using Contracts.Common.Payment.DTOs.Currency;
using Contracts.Common.Hotel.DTOs.PriceStrategyCalendarPrice;
using Microsoft.AspNetCore.Authorization;

namespace Bff.Agency.Controllers;

/// <summary>
/// 用车
/// </summary>
[Route("v1/[controller]/[action]")]
[ApiExplorerSettings(GroupName = nameof(ApiVersion.v1))]
[ApiController]
public class CarProductController : ControllerBase
{
    private readonly IProductApiCaller _productApiCaller;
    private readonly IMapper _mapper;
    private readonly IResourceApiCaller _resourceApiCaller;
    private readonly IAgencyChannelPriceService _agencyChannelPriceService;
    private readonly ITenantApiCaller _tenantApiCaller;
    private readonly IPaymentService _paymentService;

    public CarProductController(
        IProductApiCaller productApiCaller,
        IMapper mapper,
        IResourceApiCaller resourceApiCaller,
        IAgencyChannelPriceService agencyChannelPriceService,
        ITenantApiCaller tenantApiCaller,
        IPaymentService paymentService
        )
    {
        _productApiCaller = productApiCaller;
        _mapper = mapper;
        _resourceApiCaller = resourceApiCaller;
        _agencyChannelPriceService = agencyChannelPriceService;
        _tenantApiCaller = tenantApiCaller;
        _paymentService = paymentService;
    }

    /// <summary>
    /// 详情
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [Authorize]
    [ProducesResponseType(typeof(BffCarProductDetailCalendarPricesOutput), (int)HttpStatusCode.OK)]
    public async Task<IActionResult> ProductDetailCalendarPrices(BffCarProductDetailCalendarPricesInput input)
    {

        var currentUser = HttpContext.User.ParseUserInfo<CurrentUser>();
        var res = new BffCarProductDetailCalendarPricesOutput();
        var apiInput = new CarProductDetailCalendarPricesInput()
        {
            AirportTransferType = input.AirportTransferType,
            StartDate = input.StartDate,
            EndDate = input.EndDate,
            ProductId = input.ProductId,
            B2bSellingStatus = true,
            CarServiceItemId = input.CarServiceItemId,
            ServiceLanguages = input.ServiceLanguages,
            CarTypeGradeId = input.CarTypeGradeId,
            BookingDuration = input.BookingDuration,
            Departure = input.Departure,
            Destination = input.Destination,
            ResultId = input.ResultId,
            UserId = currentUser.UserId,
        };
        if (input.ProductSkuId.HasValue)
            apiInput.SkuIds = new List<long> { input.ProductSkuId!.Value };
        var product = await _productApiCaller.GetCarProductPriceDetails(apiInput);
        if (product.Product?.Id is null)
            return Ok(res);

        var agencyId = currentUser.Provider;
        var agencyInfo = await _tenantApiCaller.GetAgencyDetail(agencyId);
        var priceGroupId = agencyInfo.PriceGroupId!.Value;
        var paymentCurrencyCode = agencyInfo.CurrencyCode;
        _mapper.Map(product, res);
        res.Product.PaymentCurrencyCode = paymentCurrencyCode;
        if (res.Product?.CityCode > 0)
        {
            var city = (await _resourceApiCaller.QueryCities(new QueryInput { CityCodes = new int[] { res.Product.CityCode } })).FirstOrDefault();
            if (city is not null)
            {
                res.Product.EnCityName = city.ENName;
                res.Product.EnCountryName = city.CountryEnName;
            }
        }
        if (res.Product?.AirportId > 0)
        {
            var airport = await _resourceApiCaller.GetAirportDetail(res.Product.AirportId.Value);
            res.Product.EnAirportName = airport?.AirportENName;
        }

        var skuIds = res.Skus.Select(x => x.CarProductSkuId).ToArray();
        var settingRequest = new QueryChannelPriceInput
        {
            PriceGroupId = priceGroupId,
            ProductType = new[] { ChannelProductType.CarUsing },
            SkuIds = skuIds,
        };
        var skuSettingResponse = await _agencyChannelPriceService.QueryPricesSettings(settingRequest);
        var serviceItemIds = product.ServiceItems.Select(x => x.Id);

        var serviceSettingRequest = new QueryChannelPriceInput
        {
            PriceGroupId = priceGroupId,
            ProductType = new[] { ChannelProductType.CarUsingServiceItem },
            SkuIds = serviceItemIds.Distinct(),
        };
        var serviceSettingResponse = await _agencyChannelPriceService.QueryPricesSettings(serviceSettingRequest);

        var getExchangeRateInput = new List<GetExchangeRatesInput>();
        var costToSaleExchangeRateInput = new GetExchangeRatesInput()
        {
            BaseCurrencyCode = res.Product.CostCurrencyCode,
            TargetCurrencyCode = res.Product.SaleCurrencyCode
        };
        var saleToB2BExchangeRateInput = new GetExchangeRatesInput()
        {
            BaseCurrencyCode = res.Product.SaleCurrencyCode,
            TargetCurrencyCode = agencyInfo.CurrencyCode,
        };
        getExchangeRateInput.Add(costToSaleExchangeRateInput);
        getExchangeRateInput.Add(saleToB2BExchangeRateInput);
        var exchangeRateList = await _paymentService.GetCurrencyExchangeRateList(getExchangeRateInput);
        List<BffCarProductSkuDetailCalendarPricesOutput> outSkus = new();
        foreach (var sku in res.Skus)
        {
            var costCurrencyCode = res.Product.CostCurrencyCode;
            var saleCurrencyCode = res.Product.SaleCurrencyCode;
            var skuSettingItem = skuSettingResponse.FirstOrDefault(s => s.SkuId == sku.CarProductSkuId);
            var apiSku = product.Skus.FirstOrDefault(s => s.CarProductSkuId == sku.CarProductSkuId && s.ResultId == sku.ResultId);

            foreach (var skuPrice in sku.CalendarPrices)
            {
                var apiSkuPrice = apiSku.CalendarPrices.FirstOrDefault(s => s.Date == skuPrice.Date);
                skuPrice.ChannelPrice = _agencyChannelPriceService.ConvertB2BPrice(exchangeRateList, skuSettingItem,
                                               apiSkuPrice.SalePrice, apiSkuPrice.CostPrice,
                                               costCurrencyCode, saleCurrencyCode, agencyInfo.CurrencyCode);
                // 没价格时，不可售
                if (skuPrice.ChannelPrice == null)
                    skuPrice.IsSale = false;
                foreach (var item in skuPrice.ServiceItems)
                {
                    var serviceSettingItem = serviceSettingResponse.FirstOrDefault(s => s.SkuId == item.CarServiceItemId);
                    if (item.IsNeedCharge)
                    {
                        item.ServicePriceAndQuantityInfos.ForEach(price =>
                        {
                            var apiSkuServicePrice = apiSkuPrice.ServiceItems.FirstOrDefault(s => s.CarServiceItemId == item.CarServiceItemId)?
                                           .ServicePriceAndQuantityInfos?.FirstOrDefault();
                            price.ChannelPrice = _agencyChannelPriceService.ConvertB2BPrice(exchangeRateList, serviceSettingItem,
                                                    apiSkuServicePrice?.SalePrice, apiSkuServicePrice?.CostPrice,
                                                    costCurrencyCode, saleCurrencyCode, agencyInfo.CurrencyCode);

                            // 如果是接口的服务，得看服务是否配置了b2b，如果没配置，使用采购原价,SalePrice = 采购价 + 增减幅  ，保证服务能收费，不亏钱
                            if (product.Product.PurchaseSourceType == CarProductPurchaseSourceType.InterfaceDock && price.ChannelPrice is null)
                            {
                                //采购价=>售价汇率
                                var costToSaleExchangeRate = exchangeRateList.First(e =>
                                    e.BaseCurrencyCode == costCurrencyCode &&
                                    e.TargetCurrencyCode == saleCurrencyCode).ExchangeRate;

                                //售价=>B2B价汇率
                                var saleToB2BExchangeRate = exchangeRateList.First(e =>
                                    e.BaseCurrencyCode == saleCurrencyCode &&
                                    e.TargetCurrencyCode == agencyInfo.CurrencyCode).ExchangeRate;

                                price.ChannelPrice = Math.Round(apiSkuServicePrice.SalePrice!.Value * costToSaleExchangeRate * saleToB2BExchangeRate, 2);
                            }


                        });
                        item.ServicePriceAndQuantityInfos = item.ServicePriceAndQuantityInfos.Where(x => x.ChannelPrice != null).ToList();
                    }
                }

            }
            sku.ChannelPrice = sku.CalendarPrices.Min(x => x.ChannelPrice);
            // 计算出售价后再筛选,按照
            if (input.MinPrice.HasValue && sku.ChannelPrice < input.MinPrice)
                continue;
            if (input.MaxPrice.HasValue && sku.CalendarPrices.Max(x => x.ChannelPrice) > input.MinPrice)
                continue;

            outSkus.Add(sku);
        }
        res.Skus = outSkus;


        return Ok(res);
    }

    /// <summary>
    /// 产品套餐、项目、语言下拉
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [Authorize]
    [ProducesResponseType(typeof(CarProductSelectOutput), (int)HttpStatusCode.OK)]
    public async Task<IActionResult> GetProductSelects(BffGetProductSelectsInput input)
    {
        var apiInput = new GetProductSelectsInput()
        {
            ProductId = input.ProductId,
            B2bSellingStatus = true,
            Date = input.Date,
            AirportTransferType = input.AirportTransferType,
        };
        var result = await _productApiCaller.GetCarProductSelects(apiInput);
        return Ok(result);
    }

    /// <summary>
    /// 获取车型等级列表
    /// </summary>
    /// <returns></returns>
    [HttpPost]
    [Authorize]
    [ProducesResponseType(typeof(List<Contracts.Common.Product.DTOs.CarTypeGrade.CarTypeGradeOuput>), (int)HttpStatusCode.OK)]
    public async Task<IActionResult> GetCarTypeGrades()
    {
        var result = await _productApiCaller.GetCarTypeGrades();
        return Ok(result);
    }
}
