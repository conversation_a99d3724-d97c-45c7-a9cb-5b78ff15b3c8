using AutoMapper;
using Bff.Agency.Callers;
using Bff.Agency.Models.User;
using Cit.TokenPersistence.Context;
using Common.Jwt;
using Common.Swagger;
using Contracts.Common.Tenant.DTOs.Agency.AgencyRegister;
using Contracts.Common.Tenant.Enums;
using Contracts.Common.User.DTOs;
using Contracts.Common.User.DTOs.AgencyUser;
using Contracts.Common.User.DTOs.OperationLog;
using Contracts.Common.User.DTOs.SupplierUser;
using Contracts.Common.User.Enums;
using Extensions;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using Swashbuckle.AspNetCore.Annotations;

namespace Bff.Agency.Controllers;

/// <summary>
/// Agency用户
/// </summary>
[Route("v1/[controller]/[action]")]
[ApiExplorerSettings(GroupName = nameof(ApiVersion.v1))]
[ApiController]
public class UserController : ControllerBase
{
    private readonly IMapper _mapper;
    private readonly ITenantApiCaller _tenantApiCaller;
    private readonly IUserApiCaller _userApiCaller;
    private readonly IOptionsMonitor<JwtConfig> _jwtConfig;
    private readonly IPermissionCaller _permissionCaller;
    private readonly ITokenPersistenceContext _tokenPersistenceContext;
    private readonly IMarketingApiCaller _marketingApiCaller;
    private readonly IResourceApiCaller _resourceApiCaller;
    private readonly IWechatApiCaller _wechatApiCaller;
    private const string _desKey = "hztravel";

    public UserController(IMapper mapper,
        ITenantApiCaller tenantApiCaller,
        IUserApiCaller userApiCaller,
        IOptionsMonitor<JwtConfig> jwtConfig,
        IPermissionCaller permissionCaller,
        ITokenPersistenceContext tokenPersistenceContext,
        IMarketingApiCaller marketingApiCaller,
        IResourceApiCaller resourceApiCaller,
        IWechatApiCaller wechatApiCaller)
    {
        _mapper = mapper;
        _tenantApiCaller = tenantApiCaller;
        _userApiCaller = userApiCaller;
        _jwtConfig = jwtConfig;
        _permissionCaller = permissionCaller;
        _tokenPersistenceContext = tokenPersistenceContext;
        _marketingApiCaller = marketingApiCaller;
        _resourceApiCaller = resourceApiCaller;
        _wechatApiCaller = wechatApiCaller;
    }

    /// <summary>
    /// 注册分销商
    /// </summary>
    /// <returns></returns>
    [HttpPost]
    [SwaggerResponseExt(200, typeof(long))]
    [SwaggerResponseExt(default,
        ErrorTypes.User.VerifyCodeError,
        ErrorTypes.Tenant.AgencyFullNameExist)]
    public async Task<IActionResult> Register(RegisterInput input)
    {
        //短信、邮件验证码校验
        await ContactCheckAsync(input);

        var cities = await _resourceApiCaller.QueryCities(new Contracts.Common.Resource.DTOs.QueryInput
        {
            CityCodes = new int[] { input.CityCode },
        });

        var city = cities.FirstOrDefault(x => x.CityCode == input.CityCode);

        var request = new AgencyRegisterInput
        {
            ContactNumber = input.ContactNumber,
            Contact = input.Contact,
            Address = input.Address,
            FullName = input.FullName,
            RegisterSource = input.RegisterSource ?? AgencyRegisterSourceType.PCWeb,
            CountryCode = city?.CountryCode,
            CountryName = city?.CountryName,
            ProvinceCode = city?.ProvinceCode,
            ProvinceName = city?.ProvinceName,
            CityCode = input.CityCode,
            CityName = input.CityName,
            Email = input.Email,
            IndustryInvolved = input.IndustryInvolved,
            PromotionTraceId = input.PromotionTraceId,
            ContactCountryDialCode = input.ContactCountryDialCode,
            RegisterSubSource = AgencyRegisterSubSourceType.Register
        };

        //推广活码
        await PromotionTraceAsync(input.PromotionTraceId, request);

        var agencyId = await _tenantApiCaller.AgencyRegister(request);

        return Ok(agencyId);
    }

    /// <summary>
    /// 团房免登录注册分销商
    /// </summary>
    /// <returns></returns>
    [HttpPost]
    [SwaggerResponseExt(200, typeof(long))]
    [SwaggerResponseExt(default,
        ErrorTypes.User.VerifyCodeError,
        ErrorTypes.Tenant.AgencyFullNameExist)]
    public async Task<IActionResult> SimpleRegister(SimpleRegisterInput input)
    {
        var isNotCheck = false;
        if (input.RegisterSource is AgencyRegisterSourceType.WechatApplet)
        {
            var phoneNumber = (await _wechatApiCaller.GetHuizhiPhoneNumber(new Contracts.Common.WeChat.DTOs.Huizhi.GetPhoneNumberInput
            {
                Code = input.PhoneNumberCode,
                TenantId = HttpContext.GetTenantId()
            })).PhoneNumber;

            //使用微信小程序授权的手机号码
            input.ContactNumber = phoneNumber;

            //小程序跳过验证码校验
            isNotCheck = true;
        }
        //短信、邮件验证码校验
        await ContactCheckAsync(input, isNotCheck);

        var cities = await _resourceApiCaller.QueryCities(new Contracts.Common.Resource.DTOs.QueryInput
        {
            CityCodes = new int[] { input.CityCode },
        });

        var city = cities.FirstOrDefault(x => x.CityCode == input.CityCode);

        var request = new AgencyRegisterInput
        {
            ContactNumber = input.ContactNumber,
            FullName = input.FullName,
            RegisterSource = input.RegisterSource ?? AgencyRegisterSourceType.PCWeb,
            CountryCode = city?.CountryCode,
            CountryName = city?.CountryName,
            ProvinceCode = city?.ProvinceCode,
            ProvinceName = city?.ProvinceName,
            CityCode = input.CityCode,
            CityName = input.CityName,
            Email = input.Email,
            PromotionTraceId = input.PromotionTraceId,
            ContactCountryDialCode = input.ContactCountryDialCode,
            RegisterSubSource = AgencyRegisterSubSourceType.GroupRoom
        };

        //推广活码
        await PromotionTraceAsync(input.PromotionTraceId, request);

        var agencyId = await _tenantApiCaller.AgencyRegister(request);

        var data = JsonConvert.SerializeObject(new AgencyGenerateCodeDto(agencyId));

        var code = Common.Utils.SecurityUtil.DESEncrypt(data, _desKey);

        return Ok(code);
    }

    public record AgencyGenerateCodeDto(long AgencyId);

    private async Task PromotionTraceAsync(long? promotionTraceId, AgencyRegisterInput input)
    {
        //判断是否是活码注册
        if (promotionTraceId.HasValue)
        {
            var promotionTrace = await _marketingApiCaller.GetPromotionTrace(promotionTraceId.Value);
            if (promotionTrace == null)
            {
                input.PromotionTraceId = null;
            }
            else if (promotionTrace is { SalespersonId: not null })
            {
                var tenantUserInfo = await _userApiCaller.TenantUserFindOne(new FindOneInput
                {
                    UserId = promotionTrace.SalespersonId
                });
                //活码关联的BD需要判断是否被停用.被停用则不关联
                if (tenantUserInfo.Enabled)
                    input.SalespersonId = promotionTrace.SalespersonId;

            }
        }
    }

    private async Task ContactCheckAsync(AgencyContactCheckInput input, bool isNotCheck = false)
    {
        if (isNotCheck)
            return;

        var smsCheck = string.IsNullOrEmpty(input.SmsCode) is false && string.IsNullOrEmpty(input.ContactNumber) is false;

        string chinaDialCode = "86"; // 中国区号

        if (smsCheck && input.ContactCountryDialCode == chinaDialCode)
        {
            var isCorrect = await _userApiCaller.CheckCaptcha(new CaptchaDTO
            {
                TenantId = HttpContext.GetTenantId(),
                Key = input.ContactNumber,
                CaptchaChannelType = CaptchaChannelType.Sms,
                CaptchaType = CaptchaType.AgencyUser_SMS_Register,
                Code = input.SmsCode
            });
            if (isCorrect is false)
                throw new BusinessException(ErrorTypes.User.VerifyCodeError);
        }
        else if (string.IsNullOrEmpty(input.EmailCode) is false && string.IsNullOrEmpty(input.Email) is false)
        {
            var isCorrect = await _userApiCaller.CheckCaptcha(new CaptchaDTO
            {
                TenantId = HttpContext.GetTenantId(),
                Key = input.Email,
                CaptchaChannelType = CaptchaChannelType.Email,
                CaptchaType = CaptchaType.AgencyUser_Email_EmailBingding,
                Code = input.EmailCode
            });
            if (isCorrect is false)
                throw new BusinessException(ErrorTypes.User.VerifyCodeError);
        }
        else
        {
            throw new BusinessException(ErrorTypes.User.VerifyCodeError);
        }

    }

    /// <summary>
    /// 账号是否已存在
    /// </summary>
    /// <param name="pn">手机号</param>
    /// <returns></returns>
    [HttpPost]
    [SwaggerResponseExt(200, typeof(bool))]
    public async Task<IActionResult> IsExist([SwaggerSchema(Nullable = false)] string pn)
    {
        var entity = await _userApiCaller.FindOne(new AgencyUserFindOneInput
        {
            UserName = pn,
        });
        return Ok(entity is not null);
    }

    /// <summary>
    /// 登录
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [SwaggerResponseExt(200, typeof(TokenModel))]
    [SwaggerResponseExt(default,
        ErrorTypes.Tenant.TenantIsDisabled,
        ErrorTypes.Tenant.AgencyInvalid,
        ErrorTypes.User.VerifyCodeError,
        ErrorTypes.User.AccountOrPasswordError,
        ErrorTypes.User.AccountIsDisabled)]
    public async Task<IActionResult> LoginByPwd(LoginByPasswordInput input)
    {
        //租户是否运营中
        var tenantId = HttpContext.GetTenantId();
        var configs = await _tenantApiCaller.GetSysConfigByTenantIds(tenantId);
        if (configs.FirstOrDefault()?.OperationStatus != TenantOperationStatus.Operating)
            throw new BusinessException(ErrorTypes.Tenant.TenantIsDisabled);
        //核验验证码
        var isCorrect = await _userApiCaller.CheckImageCaptcha(new Contracts.Common.User.DTOs.ImageCaptchaDto
        {
            CodeId = input.CodeId,
            CodeValue = input.CodeValue,
        });
        if (!isCorrect) throw new BusinessException(ErrorTypes.User.VerifyCodeError);
        //是否可用
        var agencyUser = await _userApiCaller.FindOne(new AgencyUserFindOneInput
        {
            UserName = input.UserName,
            Password = input.Password,
        }) ?? throw new BusinessException(ErrorTypes.User.AccountOrPasswordError);
        if (agencyUser!.Enabled == false)
            throw new BusinessException(ErrorTypes.User.AccountIsDisabled);

        var agency = await _tenantApiCaller.GetAgencyFirstOrDefault(agencyUser.AgencyId);
        if (agency.Enable is false)
            throw new BusinessException(ErrorTypes.Tenant.AgencyInvalid);

        var config = _jwtConfig.CurrentValue;
        var user = new CurrentUser
        {
            UserId = agencyUser!.AgencyUserId,
            NickName = agencyUser.RealName ?? string.Empty,
            Provider = agencyUser.AgencyId,
            Tenant = agencyUser.TenantId,
            Client = input.Platform.ToString()
        };

        var (token, authDate, exprieDate) = JwtUtil.Create(config.SecurityKey, config.TokenExpires, SysRole.Agency.ToString(), user);

        //Token 持久化
        var tokenKey = $"TokenPersistence:{user.Tenant}:{user.Provider}:{user.UserId}:{user.Client}";
        _tokenPersistenceContext.Set(tokenKey, token, TimeSpan.FromSeconds(config.TokenExpires));

        var log = await CreateLog(input);
        input.Password = "*****";
        log.TabLogType = TabLogType.AgencyUser;
        log.KeyId = agencyUser.AgencyUserId;
        log.OperationUserId = agencyUser.AgencyUserId;
        log.OperationUserName = agencyUser.RealName;
        log.OperationType = OperationType.Login;
        log.Content = $"登录：ip：{log.Ip}，客户端：{log.Agent}";
        await _userApiCaller.AddOperationLog(log);

        return Ok(new TokenModel
        {
            AccessToken = token,
            AuthDate = authDate,
            ExprieDate = exprieDate,
        });
    }

    [HttpGet]
    [Authorize]
    [SwaggerResponseExt(200, typeof(TokenModel))]
    [SwaggerResponseExt(default,
        ErrorTypes.Tenant.TenantIsDisabled,
        ErrorTypes.User.AccountIsDisabled)]
    public async Task<IActionResult> RefreshToken()
    {
        var current = HttpContext.User.ParseUserInfo<CurrentUser>();
        var configs = await _tenantApiCaller.GetSysConfigByTenantIds(current.Tenant);
        if (configs.FirstOrDefault()?.OperationStatus != TenantOperationStatus.Operating)
            throw new BusinessException(ErrorTypes.Tenant.TenantIsDisabled);

        var user = await _userApiCaller.FindOne(new AgencyUserFindOneInput
        {
            UserId = current.UserId
        });
        if (user!.Enabled == false)
            throw new BusinessException(ErrorTypes.User.AccountIsDisabled);

        var config = _jwtConfig.CurrentValue;
        var (token, authDate, exprieDate) = JwtUtil.Create(config.SecurityKey, config.TokenExpires, SysRole.Agency.ToString(), new CurrentUser
        {
            UserId = user!.AgencyUserId,
            NickName = user.RealName ?? string.Empty,
            Provider = user.AgencyId,
            Tenant = user.TenantId
        });

        //Token 持久化
        var tokenKey = $"TokenPersistence:{current.Tenant}:{current.Provider}:{current.UserId}:{current.Client}";
        _tokenPersistenceContext.Set(tokenKey, token, TimeSpan.FromSeconds(config.TokenExpires));

        return Ok(new TokenModel
        {
            AccessToken = token,
            AuthDate = authDate,
            ExprieDate = exprieDate,
        });
    }

    /// <summary>
    /// 获取当前分销商用户信息
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    [HttpGet]
    [Authorize]
    [SwaggerResponseExt(200, typeof(AgencyUserDto))]
    public async Task<IActionResult> GetDetail()
    {
        CurrentUser currentUser = HttpContext.User.ParseUserInfo<CurrentUser>();
        var result = await _userApiCaller.GetAgencyUserDetail(currentUser.UserId);
        return Ok(result);
    }

    private async Task<OperationLogDto> CreateLog(object input)
    {
        CurrentUser currentUser = null;
        HttpContext.User.TryParseUserInfo<CurrentUser>(out currentUser);
        var request = HttpContext.Request;
        var ip = HttpContext.GetRemotingIp();
        var log = new OperationLogDto()
        {
            OperationType = OperationType.Login,
            System = SystemType.Agency,
            Host = request.Host.ToString(),
            Url = request.Path.ToString(),
            Agent = request.Headers.UserAgent,
            Ip = ip,
            Query = JsonConvert.SerializeObject(request.Query),
            Body = JsonConvert.SerializeObject(input),
            OperationUserId = currentUser?.UserId,
            OperationUserName = currentUser?.NickName,
            TenantId = currentUser?.Tenant
        };
        return log;
    }

    /// <summary>
    /// 修改分销商账号的密码
    /// </summary>
    /// <returns></returns>
    [Authorize]
    [HttpPost]
    [SwaggerResponseExt((int)HttpStatusCode.OK)]
    public async Task<IActionResult> UpdatePassword(UpdatePasswordInput input)
    {
        var user = HttpContext.User.ParseUserInfo<CurrentUser>();
        input.UserId = user.UserId;
        await _userApiCaller.UpdatePassword(input);

        //Token 持久化
        var tokenKey = $"TokenPersistence:{user.Tenant}:{user.Provider}:{user.UserId}:{user.Client}";
        _tokenPersistenceContext.Delete(tokenKey);

        return Ok();
    }
}
