using Bff.Agency.Models.AgencyAgreement;
using Bff.Agency.Models.AgencyCredit;
using Contracts.Common.Tenant.DTOs.AgencyAgreement;
using FluentValidation;

namespace Bff.Agency.Validators.AgencyAgreement;

public class BffSetAgreementRecordContentInputValidator : AbstractValidator<BffSetAgreementRecordContentInput>
{
    public BffSetAgreementRecordContentInputValidator()
    {
        RuleFor(x => x.AgreementName).NotEmpty();
        RuleFor(x => x.ConfirmContent).NotEmpty();
        RuleFor(x => x.CheckOta).NotNull();
    }
}
