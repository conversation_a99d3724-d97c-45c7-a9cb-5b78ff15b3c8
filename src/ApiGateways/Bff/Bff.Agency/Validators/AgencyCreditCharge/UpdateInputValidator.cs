using Bff.Agency.Models.AgencyCreditCharge;
using FluentValidation;

namespace Bff.Agency.Validators.AgencyCreditCharge;

public class UpdateInputValidator : AbstractValidator<UpdateInput>
{
    public UpdateInputValidator()
    {
        RuleFor(x => x.Id).GreaterThan(0);
        RuleFor(x => x.TenantBankAccountId).GreaterThan(0);
        RuleFor(x => x.Amount).GreaterThan(0);
        RuleFor(x => x.Proof).NotEmpty();
    }
}
