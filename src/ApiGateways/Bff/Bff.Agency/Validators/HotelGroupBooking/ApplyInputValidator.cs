using Bff.Agency.Models.HotelGroupBooking;
using FluentValidation;

namespace Bff.Agency.Validators.HotelGroupBooking;

public class ApplyInputValidator : AbstractValidator<ApplyInput>
{
    public ApplyInputValidator()
    {
        RuleFor(x => x.TeamNatureType).IsInEnum();
        RuleFor(x => x.Demands).NotEmpty();
        RuleForEach(x => x.Demands).SetValidator(new ApplyDemandInputValidator());
    }
}

public class ApplyDemandInputValidator : AbstractValidator<ApplyDemandInput>
{
    public ApplyDemandInputValidator()
    {
        RuleFor(x => x.AdultNum).GreaterThanOrEqualTo(1).LessThanOrEqualTo(9999).When(x => x.NeedHotelRoom, applyConditionTo: ApplyConditionTo.AllValidators);
        RuleFor(x => x.CityCode).GreaterThan(0);
        RuleFor(x => x.CityName).NotEmpty();
        RuleFor(x => x.CheckInDate).GreaterThanOrEqualTo(DateTime.Today);
        RuleFor(x => x.CheckOutDate).GreaterThanOrEqualTo(DateTime.Today);
        RuleFor(x => x.UnitPrice).GreaterThan(0).When(x => x.NeedHotelRoom, applyConditionTo: ApplyConditionTo.CurrentValidator);
        RuleFor(x => x.CurrencyCode).NotEmpty();
        RuleFor(x => x.MeetingsNum).GreaterThan(0).When(x => x.NeedMeetingRoom, applyConditionTo: ApplyConditionTo.CurrentValidator);
        RuleFor(x => x.MeetingTimes).NotEmpty().When(x => x.NeedMeetingRoom, applyConditionTo: ApplyConditionTo.CurrentValidator);
    }
}