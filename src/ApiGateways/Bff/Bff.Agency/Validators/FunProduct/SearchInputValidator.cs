using Bff.Agency.Models.FunProduct;
using FluentValidation;

namespace Bff.Agency.Validators.FunProduct;

public class SearchInputValidator : AbstractValidator<SearchInput>
{
    public SearchInputValidator()
    {
        RuleFor(x => x.PageIndex).GreaterThan(0);
        RuleFor(x => x.PageSize).GreaterThan(0);
        RuleFor(x => x.TravelDate).GreaterThanOrEqualTo(DateTime.Today)
            .When(x => x.TravelDate.HasValue, ApplyConditionTo.CurrentValidator);
    }
}
