using Bff.Agency.Models.Hotel;
using FluentValidation;

namespace Bff.Agency.Validators.Hotel;

public class GetHotelListPriceBffInputValidator : AbstractValidator<GetHotelListPriceBffInput>
{
    public GetHotelListPriceBffInputValidator()
    {
        RuleFor(x => x.HotelIds).NotEmpty();
        RuleFor(x => x.LiveDate).NotEmpty().GreaterThanOrEqualTo(DateTime.Today);
        RuleFor(x => x.LeaveDate).NotEmpty().GreaterThan(x=>x.LiveDate);
    }
}