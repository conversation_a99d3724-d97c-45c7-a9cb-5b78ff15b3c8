using Bff.Agency.Models.WorkOrder;
using FluentValidation;
using static Contracts.Common.Inventory.Consts.WorkOrderType;
using static Contracts.Common.Inventory.Consts.WorkOrderType.not_arrive_shop;

namespace Bff.Agency.Validators.WorkOrder;

public class ApplyBffInputValidator : AbstractValidator<ApplyBffInput>
{
    public ApplyBffInputValidator()
    {
        RuleFor(x => x.BaseOrderId)
            .NotEmpty();

        RuleFor(x => x.SubOrderId)
            .NotEmpty();

        RuleFor(x => x.WorkOrderType)
            .NotEmpty()
            //B2B入口添加工单时，仅可选择客户原因、到店原因、离店原因
            .Must(x => x.Contains($"{typeof(not_arrive_shop).Name}.{typeof(customer).Name}")
            || x.Contains($"{typeof(arrive_shop).Name}.{typeof(arrive_shop).Name}")
            || x.Contains($"{typeof(leave_shop).Name}.{typeof(leave_shop).Name}"));

        RuleFor(x => x.ContactEmail)
            .NotEmpty();

        RuleFor(x => x.Content)
            .NotEmpty();

    }
}
