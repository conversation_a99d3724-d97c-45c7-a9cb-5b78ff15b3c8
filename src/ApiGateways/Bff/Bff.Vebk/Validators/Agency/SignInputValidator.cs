using Bff.Vebk.Models.Agency;
using Contracts.Common.Payment.Enums;
using FluentValidation;

namespace Bff.Vebk.Validators.Agency;

public class SignInputValidator : AbstractValidator<SignInput>
{
    public SignInputValidator()
    {
        RuleFor(x => x.Id).NotNull().GreaterThan(0);

        RuleFor(x => x.FullName).NotNull()
            .Length(1, 256);

        RuleFor(x => x.FinancialStaff)
            .NotEmpty()
            .Length(1, 32);

        When(s => s.FinancialStaffNumber != null, () =>
        {
            RuleFor(s => s.FinancialStaffNumber).Length(1, 32).SetValidator(new SensitiveDataValidator()).WithMessage("FinancialStaffNumber error");
        });

        When(s => s.ContactNumber != null, () =>
        {
            RuleFor(s => s.ContactNumber).Length(1, 32).SetValidator(new SensitiveDataValidator()).WithMessage("ContactNumber error");
        });

        RuleFor(x => x.SettlementPeriod)
            .IsInEnum();

        RuleFor(x => x.CurrencyCode)
            .SetValidator(new CurrencyEnumValidator<Currency>()).WithMessage("currency code error");

    }
}
