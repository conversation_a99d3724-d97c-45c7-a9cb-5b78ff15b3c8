using Bff.Vebk.Models.CarProductOrder;
using Contracts.Common.Product.Enums;
using FluentValidation;

namespace Bff.Vebk.Validators.CarProductOrder;

public class CreateInputValidator : AbstractValidator<CreateInput>
{
    public CreateInputValidator()
    {
        RuleFor(x => x.CarProductId).GreaterThan(0);
        RuleFor(x => x.CarProductSkuId).GreaterThan(0);
        RuleFor(x => x.CarProductType).IsInEnum();
        RuleFor(x => x.AirportTransferType)
            .Must(x => x == AirportTransferType.PickUp || x == AirportTransferType.DropOff)
            .When(x => x.CarProductType == CarProductType.AirportTransfer, ApplyConditionTo.CurrentValidator);
        RuleForEach(x => x.CarItems)
            .SetInheritanceValidator(x => x.Add(new CarServiceItemCreateInputValidator()));
        //RuleFor(x => x.DepartureAddress).MaximumLength(200)
        //    .NotEmpty()
        //    .When(x => (x.CarProductType == CarProductType.AirportTransfer && x.AirportTransferType == AirportTransferType.DropOff)
        //    || x.CarProductType == CarProductType.PointToPointTransfer
        //    || x.CarProductType == CarProductType.CarChartered
        //    , ApplyConditionTo.CurrentValidator);
        //RuleFor(x => x.DestinationAddress).MaximumLength(200)
        //    .NotEmpty()
        //    .When(x => (x.CarProductType == CarProductType.AirportTransfer && x.AirportTransferType == AirportTransferType.PickUp)
        //    || x.CarProductType == CarProductType.PointToPointTransfer
        //    , ApplyConditionTo.CurrentValidator);
        //RuleFor(x => x.FlightNumber).MaximumLength(10);
        //RuleFor(x => x.AirportTerminal).MaximumLength(10);
        RuleFor(x => x.Quantity).GreaterThan(0);
        //RuleFor(x => x.Passengers).GreaterThan(0);
        //RuleFor(x => x.Baggages).GreaterThanOrEqualTo(0);
        RuleFor(x => x.TravelDate).GreaterThanOrEqualTo(DateTime.Today.AddDays(-1));
        RuleFor(x => x.Time).NotNull();
        //RuleFor(x => x.ContactsName).NotEmpty();
        //RuleFor(x => x.ContactsPhoneNumber).NotEmpty();
        RuleFor(x => x.SellingChannel).IsInEnum();
    }
}

public class CarServiceItemCreateInputValidator : AbstractValidator<CarItemCreateInput>
{
    public CarServiceItemCreateInputValidator()
    {
        RuleFor(x => x.ItemId).GreaterThan(0);
        RuleFor(x => x.Quantity).GreaterThan(0);
        RuleFor(x => x.Price).GreaterThanOrEqualTo(0);
        RuleFor(x => x.CostPrice).GreaterThanOrEqualTo(0);
    }
}
