using Bff.Vebk.Models.User;
using FluentValidation;

namespace Bff.Vebk.Validators.User;

public class SecurityVerificationInputValidator : AbstractValidator<SecurityVerificationInput>
{
    public SecurityVerificationInputValidator()
    {
        RuleFor(x => x.Code)
            .NotEmpty();

        RuleFor(x => x.FingerPrint)
            .NotEmpty();

        RuleFor(x => x.RequestId)
            .NotEmpty();

        RuleFor(x => x.TypeValue)
            .NotEmpty();

        RuleFor(x => x.Type)
            .IsInEnum();
    }
}
