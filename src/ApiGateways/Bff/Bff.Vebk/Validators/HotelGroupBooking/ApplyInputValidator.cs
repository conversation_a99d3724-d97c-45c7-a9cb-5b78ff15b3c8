using Bff.Vebk.Models.HotelGroupBooking;
using Contracts.Common.Order.DTOs.GroupBooking;
using FluentValidation;

namespace Bff.Vebk.Validators.HotelGroupBooking;

public class ApplyInputValidator : AbstractValidator<ApplyInput>
{
    public ApplyInputValidator()
    {
        RuleFor(x => x.TeamNatureType).IsInEnum();
        RuleFor(x => x.Demands).NotEmpty();
        RuleForEach(x => x.Demands).SetValidator(new ApplyDemandInputValidator());
        RuleFor(x => x.CountryDialCode).NotEmpty().MaximumLength(3).When(x => !string.IsNullOrWhiteSpace(x.ContactPhone));
        RuleFor(x => x.ContactPhone).NotEmpty().When(x => x.UserType == Contracts.Common.Order.Enums.UserType.Visitor, ApplyConditionTo.CurrentValidator);
        RuleFor(x => x.ContactName).NotEmpty().When(x => x.UserType == Contracts.Common.Order.Enums.UserType.Visitor, ApplyConditionTo.CurrentValidator);
        RuleFor(x => x.CompanyName).NotEmpty().When(x => x.UserType == Contracts.Common.Order.Enums.UserType.Visitor, ApplyConditionTo.CurrentValidator);
        //RuleFor(x => x.OperatorUserId).NotEmpty();
    }
}

public class ApplyDemandInputValidator : AbstractValidator<ApplyDemandInput>
{
    public ApplyDemandInputValidator()
    {
        RuleFor(x => x.AdultNum).GreaterThanOrEqualTo(1).LessThanOrEqualTo(9999).When(x => x.NeedHotelRoom, applyConditionTo: ApplyConditionTo.AllValidators);
        RuleFor(x => x.CityCode).GreaterThan(0);
        RuleFor(x => x.CityName).NotEmpty();
        RuleFor(x => x.CheckInDate).GreaterThanOrEqualTo(DateTime.Today);
        RuleFor(x => x.CheckOutDate).GreaterThanOrEqualTo(DateTime.Today);
        RuleFor(x => x.UnitPrice).GreaterThan(0).When(x => x.NeedHotelRoom, ApplyConditionTo.CurrentValidator);
        RuleFor(x => x.CurrencyCode).NotEmpty();
        RuleFor(x => x.MeetingsNum).GreaterThan(0).When(x => x.NeedMeetingRoom, applyConditionTo: ApplyConditionTo.CurrentValidator);
        RuleFor(x => x.MeetingTimes).NotEmpty().When(x => x.NeedMeetingRoom, applyConditionTo: ApplyConditionTo.CurrentValidator);
    }
}

public class InquiryFormDtoValidator : AbstractValidator<InquiryFormDto> 
{
    public InquiryFormDtoValidator()
    {
        RuleFor(x => x.CityCode).GreaterThan(0);
        RuleFor(x => x.CurrencyCode).NotEmpty();
        RuleFor(x => x.CheckIn).GreaterThanOrEqualTo(DateTime.Today);
        RuleFor(x => x.CheckOut).GreaterThanOrEqualTo(DateTime.Today);
        RuleFor(x => x.BudgetAmount).GreaterThanOrEqualTo(0);
        RuleFor(x => x.QueenBedNum).GreaterThanOrEqualTo(0);
        RuleFor(x => x.DoubleBedNum).GreaterThanOrEqualTo(0);
    }
}
