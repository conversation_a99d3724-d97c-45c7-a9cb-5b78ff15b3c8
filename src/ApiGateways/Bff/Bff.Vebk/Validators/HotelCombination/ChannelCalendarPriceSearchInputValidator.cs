using Contracts.Common.Hotel.DTOs.HotelCombination;
using FluentValidation;

namespace Bff.Vebk.Validators.HotelCombination;

public class ChannelCalendarPriceSearchInputValidator:AbstractValidator<ChannelCalendarPriceSearchInput>
{
    public ChannelCalendarPriceSearchInputValidator()
    {
        RuleFor(x => x.SellingChannel).IsInEnum();
        RuleFor(x => x.BeginDate).GreaterThanOrEqualTo(DateTime.Today);
        RuleFor(x => x.HotelCombinationId).GreaterThan(0);
        RuleFor(x => x.SkuId).GreaterThan(0);
    }
}
