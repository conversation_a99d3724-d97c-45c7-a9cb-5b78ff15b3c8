using Bff.Vebk.Models.TravelLineOrder;
using Contracts.Common.Product.Enums;
using FluentValidation;

namespace Bff.Vebk.Validators.TravelLineOrder;

public class ReplaceOrderProductBffInputValidator : AbstractValidator<Bff.Vebk.Models.TravelLineOrder.ReplaceOrderProductBffInput>
{
    public ReplaceOrderProductBffInputValidator()
    {
        RuleFor(x => x.BaseOrderId).NotEmpty();
        RuleFor(x => x.TravelDate).NotEmpty();
        RuleFor(x => x.LineProductId).NotEmpty();
        RuleFor(x => x.LineProductSkuId).NotEmpty();
        RuleForEach(r => r.SkuTypeItems)
            .ChildRules(c =>
            {
                // c.RuleFor(x => x.SkuPriceType).IsInEnum()
                //     .Must(x => x != LineSkuPriceType.RoomPriceDifference)
                //     .WithMessage("不支持房差");
                c.RuleFor(x => x.Price).NotEmpty();
                c.RuleFor(x => x.CostPrice).NotEmpty();
                c.RuleFor(x => x.Quantity).GreaterThanOrEqualTo(0);
            });
    }
}