using Bff.Vebk.Models.TravelLineOrder;
using FluentValidation;

namespace Bff.Vebk.Validators.TravelLineOrder;

public class UpdateContactBffInputValidator : AbstractValidator<UpdateContactBffInput>
{
    public UpdateContactBffInputValidator()
    {
        RuleFor(x => x.BaseOrderId).NotNull().GreaterThan(0);
        RuleFor(x => x.OrderFields).NotNull();
        //RuleFor(x => x.ContactsName).MaximumLength(20);
        //RuleFor(x => x.ContactsPhoneNumber).MaximumLength(20);
        //RuleFor(x => x.ContactsEmail).MaximumLength(50)
        //    .Matches(@"^[a-zA-Z0-9._-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,4}$")
        //    .When(x => !string.IsNullOrEmpty(x.ContactsEmail));
    }
}
