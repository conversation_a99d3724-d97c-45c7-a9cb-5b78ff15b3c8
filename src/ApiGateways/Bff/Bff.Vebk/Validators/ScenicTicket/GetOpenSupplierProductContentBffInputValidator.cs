using Bff.Vebk.Models.ScenicTicket;
using FluentValidation;

namespace Bff.Vebk.Validators.ScenicTicket;

public class GetOpenSupplierProductContentBffInputValidator : AbstractValidator<GetOpenSupplierProductContentBffInput>
{
    public GetOpenSupplierProductContentBffInputValidator()
    {
        RuleFor(x => x.TicketId).NotEmpty();
        RuleFor(x => x.SupplierId).NotEmpty();
        RuleFor(x => x.ActivityId).NotEmpty();
    }
}