using Bff.Vebk.Callers;
using Common.Swagger;
using Contracts.Common.Notify.DTOs.StaffNotify.Dingtalks;
using Contracts.Common.Notify.DTOs.StaffNotify.DingtalkSwitche;
using Contracts.Common.Notify.Enums;
using EfCoreExtensions.Abstract;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace Bff.Vebk.Controllers;

/// <summary>
/// 租户员工通知钉钉模版与机器人配置
/// </summary>
[Route("v1/[controller]/[action]")]
[ApiExplorerSettings(GroupName = nameof(ApiVersion.v1))]
[ApiController]
public class StaffNotifyTemplateDingtalkController : ControllerBase
{
    public readonly INotifyApiCaller _notifyApiCaller;

    public StaffNotifyTemplateDingtalkController(INotifyApiCaller notifyApiCaller)
    {
        _notifyApiCaller = notifyApiCaller;
    }

    /// <summary>
    /// 获取钉钉模版
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [Authorize]
    [HttpPost]
    [ProducesResponseType(typeof(PagingModel<StaffNotifyTemplateDingtalkListDto>), (int)HttpStatusCode.OK)]
    public async Task<IActionResult> Search(GetStaffNotifyTemplateDingtalkInput input)
    {
        var result = await _notifyApiCaller.StaffNotifyTemplateDingtalkSearch(input);
        return Ok(result);
    }

    /// <summary>
    /// 获取钉钉模版下拉选择列表
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [Authorize]
    [HttpGet]
    [ProducesResponseType(typeof(List<StaffNotifyTemplateDingtalkDto>), (int)HttpStatusCode.OK)]
    public async Task<IActionResult> List(NotifyEventType? notifyEventType)
    {
        var result = await _notifyApiCaller.StaffNotifyTemplateDingtalkList(notifyEventType);
        return Ok(result);
    }

    /// <summary>
    /// 更新钉钉模版与机器人配置
    /// </summary>
    [Authorize]
    [HttpPost]
    [ProducesResponseType((int)HttpStatusCode.OK)]
    public async Task<IActionResult> Update(StaffNotifyTemplateDingtalkDetailDto input)
    {
        await _notifyApiCaller.StaffNotifyTemplateDingtalkUpdate(input);
        return Ok();
    }

    /// <summary>
    /// 新增钉钉模版与机器人配置
    /// </summary>
    [Authorize]
    [HttpPost]
    [ProducesResponseType((int)HttpStatusCode.OK)]
    public async Task<IActionResult> Add(StaffNotifyTemplateDingtalkDetailDto input)
    {
        await _notifyApiCaller.StaffNotifyTemplateDingtalkAdd(input);
        return Ok();
    }

    /// <summary>
    /// 删除钉钉模版与机器人配置
    /// </summary>
    [Authorize]
    [HttpPost]
    [ProducesResponseType((int)HttpStatusCode.OK)]
    public async Task<IActionResult> Delete(StaffNotifyTemplateDingtalkInput input)
    {
        await _notifyApiCaller.StaffNotifyTemplateDingtalkDelete(input);
        return Ok();
    }

    /// <summary>
    /// 获取钉钉模版详情信息与机器人列表
    /// </summary>
    /// <param name="templateDingtalkId"></param>
    /// <returns></returns>
    [Authorize]
    [HttpGet]
    [ProducesResponseType(typeof(StaffNotifyTemplateDingtalkDetailDto), (int)HttpStatusCode.OK)]
    public async Task<IActionResult> DetailAsync(long templateDingtalkId)
    {
        var result = await _notifyApiCaller.StaffNotifyTemplateDingtalkDetail(templateDingtalkId);
        return Ok(result);
    }

    /// <summary>
    /// 更新业务类型钉钉模板
    /// </summary>
    [Authorize]
    [HttpPost]
    [ProducesResponseType((int)HttpStatusCode.OK)]
    public async Task<IActionResult> SaveEventSubTypeDingtalkRobot(StaffNotifyTemplateDingtalkRobotSwitcheDto input)
    {
        await _notifyApiCaller.SaveEventSubTypeDingtalkRobot(input);
        return Ok();
    }

    /// <summary>
    /// 删除业务类型钉钉模板
    /// </summary>
    [Authorize]
    [HttpPost]
    [ProducesResponseType((int)HttpStatusCode.OK)]
    public async Task<IActionResult> DeleteEventSubTypeDingtalkRobot(RemoveStaffNotifyTemplateDingtalkRobotSwitcheDto input)
    {
        await _notifyApiCaller.DeleteEventSubTypeDingtalkRobot(input);
        return Ok();
    }


}

