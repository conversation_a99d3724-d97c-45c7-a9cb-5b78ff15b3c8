using AutoMapper;
using Bff.Vebk.Callers;
using Bff.Vebk.Models.CustomerUser;
using Common.Swagger;
using Contracts.Common.Order.DTOs.BaseOrder;
using Contracts.Common.Tenant.DTOs.Agency;
using Contracts.Common.User.DTOs.CustomerUser;
using EfCoreExtensions.Abstract;
using Microsoft.AspNetCore.Mvc;

namespace Bff.Vebk.Controllers;

/// <summary>
/// c端用户管理
/// </summary>
[Route("v1/[controller]/[action]")]
[ApiExplorerSettings(GroupName = nameof(ApiVersion.v1))]
[ApiController]
public class CustomerUserController : ControllerBase
{
    private readonly IMapper _mapper;
    private readonly IUserApiCaller _userApiCaller;
    private readonly IOrderApiCaller _orderApiCaller;
    private readonly ITenantApiCaller _tenantApiCaller;

    public CustomerUserController(
        IMapper mapper,
        IUserApiCaller userApiCaller,
        IOrderApiCaller orderApiCaller,
        ITenantApiCaller tenantApiCaller)
    {
        _mapper = mapper;
        _userApiCaller = userApiCaller;
        _orderApiCaller = orderApiCaller;
        _tenantApiCaller = tenantApiCaller;
    }

    /// <summary>
    /// 分页查询用户管理数据
    /// </summary>
    [HttpPost]
    [SwaggerResponseExt(200, typeof(PagingModel<SearchUserManagementDataBffOutput>))]
    public async Task<IActionResult> SearchManagementData(SearchUserManagementDataBffInput input)
    {
        var searchInput = _mapper.Map<SearchCustomerUserPageInput>(input);
        var searchResponse = await _userApiCaller.SearchCustomerUsers(searchInput);
        var result = _mapper.Map<PagingModel<SearchUserManagementDataBffOutput>>(searchResponse);
        if (result.Data.Any() is false) return Ok(result);

        //通过用户id查询消费统计
        var userIds = result.Data.Select(x => x.CustomerUserId).ToList();
        var agencyIds = result.Data.Where(x => x.AgencyId > 0).Select(x => x.AgencyId).Distinct().ToList();
        var inviteCodeAgencyIds = result.Data.Where(x => x.InviteCodeAgencySource.HasValue).Select(x => x.InviteCodeAgencySource.Value).Distinct().ToList();
        if(inviteCodeAgencyIds.Any())
            agencyIds = agencyIds.Union(inviteCodeAgencyIds).ToList();

        List<GetAgencySimpleInfoOutput> agencies = new();
        var searchConsumptionTask = _orderApiCaller.GetConsumptionByIds(userIds);
        if (agencyIds.Any())
            agencies = await _tenantApiCaller.GetAgencySimpleInfos(new GetSimpleInfoInput { AgencyIds = agencyIds });
        var searchConsumptionResponse = await searchConsumptionTask;

        foreach (var item in result.Data)
        {
            var consumptionItem = searchConsumptionResponse.FirstOrDefault(x => x.CustomerUserId == item.CustomerUserId);
            if (consumptionItem is not null)
            {
                item.ConsumptionAmount = consumptionItem.ConsumptionAmount;
                item.ConsumptionCount = consumptionItem.ConsumptionCount;
                item.LastConsumptionTime = consumptionItem.LastConsumptionTime;
            }

            var agency = agencies.FirstOrDefault(x => x.AgencyId == item.AgencyId);
            if (agency is not null)
            {
                item.AgencyId = agency.AgencyId;
                item.AgencyFullName = agency.FullName;
            }

            var inviteCodeAgency = agencies.FirstOrDefault(x => x.AgencyId == item.InviteCodeAgencySource);
            if (inviteCodeAgency is not null)
            {
                item.InviteCodeAgencyFullName = inviteCodeAgency.FullName;
            }
        }
        return Ok(result);
    }

    /// <summary>
    /// 分页查询用户RFM数据
    /// </summary>
    [HttpPost]
    [SwaggerResponseExt(200,typeof(PagingModel<SearchUserRfmBffOutput>))]
    public async Task<IActionResult> SearchUserRfmData(SearchUserRfmBffInput input)
    {
        var searchInput = _mapper.Map<SearchUserConsumptionPageInput>(input);
        var searchResponse = await _orderApiCaller.SearchConsumptionStatistic(searchInput);
        var result = _mapper.Map<PagingModel<SearchUserRfmBffOutput>>(searchResponse);
        if (result.Data.Any() is false) return Ok(result);
        
        //通过用户id查询用户基础信息
        var userIds = result.Data.Select(x => x.CustomerUserId).ToList();
        var customerUsers = await _userApiCaller.GetCustomerUserByIds(userIds);
        if (customerUsers.Any() is false) return Ok(result);
        foreach (var item in result.Data)
        {
            var userItem = customerUsers.FirstOrDefault(x => x.Id == item.CustomerUserId);
            if(userItem is null) continue;

            item.NickName = userItem.NickName;
            item.PhoneNumber = userItem.PhoneNumber;
            item.CreateTime = userItem.CreateTime;
            item.RegisterSource = userItem.RegisterSource;
            item.DarenName = userItem.DarenName;
            item.VipLeveName = userItem.VipLeveName;
            item.VipLevelId = userItem.VipLevelId;
            item.Tags = userItem.Tags;
        }

        return Ok(result);
    }
}