using Bff.Vebk.Callers;
using Bff.Vebk.Models.AgencyChannelPriceSettings;
using Common.Swagger;
using Contracts.Common.Product.DTOs.AgencyChannelPriceSettings;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace Bff.Vebk.Controllers;

/// <summary>
/// 分销商分组价格配置
/// </summary>
[Route("v1/[controller]/[action]")]
[ApiExplorerSettings(GroupName = nameof(ApiVersion.v1))]
[ApiController]
public class AgencyChannelPriceSettingsController : ControllerBase
{
    private readonly ITenantApiCaller _tenantApiCaller;
    private readonly IProductApiCaller _productApiCaller;
    private readonly IScenicSpotApiCaller _scenicSpotApiCaller;

    public AgencyChannelPriceSettingsController(ITenantApiCaller tenantApiCaller, IProductApiCaller productApiCaller, IScenicSpotApiCaller scenicSpotApiCaller)
    {
        _tenantApiCaller = tenantApiCaller;
        _productApiCaller = productApiCaller;
        _scenicSpotApiCaller = scenicSpotApiCaller;
    }

    /// <summary>
    /// 设置B2B售卖状态
    /// </summary>
    /// <returns></returns>
    [HttpPost]
    [Authorize]
    [SwaggerResponseExt((int)HttpStatusCode.OK)]
    public async Task<IActionResult> SetB2bSellingStatus(SetB2bSellingStatusInput input)
    {
        //获取所有的价格类型分组
        var agencyPriceGroups = await _tenantApiCaller.GetAllAgencyPriceGroup();
        var priceGroupIds = agencyPriceGroups.Select(x => x.Id).ToList();

        //更新产品和SKU中的B2B售卖状态
        switch (input.ChannelPriceItems.First().ProductType)
        {
            case Contracts.Common.Product.Enums.ChannelProductType.Ticket:
                await _productApiCaller.SetProductSkuB2bSellingStatus(new Contracts.Common.Product.DTOs.ProductSku.SetB2bSellingStatusInput
                {
                    SkuIds = input.ChannelPriceItems.Select(x => x.SkuId).ToList(),
                    B2bSellingStatus = input.B2bSellingStatus
                });
                break;
            case Contracts.Common.Product.Enums.ChannelProductType.Line:
                await _productApiCaller.SetLineProductSkuB2bSellingStatus(new Contracts.Common.Product.DTOs.ProductSku.SetB2bSellingStatusInput
                {
                    SkuIds = input.ChannelPriceItems.Select(x => x.SkuId).Distinct().ToList(),
                    B2bSellingStatus = input.B2bSellingStatus
                });
                break;
            case Contracts.Common.Product.Enums.ChannelProductType.ScenicTicket:
                await _scenicSpotApiCaller.SetTicketB2bSellingStatus(new
                    Contracts.Common.Scenic.DTOs.Ticket.SetB2bSellingStatusInput
                {
                    Id = input.ChannelPriceItems.First().SkuId,
                    B2bSellingStatus = input.B2bSellingStatus
                });
                break;
            case Contracts.Common.Product.Enums.ChannelProductType.CarUsing:
                await _productApiCaller.SetCarProductSkuB2bSellingStatus(new
                    Contracts.Common.Product.DTOs.ProductSku.SetB2bSellingStatusInput
                {
                    SkuIds = input.ChannelPriceItems.Select(x => x.SkuId).ToList(),
                    B2bSellingStatus = input.B2bSellingStatus
                });
                break;
            case Contracts.Common.Product.Enums.ChannelProductType.CarUsingServiceItem:
                await _productApiCaller.SetCarServiceItemB2bSellingStatus(new
                    Contracts.Common.Product.DTOs.ProductSku.SetB2bSellingStatusInput
                {
                    SkuIds = input.ChannelPriceItems.Select(x => x.SkuId).ToList(),
                    B2bSellingStatus = input.B2bSellingStatus
                });
                break;
            case Contracts.Common.Product.Enums.ChannelProductType.Hotel:
            case Contracts.Common.Product.Enums.ChannelProductType.HuiZhiHotel:
            default:
                break;
        }

        //配置分销商渠道价格
        if (input.B2bSellingStatus)
        {
            // 7.29迭代. 不插入到价格分组
        }
        else
        {
            await _productApiCaller.RemoveListAgencyChannelPriceSettings(new RemoveChannelPriceDto
            {
                PriceGroupIds = priceGroupIds,
                SkuIds = input.ChannelPriceItems.Select(x => x.SkuId).ToList(),
                SkuSubItemIds = input.ChannelPriceItems.Where(x=>x.SkuSubItemId.HasValue)
                    .Select(x => x.SkuSubItemId!.Value).ToList()
            });
        }

        return Ok();
    }
}
