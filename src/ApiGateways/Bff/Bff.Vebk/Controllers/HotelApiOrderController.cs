using Bff.Vebk.Callers;
using Bff.Vebk.Models.SupplierUser;
using Common.Swagger;
using Common.Swagger.Header;
using Contracts.Common.Order.DTOs.HotelApiOrder;
using EfCoreExtensions.Abstract;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;

namespace Bff.Vebk.Controllers;

/// <summary>
/// api酒店订单
/// </summary>
[Route("v1/[controller]/[action]")]
[ApiExplorerSettings(GroupName = nameof(ApiVersion.v1))]
[ApiController]
public class HotelApiOrderController : ControllerBase
{
    private readonly IOrderApiCaller _orderApiCaller;

    public HotelApiOrderController(IOrderApiCaller orderApiCaller)
    {
        _orderApiCaller = orderApiCaller;
    }

    /// <summary>
    /// 汇智酒店订单状态变更通知
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [IgnoreTenantHeader]
    [SwaggerResponseExt(200, typeof(HopOrderStatusNotifyOutput))]
    public async Task<IActionResult> HopOrderStatusNotify(HopOrderStatusNotifyInput input)
    {
        var result = await _orderApiCaller.HopOrderStatusNotify(input);
        return Ok(result);
    }

    /// <summary>
    /// 汇智酒店订单变更通知 V3
    /// URL：分销商提供，需包含版本号v3，例如：https://xxx/api/v3/notify
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [IgnoreTenantHeader]
    [SwaggerResponseExt(200, typeof(HopOrderNotifyOutput))]
    public async Task<IActionResult> HopOrderNotify(HopOrderNotifyInput input)
    {
        var result = await _orderApiCaller.HopOrderNotify(input);
        return Ok(result);
    }

}
