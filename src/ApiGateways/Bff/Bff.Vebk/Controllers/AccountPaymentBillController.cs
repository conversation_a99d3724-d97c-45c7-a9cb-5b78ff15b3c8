using Bff.Vebk.Callers;
using Bff.Vebk.Models.AccountPaymentBill;
using Bff.Vebk.Models.OrderPayment;
using Common.Jwt;
using Common.Swagger;
using Contracts.Common.Order.DTOs.BaseOrder;
using Contracts.Common.Order.Enums;
using Contracts.Common.Payment.DTOs;
using Contracts.Common.Payment.DTOs.AccountPaymentBill;
using Contracts.Common.Payment.Enums;
using Contracts.Common.User.DTOs.DarenBonus;
using Contracts.Common.User.Enums;
using EfCoreExtensions.Abstract;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using NetTopologySuite.IO;
using System.Linq;
using AccountTransferOrder = Contracts.Common.Payment.DTOs.AccountTransferOrder;

namespace Bff.Vebk.Controllers;

/// <summary>
/// 在线支付账单
/// </summary>
[Route("v1/[controller]/[action]")]
[ApiExplorerSettings(GroupName = nameof(ApiVersion.v1))]
[ApiController]
public class AccountPaymentBillController : ControllerBase
{
    private readonly IPaymentApiCaller _paymentApiCaller;
    private readonly IOrderApiCaller _orderApiCaller;
    private readonly IUserApiCaller _userApiCaller;
    private readonly ITenantApiCaller _tenantApiCaller;

    public AccountPaymentBillController(IPaymentApiCaller paymentApiCaller,
        IOrderApiCaller orderApiCaller,
        IUserApiCaller userApiCaller,
        ITenantApiCaller tenantApiCaller)
    {
        _paymentApiCaller = paymentApiCaller;
        _orderApiCaller = orderApiCaller;
        _userApiCaller = userApiCaller;
        _tenantApiCaller = tenantApiCaller;
    }

    /// <summary>
    /// 搜索在线支付账单列表
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [Authorize]
    [ProducesResponseType(typeof(PagingModel<SearchOutput>), (int)HttpStatusCode.OK)]
    public async Task<IActionResult> Search(SearchInput input)
    {
        var result = await _paymentApiCaller.SearchAccountPaymentBills(input);
        return Ok(result);
    }

    /// <summary>
    /// 创建账单
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [Authorize]
    [SwaggerResponseExt(default, ErrorTypes.Payment.PaymentBillPeriodsRepeated)]
    public async Task<IActionResult> Create(CreateInput input)
    {
        CurrentUser currentUser = HttpContext.User.ParseUserInfo<CurrentUser>();
        var accountPaymentBillDetail = await AccountPaymentBillDetail(new AccountPaymentBillDetailInput
        {
            BeginDate = input.BeginDate,
            EndDate = input.EndDate,
        });
        var billAmount = accountPaymentBillDetail.Statistics.Total;
        CreateAccountPaymentBillInput createAccountPaymentBillInput = new()
        {
            BeginDate = input.BeginDate,
            EndDate = input.EndDate,
            Remark = input.Remark,
            Creator = currentUser.NickName,
            CreatorId = currentUser.UserId,
            BillAmount = billAmount,//合计金额 统计计算
            CurrencyCode = Currency.CNY.ToString(),
        };
        await _paymentApiCaller.CreateAccountPaymentBill(createAccountPaymentBillInput);
        return Ok();
    }

    /// <summary>
    /// 核销账单
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [Authorize]
    [SwaggerResponseExt(default, ErrorTypes.Common.NotSupportedOperation)]
    public async Task<IActionResult> Verify(VerifyInput input)
    {
        CurrentUser currentUser = HttpContext.User.ParseUserInfo<CurrentUser>();
        VerifyAccountPaymentBillInput verifyAccountPaymentBillInput = new()
        {
            Id = input.Id,
            Remark = input.Remark,
            Verifier = currentUser.NickName,
            VerifierId = currentUser.UserId,
        };
        await _paymentApiCaller.VerifyAccountPaymentBill(verifyAccountPaymentBillInput);
        return Ok();
    }

    /// <summary>
    /// 取消账单
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [Authorize]
    [SwaggerResponseExt(default, ErrorTypes.Common.NotSupportedOperation)]
    public async Task<IActionResult> Cancel(CancelInput input)
    {
        CancelAccountPaymentBillInput cancelAccountPaymentBillInput = new()
        {
            Id = input.Id
        };
        await _paymentApiCaller.CancelAccountPaymentBill(cancelAccountPaymentBillInput);
        return Ok();
    }

    /// <summary>
    /// 查询账单明细数据
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [Authorize]
    [ProducesResponseType(typeof(AccountPaymentBillDetailOutput), (int)HttpStatusCode.OK)]
    public async Task<IActionResult> QueryBillDetail(AccountPaymentBillDetailInput input)
    {
        var result = await AccountPaymentBillDetail(input);
        return Ok(result);
    }

    private async Task<AccountPaymentBillDetailOutput> AccountPaymentBillDetail(AccountPaymentBillDetailInput input)
    {
        AccountPaymentBillDetailOutput accountPaymentBillDetailOutput = new();

        var QueryOrderBillStatementTask = QueryOrderBillStatement(new OrderBillStatementInput
        {
            BeginDate = input.BeginDate,
            EndDate = input.EndDate
        });

        var orderPaymentsTask = _paymentApiCaller.SearchOrderPaymentDetails(new Contracts.Common.Payment.DTOs.OrderPayment.SearchOrderPaymentInput
        {
            BeginDate = input.BeginDate,
            EndDate = input.EndDate,
            PayStatus = PayStatus.Paid,
            PayType = PayType.YeePay,
            OrderPaymentTypes = new OrderPaymentType[] { OrderPaymentType.AgencyCreditCharge, OrderPaymentType.OfflineReceiptOrderPay }
        });

        var offlineReceiptOrderRefundsTask = _paymentApiCaller.SearchOrderRefundDetails(new Contracts.Common.Payment.DTOs.OrderRefund.SearchOrderRefundInput
        {
            OrderPaymentTypes = new OrderPaymentType[] { OrderPaymentType.OfflineReceiptOrderPay },
            BeginDate = input.BeginDate,
            EndDate = input.EndDate,
            PayType = PayType.YeePay,
            RefundStatus = RefundStatus.SUCCESS
        });

        #region 转账
        var SearchAccountTransferOrderTask = _paymentApiCaller.SearchAccountTransferOrders(new AccountTransferOrder.SearchInput
        {
            BeginDate = input.BeginDate,
            EndDate = input.EndDate,
            TransferStatus = TransferStatus.SUCCESS,
            UsageTypes = new AccountTransferOrderUsageType[]
            {
                AccountTransferOrderUsageType.DarenWithdrawalAfter,
                AccountTransferOrderUsageType.OfflineReceiptWithdrawalBefore,
                AccountTransferOrderUsageType.OfflineReceiptWithdrawalTransferRollback,
                AccountTransferOrderUsageType.SettlementOrderTransferBefore,
                AccountTransferOrderUsageType.SettlementOrderTransferRollback,
                AccountTransferOrderUsageType.WithdrawOrderTransferBefore,
                AccountTransferOrderUsageType.WithdrawOrderTransferRollback
            }
        });

        IEnumerable<AccountTransferOrder.SearchOutput> accountTransferOrders = await SearchAccountTransferOrderTask;
        //查询达人提现
        var darenWithdrawalTransferOrders = accountTransferOrders
            .Where(x => x.UsageType == AccountTransferOrderUsageType.DarenWithdrawalAfter)
            .ToList();
        var darenWithdrawalIds = darenWithdrawalTransferOrders
            .Select(x => x.BusinessOrderId);

        //查询收款码提现数据
        var offlineReceiptWithdrawalTransferOrders = accountTransferOrders.Where(x => x.UsageType == AccountTransferOrderUsageType.OfflineReceiptWithdrawalBefore
            || x.UsageType == AccountTransferOrderUsageType.OfflineReceiptWithdrawalTransferRollback)
            .ToList();
        var offlineReceiptWithdrawalIds = offlineReceiptWithdrawalTransferOrders
            .Select(x => x.BusinessOrderId)
            .Distinct();

        //查询付款结算单数据
        var settlementOrderTransferOrders = accountTransferOrders.Where(x => x.UsageType == AccountTransferOrderUsageType.SettlementOrderTransferBefore
            || x.UsageType == AccountTransferOrderUsageType.SettlementOrderTransferRollback)
            .ToList();
        var settlementOrderTransferOrderIds = settlementOrderTransferOrders
            .Select(x => x.BusinessOrderId)
            .Distinct();

        //调用服务取数据
        var darenWithdrawalByIdsTask = _userApiCaller.SearchDarenWithdrawalByIds(darenWithdrawalIds);
        var offlineReceiptOrderWithdrawalsTask = _paymentApiCaller.OfflineReceiptOrderWithdrawalSearch(offlineReceiptWithdrawalIds);
        var settlementOrderTransferRecordByIdsTask = _orderApiCaller.SearchSettlementOrderTransferRecordByIds(settlementOrderTransferOrderIds);
        //处理数据
        var darenWithdrawals = await darenWithdrawalByIdsTask;
        accountPaymentBillDetailOutput.DarenWithdrawalBills = darenWithdrawalTransferOrders.Select(x =>
        {
            var darenWithdrawal = darenWithdrawals.FirstOrDefault(s => s.Id == x.BusinessOrderId);
            return new DarenWithdrawalBillOutput
            {
                CreateTime = x.CreateTime,
                PhoneNumber = darenWithdrawal?.PhoneNumber,
                WithdrawApplyId = x.TransferOrderNo,
                OrderAmount = x.OrderAmount,
                DebitAmount = x.TransferOrderType == TransferOrderType.Outsite ? -x.DebitAmount : x.DebitAmount,
                ExtOrderNo = x.ExtOrderNo
            };
        });
        var offlineReceiptOrderWithdrawals = await offlineReceiptOrderWithdrawalsTask;
        accountPaymentBillDetailOutput.OfflineReceiptWithdrawalBills = offlineReceiptWithdrawalTransferOrders.Select(x =>
        {
            var offlineReceiptOrderWithdrawal = offlineReceiptOrderWithdrawals.FirstOrDefault(s => s.Id == x.BusinessOrderId);
            return new OfflineReceiptWithdrawalBillOutput
            {
                CreateTime = x.CreateTime,
                SupplierName = offlineReceiptOrderWithdrawal?.SupplierName,
                WithdrawalType = x.TransferOrderType switch
                {
                    TransferOrderType.Outsite => 1,
                    TransferOrderType.Insite => 2,
                },
                DebitAmount = x.TransferOrderType == TransferOrderType.Outsite ? -x.DebitAmount : x.ReceiveAmount,
                OfflineReceiptWithdrawalId = x.TransferOrderNo,
                ExtOrderNo = x.ExtOrderNo
            };
        });
        var settlementOrderTransferRecords = await settlementOrderTransferRecordByIdsTask;
        var supplierIds = settlementOrderTransferRecords.Select(x => x.SupplierId).Distinct();
        var suppliers = await _tenantApiCaller.GetSupplierShortInfo(new Contracts.Common.Tenant.DTOs.Supplier.ShortInfoInput
        {
            SupplierIds = supplierIds,
        });
        accountPaymentBillDetailOutput.SettlementPayOrderBills = settlementOrderTransferOrders.Select(x =>
        {
            var settlementOrderTransferRecord = settlementOrderTransferRecords.FirstOrDefault(s => s.SettlementOrderTransferRecordId == x.BusinessOrderId);
            var supplier = suppliers.FirstOrDefault(s => s.SupplierId == settlementOrderTransferRecord?.SupplierId);
            return new SettlementPayOrderBillOutput
            {
                CreateTime = x.CreateTime,
                SupplierName = supplier?.SupplierFullName,
                SettlementOrderId = settlementOrderTransferRecord?.SettlementOrderId ?? 0,
                SettlementType = x.TransferOrderType switch
                {
                    TransferOrderType.Outsite => 1,
                    TransferOrderType.Insite => 2,
                },
                OrderAmount = x.TransferOrderType switch
                {
                    TransferOrderType.Outsite => -x.OrderAmount,
                    TransferOrderType.Insite => x.OrderAmount,
                },
                DebitAmount = x.TransferOrderType switch
                {
                    TransferOrderType.Outsite => -x.DebitAmount,
                    TransferOrderType.Insite => x.ReceiveAmount,
                },
                Fee = x.TransferOrderType switch
                {
                    TransferOrderType.Outsite => -x.Fee,
                    TransferOrderType.Insite => x.Fee,
                },
                ExtOrderNo = x.ExtOrderNo,
            };
        });

        #endregion

        //订单
        accountPaymentBillDetailOutput.OrderBills = await QueryOrderBillStatementTask;

        var orderPayments = await orderPaymentsTask;
        //B2B额度充值
        var agencyCreditChargeOrderPayments = orderPayments
            .Where(x => x.OrderPaymentType == OrderPaymentType.AgencyCreditCharge)
            .ToList();
        var agencyCreditChargeOrderIds = agencyCreditChargeOrderPayments
            .Select(x => x.OrderId)
            .ToArray();
        var agencyCreditChargeOrders = await _tenantApiCaller.AgencyCreditChargeDetails(agencyCreditChargeOrderIds);
        accountPaymentBillDetailOutput.AgencyCreditChargeBills = agencyCreditChargeOrderPayments
            .Select(x =>
            {
                var agencyCreditChargeOrder = agencyCreditChargeOrders.FirstOrDefault(s => s.Id == x.OrderId);
                return new AgencyCreditChargeBillOutput
                {
                    Createtime = x.CreateTime,
                    AgencyName = agencyCreditChargeOrder?.AgencyName,
                    Amount = x.OrderAmount,
                    ExtNo = x.UniqueOrderNo,
                    PayChannel = x.PayChannel,
                    PaymentFee = x.PaymentFee - x.MerchantFee,
                    PlatformCommission = x.PlatformCommission
                };
            });
        //收款码收款
        var offlineReceiptOrderPayments = orderPayments
            .Where(x => x.OrderPaymentType == OrderPaymentType.OfflineReceiptOrderPay)
            .ToList();
        var offlineReceiptOrderRefunds = await offlineReceiptOrderRefundsTask;
        var offlineReceiptOrderIds = offlineReceiptOrderPayments.Select(x => x.OrderId);
        offlineReceiptOrderIds = offlineReceiptOrderIds.Union(offlineReceiptOrderRefunds.Select(x => x.OrderId)).Distinct();
        var offlineReceiptOrders = await _paymentApiCaller.SearchOfflineReceiptOrderByIds(offlineReceiptOrderIds);
        var offlineReceiptOrderBills = offlineReceiptOrderPayments
            .Select(x =>
            {
                var offlineReceiptOrder = offlineReceiptOrders.FirstOrDefault(s => s.Id == x.OrderId);
                return new OfflineReceiptOrderBillOutput
                {
                    CreateTime = x.CreateTime,
                    Amount = x.OrderAmount,
                    ExtNo = x.UniqueOrderNo,
                    OfflineReceiptOrderId = x.OrderId,
                    OfflineReceiptType = 1,
                    PaymentFee = x.PaymentFee - x.MerchantFee,
                    SupplerName = offlineReceiptOrder?.SupplierName,
                    ProductTitle = offlineReceiptOrder.Title,
                };
            });
        var offlineReceiptOrderRefundBills = offlineReceiptOrderRefunds
            .Select(x =>
            {
                var offlineReceiptOrder = offlineReceiptOrders.FirstOrDefault(s => s.Id == x.OrderId);
                return new OfflineReceiptOrderBillOutput
                {
                    CreateTime = x.CreateTime,
                    Amount = -x.RefundAmount,
                    OfflineReceiptOrderId = x.OrderId,
                    OfflineReceiptType = 2,
                    PaymentFee = x.PaymentFee+x.ReturnMerchantFee,
                    SupplerName = offlineReceiptOrder?.SupplierName,
                    ProductTitle = offlineReceiptOrder.Title,
                    ExtNo = x.UniqueRefundNo,
                };
            });
        accountPaymentBillDetailOutput.OfflineReceiptOrderBills = offlineReceiptOrderBills
            .Union(offlineReceiptOrderRefundBills)
            .OrderBy(x => x.CreateTime);

        List<StatementOutput> statements = new()
        {
            new StatementOutput() { BusinessType = 1, Amount=accountPaymentBillDetailOutput.OrderBills.Sum(s=>s.StatementAmount)},
            new StatementOutput() { BusinessType = 2,Amount=accountPaymentBillDetailOutput.AgencyCreditChargeBills.Sum(s=>s.StatementAmount) },
            new StatementOutput() { BusinessType = 3, Amount=accountPaymentBillDetailOutput.DarenWithdrawalBills.Sum(s=>s.DebitAmount)},
            new StatementOutput() { BusinessType = 4, Amount=accountPaymentBillDetailOutput.OfflineReceiptOrderBills.Sum(s=>s.StatementAmount)},
            new StatementOutput() { BusinessType = 5, Amount=accountPaymentBillDetailOutput.OfflineReceiptWithdrawalBills.Sum(s=>s.DebitAmount)},
            new StatementOutput() { BusinessType = 6, Amount=accountPaymentBillDetailOutput.SettlementPayOrderBills.Sum(s=>s.DebitAmount)},
        };
        accountPaymentBillDetailOutput.Statistics = new StatisticsOutput
        {
            BeginDate = input.BeginDate,
            EndDate = input.EndDate,
            Statements = statements,
            Total = statements.Sum(s => s.Amount)
        };

        return accountPaymentBillDetailOutput;
    }

    private async Task<IEnumerable<OrderBillStatementOutput>> QueryOrderBillStatement(OrderBillStatementInput input)
    {
        //查询订单支付单导出数据
        var orderStatementRequest = new GetOrderStatementInput
        {
            BeginDate = input.BeginDate,
            EndDate = input.EndDate
        };
        var orderStatements = await _paymentApiCaller.GetOrderStatementExportData(orderStatementRequest);
        var billStatements = await FillOrderStatements(orderStatements);
        return billStatements;
    }

    private async Task<IEnumerable<OrderBillStatementOutput>> FillOrderStatements(
        IEnumerable<GetOrderStatementOutput> orderStatements)
    {
        if (orderStatements?.Any() is not true)
            return Enumerable.Empty<OrderBillStatementOutput>();
        //查询订单金额信息
        var orderAmountInfoRequest = new OrderAmountInfoInput
        {
            OrderInfos = orderStatements.Select(x => new OrderInfoInput
            {
                OrderId = x.OrderId,
                OrderPaymentType = (byte)x.OrderPaymentType
            })
        };
        var orderAmountInfos = await _orderApiCaller.GetOrderAmountInfos(orderAmountInfoRequest);

        //数据聚合
        var billStatements = new List<OrderBillStatementOutput>();

        foreach (var orderStatement in orderStatements)
        {
            var orderAmountInfo = orderAmountInfos
                .Where(x => x.OrderId == orderStatement.OrderId)
                .FirstOrDefault();

            OrderBillStatementOutput statement = new()
            {
                CreateTime = orderStatement.CreateTime,
                OrderType = orderStatement.OrderType,
                OrderPaymentType = orderStatement.OrderPaymentType,
                OrderId = orderStatement.OrderId,
                PayChannel = orderStatement.PayChannel,
                StatementType = orderStatement.StatementType,
                Amount = orderStatement.Amount,
                PaymentFee = orderStatement.PaymentFee,
                PlatformCommission = orderStatement.PlatformCommission,
                TotalAmount = orderAmountInfo?.TotalAmount ?? 0,
                DiscountAmount = orderAmountInfo?.DiscountAmount ?? 0,
                AgencyName = orderAmountInfo?.AgencyName,
                ExtNo = orderStatement.ExtNo
            };
            billStatements.Add(statement);
        }

        return billStatements;
    }

}
