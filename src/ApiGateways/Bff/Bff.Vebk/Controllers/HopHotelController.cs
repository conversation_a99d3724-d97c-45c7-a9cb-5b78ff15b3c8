using Bff.Vebk.Callers;
using Common.Swagger;
using Common.Swagger.Header;
using Contracts.Common.Hotel.DTOs.HopHotel;
using Microsoft.AspNetCore.Mvc;

namespace Bff.Vebk.Controllers;

[Route("v1/[controller]/[action]")]
[ApiExplorerSettings(GroupName = nameof(ApiVersion.v1))]
[ApiController]
public class HopHotelController : ControllerBase
{
    private readonly IHotelApiCaller _hotelApiCaller;

    public HopHotelController(IHotelApiCaller hotelApiCaller)
    {
        _hotelApiCaller = hotelApiCaller;
    }

    /// <summary>
    /// HOP推送酒店变更日历报价
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [IgnoreTenantHeader]
    [ProducesResponseType(typeof(HopHotelPushNightlyPriceResponse), (int)HttpStatusCode.OK)]
    public async Task<IActionResult> PushPrices([FromBody]HopHotelPushNightlyPriceRequest request)
    {
        var result = await _hotelApiCaller.HopHotelPushNightlyPrices(request);
        return Ok(result);
    }
}
