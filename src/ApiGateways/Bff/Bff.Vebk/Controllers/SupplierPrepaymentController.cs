using AutoMapper;
using Bff.Vebk.Callers;
using Bff.Vebk.Models.SupplierPrepayment;
using Common.Swagger;
using Contracts.Common.Payment.DTOs.SupplierPrepayment;
using Contracts.Common.Tenant.DTOs.Supplier;
using EfCoreExtensions.Abstract;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace Bff.Vebk.Controllers;

/// <summary>
/// 供应商-预付款流水
/// </summary>
[Route("v1/[controller]/[action]")]
[ApiExplorerSettings(GroupName = nameof(ApiVersion.v1))]
[ApiController]
public class SupplierPrepaymentController : ControllerBase
{
    private readonly IPaymentApiCaller _paymentApiCaller;
    private readonly ITenantApiCaller _tenantApiCaller;
    private readonly IMapper _mapper;
    
    public SupplierPrepaymentController(
        IPaymentApiCaller paymentApiCaller,
        ITenantApiCaller tenantApiCaller,
        IMapper mapper)
    {
        _paymentApiCaller = paymentApiCaller;
        _tenantApiCaller = tenantApiCaller;
        _mapper = mapper;
    }

    /// <summary>
    /// 获取供应商预付款钱包余额
    /// </summary>
    /// <param name="supplierId"></param>
    /// <returns></returns>
    [Authorize]
    [HttpGet]
    [SwaggerResponseExt(200,typeof(GetSupplierPrepaymentBffOutput))]
    public async Task<IActionResult> GetBalance(long supplierId)
    {
        var result = new GetSupplierPrepaymentBffOutput
        {
            SupplierId = supplierId
        };
        var response = await _paymentApiCaller.GetSupplierPrepaymentBalances(supplierId);
        var supplierPrepayment = response.FirstOrDefault();
        result.Balance = supplierPrepayment?.Balance ?? 0;
        result.CurrencyCode = supplierPrepayment?.CurrencyCode;
        if (string.IsNullOrEmpty(supplierPrepayment?.CurrencyCode))
        {
            var supplier = await _tenantApiCaller.GetSupplierDetail(supplierId);
            result.CurrencyCode = supplier?.CurrencyCode;
        }

        return Ok(result);
    }
    
    /// <summary>
    /// 供应商-预付款流水查询
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [Authorize]
    [SwaggerResponseExt(200,typeof(PagingModel<SearchSupplierPrepaymentFlowBffOutput>))]
    public async Task<IActionResult> SearchFlows(SearchSupplierPrepaymentFlowBffInput input)
    {
        var searchRequest = _mapper.Map<SearchSupplierPrepaymentFlowInput>(input);
        if(input.SupplierId.HasValue)
            searchRequest.SupplierIds.Add(input.SupplierId.Value);
        
        var searchResponse = await _paymentApiCaller.SearchSupplierPrepaymentFlows(searchRequest);
        var result = _mapper.Map<PagingModel<SearchSupplierPrepaymentFlowBffOutput>>(searchResponse);
        if (result.Data.Any())
        {
            var supplierIds = result.Data.Select(x => x.SupplierId).Distinct().ToArray();
            var suppliers = await _tenantApiCaller.GetSupplierDetailByIds(supplierIds);
            foreach (var item in result.Data)
            {
                var supplier = suppliers.FirstOrDefault(x => x.SupplierId == item.SupplierId);
                item.SupplierName = supplier?.FullName;
                item.CurrencyCode = supplier?.CurrencyCode;
            }
        }

        return Ok(result);
    }
}