using Contracts.Common.Order.Enums;
using Contracts.Common.Payment.Enums;

namespace Bff.Vebk.Models.AfterSaleFinancialHandleOrder;

public class GetDetailOutput
{
    public long Id { get; set; }

    /// <summary>
    /// 抵冲单id
    /// </summary>
    public long OffsetOrderId { get; set; }


    /// <summary>
    /// 业务类型
    /// </summary>
    public OffsetOrderBusinessType BusinessType { get; set; }

    /// <summary>
    /// 售后类型
    /// </summary>
    public AfterSaleFinancialHandleType HandleType { get; set; }

    /// <summary>
    /// 金额(用正负区分)
    /// </summary>
    public decimal Amount { get; set; }

    public string CurrencyCode { get; set; }

    /// <summary>
    /// 分销商id
    /// </summary>
    public long AgencyId { get; set; }

    public string AgencyName { get; set; }

    /// <summary>
    /// 状态
    /// </summary>
    public HandleOrderStatus Status { get; set; }

    /// <summary>
    /// 银行信息
    /// </summary>
    public TenantBankAccountDto TenantBankAccount { get; set; } = new();

    /// <summary>
    /// 收付款时间
    /// </summary>
    public DateTime? AccountTime { get; set; }

    /// <summary>
    /// 凭证
    /// </summary>
    public string? Proof { get; set; }

    /// <summary>
    /// 备注
    /// </summary>
    public string? Remark { get; set; }

    public DateTime CreateTime { get; set; }

    /// <summary>
    /// 抵冲单-图片
    /// </summary>
    public List<string>? OffsetOrderImages { get; set; }
}

public class TenantBankAccountDto
{
    public long? Id { get; set; }

    /// <summary>
    /// 商户银行账户类型
    /// </summary>
    public TenantBankAccountType? TenantBankAccountType { get; set; }

    /// <summary>
    /// 开户名称
    /// </summary>
    public string? AccountName { get; set; }

    /// <summary>
    /// 银行编号
    /// </summary>
    public string? BankCode { get; set; }

    /// <summary>
    /// 开户银行
    /// </summary>
    public string? BankName { get; set; }

    /// <summary>
    /// 分行名称
    /// </summary>
    public string? BranchName { get; set; }

    /// <summary>
    /// 银行账号
    /// </summary>
    public string? AccountNo { get; set; }

    public string? BankAccount => AccountNo;

    /// <summary>
    /// 地址
    /// </summary>
    public string? Address { get; set; }

    public string? AbroadAccountAddress => Address;


    public string? SwiftCode { get; set; }

    /// <summary>
    /// 币种
    /// </summary>
    public string? CurrencyCode { get; set; }

    public string? AbroadAccountCurrencyCode => CurrencyCode;

    /// <summary>
    /// 开户银行代码
    /// </summary>
    public string? OpeningBankCode { get; set; }
}