using Swashbuckle.AspNetCore.Annotations;

namespace Bff.Vebk.Models.ScenicTicketPurchaseOrder;

public class GetScenicTicketPurchaseInventoryBffInput
{
    /// <summary>
    /// 门票id
    /// </summary>
    [SwaggerSchema(Nullable = false)]
    public long TicketId { get; set; }

    /// <summary>
    /// 主订单Id
    /// </summary>
    [SwaggerSchema(Nullable = true)]
    public long? BaseOrderId { get; set; }

    /// <summary>
    /// 出行日期
    /// </summary>
    [SwaggerSchema(Nullable = true)]
    public DateTime? TravelDate { get; set; }
}