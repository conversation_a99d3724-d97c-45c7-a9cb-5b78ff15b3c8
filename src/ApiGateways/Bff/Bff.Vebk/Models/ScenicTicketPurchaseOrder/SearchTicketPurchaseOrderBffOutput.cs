using Contracts.Common.Order.Enums;
using Contracts.Common.Scenic.Enums;

namespace Bff.Vebk.Models.ScenicTicketPurchaseOrder;

public class SearchTicketPurchaseOrderBffOutput
{
    /// <summary>
    /// 采购单id
    /// </summary>
    public long Id { get; set; }
    
    /// <summary>
    /// 批次名称
    /// </summary>
    public string BatchName { get; set; }

    /// <summary>
    /// 景点Id
    /// </summary>
    public long ScenicSpotId { get; set; }

    /// <summary>
    /// 景点名称
    /// </summary>
    public string ScenicSpotName { get; set; }

    /// <summary>
    /// 门票Id
    /// </summary>
    public long TicketId { get; set; }

    /// <summary>
    /// 门票名称
    /// </summary>
    public string TicketName { get; set; }
    
    /// <summary>
    /// 关联供应商Id
    /// </summary>
    public long SupplierId { get; set; }

    /// <summary>
    /// 供应商名称
    /// </summary>
    public string SupplierName { get; set; }

    /// <summary>
    /// 采购总价
    /// </summary>
    public decimal TotalAmount { get; set; }
    
    /// <summary>
    /// 可用库存采购总价
    /// 总价÷总数量×剩余数量
    /// </summary>
    public decimal TotalAvailableAmount { get; set; }
    
    /// <summary>
    /// 采购总价币种
    /// </summary>
    public string CurrencyCode { get; set; }
    
    /// <summary>
    /// 采购总库存
    /// </summary>
    public int TotalQuantity { get; set; }

    /// <summary>
    /// 可用库存
    /// </summary>
    public int AvailableQuantity { get; set; }

    /// <summary>
    /// 有效期 - 起
    /// </summary>
    public DateTime ValidityBegin { get; set; }

    /// <summary>
    /// 有效期 - 止
    /// </summary>
    public DateTime ValidityEnd { get; set; }

    /// <summary>
    /// 是否默认(默认批次优先扣减采购库存)
    /// </summary>
    public bool IsDefault { get; set; }
    
    /// <summary>
    /// 备注(最多200个字)
    /// </summary>
    public string Remark { get; set; }
    
    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreateTime { get; set; }
    
    /// <summary>
    /// 门票类型
    /// </summary>
    public ScenicTicketsType TicketsType { get; set; }
    
    /// <summary>
    /// 采购单号
    /// </summary>
    public string? SupplierOrderId { get; set; }

    /// <summary>
    /// 出库状态
    /// </summary>
    /// <returns></returns>
    public bool Outbound { get; set; }
    
    /// <summary>
    /// 入库盘点
    /// </summary>
    public bool InboundInventoryChecked { get; set; }
    
    /// <summary>
    /// 预警类型
    /// </summary>
    public PurchaseOrderValidDaysWarningType ValidDaysWarningType { get; set; }
    
    /// <summary>
    /// 剩余的有效天数
    /// </summary>
    public int RemainingValidDays { get; set; }
    
    /// <summary>
    /// 同一采购单号汇总（入库）-总数量
    /// </summary>
    public int StockInTotalQuantity { get; set; }
    /// <summary>
    /// 同一采购单号汇总（入库）-采购总价
    /// </summary>
    public decimal StockInTotalAmount { get; set; }
    
    /// <summary>
    /// 同一采购单号汇总（出库）- 总数量
    /// </summary>
    public int StockOutTotalQuantity { get; set; }
    
    /// <summary>
    /// 同一采购单号汇总（出库）- 采购总价
    /// </summary>
    public decimal StockOutTotalAmount { get; set; }
    
    /// <summary>
    /// 同一采购单号汇总（出库）-  关联订单
    /// </summary>
    public List<long> StockOutRelatedBaseOrderIds { get; set; }
    
    /// <summary>
    /// 同一采购单号汇总（出库已核销）- 总数量
    /// </summary>
    public int StockOutWriteOffTotalQuantity { get; set; }
    
    /// <summary>
    /// 同一采购单号汇总（出库已核销）- 采购总价
    /// </summary>
    public decimal StockOutWriteOffTotalAmount { get; set; }
    
    /// <summary>
    ///  同一采购单号汇总（出库已核销）- 关联订单
    /// </summary>
    public List<long> StockOutWriteOffRelatedBaseOrderIds { get; set; }
    
    /// <summary>
    /// 采购折扣比例[0-100] 2位小数
    /// <remarks>
    /// <para>如15表示为15%的折扣</para>
    /// </remarks>
    /// </summary>
    public decimal CostDiscountRate { get; set; }
}