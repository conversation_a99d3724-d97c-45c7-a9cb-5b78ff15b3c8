using Contracts.Common.Order.Enums;
using Swashbuckle.AspNetCore.Annotations;

namespace Bff.Vebk.Models.OrderRemark;

public class QueryBaseOrderRemarkBffInput
{
    /// <summary>
    ///  订单id
    /// </summary>
    [SwaggerSchema(Nullable = false)]
    public long BaseOrderId { get; set; }
    
    /// <summary>
    /// 订单备注类型
    /// </summary>
    [SwaggerSchema(Nullable = true)]
    public BaseOrderRemarkType? RemarkType { get; set; }
}