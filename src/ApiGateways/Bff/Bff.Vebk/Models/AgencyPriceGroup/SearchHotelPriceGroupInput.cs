using Contracts.Common.Hotel.Enums;
using Swashbuckle.AspNetCore.Annotations;

namespace Bff.Vebk.Models.AgencyPriceGroup;

public class SearchHotelPriceGroupInput : PagingInput
{
    /// <summary>
    /// 酒店名称
    /// </summary>
    [SwaggerSchema(Nullable = true)]
    public string HotelName { get; set; }
    
    /// <summary>
    /// 酒店运营模式
    /// </summary>
    [SwaggerSchema(Nullable = true)]
    public OperatingModel OperatingModel { get; set; }

    /// <summary>
    /// 供应商id
    /// </summary>
    [SwaggerSchema(Nullable = true)]
    public long? SupplierId { get; set; }
    
    /// <summary>
    /// 是否上下架
    /// </summary>
    [SwaggerSchema(Nullable = true)]
    public bool? Enabled { get; set; }

    /// <summary>
    /// 价格策略id列表
    /// </summary>
    [SwaggerSchema(Nullable = true)]
    public IEnumerable<long> PriceStrategyIds { get; set; } = Enumerable.Empty<long>();

    /// <summary>
    /// 价格分组id
    /// </summary>
    [SwaggerSchema(Nullable = false)]
    public long PriceGroupId { get; set; }
}