using AutoMapper;
using Contracts.Common.Order.DTOs.TicketsCombinationOrder;
using EfCoreExtensions.Abstract;

namespace Bff.Vebk.Models.TicketsCombinationOrder;

public class TicketCombinationOrderMapProfile : Profile
{
    public TicketCombinationOrderMapProfile()
    {
        CreateMap<SearchTicketsCombinationOrderBffInput, SearchTicketsCombinationOrderInput>();
        CreateMap<SearchTicketsCombinationOrderOutput, SearchTicketsCombinationOrderBffOutput>();
        CreateMap<PagingModel<SearchTicketsCombinationOrderOutput, CombinationOrderStatistics>,
            PagingModel<SearchTicketsCombinationOrderBffOutput, CombinationOrderStatistics>>();

        CreateMap<CreateCombinationOrderSalesBffInfo, CreateCombinationOrderSalesInfo>();
        CreateMap<CreateCombinationOrderFieldBffInfo, CreateCombinationOrderFieldInfo>();
        CreateMap<CreateCombinationOrderExtraBffInfos, CreateCombinationOrderExtraInfos>();
        CreateMap<CreateCombinationOrderBffInput,CreateCombinationOrderInput>();
        CreateMap<CreateCombinationOrderOutput,CreateCombinationOrderBffOutput>();
    }
}