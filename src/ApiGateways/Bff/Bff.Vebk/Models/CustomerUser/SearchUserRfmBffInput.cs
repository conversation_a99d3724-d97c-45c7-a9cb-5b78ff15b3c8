namespace Bff.Vebk.Models.CustomerUser;

public class SearchUserRfmBffInput : PagingInput
{
    /// <summary>
    /// 最近消费日期-开始
    /// </summary>
    public DateTime? BeginConsumptionTime { get; set; }

    /// <summary>
    /// 最近消费日期-结束
    /// </summary>
    public DateTime? EndConsumptionTime { get; set; }

    /// <summary>
    /// 累计最小消费金额
    /// </summary>
    public decimal? MinConsumptionAmount { get; set; }

    /// <summary>
    /// 累计最大消费金额
    /// </summary>
    public decimal? MaxConsumptionAmount { get; set; }

    /// <summary>
    /// 累计最小消费次数
    /// </summary>
    public int? MinConsumptionCount { get; set; }

    /// <summary>
    /// 累计最大消费次数
    /// </summary>
    public int? MaxConsumptionCount { get; set; }

}