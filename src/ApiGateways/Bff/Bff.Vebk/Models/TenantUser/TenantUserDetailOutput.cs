namespace Bff.Vebk.Models.TenantUser;

public class TenantUserDetailOutput
{
    public long Id { get; set; }

    /// <summary>
    /// 账号
    /// </summary>
    public string Account { get; set; }

    /// <summary>
    /// 姓名
    /// </summary>
    public string Name { get; set; }

    /// <summary>
    /// 昵称
    /// </summary>
    public string NickName { get; set; }

    /// <summary>
    /// 手机号
    /// </summary>
    public string PhoneNumber { get; set; }

    /// <summary>
    /// 是否可用
    /// </summary>
    public bool Enabled { get; set; }

    public DateTime CreateTime { get; set; }

    /// <summary>
    /// 微信绑定
    /// </summary>
    public bool WechatBind { get; set; }

    public string QrCodeUrl { get; set; }

    /// <summary>
    /// 角色Id
    /// </summary>
    public long RoleId { get; set; }

    /// <summary>
    /// 角色
    /// </summary>
    public string RoleName { get; set; }

    /// <summary>
    /// 邮箱
    /// </summary>
    public string? Email { get; set; }

    /// <summary>
    /// 微信昵称
    /// </summary>
    public string? WechatNickName { get; set; }

    /// <summary>
    /// 是否验证手机
    /// </summary>
    public bool IsVaildPhoneNumber { get; set; }

    /// <summary>
    /// 是否验证邮箱
    /// </summary>
    public bool IsVaildEmail { get; set; }
}
