using Contracts.Common.Order.DTOs.GroupBooking;
using Contracts.Common.Order.Enums;

namespace Bff.Vebk.Models.HotelGroupBooking;

public class SearchInput : PagingInput
{
    public long? AgencyId { get; set; }

    public DateTime? BeginDate { get; set; }
    public DateTime? EndDate { get; set; }

    /// <summary>
    /// 出行开始时间
    /// </summary>
    public DateTime? CheckInBeginDate { get; set; }

    /// <summary>
    /// 出行结束时间
    /// </summary>
    public DateTime? CheckInEndDate { get; set; }

    public GroupBookingApplicationFormStatus[]? Statuses { get; set; }

    /// <summary>
    /// 申请单号id long 可选
    /// </summary>
    public long? Id { get; set; }

    /// <summary>
    /// 申请预订单状态
    /// </summary>
    public GroupBookingFormPreOrderStatus? GroupBookingFormPreOrderStatus { get; set; }

    /// <summary>
    /// 国家编码
    /// </summary>
    public int[] CountryCode { get; set; } = Array.Empty<int>();
    /// <summary>
    /// 城市编码
    /// </summary>
    public int[] CityCode { get; set; } = Array.Empty<int>();

    /// <summary>
    /// 跟进运营id 商户员工id
    /// </summary>
    public long? OperatorUserId { get; set; }

    /// <summary>
    /// 销售BD
    /// </summary>
    public long? SalespersonId { get; set; }

    /// <summary>
    /// 申请酒店名称
    /// </summary>
    public string? HotelName { get; set; }

    /// <summary>
    /// 报价酒店名称
    /// </summary>
    public string? QuotationHotelName { get; set; }
}