using AutoMapper;
using Contracts.Common.Order.DTOs.OrderFieldInformation;
using Contracts.Common.Product.DTOs.Fields;
using Contracts.Common.Product.DTOs.InformationTemplateFields;
using Contracts.Common.Product.DTOs.ProductInformationTemplate;

namespace Bff.Vebk.Models.OrderFieldInformation;

public class OrderFieldInformationMapProfile : Profile
{
    public OrderFieldInformationMapProfile()
    {
        CreateMap<OrderFieldInformationTypeOutput, BffOrderFieldInformationTypeOutput>();
        CreateMap<OrderFieldInformationOutput, OrderFieldInformationOutput>();
        CreateMap<InformationTemplateFieldsOutput, OrderFieldInformationDto>();

        CreateMap<InformationTemplateFieldsOutput, OrderFieldInformationOutput>();
        CreateMap<ProductInformationTemplateDetail, BffOrderFieldInformationTypeOutput>();


        CreateMap<OrderFieldInformationTypeOutput, SaveOrderFieldInformationTypeDto>();

        CreateMap<FieldsOutput, InformationTemplateFieldsOutput>();

        CreateMap<SaveOrderFieldInformationTypeDto, OrderFieldInformationTypeOutput>();
        CreateMap<OrderFieldInformationDto, OrderFieldInformationOutput>();
    }
}
