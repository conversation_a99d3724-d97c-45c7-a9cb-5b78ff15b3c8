using Contracts.Common.Tenant.DTOs.B2BPopUp;
using Contracts.Common.Tenant.Enums;

namespace Bff.Vebk.Models.B2BPopUp;

public class UpdateB2BPopUpBffInput
{
    public long Id { get; set; }
    /// <summary>
    /// 弹窗名称
    /// </summary>
    public string Name { get; set; }

    /// <summary>
    /// 是否启用
    /// </summary>
    public bool Enable { get; set; }

    /// <summary>
    /// 生效时间
    /// </summary>
    public DateTime BeginTime { get; set; }

    /// <summary>
    /// 结束时间
    /// </summary>
    public DateTime EndTime { get; set; }
    /// <summary>
    /// 触达客户
    /// </summary>
    public B2BPopUpAgencyType B2BPopUpAgencyType { get; set; } = B2BPopUpAgencyType.All;

    /// <summary>
    /// 展示次数
    /// </summary>
    public B2BPopUpShowType B2BPopUpShowType { get; set; } = B2BPopUpShowType.DayOnce;

    /// <summary>
    ///  弹窗弹窗内容
    /// </summary>
    public List<B2BPopUpContentInfo> B2BPopUpContents { get; set; }

    /// <summary>
    /// 分销商Id
    /// </summary>
    public List<long> AgencyIds { get; set; }
}