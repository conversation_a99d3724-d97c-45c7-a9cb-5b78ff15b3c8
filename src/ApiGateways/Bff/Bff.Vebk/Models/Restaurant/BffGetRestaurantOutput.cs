using Contracts.Common.Hotel.Enums;
using Contracts.Common.Resource.Enums;

namespace Bff.Vebk.Models.Restaurant;

public class BffGetRestaurantOutput
{
    public long Id { get; set; }

    public OperatingModel OperatingModel { get; set; }

    public string Name { get; set; }

    /// <summary>
    /// 国家
    /// </summary>
    public int CountryCode { get; set; }

    public string ZhCountryName { get; set; }

    public int ProvinceCode { get; set; }

    public string ZhProvinceName { get; set; }

    public int CityCode { get; set; }

    public string ZhCityName { get; set; }

    public int DistrictCode { get; set; }

    public string DistrictName { get; set; }

    public string Address { get; set; }

    public string HouseNumber { get; set; }

    public double? Longitude { get; set; }

    public double? Latitude { get; set; }

    /// <summary>
    /// 坐标系默认BD09
    /// </summary>
    public CoordinateType CoordinateType { get; set; } = CoordinateType.BD09;

    /// <summary>
    /// 营业时间
    /// </summary>
    public string ServiceTime { get; set; }

    /// <summary>
    /// 联系电话
    /// </summary>
    public string Telephone { get; set; }

    public bool Enabled { get; set; }

    public List<string> Paths { get; set; }


    public string EnCountryName { get; set; }

    public string EnProvinceName { get; set; }

    public string EnCityName { get; set; }
}
