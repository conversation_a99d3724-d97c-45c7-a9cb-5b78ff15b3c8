namespace Bff.Vebk.Models.AgencyAgreement;

public class GetAgencyAgreementOutput
{
    /// <summary>
    /// 登录协议配置
    /// </summary>
    public IEnumerable<LoginAgreementDto> LoginAgreements { get; set; }

    /// <summary>
    /// 注册协议配置
    /// </summary>
    public IEnumerable<RegisterAgreementDto> RegisterAgreements { get; set; }
}

public class LoginAgreementDto
{
    public long Id { get; set; }

    /// <summary>
    /// 是否启用
    /// </summary>
    public bool Enabled { get; set; }

    /// <summary>
    /// 内容
    /// </summary>
    public string Content { get; set; }

    /// <summary>
    /// 语言
    /// </summary>
    public string? Language { get; set; }

    /// <summary>
    /// 登录图片地址
    /// </summary>
    public List<string> LoginImagePath { get; set; }
}

public class RegisterAgreementDto
{
    public long Id { get; set; }

    /// <summary>
    /// 是否启用
    /// </summary>
    public bool Enabled { get; set; }

    /// <summary>
    /// 内容
    /// </summary>
    public string Content { get; set; }

    /// <summary>
    /// 语言
    /// </summary>
    public string? Language { get; set; }
}
