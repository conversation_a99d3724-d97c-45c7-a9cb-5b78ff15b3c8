using Contracts.Common.Payment.Enums;

namespace Bff.Vebk.Models.VirtualCreditCard;

public class GetVirtualCreditCardPaymentDataBffOutput
{
    /// <summary>
    /// saas银行账户id
    /// </summary>
    public long? BankAccountId { get; set; }
    
    /// <summary>
    /// VCC供应商类型
    /// </summary>
    public VccSupplierType VccSupplierType { get; set; }

    /// <summary>
    /// vcc卡供应商名称
    /// </summary>
    public string VccSupplierName { get; set; }
    
    /// <summary>
    /// 出账原币金额
    /// </summary>
    public decimal? OriginalAmount { get; set; }
    
    /// <summary>
    /// 出账原币金额的币种
    /// </summary>
    public string? OriginalCurrency { get; set; }

    /// <summary>
    /// saas付款结算时间(实际出账时间)
    /// </summary>
    public DateTime? SettlementTime { get; set; }

    /// <summary>
    /// VCC侧货币(实际出账币种)
    /// </summary>
    public string? Currency { get; set; }
    
    /// <summary>
    /// VCC交易金额(实际出账金额)
    /// <remarks>会存在多次扣款的情况，该金额会变动</remarks>
    /// </summary>
    public decimal? TransactionAmount { get; set; }

    /// <summary>
    /// 交易比率(出账币种汇率)
    /// <remarks>出账币种金额/原币金额的比率</remarks>
    /// </summary>
    public decimal? TransactionRate { get; set; }

    /// <summary>
    /// 出账人民币金额
    /// </summary>
    public decimal? TransactionRMBAmount { get; set; }

    /// <summary>
    /// 出账人民币汇率
    /// </summary>
    public decimal? TransactionRMBRate { get; set; }
    
    /// <summary>
    /// 是否可以开卡
    /// </summary>
    public bool IsCanCreateVirtualCreditCard { get; set; }
}