using Swashbuckle.AspNetCore.Annotations;

namespace Bff.Vebk.Models.TicketsCombinationPackage;

public class DeleteTicketsCombinationPackageBffInput
{
    /// <summary>
    /// 组合产品id
    /// </summary>
    [SwaggerSchema(Nullable = false)]
    public long TicketsCombinationId { get; set; }
    
    /// <summary>
    /// 组合套餐id
    /// </summary>
    [SwaggerSchema(Nullable = false)]
    public long TicketsCombinationPackageId { get; set; }
}