using Contracts.Common.Order.Enums;

namespace Bff.Vebk.Models.OpenChannelSyncFailOrder;

public class SearchOpenChannelSyncFailOrderBffOutput
{
    /// <summary>
    /// saas失败订单号
    /// </summary>
    public long Id { get; set; }
    
    /// <summary>
    /// 渠道订单号
    /// </summary>
    public string ChannelOrderNo  { get; set; }
    
    /// <summary>
    /// 渠道订单号列表格式
    /// <value>同步失败订单渠道单号都是单个,目前不存在多个.为了兼容saas多渠道单号.渠道单号数据列表格式返回</value>
    /// </summary>
    public List<string> ChannelOrderNos  { get; set; }

    /// <summary>
    /// 产品id
    /// 门票-景点id,线路-产品id
    /// </summary>
    public long? ProductId { get; set; }

    /// <summary>
    /// 产品名称
    /// </summary>
    public string? ProductName { get; set; }

    /// <summary>
    /// 规格id
    /// 门票-门票id
    /// 线路-套餐id
    /// </summary>
    public long? SkuId { get; set; }

    /// <summary>
    /// 规格套餐名称
    /// </summary>
    public string? SkuName { get; set; }

    /// <summary>
    /// 时段id
    /// </summary>
    public long? TimeSlotId { get; set; }

    /// <summary>
    /// 时段信息
    /// </summary>
    public TimeSpan? TimeSlot { get; set; }

    /// <summary>
    /// 是否组合订单
    /// </summary>
    public bool IsCombination { get; set; }
    
    /// <summary>
    /// 分销商id
    /// </summary>
    public long? AgencyId { get; set; }

    /// <summary>
    /// 出行日期
    /// </summary>
    public DateTime TravelDate { get; set; }

    /// <summary>
    /// 联系人名称
    /// </summary>
    public string? ContactsName { get; set; }

    /// <summary>
    /// 联系人手机号码
    /// </summary>
    public string? ContactsPhoneNumber { get; set; }

    /// <summary>
    /// 联系人邮箱
    /// </summary>
    public string? ContactsEmail { get; set; }

    /// <summary>
    /// 分销端支付金额
    /// </summary>
    public decimal PaymentAmount { get; set; }
    
    /// <summary>
    /// 订单类型
    /// </summary>
    public OrderType OrderType { get; set; }
    
    /// <summary>
    /// 售卖平台
    /// </summary>
    public SellingPlatform SellingPlatform { get; set; }

    /// <summary>
    /// 售卖渠道
    /// </summary>
    public SellingChannels SellingChannels { get; set; }
    
    /// <summary>
    /// 同步状态
    /// </summary>
    public OpenChannelFailOrderSyncStatus SyncStatus { get; set; }

    /// <summary>
    /// 关联saas系统订单id
    /// </summary>
    public List<long> RelatedBaseOrderIds { get; set; } = new();
    
    /// <summary>
    /// 同步时间
    /// </summary>
    public DateTime CreateTime { get; set; } 

    /// <summary>
    /// 更新时间
    /// </summary>
    public DateTime? UpdateTime { get; set; }

    /// <summary>
    /// 原因
    /// </summary>
    public string Reason { get; set; }
}