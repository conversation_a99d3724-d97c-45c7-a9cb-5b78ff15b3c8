using Contracts.Common.Product.Enums;
using Contracts.Common.Scenic.Enums;
using Swashbuckle.AspNetCore.Annotations;

namespace Bff.Vebk.Models.LineProductSkuCalendarPrice;

public class GetLineProductSkuCalendarPriceBffInput
{
    /// <summary>
    /// 线路产品id
    /// </summary>
    [SwaggerSchema(Nullable = false)]
    public long LineProductId { get; set; }
    
    /// <summary>
    /// 线路产品sku id
    /// </summary>
    [SwaggerSchema(Nullable = true)]
    public long LineProductSkuId { get; set; }
    
    /// <summary>
    /// 线路产品sku id列表
    /// <remarks>
    /// 暂时兼容.后续需要改造调整
    /// </remarks>
    /// </summary>
    [SwaggerSchema(Nullable = true)]
    public List<long> LineProductSkuIds { get; set; } = new();
    
    /// <summary>
    /// 开始日期
    /// </summary>
    [SwaggerSchema(Nullable = false)]
    public DateTime StartDate { get; set; }
    
    /// <summary>
    /// 结束日期
    /// </summary>
    [SwaggerSchema(Nullable = false)]
    public DateTime EndDate { get; set; }
    
    /// <summary>
    /// 是否上架
    /// </summary>
    [SwaggerSchema(Nullable = true)]
    public bool? Enabled { get; set; }
    
    /// <summary>
    /// 分销商id
    /// 用于汇率换算
    /// </summary>
    [SwaggerSchema(Nullable = true)]
    public long? AgencyId { get; set; }
    
    /// <summary>
    /// 库存预警类型
    /// </summary>
    [SwaggerSchema(Nullable = true)]
    public InventoryWarningType? InventoryWarningType { get; set; }
    
    /// <summary>
    /// 日历价格渠道类型
    /// </summary>
    [SwaggerSchema(Nullable = true)]
    public CalendarPriceChannelType PriceChannelType { get; set; } = CalendarPriceChannelType.System;
    
    /// <summary>
    /// 是否只查询有价格的记录
    /// </summary>
    [SwaggerSchema(Nullable = true)]
    public bool OnlyWithSellingPrice { get; set; }
}