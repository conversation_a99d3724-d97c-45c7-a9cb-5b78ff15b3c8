using Bff.Vebk.Models.TenantUser;

namespace Bff.Vebk.Models.User;

public class GetTenantUserSecurityVerificationOutput
{
    /// <summary>
    /// 是否需要安全验证
    /// </summary>
    public bool IsNeedSecurityVerification { get; set; }

    /// <summary>
    /// 是否验证任意一种联系方式
    /// </summary>
    public bool? IsValidContact { get; set; }

    /// <summary>
    /// 用户信息
    /// </summary>
    public TenantUserDetailOutput? User { get; set; }
}
