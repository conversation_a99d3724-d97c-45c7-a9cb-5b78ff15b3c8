namespace Bff.Vebk.Models.SpecializedHotel;

public class GetSpecializedHotelCitySummaryBffOutput
{
    /// <summary>
    /// 国家名
    /// </summary>
    public string CountryName { get; set; }
    /// <summary>
    /// 国家英文名
    /// </summary>
    public string EnCountryName { get; set; }

    /// <summary>
    /// 国家代码
    /// </summary>
    public long CountryCode { get; set; }

    public List<GetSpecializedHotelCityItemBffOutput> Cities { get; set; }
}

public class GetSpecializedHotelCityItemBffOutput
{
    /// <summary>
    /// 城市代码
    /// </summary>
    public int CityCode { get; set; }
    /// <summary>
    /// 城市名
    /// </summary>
    public string CityName { get; set; }

    /// <summary>
    /// 城市英文名
    /// </summary>
    public string EnCityName { get; set; }

    /// <summary>
    /// 酒店总数
    /// </summary>
    public long? Count { get; set; }
}
