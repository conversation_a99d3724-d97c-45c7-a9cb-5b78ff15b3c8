using Contracts.Common.Order.Enums;
using Swashbuckle.AspNetCore.Annotations;

namespace Bff.Vebk.Models.OpenChannelOrder;

public class ReSendVoucherBffInput
{
    /// <summary>
    /// 主订单id
    /// </summary>
    [SwaggerSchema(Nullable = false)]
    public long BaseOrderId { get; set; }
    
    /// <summary>
    /// 订单类型
    /// </summary>
    [SwaggerSchema(Nullable = false)]
    public OrderType OrderType { get; set; }
}