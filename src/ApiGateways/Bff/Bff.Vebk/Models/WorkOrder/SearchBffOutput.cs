using Contracts.Common.Order.Enums;

namespace Bff.Vebk.Models.WorkOrder;

public class SearchBffOutput
{
    public long Id { get; set; }

    public string AgencyFullName { get; set; }

    public DateTime CreateTime { get; set; }

    /// <summary>
    /// HOP工单号
    /// </summary>
    public string? HopWorkOrderNumber { get; set; }

    public long BaseOrderId { get; set; }

    /// <summary>
    /// 工单类型
    /// </summary>
    public string WorkOrderType { get; set; }

    /// <summary>
    /// 订单状态
    /// </summary>
    public WorkOrderStatus Status { get; set; }

    /// <summary>
    /// 最新一条回复
    /// </summary>
    public string LatestReplyContent { get; set; }

    /// <summary>
    /// 服务评价
    /// </summary>
    public EvaluationType? Evaluation { get; set; }

    /// <summary>
    /// 服务评价内容
    /// </summary>
    public string ServiceEvaluationContent { get; set; }

    /// <summary>
    /// 服务评价时间
    /// </summary>
    public DateTime? ServiceEvaluationTime { get; set; }

    /// <summary>
    /// 创建人姓名
    /// </summary>
    public string CreatorName { get; set; }

    /// <summary>
    /// 认领人
    /// </summary>
    public string? QuoteStaff { get; set; }
}