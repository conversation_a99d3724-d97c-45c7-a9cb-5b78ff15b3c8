using AutoMapper;
using Contracts.Common.Tenant.DTOs.B2BIndexHotel;

namespace Bff.Vebk.Models.B2BHotHotel;

public class B2BHotHotelProfile : Profile
{
    public B2BHotHotelProfile()
    {
        CreateMap<GetB2BHotHotelBffInput, GetB2BHotHotelInput>();
        CreateMap<GetB2BHotHotelOutput, GetB2BHotHotelBffOutput>();
        CreateMap<GetB2BIndexPageHotHotelCityOutput, GetB2BIndexPageHotHotelCityBffOutput>();
    }
}
