using Contracts.Common.Tenant.Enums;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Bff.Vebk.Models.AgencyLevelConfig;
public class AgencyLevelConfigExpenseTransformSaveInput
{
    /// <summary>
    /// 业务
    /// </summary>
    public AgencyLevelBusinessType BusinessType { get; set; }

    /// <summary>
    /// 转换值 每消费100 CNY 获得的成长值
    /// </summary>
    public int TransformToValue { get; set; }

    /// <summary>
    /// 是否启用
    /// </summary>
    public bool Enable { get; set; }
}
