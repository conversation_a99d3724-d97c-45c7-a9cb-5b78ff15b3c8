using Contracts.Common.Order.Enums;
using Swashbuckle.AspNetCore.Annotations;

namespace Bff.Vebk.Models.CompensationOrder;

public class UnBindCompensationOrderBffInput
{
    /// <summary>
    /// 订单id
    /// </summary>
    [SwaggerSchema(Nullable = false)]
    public long BaseOrderId { get; set; }

    /// <summary>
    /// 订单类型
    /// </summary>
    [SwaggerSchema(Nullable = false)]
    public OrderType OrderType { get; set; }
    
    /// <summary>
    /// 绑定的订单id
    /// </summary>
    [SwaggerSchema(Nullable = false)]
    public List<long> UnBindBaseOrderIdArr { get; set; } = new();
}