using Contracts.Common.Order.Enums;
using Contracts.Common.Resource.DTOs.FileResource;
using Newtonsoft.Json;

namespace Bff.Vebk.Models.OpenSupplierOrder;

/// <summary>
/// 供货端-通知
/// </summary>
public class SupplierOrderBaseNotifyBffInput
{
    public string AppKey { get; set; }

    public DateTime RequestTime { get; set; }
    
    public string Sign { get; set; }

    public NotificationData Data { get; set; }
}

public class NotificationData
{
    /// <summary>
    /// 通知类型：
    /// <list>
    /// <item>url发货通知：productDeliverNotify</item>
    /// <item>流数据发货通知：productDeliverStreamNotify</item>
    /// <item>价格通知: productStockNotify</item>
    /// <item>库存通知: productPriceNotify</item>
    /// <item>用车 - 询价结果通知 : carSearchNotify</item>
    /// <item>用车 - 预订结果通知 : carReservationNotify</item>
    /// </list>
    /// </summary>
    public string NotifyType { get; set; }
    
    /// <summary>
    /// 供应商类型
    /// </summary>
    public string SupplierType { get; set; }
    
    /// <summary>
    /// 通知主体
    /// </summary>
    public object NotifyBody { get; set; }
}

public class NotifyBodyBase
{
    /// <summary>
    /// 供应商订单号
    /// </summary>
    public string OutOrderId { get; set; }

    /// <summary>
    /// Saas订单号
    /// </summary>
    public string OrderId { get; set; }
    
    public string OutProductId { get; set; }

    public string OutProductOptionId { get; set; }

    public string OutSkuId { get; set; }
    
    /// <summary>
    /// 异步通知号
    /// </summary>
    public string NotifyId { get; set; }
}

#region Delivery Notify

/// <summary>
/// 凭证链接地址通知
/// </summary>
public class DeliveryUrlNotifyBody : NotifyBodyBase
{
    /// <summary>
    /// 凭证地址(旧版.目前沿用.后续需要变更再调整)
    /// </summary>
    public string ETicketUrl { get; set; }
    
    /// <summary>
    /// 凭证数据(包含转换的凭证数据)
    /// </summary>
    public List<DeliveryUrlNotifyVouchers> Vouchers { get; set; }
}

public class DeliveryUrlNotifyVouchers
{
    /// <summary>
    /// 凭证类型
    /// <list type="number">
    ///   <item>1.图片</item>
    ///   <item>2.pdf</item>
    /// </list>
    /// </summary>
    public OpenSupplierNotifyFileType FileType { get; set; }
    
    /// <summary>
    /// 凭证url
    /// </summary>
    public string VoucherUrl { get; set; }
}

/// <summary>
/// 凭证数据流通知
/// </summary>
public class DeliveryStreamNotifyBody : NotifyBodyBase
{
    /// <summary>
    /// 凭证数据流
    /// </summary>
    [JsonConverter(typeof(MemoryStreamJsonConverter))]
    public Stream FileStream { get; set; }
}

#endregion

#region Order Notify

/// <summary>
/// 订单取消
/// </summary>
public class OrderCancelNotifyBody : NotifyBodyBase
{
    /// <summary>
    /// SaaS订单状态
    /// ORDERED-下单成功，
    /// PAYED-已支付，
    /// SHIPPED-订单已发货，
    /// CANCLED-已取消
    /// </summary>
    public string OrderStatus { get; set; }
}

#endregion

#region Price Inventory Notify

public class InventoryNotifyBody : NotifyBodyBase
{
    /// <summary>
    /// 是否在售
    /// <value>默认为true,true-在售 ,false-下架</value>
    /// </summary>
    public bool IsSale { get; set; } = true;
    
    /// <summary>
    /// 价库开始日期
    /// </summary>
    public DateTime StartDate { get; set; }

    /// <summary>
    /// 价库截止日期
    /// </summary>
    public DateTime EndDate { get; set; }
    
    /// <summary>
    /// 数据类型
    /// <value>
    /// <para>OpenSupplierNotifyPricingDataType</para>
    /// <para>Regular-常规性价库数据</para>
    /// <para>Operation - 运维性价库数据</para>
    /// </value>
    /// </summary>
    public string DataType { get; set; }
    
    public List<InventoryNotifyBodyStock> Stocks { get; set; }
}

public class InventoryNotifyBodyStock
{
    public int Available { get; set; }
    public int Total { get; set; }
    public DateTime Time { get; set; }

    public decimal? Price { get; set; }

    public string? Currency { get; set; }
    
    /// <summary>
    /// 时段场次名称
    /// </summary>
    public string? TimeSlot { get; set; }
}

public class PriceNotifyBody : NotifyBodyBase
{
    public decimal? NewPrice { get; set; }
    public string? Currency { get; set; }
}

#endregion

#region OrderStatus Notify

public class OpenSupplierOrderStatusNotifyBody : NotifyBodyBase
{
    /// <summary>
    /// 订单状态
    /// OpenSupplierOrderStatus
    /// </summary>
    public string OrderStatus { get; set; }

    /// <summary>
    /// 供应商原始凭证地址
    /// <list type="number">
    ///   <item>该值仅当发货失败错误时返回，供应商原始凭证地址可能为空，也可能为其他格式，请根据实际情况进行处理. </item>
    ///   <item>目前该值直接返回给Saas当做错误信息展示. 需要人工处理</item>
    ///   <item>发货失败通知下.该值为空.表示供应端发货失败.该值不为空.表示供应端发货成功.只是开放平台通知Saas失败.需要人工处理.</item>
    /// </list>
    /// </summary>
    public string VoucherUrl { get; set; }
}

#endregion

#region Car SearchNotify - 询价结果通知

public class OpenSupplierCarSearchNotifyBody : NotifyBodyBase
{
    /// <summary>
    /// 搜索id
    /// </summary>
    public string SearchId { get; set; }

    /// <summary>
    /// 结果列表
    /// </summary>
    public List<OpenSupplierCarSearchNotifyResultItem> Results { get; set; }
}

public class OpenSupplierCarSearchNotifyResultItem
{
    /// <summary>
    /// 搜索结果id
    /// </summary>
    public string ResultId { get; set; }

    /// <summary>
    /// 价格
    /// </summary>
    public decimal Price { get; set; }

    /// <summary>
    /// 币种
    /// </summary>
    public string Currency { get; set; }

    /// <summary>
    /// 车辆信息
    /// </summary>
    public OpenSupplierCarSearchNotifyVehicleItem Vehicle { get; set; }
}

public class OpenSupplierCarSearchNotifyVehicleItem
{
    /// <summary>
    /// 最大乘客数
    /// </summary>
    public int MaxPassengerCount { get; set; }

    /// <summary>
    /// 最大行李数
    /// </summary>
    public int MaxLuggageCount { get; set; }
}

#endregion

#region Car ReservationNotify - 预订结果通知

public class OpenSupplierCarReservationNotifyBody : NotifyBodyBase
{
    /// <summary>
    /// 订单状态
    /// <list type="number">
    /// <item>pending-下单成功</item>
    /// <item>completed-下单成功</item>
    /// <item>failed-下单成功</item>
    /// <item>cancelled-下单成功</item>
    /// </list>
    /// </summary>
    public string OrderStatus { get; set; }

    /// <summary>
    /// 处理结果返回信息，预定失败时必传
    /// </summary>
    public string ResultMessage { get; set; }

    /// <summary>
    /// 订单确认号
    /// </summary>
    public string ConfirmationNumber { get; set; }
}

#endregion