using Contracts.Common.Product.DTOs.CarProduct;
using Contracts.Common.Product.DTOs.CarProductSku;

namespace Bff.Vebk.Models.CarProduct;

public class BffCarProductSkuDetailCalendarPricesOutput : CarProductSkuDetailDto
{
    /// <summary>
    /// 服务项目
    /// </summary>
    public new List<BffCarProductSkuServiceItemOutput> ServiceItems { get; set; } = new();

    public new List<BffCarProductSkuCalendarPricesOutput> CalendarPrices { get; set; } = new();
   

}

public class BffCarProductSkuCalendarPricesOutput
{
    /// <summary>
    /// 是否售卖
    /// </summary>
    public bool IsSale { get; set; } = false;

    #region CalendarPrice
    /// <summary>
    /// 日期
    /// </summary>
    public DateTime Date { get; set; }

    /// <summary>
    /// 可用库存
    /// </summary>
    public int AvailableQuantity { get; set; }

    /// <summary>
    /// B2B销售单价
    /// </summary>
    public decimal? ChannelPrice { get; set; }

    /// <summary>
    /// 采购单价
    /// </summary>
    public decimal? CostPrice { get; set; }

    #endregion

}

public class BffCarProductSkuServiceItemOutput : CarProductSkuServiceItemOutput
{
    /// <summary>
    /// 服务日历价
    /// </summary>
    public new List<BffCarProductCalendarPriceAndQuantityInfo> ServicePriceAndQuantityInfos { get; set; } = new();
}

public class BffCarProductCalendarPriceAndQuantityInfo
{
    public long Id { get; set; }
    /// <summary>
    /// 日期
    /// </summary>
    public DateTime Date { get; set; }

    /// <summary>
    /// 可用库存
    /// </summary>
    public int AvailableQuantity { get; set; }

    /// <summary>
    /// 售卖状态
    /// </summary>
    public bool Enabled { get; set; }

    /// <summary>
    /// 采购单价
    /// </summary>
    public decimal? CostPrice { get; set; }

    /// <summary>
    /// B2b销售单价
    /// </summary>
    public decimal? ChannelPrice { get; set; }
}
