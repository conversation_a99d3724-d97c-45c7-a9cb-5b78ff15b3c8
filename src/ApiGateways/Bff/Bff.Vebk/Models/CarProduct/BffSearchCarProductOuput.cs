using Contracts.Common.Order.Enums;
using Contracts.Common.Product.Enums;

namespace Bff.Vebk.Models.CarProduct;

public class BffSearchCarProductOuput
{
    public long Id { get; set; }

    /// <summary>
    /// 产品标题
    /// </summary>
    public string Title { get; set; }

    /// <summary>
    /// 英文标题
    /// </summary>
    public string EnTitle { get; set; }

    /// <summary>
    /// 产品类型
    /// </summary>
    public CarProductType CarProductType { get; set; }

    /// <summary>
    /// 国家
    /// </summary>
    public int CountryCode { get; set; }

    /// <summary>
    /// 国家
    /// </summary>
    public string CountryName { get; set; }

    /// <summary>
    /// 城市 接送机为机场所在城市
    /// </summary>
    public int CityCode { get; set; }

    public string CityName { get; set; }

    /// <summary>
    /// 机场id
    /// </summary>
    public long? AirportId { get; set; }

    public string? AirportName { get; set; }

    public DateTime CreateTime { get; set; }

    public DateTime? UpdateTime { get; set; }

    /// <summary>
    /// B2b售卖状态
    /// </summary>
    public B2bSellingStatusType B2bSellingStatusType { get; set; }

    /// <summary>
    /// 上架/下架
    /// </summary>
    public bool Enabled { get; set; }

    /// <summary>
    /// 最低价格
    /// </summary>
    public decimal? MinPrice { get; set; }

    /// <summary>
    /// 最高价格
    /// </summary>
    public decimal? MaxPrice { get; set; }

    /// <summary>
    /// 可用库存数
    /// </summary>
    public int AvailableInventory { get; set; }

    /// <summary>
    /// 销量
    /// </summary>
    public int Sales { get; set; }


    /// <summary>
    /// 采购价币种 (供应商币种)
    /// </summary>
    public string CostCurrencyCode { get; set; }

    /// <summary>
    /// 售价币种 (商户币种)
    /// </summary>
    public string SaleCurrencyCode { get; set; }

    /// <summary>
    /// 供应商
    /// </summary>
    public long SupplierId { get; set; }

    /// <summary>
    /// 供应商名称
    /// </summary>
    public string? SupplierName { get; set; }

    /// <summary>
    /// 采购来源类型
    /// </summary>
    public CarProductPurchaseSourceType PurchaseSourceType { get; set; }

    public string EnCityName { get; set; }

    public string EnCountryName { get; set; }

    public string? EnAirportName { get; set; }


    /// <summary>
    /// 产品人id，产品开发者
    /// </summary>
    public long? DevelopUserId { get; set; }

    /// <summary>
    /// 产品人名称
    /// </summary>
    public string DevelopUserName { get; set; }

    public List<ProductOperatorUserBffItem> ProductOperatorUser { get; set; } = new();
}

/// <summary>
/// 产品运营人信息
/// </summary>
public class ProductOperatorUserBffItem
{
    /// <summary>
    /// 售卖平台
    /// </summary>
    public SellingPlatform SellingPlatform { get; set; }

    /// <summary>
    /// 运营人id
    /// </summary>
    public long? OperatorUserId { get; set; }

    /// <summary>
    /// 运营人名称
    /// </summary>
    public string OperatorUserName { get; set; }

    /// <summary>
    /// 运营助理
    /// </summary>
    public long? OperatorAssistantUserId { get; set; }

    /// <summary>
    /// 运营助理名称
    /// </summary>
    public string OperatorAssistantUserName { get; set; }
}