using Contracts.Common.Tenant.DTOs.AgencyCredit;
using Contracts.Common.Tenant.Enums;

namespace Bff.Vebk.Models.AgencyCredit;

public class RecordStatementOutput
{
    public long AgencyCreditId { get; set; }
    public long AgencyId { get; set; }

    /// <summary>
    /// 分销商名称
    /// </summary>
    public string FullName { get; set; }

    /// <summary>
    /// 所属部门
    /// </summary>
    public string? DepartmentName { get; set; }

    /// <summary>
    /// 结算维度
    /// </summary>
    public SettlementDimensionType DimensionType { get; set; }

    /// <summary>
    /// 结算周期
    /// </summary>
    public SupplierSettlementPeriod SettlementPeriod { get; set; }

    public long? SalespersonId { get; set; }
    /// <summary>
    /// 销售BD
    /// </summary>
    public string? SalespersonName { get; set; }

    /// <summary>
    /// 价格分组名称
    /// </summary>
    public string PriceGroupName { get; set; }

    /// <summary>
    /// 会员等级名称
    /// </summary>
    public string? LevelName { get; set; }

    /// <summary>
    /// 授信额度
    /// </summary>
    public decimal CreditLine { get; set; }
    /// <summary>
    /// 币种
    /// </summary>
    public string CurrencyCode { get; set; }
    /// <summary>
    /// 0~15日内待结算
    /// </summary>
    public decimal AChangeAmount { get; set; }
    /// <summary>
    /// 16~30日内待结算
    /// </summary>
    public decimal BChangeAmount { get; set; }
    /// <summary>
    /// 31~60日待结算
    /// </summary>
    public decimal CChangeAmount { get; set; }
    /// <summary>
    /// 61日以上待结算
    /// </summary>
    public decimal DChangeAmount { get; set; }

    /// <summary>
    /// 合计
    /// </summary>
    public decimal TotalAmount => AChangeAmount + BChangeAmount + CChangeAmount + DChangeAmount;

    /// <summary>
    /// 已对账未收款金额
    /// </summary>
    public decimal ReconciledUnpaidAmount { get; set; }
}
