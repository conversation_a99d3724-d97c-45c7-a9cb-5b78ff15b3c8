using Contracts.Common.Tenant.Enums;

namespace Bff.Vebk.Models.AgencyCredit;

public class UpdateInput
{
    /// <summary>
    /// 额度id
    /// </summary>
    public long Id { get; set; }

    /// <summary>
    /// 分销商id
    /// </summary>
    public long AgencyId { get; set; }

    /// <summary>
    /// 授信额度
    /// </summary>
    public decimal CreditLine { get; set; }

    /// <summary>
    /// 预警额度率 值0.1 表示 10%比例
    /// </summary>
    public decimal EarlyWarningRate { get; set; }

    /// <summary>
    /// 额度状态
    /// </summary>
    public bool Status { get; set; }

    /// <summary>
    /// 结算周期
    /// </summary>
    public SupplierSettlementPeriod SettlementPeriod { get; set; }

    /// <summary>
    /// 结算维度类型 默认预订时间
    /// </summary>
    public SettlementDimensionType DimensionType { get; set; }
}

public class DelayedCreditUpdateInput
{

    /// <summary>
    /// 分销商id
    /// </summary>
    public long AgencyId { get; set; }

    /// <summary>
    /// 延时支付额度
    /// </summary>
    public decimal DelayedCreditLine { get; set; }

    /// <summary>
    /// 延时支付开关状态
    /// </summary>
    public bool DelayedStatus { get; set; }

}