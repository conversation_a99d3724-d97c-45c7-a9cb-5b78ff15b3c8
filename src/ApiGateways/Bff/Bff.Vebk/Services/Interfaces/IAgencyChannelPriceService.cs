using Contracts.Common.Payment.DTOs.Currency;
using Contracts.Common.Product.DTOs.AgencyChannelPriceSettings;

namespace Bff.Vebk.Services.Interfaces;

public interface IAgencyChannelPriceService
{
    /// <summary>
    /// B2B价格汇率换算
    /// </summary>
    /// <param name="exchangeRateList">汇率列表</param>
    /// <param name="setting">价格分组配置</param>
    /// <param name="salePrice">售价</param>
    /// <param name="costPrice">采购价</param>
    /// <param name="costCurrencyCode">采购价币种</param>
    /// <param name="saleCurrencyCode">售价币种</param>
    /// <param name="agencyCurrencyCode">B2B币种</param>
    /// <returns></returns>
    decimal? ConvertB2BPrice(List<GetExchangeRateOutput> exchangeRateList, AgencyChannelPriceSettingOutput setting,
        decimal? salePrice, decimal? costPrice,
        string costCurrencyCode, string saleCurrencyCode, string agencyCurrencyCode);

    /// <summary>
    /// 查询价格分组配置信息
    /// - 过滤掉产品侧删除的sku
    /// </summary>
    Task<List<AgencyChannelPriceSettingOutput>> QueryPricesSettings(QueryChannelPriceInput input);
}
