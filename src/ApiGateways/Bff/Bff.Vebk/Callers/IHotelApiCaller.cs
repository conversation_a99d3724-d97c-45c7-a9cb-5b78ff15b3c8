using Common.Caller;
using Contracts.Common.Hotel.DTOs.ApiHotel;
using Contracts.Common.Hotel.DTOs.HopHotel;
using Contracts.Common.Hotel.DTOs.Hotel;
using Contracts.Common.Hotel.DTOs.HotelCombination;
using Contracts.Common.Hotel.DTOs.HotelMapping;
using Contracts.Common.Hotel.DTOs.HotelOperationConfig;
using Contracts.Common.Hotel.DTOs.HotelTag;
using Contracts.Common.Hotel.DTOs.PriceStrategy;
using Contracts.Common.Hotel.DTOs.PriceStrategyCalendarPrice;
using Contracts.Common.Hotel.DTOs.SpecializedHotel;
using Contracts.Common.Hotel.DTOs.SpecializedHotelDetail;
using Contracts.Common.Hotel.DTOs.Tag;
using EfCoreExtensions.Abstract;
using HotelCombination = Contracts.Common.Hotel.DTOs.HotelCombination;

namespace Bff.Vebk.Callers;

public interface IHotelApiCaller : IHttpCallerBase
{
    #region hotel

    /// <summary>
    /// 查询酒店列表
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<PagingModel<SearchHotelsOutput>> HotelSearch(SearchHotelsInput input);

    /// <summary>
    /// ES酒店详情
    /// </summary>
    /// <param name="ids"></param>
    /// <returns></returns>
    Task<List<SearchEsHotelOutput>> GetEsHotelDetail(params long[] ids);
    
    /// <summary>
    /// ES酒店详情
    /// </summary>
    /// <param name="mixHotelIds"></param>
    /// <returns></returns>
    Task<List<SearchEsHotelOutput>> GetEsHotelDetailV2(params long[] mixHotelIds);

    /// <summary>
    /// 查询本地酒店详情
    /// </summary>
    Task<GetHotelDetailsOutput> GetHotelDetail(long hotelId);

    /// <summary>
    /// 根据酒店id效验酒店是否上架
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<List<CheckIsEnabledOutput>> CheckHotelIsEnabled(CheckIsEnabledInput input);

    /// <summary>
    /// 获取本地酒店的房型信息
    /// </summary>
    Task<GetHotelRoomsOutput> GetHotelRooms(GetHotelRoomsInput input);

    /// <summary>
    /// 批量获取酒店首图
    /// </summary>
    /// <param name="ids"></param>
    /// <returns></returns>
    Task<IEnumerable<GetHotelFirstPhotoOutput>> GetHotelFirstPhoto(long[] ids);

    /// <summary>
    /// 根据资源酒店id获取酒店ID
    /// </summary>
    /// <param name="ids"></param>
    /// <returns></returns>
    Task<IEnumerable<GetHotelIdsByResourceHotelIdsOutput>> GetHotelIdsByResourceHotelIds(long[] ids);

    /// <summary>
    /// 获取酒店城市
    /// </summary>
    /// <returns></returns>
    Task<List<Contracts.Common.Hotel.DTOs.Hotel.GetCitiesOutput>> GetHotelCities(bool isCache, bool needAvailable);

    /// <summary>
    /// 添加酒店
    /// </summary>
    Task AddHotel(AddHotelInput input);

    /// <summary>
    /// 修改酒店详情
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task UpdateHotel(UpdateHotelDetailsInput input);

    #endregion

    #region ApiHotel

    /// <summary>
    /// 汇智/第三方酒店设置是否置顶
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task ApiHotelSetOnTop(SetOnTopInput input);

    /// <summary>
    /// 获取第三方酒店配置
    /// </summary>
    /// <returns></returns>
    Task<IEnumerable<ApiHotelSettingDto>> GetApiHotelSettings();

    /// <summary>
    /// api酒店详情
    /// </summary>
    /// <param name="ids"></param>
    /// <returns></returns>
    Task<List<GetApiHotelDetailOutput>> GetApiHotelDetail(params long[] ids);

    Task<List<GetApiHotelDetailOutput>> GetApiHotelDetails(GetApiHotelDetailsInput input);

    /// <summary>
    /// 查询第三方酒店
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [Obsolete]
    Task<PagingModel<Contracts.Common.Hotel.DTOs.ApiHotel.SearchOutput>> ApiHotelSearch(Contracts.Common.Hotel.DTOs.ApiHotel.SearchInput input);
    
    /// <summary>
    /// 查询第三方酒店 - v2
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<PagingModel<Contracts.Common.Hotel.DTOs.ApiHotel.SearchOutput>> ApiHotelSearchV2(Contracts.Common.Hotel.DTOs.ApiHotel.SearchInput input);

    /// <summary>
    /// 根据资源酒店Id检查酒店是否已经添加
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<List<Contracts.Common.Hotel.DTOs.ApiHotel.CheckRefOutput>> CheckRefByResourceHotelIds(Contracts.Common.Hotel.DTOs.ApiHotel.CheckRefInput input);

    #endregion

    #region HotelMapping

    /// <summary>
    /// 查询酒店售卖匹配信息
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<PagingModel<MappingInfoOutput>> HotelMappingSearch(SearchHotelMappingsInput input);

    /// <summary>
    /// 酒店匹配详细信息
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<HotelMappingDetailOutput> HotelMappingDetail(HotelMappingDetailInput input);

    /// <summary>
    /// 查询酒店匹配信息
    /// </summary>
    /// <param name=""></param>
    /// <returns></returns>
    Task<IEnumerable<HotelMappingOutput>> GetHotelMappings(HotelMappingInput input);

    /// <summary>
    /// 添加酒店匹配
    /// </summary>
    /// <param name="input"></param>
    /// <exception cref="ErrorTypes.Hotel.HotelMappingHasSuccessed"></exception>
    /// <exception cref="ErrorTypes.Hotel.HotelMappingIsProcessing"></exception>
    /// <returns></returns>
    Task AddHotelMapping(AddHotelMappingInput input);

    /// <summary>
    /// 酒店房型匹配
    /// </summary>
    /// <param name="input"></param>
    Task SetHotelRoomMapping(SetHotelRoomMappingInput input);

    /// <summary>
    /// 酒店价格策略匹配
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task SetPriceStrategyMapping(SetPriceStrategyMappingInput input);


    /// <summary>
    /// 更新酒店匹配情况
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task HotelMappingPushQueryHandle(HotelMappingPushQueryInput input);


    #endregion

    #region PirceStrategy

    /// <summary>
    /// 价格分组查询价格策略,酒店.房型信息
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<PagingModel<GetHotelAndPriceStrategyOutput>> GetHotelAndPriceStrategy(GetHotelAndPriceStrategyInput input);

    /// <summary>
    /// 获取指定酒店所有价格策略信息
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<List<GetSimpleInfoByHotelIdsOutput>> GetSimpleInfoByHotelIds(GetSimpleInfoByHotelIdsInput input);


    /// <summary>
    /// 通过价格策略Id列表查询价格策略
    /// </summary>
    /// <returns></returns>
    Task<List<GetPriceStrategyOutput>> GetPriceStrategyByIds(List<long> priceStrategyIds);


    /// <summary>
    /// 分销商酒店详情策略
    /// </summary>
    Task<List<AgencyGetOutput>> GetAgencyStrategies(AgencyGetInput input);

    /// <summary>
    /// 验证价格
    /// </summary>
    /// <param name="request"></param>
    /// <exception cref="ErrorTypes.Inventory.GetInventoryFail"></exception>
    /// <returns></returns>
    Task<CheckSaleOutput> PreOrderCheckSale(CheckSaleInput request);

    #endregion

    #region Tag
    /// <summary>
    /// 查找标签
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<List<SearchTagOutput>> SearchTag(SearchTagInput input);

    /// <summary>
    /// 添加标签
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task AddTag(AddTagInput input);

    /// <summary>
    /// 编辑标签
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task EditTag(EditTagInput input);

    /// <summary>
    /// 删除标签
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    Task DeleteTag(long id);

    /// <summary>
    /// 获取当前租户下所有标签
    /// </summary>
    /// <returns></returns>
    Task<IEnumerable<GetTagsOutput>> GetAll();
    #endregion

    #region HotelTag
    /// <summary>
    /// 酒店添加标签
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task AddHotelTag(SetHotelTagsInput input);

    /// <summary>
    /// 酒店删除标签
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task DeleteHotelTag(SetHotelTagsInput input);
    /// <summary>
    /// 根据汇智酒店id获取标签
    /// </summary>
    /// <param name="apiHotelIds"></param>
    /// <returns></returns>
    Task<IEnumerable<GetHotelTagsOutput>> GetByApiHotelIds(List<long> apiHotelIds);

    /// <summary>
    /// 根据汇智酒店id获取标签V2
    /// </summary>
    /// <param name="apiHotelIds"></param>
    /// <returns></returns>
    Task<IEnumerable<GetHotelTagsOutput>> GetByApiHotelIdsV2(GetHotelTagsInput input);

    /// <summary>
    /// 更新标签排序
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task TagUpdateSort(UpdateSortInput input);
    #endregion

    #region HotelCombination 酒店打包
    /// <summary>
    /// 创建
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<HotelCombination.CreateOutput> CreateHotelCombination(HotelCombination.CreateInput input);

    /// <summary>
    /// 获取详情
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    Task<HotelCombination.DetailOutput> GetHotelCombination(long id);

    /// <summary>
    /// 分页查询
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<PagingModel<HotelCombination.SearchOutput>> SearchHotelCombination(HotelCombination.SearchInput input);

    /// <summary>
    /// 删除
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    Task DeleteHotelCombination(long id);

    /// <summary>
    /// 删除Sku配置
    /// </summary>
    /// <param name="settingId"></param>
    /// <returns></returns>
    Task DeleteHotelCombinationSkus(List<long> skuIds);

    /// <summary>
    /// 更新
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task UpdateHotelCombination(HotelCombination.UpdateInput input);

    /// <summary>
    /// 获取酒店打包产品渠道加价信息
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<HotelCombination.GetChannelMarkupOutput> GetHotelCombinationChannelMarkup(HotelCombination.GetChannelMarkupInput input);

    /// <summary>
    /// 保存酒店打包产品渠道加价信息
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task HotelCombinationChannelMarkupSave(HotelCombination.ChannelMarkupSaveInput input);

    /// <summary>
    /// 查询酒店打包产品渠道每日售价信息
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<HotelCombination.ChannelCalendarPriceSearchOutput> HotelCombinationChannelCalendarPriceSearch(HotelCombination.ChannelCalendarPriceSearchInput input);

    /// <summary>
    /// 获取已使用的城市列表
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<PagingModel<HotelCombination.GetCitiesOutput>> HotelCombinationGetCities(GetCitiesInput input);
    #endregion

    #region HopHotel

    /// <summary>
    /// HOP推送变更报价
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<HopHotelPushNightlyPriceResponse> HopHotelPushNightlyPrices(HopHotelPushNightlyPriceRequest input);

    #endregion


    #region SpecializedHotel
    Task<long> AddSpecializedHotel(AddSpecializedHotelInput input);

    Task UpdateSpecializedHotel(UpdateSpecializedHotelInput input);

    Task<PagingModel<SearchSpecializedHotelOutput>> SearchSpecializedHotel(SearchSpecializedHotelInput input);

    Task DeleteSpecializedHotel(List<long> ids);

    Task<bool> ImportSpecializedHotel(ImportHotelInput input);

    Task<PagingModel<SearchImportLogOutput>> SearchSpecializedHotelImportLog(SearchImportLogInput input);

    /// <summary>
    /// 获取专用酒店的城市数据
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    Task<List<GetSpecializedHotelCitySummaryOutput>> GetSpecializedHotelCitySummary(long id);
    #endregion

    #region SpecializedHotelDetail
    Task<List<SpecializedHotelDetailOutput>> GetBySpecializedHotelId(long specializedHotelId);

    Task<List<SpecializedHotelDetailOutput>> GetSpecializedHotelItems(SpecializedHotelItemInput input);
    #endregion

    #region HotelOperationConfig

    /// <summary>
    /// 获取酒店运营配置
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<List<HotelOperationConfigOutput>> GetHotelOperationConfigs(GetHotelOperationConfigsInput input);

    /// <summary>
    /// 设置酒店运营配置
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task HotelOperationConfig(HotelOperationConfigInput input);

    #endregion
}
