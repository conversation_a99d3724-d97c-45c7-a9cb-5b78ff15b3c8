using Bff.Vebk.Models.AgencyACL;
using Common.Caller;
using Common.ServicesHttpClient;
using Contracts.Common.Permission.DTOs.ACL;
using Contracts.Common.Permission.DTOs.Permissions;
using Contracts.Common.Permission.DTOs.Role;
using Microsoft.Extensions.Options;

namespace Bff.Vebk.Callers.HttpImplements;

public class PermissionCaller : HttpCallerBase, IPermissionCaller
{
    public PermissionCaller(IOptions<ServicesAddress> options, IHttpClientFactory httpClientFactory)
        : base(options.Value.Permission, httpClientFactory)
    {
    }

    public async Task<IEnumerable<GetUserRoleNamesOutput>> GetTenantUserRoleNames(GetUserRoleNamesInput input)
    {
        string relativePath = "/Role/GetTenantUserRoleNames";
        return await PostAsync<GetUserRoleNamesInput, IEnumerable<GetUserRoleNamesOutput>>(relativePath, input);
    }

    #region ACL

    public async Task<IEnumerable<AgencyAclDTO>> GetAllACL()
    {
        string relativePath = "/AgencyAcl/GetAll";
        return await GetAsync<IEnumerable<AgencyAclDTO>>(relativePath);
    }

    public async Task<IEnumerable<string>> GetAclsByUserId(long userId)
    {
        string relativePath = "/AgencyUserAcl/GetByUserId?userId=" + userId;
        return await GetAsync<IEnumerable<string>>(relativePath);
    }

    public async Task SetAgencyUserACL(SetUserAclDTO input)
    {
        string relativePath = "/AgencyUserAcl/Set";
        await PostAsync(relativePath, input);
    }

    public async Task<List<GetPermissionsTreeOutput>> GetTenantUserPermissionsTree()
    {
        string relativePath = "/permissions/gettenantuserpermissionstree";
        return await GetAsync<List<GetPermissionsTreeOutput>>(relativePath);
    }

    public async Task<List<GetPermissionsTreeOutput>> GetTenantPermissionsTree()
    {
        string relativePath = "/permissions/GetTenantPermissionsTree";
        return await GetAsync<List<GetPermissionsTreeOutput>>(relativePath);
    }

    #endregion
}
