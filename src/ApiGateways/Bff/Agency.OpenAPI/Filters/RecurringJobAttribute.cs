namespace Agency.OpenAPI.Filters
{
    /// <summary>
    /// 定时任务
    /// <para>通过此属性标注哪些方法需要被加入定时任务队列</para>
    /// </summary>
    [AttributeUsage(AttributeTargets.Class | AttributeTargets.Method | AttributeTargets.Property)]
    public class RecurringJobAttribute : Attribute
    {
        /// <summary>
        /// 
        /// </summary>
        /// <param name="recurringJobId">任务Id，{服务名}.{简短标题}</param>
        /// <param name="cronExpression">Cron表达式 6位</param>
        public RecurringJobAttribute(string recurringJobId, string cronExpression)
        {
            RecurringJobId = recurringJobId;
            CronExpression = cronExpression;
        }

        public string RecurringJobId { get; set; }

        public string CronExpression { get; set; }
    }
}
