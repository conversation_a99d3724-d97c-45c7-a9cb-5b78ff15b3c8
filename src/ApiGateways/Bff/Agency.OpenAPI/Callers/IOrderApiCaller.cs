using Agency.OpenAPI.Areas.v1.Models.Hotel;
using Agency.OpenAPI.Areas.v1.Models.RequestModel;
using Common.Caller;
using Contracts.Common.Order.DTOs.BaseOrder;
using Contracts.Common.Order.DTOs.HotelOrder;

namespace Agency.OpenAPI.Callers;

public interface IOrderApiCaller : IHttpCallerBase
{
    #region BaseOrder
    /// <summary>
    /// 检查渠道单号是否已存在
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<bool> CheckChannelOrderNoExist(CheckChannelOrderNoExistInput input);
    #endregion

    #region HotelOrder
    /// <summary>
    /// 酒店日历房下单
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<CreateHotelOrderOutput> HotelOrderCreate(CreateHotelOrderInput input);

    /// <summary>
    /// 取消订单
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<RefundHotelOrderOutput> HotelOrderCancelByAgency(CancelByAgencyInput input);

    /// <summary>
    /// 查询酒店订单
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<QueryHotelOrderOutput> HotelOrderDetailByAgency(DetailByAgencyInput input);
    #endregion
}