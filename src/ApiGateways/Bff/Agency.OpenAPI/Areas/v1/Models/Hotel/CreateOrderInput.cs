using Agency.OpenAPI.Enums;

namespace Agency.OpenAPI.Areas.v1.Models.Hotel
{
    public class CreateOrderInput
    {
        /// <summary>
        /// 售卖平台
        /// </summary>
        public SellingPlatform OtaChannel { get; set; }
        /// <summary>
        /// 分销商订单ID
        /// </summary>
        public string ChannelOrderId { get; set; }
        /// <summary>
        /// 酒店Id
        /// </summary>
        public long HotelId { get; set; }
        /// <summary>
        /// 房型Id
        /// </summary>
        public long RoomId { get; set; }
        /// <summary>
        /// 价格策略Id
        /// </summary>
        public long PriceStrategyId { get; set; }
        /// <summary>
        /// 入住时间
        /// </summary>
        public DateTime CheckIn { get; set; }
        /// <summary>
        /// 离店时间
        /// </summary>
        public DateTime CheckOut { get; set; }
        /// <summary>
        /// 房间数
        /// </summary>
        public int RoomCount { get; set; }
        /// <summary>
        /// 入住人数
        /// </summary>
        public int Adults { get; set; }
        /// <summary>
        /// 付款金额
        /// </summary>
        public decimal Amount { get; set; }
        /// <summary>
        /// 入住人信息
        /// </summary>
        public List<string> Customers { get; set; }

        /// <summary>
        /// 联系人
        /// </summary>
        public string ContactsName { get; set; }
        /// <summary>
        /// 联系人手机号
        /// </summary>
        public string ContactsPhoneNumber { get; set; }


        /// <summary>
        /// 联系人邮箱
        /// </summary>
        public string? ContactsEmail { get; set; }
        /// <summary>
        /// 备注
        /// </summary>
        public string? Message { get; set; }
    }
}
