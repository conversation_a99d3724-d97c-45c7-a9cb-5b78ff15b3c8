using Agency.OpenAPI.Areas.v1.Models.Hotel;
using Agency.OpenAPI.Areas.v1.Models.RequestModel;
using Agency.OpenAPI.Callers;
using Agency.OpenAPI.ConfigModel;
using Common.ServicesHttpClient;
using Common.Swagger;
using EfCoreExtensions.Abstract;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using System.Text.RegularExpressions;
using static System.Net.HttpStatusCode;

namespace Agency.OpenAPI.Areas.v1.Controllers;

[Route("[area]/[controller]")]
[ApiExplorerSettings(GroupName = nameof(ApiVersion.v1))]
[ApiController]
[Area(nameof(ApiVersion.v1))]
public class HotelController : Controller
{
    private readonly IHttpClientFactory _httpClientFactory;
    private readonly ServicesAddress _servicesAddress;
    private readonly AliYunSetting _aliYunSetting;
    private readonly IHotelApiCaller _hotelApiCaller;
    private readonly IResourceApiCaller _resourceApiCaller;

    public HotelController(
        IHttpClientFactory httpClientFactory,
        IOptions<ServicesAddress> options,
        IOptions<AliYunSetting> aliYunOptions,
        IResourceApiCaller resourceApiCaller,
        IHotelApiCaller hotelApiCaller)
    {
        _httpClientFactory = httpClientFactory;
        _servicesAddress = options.Value;
        _aliYunSetting = aliYunOptions.Value;
        _resourceApiCaller = resourceApiCaller;
        _hotelApiCaller = hotelApiCaller;
    }

    /// <summary>
    /// 酒店信息分页查询获取酒店Id列表
    /// </summary>
    [HttpPost]
    [Route("ids/search")]
    [ProducesResponseType(typeof(ResultModel<PagingModel<long>>), (int)OK)]
    public async Task<IActionResult> GetIds(GetHotelIdsInput input)
    {
        var data = await _hotelApiCaller.GetByCityCodeAsync(input);
        var result = new GetHotelIdsOutput { 
            PageIndex = data.PageIndex,
            PageSize = data.PageSize,
            HotelIds = data.Data,
            Total = data.Total,
        };
        return Ok(result);
    }

    /// <summary>
    /// 根据酒店id批量查询酒店信息
    /// </summary>
    [HttpPost]
    [Route("details")]
    [ProducesResponseType(typeof(ResultModel<List<GetHotelDetailByIdsOutput>>), (int)OK)]
    public async Task<IActionResult> GetDetail(GetHotelDetailByIdsInput input)
    {
        if (input.HotelIds.Any() is false) return Ok(new List<GetHotelDetailByIdsOutput>());

        input.Enabled = true;
        input.IsSearchAllPhotos = true;
        //获取酒店基础信息
        var hotelDetails = await _hotelApiCaller.GetByIdsAsync(input);

        //查询设施资源列表
        var facilities = await _resourceApiCaller.GetFacilitiesAsync();

        //查询该酒店已有的设施列表
        var hotelFacilities = await _hotelApiCaller.GetFacilitiesByIdsAsync(input.HotelIds);

        var cities = await _resourceApiCaller.QueryCity(new Contracts.Common.Resource.DTOs.QueryInput { 
            CityCodes = hotelDetails.Select(x=> Convert.ToInt32(x.CityCode)).Distinct().ToArray()
        });

        var hotelFacilitiesIds = hotelFacilities.Select(x => x.FacilityId);

        var regx = new Regex(@"[a-zA-z]+://[\S]*");

        foreach (var item in hotelDetails)
        {
            var city = cities.FirstOrDefault(x => x.CityCode == item.CityCode);
            if (city != null)
            {
                item.CountryENName = city.CountryEnName;
                item.CityENName = city.ENName;
                item.ProvinceENName = city.ProvinceEnName;
            }
            item.FacilitiesInfos = facilities.Where(x => hotelFacilitiesIds.Contains(x.Id))
                .Select(x => new FacilitiesListInfo
                {
                    Id = x.Id,
                    Name = x.Name,
                    ENName = x.ENName,
                    FacilityType = x.Type
                }).ToList();

            if (string.IsNullOrEmpty(item.HotelPicture) is false && regx.IsMatch(item.HotelPicture) is false)
            {
                item.HotelPicture = $"https://{_aliYunSetting.Oss.DomainName}/{item.HotelPicture}";
                var newPhotos = new List<string>();
                foreach (var photo in item.HotelPictures)
                {
                    var newPhoto = photo;
                    if (string.IsNullOrEmpty(photo) is false && regx.IsMatch(photo) is false)
                        newPhoto = $"https://{_aliYunSetting.Oss.DomainName}/{photo}";
                    newPhotos.Add(newPhoto);
                }
                item.HotelPictures = newPhotos;
            }
        }

        return Ok(hotelDetails);
    }

}