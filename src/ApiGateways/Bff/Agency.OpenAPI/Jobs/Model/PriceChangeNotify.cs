namespace Agency.OpenAPI.Jobs.Model
{
    public class PriceChangeNotify
    {
        public long HotelId { get; set; }

        public IEnumerable<StrategyPriceChange> Strategies { get; set; } = Enumerable.Empty<StrategyPriceChange>();
    }

    public record StrategyPriceChange
    {
        public long StrategyId { get; set; }

        /// <summary>
        /// 时间段-开始日期
        /// </summary>
        public DateTime? BeginDate { get; set; }

        /// <summary>
        /// 时间段-结束日期
        /// </summary>
        public DateTime? EndDate { get; set; }
    }
}
