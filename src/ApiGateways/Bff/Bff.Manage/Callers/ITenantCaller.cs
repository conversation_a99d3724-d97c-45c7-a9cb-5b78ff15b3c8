using Common.Caller;
using Contracts.Common.Payment.Enums;
using Contracts.Common.Tenant.DTOs.Agency;
using Contracts.Common.Tenant.DTOs.AgencyPriceGroup;
using Contracts.Common.Tenant.DTOs.Supplier;
using Contracts.Common.Tenant.DTOs.SupplierApi;
using Contracts.Common.Tenant.DTOs.Tenant;
using Contracts.Common.Tenant.DTOs.TenantAccounts;
using Contracts.Common.Tenant.DTOs.TenantApiSupplierConfig;
using Contracts.Common.Tenant.Enums;
using EfCoreExtensions.Abstract;

namespace Bff.Manage.Callers;

public interface ITenantCaller : IHttpCallerBase
{
    #region Tenant

    /// <summary>
    /// 停用/启用 商户
    /// </summary>
    Task SetEnabled(TenantSetEnabledInput input);

    /// <summary>
    /// 根据商户id获取配置信息
    /// </summary>
    /// <param name="tenantIds"></param>
    /// <returns></returns>
    Task<List<GetTenantSysConfigOutput>> GetSysConfigByTenantIds(List<long> tenantIds);


    /// <summary>
    /// 配置商户支付号类型
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task SetYeePaymentMerchantNoType((long tenantId, YeePaymentMerchantNoType merchantNoType) input);

    /// <summary>
    /// 查询商户运营列表
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<PagingModel<SearchTenantsOutput>> OperatorsSearch(SearchTenantsInput input);

    /// <summary>
    /// 保存该商户关联账号
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task TenantAccountSave(SaveTenantAccountInput input);

    /// <summary>
    /// 设置该商户账号关联状态
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task TenantAccountSetEnabled(SetTenantAccountEnabledInput input);

    /// <summary>
    /// 获取该商户账号基础信息
    /// </summary>
    /// <param name="tenantId"></param>
    /// <returns></returns>
    Task<TenantAccountDto> GetTenantAccountInfo(long tenantId);

    #endregion

    #region Agency

    /// <summary>
    /// 获取分销商详情
    /// </summary>
    /// <param name="agencyId"></param>
    /// <returns></returns>
    Task<GetAgenciesByIdsOutput> GetAgencyDetail(long agencyId);

    /// <summary>
    /// 根据id获取分销商
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<List<GetAgenciesByIdsOutput>> GetAgencyByIds(GetAgenciesByIdsInput input);
    #endregion

    #region AgencyPriceGroup

    /// <summary>
    /// 获取价格分组详情
    /// </summary>
    /// <param name="id">分销商id</param>
    /// <returns></returns>
    Task<PriceGroupDetailOutput> GetPriceGroupDetail(long id);

    #endregion

    #region Supplier

    /// <summary>
    /// 获取供应商
    /// </summary>
    /// <param name="supplierType"></param>
    /// <returns></returns>
    Task<IEnumerable<GetSuppliersOutput>> GetSuppliers(SupplierType supplierType);

    #endregion

    #region DingtalkApi
    /// <summary>
    /// 用户详情
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<List<Contracts.Common.Tenant.DTOs.DingtalkApi.GetUserOutput>> GetDingtalkApiUser(Contracts.Common.Tenant.DTOs.DingtalkApi.GetUserInput input);
    #endregion

    #region SupplierApi
    Task<List<SupplierApiListOutput>> SupplierApiList();
    #endregion

    #region TenantApiSupplierConfig

    Task SetTenantApiSupplierConfig(SetTenantApiSupplierConfigInput input);
    Task<List<QueryTenantApiSupplierConfigListOutput>> TenantApiSupplierConfigList(QueryTenantApiSupplierConfigListInput input);

    Task<GetTenantApiSupplierConfigDetailOutput> TenantApiSupplierConfigDetail(GetTenantApiSupplierConfigDetailInput input);

    Task DeleteTenantApiSupplierConfig(DeleteTenantApiSupplierConfigInput input);

    #endregion
}
