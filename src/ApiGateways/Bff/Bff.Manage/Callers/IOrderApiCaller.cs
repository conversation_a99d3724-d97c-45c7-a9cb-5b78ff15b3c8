using Common.Caller;
using Contracts.Common.Order.DTOs.BaseOrderRemark;
using Contracts.Common.Order.DTOs.GroupBooking;
using Contracts.Common.Order.DTOs.GroupBookingHotelTemplate;
using EfCoreExtensions.Abstract;

namespace Bff.Manage.Callers;

public interface IOrderApiCaller : IHttpCallerBase
{
    #region Order

    /// <summary>
    /// 根据单号获取所以订单备注
    /// </summary>
    /// <param name="baseOrderId"></param>
    /// <returns></returns>
    Task<IEnumerable<BaseOrderRemarkOutput>> GetAllBaseOrderRemarks(long baseOrderId);

    /// <summary>
    /// 添加单据内部备注
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task AddBaseOrderRemarks(AddBaseOrderRemarkInput input);

    #endregion

    #region GroupBooking

    /// <summary>
    /// 团房单新申请 分销商/商户 申请
    /// </summary>
    /// <returns></returns>
    Task<long> GroupBookingApplicationFormApply(ApplicationFormApplyInput input);

    Task<GroupBookingInquiryInformationDto> GroupBookingInquiryInformation(GroupBookingInquiryInformationInput input);

    Task<long> GroupBookingSaveInquiryInformation(SaveInquiryInformationInput input);

    Task<GroupBookingHotelInquiryRecordDto> GetGroupBookingHotelInquiryRecord(long groupBookingHotelInquiryId);

    /// <summary>
    /// 团房申请单列表搜索
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<PagingModel<ApplicationFormSearchOutput, ApplicationFormSearchSupplement>> GroupBookingApplicationFormSearch(ApplicationFormSearchInput input);

    /// <summary>
    /// 申请单信息
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<ApplicationFormOutput> GetApplicationForm(GetApplicationFormInput input);

    /// <summary>
    /// 申请单信息编辑 
    /// </summary>
    /// <returns></returns>
    Task GroupBookingApplicationFormModify(ApplicationFormModifyInput input);

    /// <summary>
    /// 申请单状态变更
    /// </summary>
    /// <returns></returns>
    Task GroupBookingApplicationFormStatusModify(ApplicationFormStatusModifyInput input);

    /// <summary>
    /// 团房申请单 指派
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task GroupBookingApplicationFormAssign(ApplicationFormAssignInput input);

    /// <summary>
    /// 申请单 保存合同
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task SaveContractContent(SaveContractContentInput input);

    /// <summary>
    /// 操作日志记录
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<IEnumerable<OperationLogOutput>> GetGroupBookingOperationLogs(GetOperationLogInput input);

    /// <summary>
    /// 商户确认申请 批量生成询价单
    /// </summary>
    /// <returns></returns>
    Task GroupBookingInquiry(InquiryInput input);

    /// <summary>
    /// 无法报价
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task GroupBookingUnableToQuote(UnableToQuoteInput input);

    /// <summary>
    /// SaaS询价单批量生成报价单 //商户已询价 客户待询价
    /// </summary>
    /// <returns></returns>
    Task GroupBookingQuotation(QuotationInput input);

    /// <summary>
    /// 查询关联询价报价单
    /// </summary>
    /// <param name="applicationFormId"></param>
    /// <returns></returns>
    Task<IEnumerable<QuotationOutput>> GetGroupBookingQuotations(long applicationFormId);

    /// <summary>
    /// 客户确认保存报价单 //确认报价
    /// </summary>
    /// <returns></returns>
    Task GroupBookingQuotationConfirm(QuotationConfirmInput input);

    /// <summary>
    /// SaaS生成待审预订单
    /// </summary>
    /// <returns></returns>
    Task GroupBookingPreOrder(PreOrderInput input);

    /// <summary>
    /// 查询申请单关联预订单
    /// </summary>
    /// <param name="applicationFormId"></param>
    /// <returns></returns>
    Task<List<PreOrderOutput>> GetGroupBookingPreOrders(long applicationFormId);

    /// <summary>
    /// 商户确认预订单 //预订单
    /// </summary>
    /// <returns></returns>
    Task GroupBookingPreOrderConfirm(PreOrderConfirmInput input);

    /// <summary>
    /// 获取所有城市信息
    /// </summary>
    /// <returns></returns>
    Task<CityOutput> GroupBookingGetCities();

    /// <summary>
    /// 查询团房目的地
    /// </summary>
    /// <param name="applicationFormId"></param>
    /// <returns></returns>
    Task<List<ApplicationFormDemandOutput>> GetGroupBookingApplicationDemands(long applicationFormId);

    /// <summary>
    /// 目的地需求酒店查询
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<List<ApplicationDemandHotelOutput>> GroupBookingDemandHotelSearch(ApplicationDemandHotelSearchInput input);

    /// <summary>
    /// 酒店发单询价查询
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<List<GroupBookingHotelInquiryOutput>> GroupBookingHotelInquirySearch(GroupBookingHotelInquirySearchInput input);

    /// <summary>
    /// 酒店发单邮件 模板内容渲染
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<GroupBookingHotelInquiryTemplateRenderOutput> GroupBookingHotelInquiryTemplateRender(GroupBookingHotelInquiryTemplateRenderInput input);

    /// <summary>
    /// 酒店发单询价
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task GroupBookingHotelInquiry(GroupBookingHotelInquiryInput input);

    /// <summary>
    /// 酒店报价
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task GroupBookingHotelQuote(GroupBookingHotelQuoteInput input);

    /// <summary>
    /// 酒店无法报价
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task GroupBookingHotelUnableToQuote(GroupBookingHotelUnableToQuoteInput input);

    /// <summary>
    /// 酒店报价搜索
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<List<GroupBookingHotelQuoteOutput>> GroupBookingHotelQuoteSearch(GroupBookingHotelQuoteSearchInput input);

    /// <summary>
    /// 保存酒店人工报价信息
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task SaveGroupHotelManualRecord(SaveGroupHotelManualRecordInput input);

    /// <summary>
    /// 获取酒店人工报价信息
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<List<GroupBookingHotelManualRecordDto>> GetGroupBookingHotelManualRecord(GetGroupBookingHotelManualRecordInput input);

    /// <summary>
    ///  获取询价发单记录模版渲染信息
    /// </summary>
    /// <param name="inquiryRecordId"></param>
    /// <returns></returns>
    Task<GroupBookingHotelInquiryRecordRenderDto> GetInquiryRecordRender(long inquiryRecordId);

    /// <summary>
    /// 更新酒店回复时间
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task UpdateGroupBookingHotelRecoveryTime(UpdateHotelRecoveryTimeInput input);

    #endregion

    #region GroupBookingHotelTemplate

    Task<PagingModel<GroupBookingHotelTemplateSearchOutput>> GroupBookingHotelTemplateSearch(GroupBookingHotelTemplateSearchInput input);

    Task<long> GroupBookingHotelTemplateAdd(GroupBookingHotelTemplateAddInput input);

    Task GroupBookingHotelTemplateUpdate(GroupBookingHotelTemplateUpdateInput input);

    Task GroupBookingHotelTemplateDelete(GroupBookingHotelTemplateDeleteInput input);

    Task<GroupBookingHotelTemplateDetailOutput> GroupBookingHotelTemplateDetail(GroupBookingHotelTemplateDetailInput input);

    #endregion
}
