using Common.ServicesHttpClient;
using Contracts.Common.WeChat.DTOs.WechatAuthorizer;
using Microsoft.Extensions.Options;
using Common.Caller;

namespace Bff.Manage.Callers.HttpImplements;

public class WechatAuthorizerCaller : HttpCallerBase, IWechatAuthorizerCaller
{
    public WechatAuthorizerCaller(IOptions<ServicesAddress> options, IHttpClientFactory httpClientFactory)
        : base(options.Value.WeChat, httpClientFactory)
    {
    }

    public async Task UnAuthorized(UnAuthorizedInput input)
    {
        string relativePath = "/WechatAuthorizer/UnAuthorized";
        await PostAsync(relativePath, input);
    }
}
