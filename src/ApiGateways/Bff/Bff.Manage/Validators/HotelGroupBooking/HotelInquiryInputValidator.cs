using Bff.Manage.Models.HotelGroupBooking;
using FluentValidation;

namespace Bff.Manage.Validators.HotelGroupBooking;

public class HotelInquiryInputValidator : AbstractValidator<HotelInquiryInput>
{
    public HotelInquiryInputValidator()
    {
        RuleFor(s => s.TenantId).GreaterThan(0);
        RuleFor(s => s.ApplicationFormId).GreaterThan(0);
        RuleFor(s => s.DemandHoteId).GreaterThan(0);
    }
}
