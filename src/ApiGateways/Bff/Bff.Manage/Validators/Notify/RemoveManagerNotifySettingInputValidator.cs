using Contracts.Common.Notify.DTOs.ManagerNotify;
using FluentValidation;

namespace Bff.Manage.Validators.Notify;

public class RemoveManagerNotifySettingInputValidator : AbstractValidator<RemoveManagerNotifySettingInput>
{
    public RemoveManagerNotifySettingInputValidator()
    {
        RuleFor(x => x.NotifyEventType)
           .NotEmpty()
           .IsInEnum()
           .When(x => x.NotifyEventSubType is null);

        RuleFor(x => x.NotifyEventSubType)
            .NotEmpty()
            .IsInEnum()
            .When(x => x.NotifyEventType is null);
    }
}