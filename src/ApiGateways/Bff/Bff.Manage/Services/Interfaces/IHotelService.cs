using Contracts.Common.Product.DTOs.AgencyChannelPriceSettings;
using Contracts.Common.Resource.Enums;

namespace Bff.Manage.Services.Interfaces;

public interface IHotelService
{
    /// <summary>
    /// 检查汇智酒店不同售卖渠道配置
    /// </summary>
    /// <param name="huiZhiPriceSettings">汇智酒店价格配置</param>
    /// <param name="hotelId">汇智酒店id</param>
    /// <param name="isStaffTag">是否主推散房标签</param>
    /// <param name="isReunionRoomTag">是否主推团房标签</param>
    /// <param name="tags">其他售卖标签</param>
    /// <returns></returns>
    public (List<HuiZhiHotelChannelPriceItem> thirdHotelChannelPriceSettings,
        List<HuiZhiHotelDefaultChannelPriceItem> defaultChannelHotelPriceSettings,
        List<AgencyChannelPriceSettingOutput> agencyChannelPriceSetting
        ) CheckHuiZhiSellChannelSettings(GetHuiZhiHotelChannelPriceOutput huiZhiPriceSettings,
            long hotelId,
            bool isStaffTag,
            bool isReunionRoomTag,
            List<SellHotelTag> tags);

    /// <summary>
    /// 价格配置判断
    /// </summary>
    /// <param name="priceGroupId"></param>
    /// <returns>
    /// localHotelIds : 本地酒店id列表
    /// huiZhiPriceSettings : 汇智酒店配置数据
    /// queryAgencyThirdHotel : 是否查询汇智酒店
    /// isReunionRoomTag : 是否只查主推团房
    /// </returns>
    Task<(List<(long hotelId, long strategyId)> localHotelIds, GetHuiZhiHotelChannelPriceOutput huiZhiPriceSettings,
        bool queryAgencyThirdHotel,
        bool defaultIsStaffTag,
        List<SellHotelTag> defaultTags,
        bool defaultIsReunionRoomTag)> CheckAgencyPriceSetting(long priceGroupId);


    /// <summary>
    /// 查询符合价格策略售卖类型的汇智酒店配置
    /// </summary>
    /// <param name="isStaffTag"></param>
    /// <param name="tag"></param>
    /// <param name="agencyChannelPriceSettings"></param>
    /// <returns></returns>
    public AgencyChannelPriceSettingOutput? GetStrategyChannelPriceSetting(bool isStaffTag, bool isReunionRoomTag, SellHotelTag? tag,
        List<AgencyChannelPriceSettingOutput> agencyChannelPriceSettings);
}
