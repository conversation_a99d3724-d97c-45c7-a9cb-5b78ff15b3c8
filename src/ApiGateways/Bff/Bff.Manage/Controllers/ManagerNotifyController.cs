using Bff.Manage.Callers;
using Bff.Manage.Models.ManagerNotify;
using Common.Swagger;
using Contracts.Common.Notify.DTOs.ManagerNotify;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace Bff.Manage.Controllers;

/// <summary>
/// 管理后台通知
/// </summary>
[Route("v1/[controller]/[action]")]
[ApiExplorerSettings(GroupName = nameof(ApiVersion.v1))]
[ApiController]
public class ManagerNotifyController : ControllerBase
{
    private readonly INotifyApiCaller _notifyApiCaller;

    public ManagerNotifyController(INotifyApiCaller notifyApiCaller)
    {
        _notifyApiCaller = notifyApiCaller;
    }

    [Authorize]
    [HttpGet]
    [SwaggerResponseExt((int)HttpStatusCode.OK, typeof(List<GetManagerNotifySwitchesOutput>))]
    public async Task<IActionResult> GetSwitches()
    {
        var result = await _notifyApiCaller.GetManagerNotifySwitches();
        return Ok(result);
    }

    [Authorize]
    [HttpPost]
    [SwaggerResponseExt((int)HttpStatusCode.OK)]
    public async Task<IActionResult> UpdateSwitch(UpdateManagerNotifySwitchInput input)
    {
        await _notifyApiCaller.UpdateManagerNotifySwitch(input);
        return Ok();
    }

    [Authorize]
    [HttpPost]
    [SwaggerResponseExt((int)HttpStatusCode.OK)]
    [SwaggerResponseExt(default, ErrorTypes.Notify.NotifySettingExisted)]
    public async Task<IActionResult> AddSetting(AddManagerNotifySettingInput input)
    {
        await _notifyApiCaller.AddManagerNotifySetting(input);
        return Ok();
    }

    [Authorize]
    [HttpPost]
    [SwaggerResponseExt((int)HttpStatusCode.OK, typeof(List<GetSettingsOutput>))]
    public async Task<IActionResult> GetSettings(GetSettingsInput input)
    {
        var result = await _notifyApiCaller.GetManagerNotifySettings(input);
        return Ok(result);
    }

    [Authorize]
    [HttpPost]
    [SwaggerResponseExt((int)HttpStatusCode.OK)]
    public async Task<IActionResult> UpdateSetting(UpdateManagerNotifySettingInput input)
    {
        await _notifyApiCaller.UpdateManagerNotifySetting(input);
        return Ok();
    }

    [Authorize]
    [HttpPost]
    [SwaggerResponseExt((int)HttpStatusCode.OK)]
    public async Task<IActionResult> RemoveSetting(RemoveManagerNotifySettingInput input)
    {
        await _notifyApiCaller.RemoveManagerNotifySetting(input);
        return Ok();
    }

    /// <summary>
    /// 更新业务类型钉钉模板
    /// </summary>
    [Authorize]
    [HttpPost]
    [ProducesResponseType((int)HttpStatusCode.OK)]
    public async Task<IActionResult> SaveEventSubTypeDingtalkRobot(SaveManagerNotifyTemplateDingtalkRobotSwitcheDto input)
    {
        await _notifyApiCaller.SaveEventSubTypeDingtalkRobot(input);
        return Ok();
    }

    /// <summary>
    /// 删除业务类型钉钉模板
    /// </summary>
    [Authorize]
    [HttpPost]
    [ProducesResponseType((int)HttpStatusCode.OK)]
    public async Task<IActionResult> DeleteEventSubTypeDingtalkRobot(RemoveManagerNotifyTemplateDingtalkRobotSwitcheDto input)
    {
        await _notifyApiCaller.DeleteEventSubTypeDingtalkRobot(input);
        return Ok();
    }

}
