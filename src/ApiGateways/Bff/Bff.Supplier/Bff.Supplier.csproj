<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net6.0</TargetFramework>
    <LangVersion>10.0</LangVersion>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <SatelliteResourceLanguages>zh</SatelliteResourceLanguages>
    <GenerateDocumentationFile>true</GenerateDocumentationFile>
    <NoWarn>$(NoWarn);1591,NU1803</NoWarn>
  </PropertyGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\..\BuildingBlocks\CommonSupport\Common\Common.csproj" />
    <ProjectReference Include="..\..\..\BuildingBlocks\CommonSupport\EfCoreExtensions\EfCoreExtensions.csproj" />
    <ProjectReference Include="..\..\..\BuildingBlocks\CommonSupport\Extensions\Extensions.csproj" />
    <ProjectReference Include="..\..\..\Contracts\Contracts.Common\Contracts.Common.csproj" />
  </ItemGroup>

</Project>
