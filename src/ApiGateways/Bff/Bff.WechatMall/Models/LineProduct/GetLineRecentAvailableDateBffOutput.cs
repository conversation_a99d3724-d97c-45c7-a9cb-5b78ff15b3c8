using Contracts.Common.Product.DTOs.LineProductSkuCalendarPrice;
using Contracts.Common.Product.Enums;

namespace Bff.WechatMall.Models.LineProduct;

public class GetLineRecentAvailableDateBffOutput
{
    /// <summary>
    /// 日期
    /// </summary>
    public DateTime Date { get; set; }

    /// <summary>
    /// 可用库存
    /// </summary>
    public int AvailableQuantity { get; set; }

    /// <summary>
    /// 总库存
    /// </summary>
    public int TotalQuantity { get; set; }

    /// <summary>
    /// 售卖状态
    /// </summary>
    public bool Enabled { get; set; }

    /// <summary>
    /// 日历价格
    /// </summary>
    public List<LineRecentAvailableDatePrice> SkuCalendarPriceInfos { get; set; } = new();
}

public class LineRecentAvailableDatePrice
{
    public long LineProductId { get; set; }

    public long LineProductSkuId { get; set; }

    /// <summary>
    /// 线路价格类型
    /// </summary>
    public LineSkuPriceType Type { get; set; }

    /// <summary>
    /// 日期
    /// </summary>
    public DateTime Date { get; set; }

    /// <summary>
    /// 价格
    /// </summary>
    public decimal? Price { get; set; }

    /// <summary>
    /// 渠道价
    /// </summary>
    public decimal? ChannelPrice { get; set; }
}