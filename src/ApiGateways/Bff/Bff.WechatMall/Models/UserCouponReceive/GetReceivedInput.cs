using Contracts.Common.Marketing.Enums;
using Swashbuckle.AspNetCore.Annotations;

namespace Bff.WechatMall.Models.UserCouponReceive;

public class GetReceivedInput : PagingInput
{
    /// <summary>
    /// 活动类型
    /// </summary>
    public CouponActivityType CouponActivityType { get; set; }

    /// <summary>
    /// 活动id 可选
    /// </summary>
    [SwaggerSchema(Nullable = true)]
    public long? CouponActivityId { get; set; }

    /// <summary>
    /// 消费关联订单id 可选
    /// </summary>
    [SwaggerSchema(Nullable = true)]
    public long? ReceiveBaseOrderId { get; set; }
}