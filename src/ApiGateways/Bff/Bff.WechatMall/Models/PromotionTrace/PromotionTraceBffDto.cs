using Contracts.Common.Marketing.Enums;
using Contracts.Common.Product.Enums;
using Contracts.Common.Tenant.DTOs.Page;
using Swashbuckle.AspNetCore.Annotations;

namespace Bff.WechatMall.Models.PromotionTrace;

public class PromotionTraceBffDto
{
    [SwaggerSchema(Nullable = true)]
    public string Title { get; set; }

    /// <summary>
    /// 追踪类型
    /// </summary>
    [SwaggerSchema(Nullable = false)]
    public PromotionTraceType TraceType { get; set; }

    /// <summary>
    /// 标的物Id, 产品Id、活动Id等等
    /// </summary>
    [SwaggerSchema(Nullable = true)]
    public long? TargetId { get; set; }

    /// <summary>
    /// 标的物标题, XX大酒店、产品名称等等
    /// </summary>
    [SwaggerSchema(Nullable = true)]
    public string? TargetTitle { get; set; }

    /// <summary>
    /// 推广投放位置Id
    /// </summary>
    [SwaggerSchema(Nullable = false)]
    public long? PromotionPositionId { get; set; }

    /// <summary>
    /// 推广投放位置Name
    /// </summary>
    [SwaggerSchema(Nullable = true)]
    public string? PromotionPositionName { get; set; }

    /// <summary>
    /// 商品类型
    /// </summary>
    [SwaggerSchema(Nullable = true)]
    public ProductType? ProductType { get; set; }

    /// <summary>
    /// 被分享人ID
    /// </summary>
    [SwaggerSchema(Nullable = true)]
    public long? UserId { get; set; }

    /// <summary>
    /// 其他页面链接
    /// </summary>
    [SwaggerSchema(Nullable = true)]
    public LinkChoose? Content { get; set; }

}
