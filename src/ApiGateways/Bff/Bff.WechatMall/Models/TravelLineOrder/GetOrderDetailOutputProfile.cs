using AutoMapper;
using Contracts.Common.Order.DTOs.OrderFieldInformation;
using Contracts.Common.Product.DTOs.InformationTemplateFields;
using Contracts.Common.Product.DTOs.ProductInformationTemplate;
using TravelLineOrderDto = Contracts.Common.Order.DTOs.TravelLineOrder;

namespace Bff.WechatMall.Models.TravelLineOrder;

public class GetOrderDetailOutputProfile : Profile
{
    public GetOrderDetailOutputProfile()
    {
        CreateMap<TravelLineOrderDto.BaseOrderDetailOutput, BaseOrderDetailOutput>();
        CreateMap<TravelLineOrderDto.TravelLineOrderDetailOutput, TravelLineOrderDetailOutput>();
        CreateMap<TravelLineOrderDto.OrderTravelerOutput, OrderTravelerOutput>();
        CreateMap<TravelLineOrderDto.TravelLineOrderPriceOutput, TravelLineOrderPriceOutput>();
        CreateMap<TravelLineOrderDto.OrderDetailOutput, GetOrderDetailOutput>();

        CreateMap<ProductInformationTemplateDetail, SaveOrderFieldInformationTypeDto>();

        CreateMap<InformationTemplateFieldsOutput, OrderFieldInformationDto>();
    }
}
