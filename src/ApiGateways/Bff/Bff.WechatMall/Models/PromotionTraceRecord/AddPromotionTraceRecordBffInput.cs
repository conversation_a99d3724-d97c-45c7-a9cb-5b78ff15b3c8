using Contracts.Common.Marketing.Enums;
using Swashbuckle.AspNetCore.Annotations;

namespace Bff.WechatMall.Models.PromotionTraceRecord;

public class AddPromotionTraceRecordBffInput
{
    /// <summary>
    /// 追踪Id
    /// </summary>
    [SwaggerSchema(Nullable = false)]
    public long PromotionTraceId { get; set; }

    /// <summary>
    /// C端用户Id
    /// </summary>
    [SwaggerSchema(Nullable = true)]
    public long? CustomerId { get; set; }

    /// <summary>
    /// 其他身份识别码(访客)
    /// </summary>
    [SwaggerSchema(Nullable = true)]
    public string OtherIdentifier { get; set; } = string.Empty;
    
    /// <summary>
    /// 行为
    /// </summary>
    [SwaggerSchema(Nullable = false)]
    public TraceBehaviorType BehaviorType { get; set; }
    
    /// <summary>
    /// 是否直接访问
    /// </summary>
    [SwaggerSchema(Nullable = false)]
    public bool IsDirectVisit { get; set; }

    /// <summary>
    /// 客户端IP
    /// </summary>
    [SwaggerSchema(Nullable = true)]
    public string ClientIP { get; set; }
    
    /// <summary>
    /// 跟踪日志创建时间
    /// </summary>
    [SwaggerSchema(Nullable = false)]
    public DateTime CreateTime { get; set; } = DateTime.Now;
    
    /// <summary>
    /// 推广码分享形式
    /// </summary>
    [SwaggerSchema(Nullable = true)]
    public TraceShareType ShareType { get; set; }

    /// <summary>
    /// 推广码访问目标
    /// </summary>
    [SwaggerSchema(Nullable = false)]
    public TraceVisitTargetType VisitTargetType { get; set; }
    
    /// <summary>
    /// 推广码访问目标名称
    /// </summary>
    [SwaggerSchema(Nullable = true)]
    public string VisitTargetName { get; set; }
}