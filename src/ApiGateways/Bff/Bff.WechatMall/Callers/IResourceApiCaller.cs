using Common.Caller;
using Contracts.Common.Resource.DTOs;
using Contracts.Common.Resource.DTOs.AggregateResource;
using Contracts.Common.Resource.DTOs.EsGdsHotel;
using Contracts.Common.Resource.DTOs.EsPopularCity;
using Contracts.Common.Resource.DTOs.EsTradingArea;
using Contracts.Common.Resource.DTOs.GDSHotel;
using Contracts.Common.Resource.DTOs.Hotel;
using Contracts.Common.Resource.DTOs.PopularCity;
using Contracts.Common.Resource.DTOs.StandardHotelRoomCode;
using Contracts.Common.Resource.DTOs.ThirdHotel;
using Contracts.Common.Resource.DTOs.YouxiaHotel;
using EfCoreExtensions.Abstract;

namespace Bff.WechatMall.Callers;

public interface IResourceApiCaller : IHttpCallerBase
{
    #region EsTradingArea

    /// <summary>
    /// 查询es商圈数据
    /// </summary>
    Task<IEnumerable<SearchEsTradingAreaOutput>> SearchEsTradingArea(SearchEsTradingAreaInput input);
    /// <summary>
    /// 查询es商圈数据
    /// </summary>
    Task<IEnumerable<SearchEsTradingAreaOutput>> SearchEsTradingAreaV2(SearchEsTradingAreaInput input);

    /// <summary>
    /// es商圈类型聚合统计
    /// </summary>
    /// <returns></returns>
    Task<List<GetEsTradingAreaAggregationsOutput>> GetEsTradingAreaAggregations(GetEsTradingAreaAggregationsInput input);
    
    #endregion

    #region Hotel

    /// <summary>
    /// 查询资源酒店基础信息
    /// </summary>
    Task<List<GetSimpleInfoOutput>> GetByHotelIds(params long[] ids);

    /// <summary>
    /// 查询资源酒店详情信息
    /// </summary>
    Task<Contracts.Common.Resource.DTOs.HotelDetailOutput> GetHotelDetail(long hotelId);
    
    /// <summary>
    /// 获取酒店基础设施信息
    /// </summary>
    Task<List<FacilitiesOutput>> GetHotelFacilities();

    /// <summary>
    /// 获取资源酒店(包括房型)图片
    /// </summary>
    Task<HotelPhotosOutput> GetHotelPhotos(long hotelId);
    
    /// <summary>
    /// 获取资源酒店房型信息
    /// </summary>
    Task<List<GetRoomsByHotelIdsOutput>> GetRoomsByHotelIds(GetRoomsByHotelIdsInput input);
    
    #endregion

    #region ThirdHotel

    /// <summary>
    /// 查询第三方平台多酒店日历最低价
    /// </summary>
    Task<List<QueryDateMinPriceOutput>> QueryThirdHotelDateMinPrices(QueryDateMinPriceInput input);

    /// <summary>
    /// 查询第三方平台酒店报价信息
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<GetThirdHotelPriceOutput> GetThirdHotelPrice(GetThirdHotelPriceInput input);

    /// <summary>
    /// 第三方平台酒店试单
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<CheckAvailabilityOutput> ThirdHotelCheckAvailability(CheckAvailabilityInput input);

    /// <summary>
    /// 根据酒店获取所有第三方酒店关联关系
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<IEnumerable<ThirdHotelBindOutput>> GetThirdHotelBinds(GetThirdHotelBindsInput input);

    /// <summary>
    /// 根据酒店ids批量获取所有第三方酒店关联关系
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<IEnumerable<GetThirdHotelBindOutput>> GetThirdHotelBindList(GetThirdHotelBindListInput input);

    #endregion

    #region Es AggregateResource

    /// <summary>
    /// 聚合资源-目的地筛选
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<DestinationSearchOutput> AggregateResourceSearchDestinations(DestinationSearchInput input);

    #endregion

    #region ESPopularCity
    /// <summary>
    /// ES查询热门城市
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<IEnumerable<Contracts.Common.Resource.DTOs.EsPopularCity.SearchEsPopularCityOutput>> SearchEsPopularCity(SearchEsPopularCityInput input);

    /// <summary>
    /// 热门酒店城市列表
    /// </summary>
    /// <returns></returns>
    Task<IEnumerable<Contracts.Common.Resource.DTOs.EsPopularCity.SearchEsPopularCityOutput>> GetEsPopularCity();
    #endregion

    #region PopularCity
    /// <summary>
    /// 热门酒店
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<PagingModel<SearchPopularCityDto>> SearchPopularCity(SearchPopularCityInput input);
    #endregion

    #region city
    Task<List<CityOutput>> QueryCity(QueryInput input); 
    #endregion

    #region GDSHotelPriceStrategy
    /// <summary>
    /// 查询GDS最低价
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<List<SearchGDSHotelMinPriceOutput>> SearchGDSHotelMinPrice(SearchGDSHotelMinPriceInput input);

    /// <summary>
    /// 查询RP
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<List<SearchGDSHotelPriceStrategyOutput>> SearchGDSPriceStrategy(SearchGDSHotelPriceStrategyInput input);

    /// <summary>
    /// 查询价格策略以及其他汇总信息
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<GDSSearchPriceStrategySupplementOutput> GDSSearchPriceStrategySupplement(SearchGDSHotelPriceStrategyInput input);

    /// <summary>
    /// 查询GDS扩展信息-最低价，尊享权益
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<List<GetGDSHotelListExtendOutput>> GetGDSHotelListExtend(GetGDSHotelListExtendInput input);

    /// <summary>
    /// 查询GDS房型图片
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<List<GetGdsHotelRoomPhotoOutput>> GetGDSRoomPhotos(GetGdsHotelRoomPhotoInput input);
    #endregion

    #region StandardHotelRommCode
    /// <summary>
    /// 查询所有标准房型Codes
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<List<SearchStandardHotelRoomCodeOutput>> SearchStandardHotelRoomCodes(SearchStandardHotelRoomCodeInpuy input);

    /// <summary>
    /// 查询第三方关联的房型数据
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<List<GetThirdCodeMappingOutput>> GetThirdCodeMapping(GetThirdCodeMappingInput input);
    #endregion

    #region GDSHotel
    /// <summary>
    /// 分页查询GDS酒店 es
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<PagingModel<SearchEsGdsHotelPageOutput>> SearchESGDSHotel(SearchEsGdsHotelPageInput input);

    /// <summary>
    /// 高定酒店详情
    /// </summary>
    /// <param name="hotelId"></param>
    /// <returns></returns>
    Task<Contracts.Common.Resource.DTOs.GDSHotel.GDSHotelDetailOutput> GDSHotelDetail(long hotelId);

    /// <summary>
    /// 高定酒店详情（多个）
    /// </summary>
    /// <param name="hotelIds"></param>
    /// <returns></returns>
    Task<List<Contracts.Common.Resource.DTOs.GDSHotel.GDSHotelDetailOutput>> GetGDSHotelDetails(params long[] hotelIds);

    /// <summary>
    /// GDS酒店首图
    /// </summary>
    /// <param name="hotelIds"></param>
    /// <returns></returns>
    Task<List<Contracts.Common.Resource.DTOs.GDSHotel.GetGDSHotelFirstPhotoOutput>> GetGDSHotelFirstPhoto(params long[] hotelIds);

    /// <summary>
    /// GDS酒店图片
    /// </summary>
    /// <param name="hotelId"></param>
    /// <returns></returns>
    Task<Contracts.Common.Resource.DTOs.GDSHotel.GDSHotelPhotosOutput> GDSHotelPhotos(long hotelId);

    /// <summary>
    /// 获取高定酒店绑定的第三方id
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<List<GetThirdHotelBindsOutput>> GetGDSThirdHotelBinds(Contracts.Common.Resource.DTOs.GDSHotel.GetGDSThirdHotelBindsInput input);

    /// <summary>
    /// 高定酒店详情
    /// </summary>
    /// <param name="hotelId"></param>
    /// <returns></returns>
    Task<List<GDSHotelExtendOutput>> GetGDSHotelExtend(params long[] hotelIds);

    /// <summary>
    /// 获取高定房型图片
    /// </summary>
    /// <param name="hotelRoomIds"></param>
    /// <returns></returns>
    Task<List<GetGDSHotelRoomFirstPhotoOutput>> GetGDSHotelRoomFirstPhotos(params long[] hotelRoomIds);

    /// <summary>
    /// 价格检查
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<GDSHotelPriceCheckOutput> PriceCheck(GDSHotelPriceCheckInput input);

    /// <summary>
    /// 获取高定酒店房型
    /// </summary>
    /// <param name="hotelId"></param>
    /// <returns></returns>
    Task<List<GetRoomsByHotelIdOutput>> GetGDSHotelRoomsByHotelId(long hotelId);

    /// <summary>
    /// 获取GDS酒店房型
    /// </summary>
    /// <param name="roomId"></param>
    /// <returns></returns>
    Task<GetGDSHotelRoomDetailOutput> GetGDSRoomByRoomId(long roomId);
    #endregion

    #region youxia
    Task<List<SearchGDSHotelMinPriceOutput>> SearchYouxiaHotelMinPrice(SearchYouxiaHotelMinPriceInput input);

    Task<List<SearchGDSHotelPriceStrategyOutput>> SearchYouxiaHotelPriceStrategy(SearchYouxiaHotelPriceStrategyInput input);

    Task<SearchGDSHotelPriceStrategyOutput> PriceStrategyDetail(SearchYouxiaHotelPriceStrategyDetailInput input);
    #endregion

    #region Country
    /// <summary>
    /// 获取国家列表
    /// </summary>
    /// <returns></returns>
    Task<IEnumerable<CountryOutput>> GetCountries(); 
    #endregion
}