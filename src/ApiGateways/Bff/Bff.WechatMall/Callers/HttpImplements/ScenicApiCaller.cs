using Common.Caller;
using Common.ServicesHttpClient;
using Contracts.Common.Product.DTOs.ProductInformationTemplate;
using Contracts.Common.Scenic.DTOs.ScenicSpot;
using Contracts.Common.Scenic.DTOs.Ticket;
using Contracts.Common.Scenic.DTOs.TicketsCalendarPrice;
using EfCoreExtensions.Abstract;
using Microsoft.Extensions.Options;

namespace Bff.WechatMall.Callers.HttpImplements;

public class ScenicApiCaller : HttpCallerBase, IScenicApiCaller
{
    public ScenicApiCaller(IOptions<ServicesAddress> options, IHttpClientFactory httpClientFactory)
        : base(options.Value.Scenic, httpClientFactory)
    {
    }

    #region ScenicSpot

    public async Task<List<GetScenicSpotDetailOutput>> GetScenicSpotDetailByIds(List<long> ids)
    {
        var relativePath = "/ScenicSpot/GetByIds";
        return await PostAsync<List<long>, List<GetScenicSpotDetailOutput>>(relativePath, ids);
    }

    public async Task<PagingModel<SearchScenicSpotByCustomerOutput>> SearchScenicSpot(SearchScenicSpotsInput input)
    {
        var relativePath = "/ScenicSpot/SearchByCustomer";
        return await PostAsync<SearchScenicSpotsInput, PagingModel<SearchScenicSpotByCustomerOutput>>(relativePath,
            input);
    }

    public async Task<List<GetMinPriceOutput>> GetScenicSpotMinPrice(params long[] scenicSpotIds)
    {
        var relativePath = "/ScenicSpot/GetMinPrice";
        return await PostAsync<long[], List<GetMinPriceOutput>>(relativePath, scenicSpotIds);
    }

    #endregion

    #region Ticket

    public async Task<IEnumerable<TicketPriceOutput>> SearchTicketPrice(SearchTicketPriceInput input)
    {
        var relativePath = "/TicketPrice/Search";
        return await PostAsync<SearchTicketPriceInput, IEnumerable<TicketPriceOutput>>(relativePath, input);
    }

    public async Task<IEnumerable<TicketCalendarOutput>> SearchTicketCalendarPrice(SearchTicketCalendarInput input)
    {
        var relativePath = "/TicketCalendarPrice/Search";
        return await PostAsync<SearchTicketCalendarInput, IEnumerable<TicketCalendarOutput>>(relativePath, input);
    }

    public async Task<GetTicketDetailOutput> GetTicketDetail(long id)
    {
        var relativePath = $"/Ticket/Detail?id={id}";
        return await GetAsync<GetTicketDetailOutput>(relativePath);
    }

    public async Task<IEnumerable<TicketListQueryOutput>> QueryScenicTicketList(TicketListQueryInput input)
    {
        var relativePath = "/Ticket/QueryTicketList";
        return await PostAsync<TicketListQueryInput, IEnumerable<TicketListQueryOutput>>(relativePath, input);
    }

    public async Task<List<GenericScenicInventoryQueryOutput>> GenericScenicInventoryQuery(
        GenericScenicInventoryQueryInput input)
    {
        var relativePath = "/Ticket/GenericScenicInventoryQuery";
        return await PostAsync<GenericScenicInventoryQueryInput, List<GenericScenicInventoryQueryOutput>>(relativePath,input);
    }
    #endregion

    #region ScenicSpotRedundantData

    public async Task<List<GetScenicSpotRedundantDataOutput>> GetScenicSpotRedundantDataByIds(List<long> scenicSpotIds)
    {
        var relativePath = "/ScenicSpotRedundantData/GetByIds";
        return await PostAsync<List<long>,List<GetScenicSpotRedundantDataOutput>>(relativePath,scenicSpotIds);
    }

    #endregion

    #region ProductInformationTemplate
    /// <summary>
    /// 更小颗粒度上配置的模版优先，套餐层模版设置 > 产品层模版设置 > 默认模版设置
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    public async Task<GetProductTempFieldsDetailOutput> ProductInformationTemplateGetProductTempFieldsDetail(GetProductTempFieldsDetailInput input)
    {
        var relativePath = "/ProductInformationTemplate/GetProductTempFieldsDetail";
        return await PostAsync<GetProductTempFieldsDetailInput, GetProductTempFieldsDetailOutput>(relativePath, input);
    }
    #endregion

}
