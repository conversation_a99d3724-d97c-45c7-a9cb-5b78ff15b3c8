using Common.Caller;
using Contracts.Common.Hotel.DTOs;
using Contracts.Common.Hotel.DTOs.ApiHotel;
using Contracts.Common.Hotel.DTOs.Hotel;
using Contracts.Common.Hotel.DTOs.HotelExtend;
using Contracts.Common.Hotel.DTOs.HotelOperationConfig;
using Contracts.Common.Hotel.DTOs.HotelRedundantData;
using Contracts.Common.Hotel.DTOs.PriceStrategyCalendarPrice;
using Contracts.Common.Hotel.Enums;
using EfCoreExtensions.Abstract;

namespace Bff.WechatMall.Callers;

public interface IHotelApiCaller : IHttpCallerBase
{
    #region Hotel

    /// <summary>
    /// ES酒店列表数据查询
    /// </summary>
    Task<PagingModel<SearchEsHotelOutput>> SearchEsHotel(EsHotelSearchInput request);
    
    /// <summary>
    /// ES酒店分页查询V2
    /// </summary>
    Task<PagingModel<SearchEsHotelOutput>> SearchEsHotelV2(EsHotelSearchInput input);

    /// <summary>
    /// ES酒店详情
    /// </summary>
    /// <param name="ids"></param>
    /// <returns></returns>
    Task<List<SearchEsHotelOutput>> GetEsHotelDetail(params long[] ids);
    
    /// <summary>
    /// ES酒店详情V2
    /// </summary>
    /// <param name="mixHotelIds"></param>
    /// <returns></returns>
    Task<List<SearchEsHotelOutput>> GetEsHotelDetailV2(params long[] mixHotelIds);
    
    /// <summary>
    /// 根据酒店ID获取本地酒店基础信息
    /// </summary>
    Task<List<GetHotelByIdsOutput>> GetHotelByIds(GetHotelByIdsInput input);

    /// <summary>
    /// 查询本地酒店详情
    /// </summary>
    Task<GetHotelDetailsOutput> GetHotelDetail(long hotelId);

    /// <summary>
    /// 查询本地酒店图片(包括房型)
    /// </summary>
    Task<GetHotelPhotosOutput> GetHotelPhotos(long hotelId);

    /// <summary>
    /// 获取酒店房型列表
    /// </summary>
    Task<GetHotelRoomsOutput> GetHotelRooms(GetHotelRoomsInput input);

    /// <summary>
    /// 本地酒店分页查询
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<PagingModel<SearchHotelsOutput>> SearchLocalHotel(SearchHotelsInput input);

    /// <summary>
    /// 本地酒店分页查询
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<List<GetHotelIdsByResourceHotelIdsOutput>> GetResourceHotelIdByHotelIds(List<long> ids);

    /// <summary>
    /// 获取酒店扩展信息（基础扩展信息，酒店政策）
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<HotelPolicyOutput> DetailPolicy(HotelPolicyDetailInput input);

    /// <summary>
    /// 根据资源酒店id获取酒店id
    /// </summary>
    /// <param name="ids"></param>
    /// <returns></returns>
    Task<List<GetHotelIdsByResourceHotelIdsOutput>> GetHotelIdsByResourceHotelIds(List<long> ids);

    #endregion

    #region ApiHotel

    /// <summary>
    /// 获取当前租户下的汇智酒店配置
    /// </summary>
    Task<List<ApiHotelSettingDto>> GetApiHotelSettings();

    /// <summary>
    /// 推送酒店权重值
    /// </summary>
    Task PushWeightValue(IEnumerable<PushWeightValueInput> input);

    #endregion

    #region PriceStrategy

    /// <summary>
    /// 获取本地酒店价格策略最低价
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    /// <exception cref="ErrorTypes.Inventory.GetInventoryFail"></exception>
    Task<List<GetMinPriceOutput>> GetPriceStrategyMinPrice(GetMinPriceInput input);


    /// <summary>
    /// 获取本地酒店可售报价
    /// </summary>
    Task<GetSaleByHotelOutput> GetSalePriceStrategyByHotel(GetSaleByHotelInput input);

    /// <summary>
    /// 验证价格
    /// </summary>
    /// <param name="request"></param>
    /// <exception cref="ErrorTypes.Inventory.GetInventoryFail"></exception>
    /// <returns></returns>
    Task<CheckSaleOutput> PreOrderCheckSale(CheckSaleInput request);

    /// <summary>
    /// 查询近30天日历价最低最高价
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<List<GetRecentPriceOutput>> GetRecentMinOrMaxPrice(
        (List<long> hotelIds, RecentPriceType recentPriceType) input);

    /// <summary>
    /// 日历酒店的价格策略按可预订最近日期
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<GetSaleableOutput> GetPriceStrategySaleable(GetSaleableInput input);

    #endregion

    #region HotelRedundantData

    /// <summary>
    /// 获取有分销佣金的设置的酒店id列表
    /// </summary>
    Task<List<long>> GetHasCommissionHotelIds();
    
    /// <summary>
    /// 批量获取酒店冗余数据
    /// </summary>
    Task<List<GetHotelRedundantDataOutput>> GetRedundantDataByIds(List<long> hotelIds);

    #endregion

    #region HotelOperationConfig

    /// <summary>
    /// 获取酒店运营配置
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<List<HotelOperationConfigOutput>> GetHotelOperationConfigs(GetHotelOperationConfigsInput input);

    #endregion
}