using Contracts.Common.Hotel.DTOs.PriceStrategyCalendarPrice;
using FluentValidation;

namespace Bff.WechatMall.Validators.HotelOrder;

public class CheckSaleInputValidator : AbstractValidator<CheckSaleInput>
{
    public CheckSaleInputValidator()
    {
        RuleFor(x => x.HotelId).NotEmpty();
        RuleFor(x => x.RoomId).NotEmpty();
        RuleFor(x => x.PriceStrategyId).NotEmpty();
        RuleFor(x => x.Quantity).GreaterThan(0);
        RuleFor(x => x.BeginDate).NotEmpty().GreaterThanOrEqualTo(DateTime.Today);
        RuleFor(x => x.EndDate).NotEmpty().GreaterThan(x => x.BeginDate);
    }
}
