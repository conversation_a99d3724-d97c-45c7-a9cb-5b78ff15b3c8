using Bff.WechatMall.Models.User;
using FluentValidation;

namespace Bff.WechatMall.Validators.User;

public class RegisterByWechatMPInputValidator : AbstractValidator<RegisterByWechatMPInput>
{
    public RegisterByWechatMPInputValidator()
    {
        RuleFor(s => s.Code)
            .NotEmpty();

        RuleFor(s => s.PhoneNumber)
            .NotEmpty()
            .Length(11);

        RuleFor(s => s.VerifyCode)
            .NotEmpty()
            .Length(6);
    }
}
