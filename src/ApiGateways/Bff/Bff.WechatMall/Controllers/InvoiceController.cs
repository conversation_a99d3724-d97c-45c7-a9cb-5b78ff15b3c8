using AutoMapper;
using Bff.WechatMall.Callers;
using Bff.WechatMall.Models.Invoice;
using Common.Jwt;
using Common.Swagger;
using Contracts.Common.Order.DTOs;
using Contracts.Common.Order.DTOs.Invoice;
using Contracts.Common.Order.Enums;
using EfCoreExtensions.Abstract;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace Bff.WechatMall.Controllers;

[Route("v1/[controller]/[action]")]
[ApiExplorerSettings(GroupName = nameof(ApiVersion.v1))]
[ApiController]
public class InvoiceController : ControllerBase
{
    private readonly IOrderApiCaller _orderApiCaller;
    private readonly IMapper _mapper;

    public InvoiceController(IOrderApiCaller orderApiCaller,
        IMapper mapper)
    {
        _orderApiCaller = orderApiCaller;
        _mapper = mapper;
    }

    /// <summary>
    /// 查询开票记录
    /// </summary>
    /// <returns></returns>
    [HttpPost]
    [ProducesResponseType(typeof(PagingModel<InvoiceRecordDto>), (int)HttpStatusCode.OK)]
    [Authorize]
    public async Task<IActionResult> Search(SearchBffInput bffInput)
    {
        var userId = HttpContext.User.ParseUserInfo<CurrentUser>().UserId;
        var input = _mapper.Map<SearchInvoiceRecordInput>(bffInput);
        input.CreatorId = userId;
        var result = await _orderApiCaller.SearchInvoice(input);
        return Ok(result);
    }

    /// <summary>
    /// 申请开票
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ProducesResponseType(typeof(ApplyOutput), (int)HttpStatusCode.OK)]
    [Authorize]
    public async Task<IActionResult> Apply(ApplyBffInput bffInput)
    {
        var currentUser = HttpContext.User.ParseUserInfo<CurrentUser>();
        var input = _mapper.Map<ApplyInput>(bffInput);
        input.CreatorId = currentUser.UserId;
        input.CreatorName = currentUser.NickName;
        input.SourceChannel = InvoiceSourceChannel.WechatMall;
        input.OperationUser = new OperationUserDto
        {
            UserType = UserType.Merchant,
            UserId = currentUser.UserId,
            Name = currentUser.NickName
        };
        var result = await _orderApiCaller.ApplyInvoice(input);
        return Ok(result);
    }

    /// <summary>
    /// 根据订单Id检查开票状态
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ProducesResponseType(typeof(List<CheckStatusOutPut>), (int)HttpStatusCode.OK)]
    [Authorize]
    public async Task<IActionResult> CheckStatusByOrderIds(CheckStatusBffInput bffInput)
    {
        var user = HttpContext.User.ParseUserInfo<CurrentUser>();
        var input = _mapper.Map<CheckStatusInput>(bffInput);
        input.SourceChannel = InvoiceSourceChannel.WechatMall;
        input.CreatorId = user.UserId;
        var result = await _orderApiCaller.InvoiceCheckStatusByOrderIds(input);
        return Ok(result);
    }
}
