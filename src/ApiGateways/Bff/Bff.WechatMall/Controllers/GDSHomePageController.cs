using AutoMapper;
using Bff.WechatMall.Callers;
using Bff.WechatMall.Models.GDSHomePage;
using Common.Swagger;
using Contracts.Common.Hotel.DTOs.Hotel;
using Contracts.Common.Resource.DTOs.EsPopularCity;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace Bff.WechatMall.Controllers;

/// <summary>
/// GDS首页数据
/// </summary>
[Route("v1/[controller]/[action]")]
[ApiExplorerSettings(GroupName = nameof(ApiVersion.v1))]
[ApiController]
public class GDSHomePageController : ControllerBase
{
    private readonly IHotelApiCaller _hotelApiCaller;
    private readonly IResourceApiCaller _resourceApiCaller;
    private readonly IMapper _mapper;

    public GDSHomePageController(IHotelApiCaller hotelApiCaller, IMapper mapper, IResourceApiCaller resourceApiCaller)
    {
        _hotelApiCaller = hotelApiCaller;
        _mapper = mapper;
        _resourceApiCaller = resourceApiCaller;
    }

    /// <summary>
    /// 酒店搜索页
    /// </summary>
    /// <returns></returns>
    [HttpPost]
    [ProducesResponseType(typeof(SearchGDSHomePageBffOutput), (int)HttpStatusCode.OK)]
    public async Task<IActionResult> Search(SearchGDSHomePageBffInput input)
    {
        var result = new SearchGDSHomePageBffOutput();
        var hotels = await _hotelApiCaller.SearchLocalHotel(new SearchHotelsInput
        {
            KeyWord = input.KeyWord,
            PageIndex = 1,
            PageSize = 999,
        });
        result.Hotels = _mapper.Map<List<SearchGDSHomePageHotelBffOutput>>(hotels.Data);
        var cities = await _resourceApiCaller.SearchEsPopularCity(new SearchEsPopularCityInput
        {
            CityName = input.KeyWord
        });
        if (cities?.Any() == true)
            result.City = _mapper.Map<List<SearchGDSHomePageCityBffOutput>>(cities);
        return Ok(result);
    }

}
