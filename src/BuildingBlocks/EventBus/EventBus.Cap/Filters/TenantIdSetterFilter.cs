using DotNetCore.CAP.Filter;

namespace EventBus.Cap.Filters;
public class TenantIdSetterFilter : SubscribeFilter
{
    internal const string _capHeaderTenantName = "custom-tenant";

    private readonly Action<long> _setTenant;

    public TenantIdSetterFilter(Action<long> setTenant)
    {
        _setTenant = setTenant;
    }

    public override async Task OnSubscribeExecutingAsync(ExecutingContext context)
    {
        context.DeliverMessage.Headers.TryGetValue(_capHeaderTenantName, out string? tenant);
        _ = long.TryParse(tenant, out var tenantId);
        _setTenant(tenantId);
        await Task.CompletedTask;
    }
}
