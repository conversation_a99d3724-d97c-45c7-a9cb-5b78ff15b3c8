using DotNetCore.CAP;
using DotNetCore.CAP.Transport;
using EventBus.Cap.Transaction;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.DependencyInjection;

namespace EventBus.Cap;

public class CapPublisherDecorator : ICapPublisher
{
    private readonly ICapPublisher _capPublisher;
    private readonly IHttpContextAccessor _contextAccessor;

    public CapPublisherDecorator(ICapPublisher capPublisher,
        IHttpContextAccessor contextAccessor)
    {
        _capPublisher = capPublisher;
        _contextAccessor = contextAccessor;
    }

    public IServiceProvider ServiceProvider => throw new NotImplementedException();

    public AsyncLocal<ICapTransaction> Transaction => throw new NotImplementedException();

    private ICapTransaction CapTransaction { get; set; }

    private void SetTransaction()
    {
        var _transactionReplacer = _capPublisher.ServiceProvider.GetService<IEFCoreTransactionReplacer>();
        var originalTransaction = _transactionReplacer.GetOriginalTransaction();
        if (originalTransaction is null)
            return;
        if (CapTransaction is null)
        {
            var dispatcher = _capPublisher.ServiceProvider.GetRequiredService<IDispatcher>();
            var transaction = new EFCoreTransaction(dispatcher, originalTransaction);
            CapTransaction = transaction;
            _transactionReplacer.Replace(transaction);
        }
        if (_capPublisher.Transaction.Value is null)
        {
            _capPublisher.Transaction.Value = CapTransaction;
        }
    }

    private void SetTenant(IDictionary<string, string?> headers)
    {
        if (_contextAccessor?.HttpContext is null)
            return;
        _contextAccessor.HttpContext.Request.Headers.TryGetValue("Tenant", out var tenantId);
        headers.TryAdd(Filters.TenantIdSetterFilter._capHeaderTenantName, tenantId);
    }

    public void Publish<T>(string name, T? value, string? callbackName = null)
    {
        var headers = new Dictionary<string, string?> { { DotNetCore.CAP.Messages.Headers.CallbackName, callbackName } };
        Publish(name, value, headers);
    }

    public void Publish<T>(string name, T? contentObj, IDictionary<string, string?> headers)
    {
        SetTenant(headers);
        SetTransaction();
        _capPublisher.Publish(name, contentObj, headers);
    }

    public Task PublishAsync<T>(string name, T? contentObj, string? callbackName = null, CancellationToken cancellationToken = default)
    {
        var headers = new Dictionary<string, string?> { { DotNetCore.CAP.Messages.Headers.CallbackName, callbackName } };
        return PublishAsync(name, contentObj, headers, cancellationToken);
    }

    public Task PublishAsync<T>(string name, T? contentObj, IDictionary<string, string?> headers, CancellationToken cancellationToken = default)
    {
        SetTenant(headers);
        SetTransaction();
        return _capPublisher.PublishAsync(name, contentObj, headers, cancellationToken);
    }

    public void PublishDelay<T>(TimeSpan delayTime, string name, T? contentObj, string? callbackName = null)
    {
        var headers = new Dictionary<string, string?> { { DotNetCore.CAP.Messages.Headers.CallbackName, callbackName } };
        PublishDelay(delayTime, name, contentObj, headers);
    }

    public void PublishDelay<T>(TimeSpan delayTime, string name, T? contentObj, IDictionary<string, string?> headers)
    {
        SetTenant(headers);
        SetTransaction();
        _capPublisher.PublishDelay(delayTime, name, contentObj, headers);
    }

    public Task PublishDelayAsync<T>(TimeSpan delayTime, string name, T? contentObj, string? callbackName = null, CancellationToken cancellationToken = default)
    {
        var headers = new Dictionary<string, string?> { { DotNetCore.CAP.Messages.Headers.CallbackName, callbackName } };
        return PublishDelayAsync(delayTime, name, contentObj, headers, cancellationToken);
    }

    public Task PublishDelayAsync<T>(TimeSpan delayTime, string name, T? contentObj, IDictionary<string, string?> headers, CancellationToken cancellationToken = default)
    {
        SetTenant(headers);
        SetTransaction();
        return _capPublisher.PublishDelayAsync(delayTime, name, contentObj, headers, cancellationToken);
    }
}