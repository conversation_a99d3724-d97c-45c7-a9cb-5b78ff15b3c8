using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Common.GlobalException
{
    public class ErrorResponse
    {
        public ErrorResponse()
        {
        }

        public ErrorResponse(string message)
        {
            Message = message;
        }

        public ErrorResponse(string businessErrorType, string message, object data = null)
        {
            BusinessErrorType = businessErrorType;
            Message = message;
            Data = data;
        }

        public string BusinessErrorType { get; set; }

        public string Message { get; set; }

        public object Data { get; set; }
    }
}
