using Serilog.Events;
using Serilog.Formatting.Elasticsearch;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;

namespace Common.Serilog
{
    public class CustomElasticsearchJsonFormatter : ElasticsearchJsonFormatter
    {
        private readonly bool _inlineFields;

        public CustomElasticsearchJsonFormatter(bool omitEnclosingObject = false, string closingDelimiter = null, bool renderMessage = true, IFormatProvider formatProvider = null, ISerializer serializer = null, bool inlineFields = false, bool renderMessageTemplate = true, bool formatStackTraceAsArray = false)
            : base(omitEnclosingObject, closingDelimiter, renderMessage, formatProvider, serializer, inlineFields, renderMessageTemplate, formatStackTraceAsArray)
        {
            _inlineFields = inlineFields;
        }

        /// <summary>
        /// Writes out the attached properties
        /// </summary>
        protected override void WriteProperties(IReadOnlyDictionary<string, LogEventPropertyValue> properties, TextWriter output)
        {
            if (_inlineFields)
            {
                output.Write(",");
                WritePropertiesValues(properties, output);
                return;
            }
            var logs = output.ToString().Replace("{@", "{");
            output.Write(",\"{0}\":{{", "fields");
            string precedingDelimiter = string.Empty;
            var templateProperties = properties.Where(x => logs.Contains("{" + x.Key + "}"));
            if (templateProperties.Any())
            {
                output.Write("\"{0}\":[", "Template");
                foreach (var property in templateProperties)
                {
                    output.Write(precedingDelimiter);
                    output.Write("{");
                    output.Write($"\"key\":\"{property.Key}\",");
                    precedingDelimiter = string.Empty;
                    var val = LogValueRander.Render(property.Value);
                    WriteJsonProperty("value", val, ref precedingDelimiter, output);
                    output.Write("}");
                }
                output.Write("]");
            }

            var otherProperties = properties.Except(templateProperties);
            if (otherProperties.Any())
            {
                output.Write(precedingDelimiter);
                precedingDelimiter = string.Empty;
                foreach (var property in otherProperties)
                {
                    object val = property.Value;
                    WriteJsonProperty(property.Key, val, ref precedingDelimiter, output);
                }
            }
            output.Write("}");
        }
    }

    public static class LogValueRander
    {
        public static ScalarValue Render(LogEventPropertyValue propertyValue,
            string format = null, IFormatProvider formatProvider = null)
        {
            if (propertyValue is ScalarValue scalarValue)
            {
                return scalarValue;
            }

            StringBuilder stringBuilder = new();
            ValueRander(stringBuilder, propertyValue, format, formatProvider);

            scalarValue = new(stringBuilder);
            return scalarValue;
        }

        /// <summary>
        /// 属性值渲染
        /// </summary>
        /// <param name="output"></param>
        /// <param name="propertyValue"></param>
        /// <param name="format"></param>
        /// <param name="formatProvider"></param>
        static void ValueRander(StringBuilder stringBuilder, LogEventPropertyValue propertyValue, string format = null, IFormatProvider formatProvider = null)
        {
            if (propertyValue is ScalarValue scalarValue)
            {
                var val = scalarValue.Value is not null ? $"\"{scalarValue.Value}\"" : "null";
                stringBuilder.Append(val);
            }
            else if (propertyValue is StructureValue structureValue)
            {
                StructureValueRender(stringBuilder, structureValue, format, formatProvider);
            }
            else if (propertyValue is SequenceValue sequenceValue)
            {
                SequenceValueRender(stringBuilder, sequenceValue, format, formatProvider);
            }
            else
                stringBuilder.Append($"{propertyValue}");
        }

        /// <summary>
        /// 属性渲染
        /// </summary>
        /// <param name="output"></param>
        /// <param name="property"></param>
        /// <param name="format"></param>
        /// <param name="formatProvider"></param>
        static void PropertyRender(StringBuilder stringBuilder, LogEventProperty property, string format = null, IFormatProvider formatProvider = null)
        {
            stringBuilder.Append($"\"{property.Name}\"");
            stringBuilder.Append(": ");
            ValueRander(stringBuilder, property.Value, format, formatProvider);
        }

        static void StructureValueRender(StringBuilder stringBuilder,
           StructureValue structureValue,
           string format = null,
           IFormatProvider formatProvider = null)
        {
            var properties = structureValue.Properties.ToArray();
            stringBuilder.Append("{ ");
            var allButLast = properties.Length - 1;
            for (var i = 0; i < allButLast; i++)
            {
                var property = properties[i];
                PropertyRender(stringBuilder, property, format, formatProvider);
                stringBuilder.Append(", ");
            }

            if (properties.Length > 0)
            {
                var last = properties[^1];
                PropertyRender(stringBuilder, last, format, formatProvider);
            }
            stringBuilder.Append(" }");
        }

        static void SequenceValueRender(StringBuilder stringBuilder,
            SequenceValue sequenceValue,
            string format = null,
            IFormatProvider formatProvider = null)
        {
            var elements = sequenceValue.Elements.ToArray();

            stringBuilder.Append('[');
            var allButLast = elements.Length - 1;
            for (var i = 0; i < allButLast; ++i)
            {
                ValueRander(stringBuilder, elements[i], format, formatProvider);
                stringBuilder.Append(", ");
            }

            if (elements.Length > 0)
            {
                ValueRander(stringBuilder, elements[^1], format, formatProvider);
            }
            stringBuilder.Append(']');
        }
    }
}
