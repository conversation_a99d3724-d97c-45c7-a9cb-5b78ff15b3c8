using Microsoft.AspNetCore.Hosting;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using SkyApm.Diagnostics.CAP;
using SkyApm.Utilities.DependencyInjection;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Common.SkyWalking
{
    public static class SkyApmRegisterExtensions
    {
        public static IServiceCollection AddSkyApmWithCap(this IServiceCollection services, IWebHostEnvironment env)
        {
            if (env.IsDevelopment() == false)
            {
                services.AddSkyApmExtensions()
                    .AddCap();
            }
            return services;
        }
    }
}
