using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Primitives;

namespace EfCoreExtensions.Abstract;

public interface ITenantIdentify
{
    public long GetTenantId();
    public void SetTenantId(long id);
}

//TODO: 移除依赖和硬编码，由外部实现该接口
public class TenantIdentify : ITenantIdentify
{
    private long? _tenantId;
    private readonly IHttpContextAccessor? _httpContextAccessor;

    public TenantIdentify(long tenantId)
    {
        _tenantId = tenantId;
    }

    public TenantIdentify(IHttpContextAccessor httpContextAccessor)
    {
        _httpContextAccessor = httpContextAccessor;
    }

    public long GetTenantId()
    {
        if (_tenantId.HasValue)
            return _tenantId.Value;

        var tenantHeader = _httpContextAccessor!.HttpContext.Request.Headers
            .FirstOrDefault(s => string.Equals(s.Key, "Tenant", StringComparison.OrdinalIgnoreCase));
        if (tenantHeader.Value != StringValues.Empty)
        {
            _tenantId = long.Parse(tenantHeader.Value!);
        }
        return _tenantId ?? 0;
    }

    public void SetTenantId(long id)
    {
        _tenantId = id;
    }
}
