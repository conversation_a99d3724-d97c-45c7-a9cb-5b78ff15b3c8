using Hangfire;
using Hangfire.Redis;
using Hangfire.Storage.SQLite;
using Microsoft.AspNetCore.Hosting;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Newtonsoft.Json;
using Newtonsoft.Json.Serialization;
using System;
using System.Text.RegularExpressions;

namespace HangfireClient;

public static class ServiceCollectionExtensions
{
    public static JsonSerializerSettings jsonSerializerSettings = new()
    {
        DateFormatString = "yyyy-MM-dd HH:mm:ss",
        ReferenceLoopHandling = ReferenceLoopHandling.Ignore,
        NullValueHandling = NullValueHandling.Ignore,
        ContractResolver = new CamelCasePropertyNamesContractResolver()
    };

    /// <summary>
    /// Hangfire客户端
    /// </summary>
    /// <returns></returns>
    public static IServiceCollection AddHangfireClient(this IServiceCollection services, string connectionString, IWebHostEnvironment env)
    {
        var globalConfiguration = GlobalConfiguration.Configuration
               .SetDataCompatibilityLevel(CompatibilityLevel.Version_170)
               .UseColouredConsoleLogProvider()
               .UseSimpleAssemblyNameTypeSerializer()
               .UseSerializerSettings(jsonSerializerSettings);

        Hangfire.Logging.LogProvider.SetCurrentLogProvider(null);

        if (env.IsDevelopment())
        {
            var fullPath = Regex.Match(AppContext.BaseDirectory, @"(.+?)\\src\\").Groups[1].Value;
            fullPath += connectionString;
            //TODO: 等待pr合并再引入包
            globalConfiguration.UseSQLiteStorage(fullPath);
        }
        else
        {
            var db = StackExchange.Redis.ConfigurationOptions
                    .Parse(connectionString)
                    .DefaultDatabase.GetValueOrDefault();
            globalConfiguration.UseRedisStorage(connectionString, new RedisStorageOptions
            {
                Db = db,
                SucceededListSize = 50000,
                DeletedListSize = 50000,
                InvisibilityTimeout = TimeSpan.FromHours(2)
            });
        }

        services.AddHangfire(configuration =>
        {
            configuration = globalConfiguration;
        });

        //全局设置
        JobStorage.Current.JobExpirationTimeout = TimeSpan.FromDays(15);
        GlobalJobFilters.Filters.Add(new AutomaticRetryAttribute { Attempts = 3 });

        return services;
    }
}
