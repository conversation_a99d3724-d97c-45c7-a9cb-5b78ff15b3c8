using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace HangfireClient.Jobs.Order.DTOs;
public class GroupBookingDemandHotelInquiryHandleDto
{
    public long ApplicationFormId { get; set; }

    public long TenantId { get; set; }

    public long[]? HotelInquiryIds { get; set; }

    public DemandHotelInquiryHandleType HandleType { get; set; }

    public DateTime? ExpiredTime { get; set; }
}

public enum DemandHotelInquiryHandleType
{
    /// <summary>
    /// 发单询价
    /// </summary>
    Quotation = 1,
    /// <summary>
    /// 已存在询价
    /// </summary>
    Quoted = 2,
    /// <summary>
    /// 发单询价 已过期
    /// </summary>
    ExpiredQuotation = 3,
    /// <summary>
    /// 发单询价 已结束
    /// </summary>
    EndedQuotation = 4,

    /// <summary>
    /// 重新询价
    /// </summary>
    RevisedQuotation= 5,
}