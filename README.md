feature list:

- [x] 项目文档、架构图、编码规范等文档
- [x] 多项目多环境网关
- [x] 数据聚合BFF层
- [x] 基于CAP组件的EventBus
- [x] SkyWalking链路追踪
- [x] Docker Swarm集群部署
- [x] 统一身份认证集成
- [x] 配置中心
- [x] 公共的数据层基础设施
- [x] 底层自动隔离租户数据
- [x] 后台任务服务
- [x] EFK日志收集
- [x] 基础镜像、精简部署
- [x] 审计、操作日志
- [x] 各网关接口文档，团队协作
- [x] 持续构建
- [x] 更换网关kong，网关dashboard
- [x] 引入分布式事务框架dtm


<br/>
improve list:

- [x] hangfire定时任务创建优化，以 Attribute 形式创建代替 Startup 中创建
- [x] hangfire任务失败警报
- [x] 带授权的Dashboard导航统一入口（CAP、Cousul、SkyWalking、Hangfire）
- [x] 多个服务单独编译导致整体部署时间略久，可考虑按解决方案编译
- [x] fluentd 收集集群日志，es格式化日志
- [ ] 是否允许service调service的问题，如果允许则需要解决循环引用和跨service事务问题
- [ ] efcore codefirst ci/cd
- [ ] 服务内部请求gRPC


<br/>
fix list:  

- [x] Cap仪表盘发现服务，~~断开consul连接后不会重连,~~   
        consul容器销毁重建导致，data未挂载到宿主机，暂时忽略
- [ ] nginx proxy_pass dns缓存问题(未验证) 
        方案1 upstream  https://www.zhihu.com/question/61786355 <br/>
        方案2 resolver 127.0.0.11  https://stackoverflow.com/questions/52823279/how-to-nginx-reverse-proxy-outside-of-docker-to-proxy-pass-to-docker-containers
- [x] fluentd推送日志到es报错 warning: 299 [types removal] Specifying types in bulk requests is deprecated.